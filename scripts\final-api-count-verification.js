#!/usr/bin/env node

/**
 * 最终API接口数量验证脚本
 * 确保HTML文档中的所有数量都已正确更新
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 最终API接口数量验证\n');

/**
 * 验证HTML文档中的所有数量
 */
function verifyHTMLCounts() {
  const htmlPath = path.join(__dirname, '..', 'docs/api.html');
  
  if (!fs.existsSync(htmlPath)) {
    console.log('❌ HTML文档不存在');
    return false;
  }
  
  const htmlContent = fs.readFileSync(htmlPath, 'utf8');
  
  console.log('📊 检查HTML文档中的接口数量:');
  console.log('='.repeat(50));
  
  let allCorrect = true;
  
  // 1. 检查标题中的总数
  const titleMatch = htmlContent.match(/完整的RESTful API接口文档 - 共(\d+)个接口/);
  if (titleMatch) {
    const titleCount = parseInt(titleMatch[1]);
    if (titleCount === 101) {
      console.log(`✅ 标题总数: ${titleCount}个接口`);
    } else {
      console.log(`❌ 标题总数: ${titleCount}个接口 (应为101个)`);
      allCorrect = false;
    }
  } else {
    console.log('❌ 未找到标题中的接口数量');
    allCorrect = false;
  }
  
  // 2. 检查基础信息中的总数
  const infoMatch = htmlContent.match(/<li><strong>总接口数:<\/strong> (\d+)个<\/li>/);
  if (infoMatch) {
    const infoCount = parseInt(infoMatch[1]);
    if (infoCount === 101) {
      console.log(`✅ 基础信息总数: ${infoCount}个接口`);
    } else {
      console.log(`❌ 基础信息总数: ${infoCount}个接口 (应为101个)`);
      allCorrect = false;
    }
  } else {
    console.log('❌ 未找到基础信息中的接口数量');
    allCorrect = false;
  }
  
  // 3. 检查各模块数量
  const moduleChecks = [
    { name: '认证模块', expected: 11, pattern: /🔐 认证模块.*?(\d+)个接口/ },
    { name: '用户模块', expected: 12, pattern: /👤 用户模块.*?(\d+)个接口/ },
    { name: '视频模块', expected: 22, pattern: /🎬 视频模块.*?(\d+)个接口/ },
    { name: '互动模块', expected: 15, pattern: /💬 互动模块.*?(\d+)个接口/ },
    { name: '会员模块', expected: 16, pattern: /💎 会员模块.*?(\d+)个接口/ },
    { name: '支付模块', expected: 8, pattern: /💳 支付模块.*?(\d+)个接口/ },
    { name: '管理模块', expected: 17, pattern: /🔧 管理模块.*?(\d+)个接口/ }
  ];
  
  console.log('\n📋 各模块接口数量:');
  moduleChecks.forEach(check => {
    const match = htmlContent.match(check.pattern);
    if (match) {
      const count = parseInt(match[1]);
      if (count === check.expected) {
        console.log(`✅ ${check.name}: ${count}个接口`);
      } else {
        console.log(`❌ ${check.name}: ${count}个接口 (应为${check.expected}个)`);
        allCorrect = false;
      }
    } else {
      console.log(`❌ ${check.name}: 未找到数量信息`);
      allCorrect = false;
    }
  });
  
  // 4. 检查底部统计总数
  const summaryMatch = htmlContent.match(/<p><strong>(\d+)个接口<\/strong><\/p>/);
  if (summaryMatch) {
    const summaryCount = parseInt(summaryMatch[1]);
    if (summaryCount === 101) {
      console.log(`\n✅ 底部统计总数: ${summaryCount}个接口`);
    } else {
      console.log(`\n❌ 底部统计总数: ${summaryCount}个接口 (应为101个)`);
      allCorrect = false;
    }
  } else {
    console.log('\n❌ 未找到底部统计中的接口数量');
    allCorrect = false;
  }
  
  // 5. 检查是否还有旧的错误数量
  const oldCounts = [84, 98];
  oldCounts.forEach(oldCount => {
    const regex = new RegExp(`\\b${oldCount}\\b`, 'g');
    const matches = htmlContent.match(regex);
    if (matches) {
      console.log(`⚠️ 发现旧数量 ${oldCount} 出现 ${matches.length} 次`);
      allCorrect = false;
    }
  });
  
  return allCorrect;
}

/**
 * 统计实际的endpoint数量
 */
function countActualEndpoints() {
  const htmlPath = path.join(__dirname, '..', 'docs/api.html');
  const htmlContent = fs.readFileSync(htmlPath, 'utf8');
  
  // 统计 <div class="endpoint"> 的数量
  const endpointMatches = htmlContent.match(/<div class="endpoint">/g);
  const actualCount = endpointMatches ? endpointMatches.length : 0;
  
  console.log('\n📊 实际endpoint统计:');
  console.log(`HTML文档中实际的endpoint数量: ${actualCount}个`);
  
  if (actualCount === 101) {
    console.log('✅ 实际endpoint数量与声明一致');
    return true;
  } else {
    console.log(`❌ 实际endpoint数量与声明不符 (声明101个)`);
    return false;
  }
}

/**
 * 主验证函数
 */
function main() {
  console.log('🚀 开始最终API接口数量验证');
  console.log('='.repeat(50));
  
  const countsCorrect = verifyHTMLCounts();
  const endpointsCorrect = countActualEndpoints();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 验证结果汇总:');
  console.log('='.repeat(50));
  
  console.log(`数量标注检查: ${countsCorrect ? '✅ 通过' : '❌ 失败'}`);
  console.log(`实际endpoint检查: ${endpointsCorrect ? '✅ 通过' : '❌ 失败'}`);
  
  const overallSuccess = countsCorrect && endpointsCorrect;
  
  if (overallSuccess) {
    console.log('\n🎉 所有验证通过！');
    console.log('📋 确认信息:');
    console.log('- ✅ 标题显示: 101个接口');
    console.log('- ✅ 基础信息: 101个接口');
    console.log('- ✅ 各模块数量: 全部正确');
    console.log('- ✅ 底部统计: 101个接口');
    console.log('- ✅ 实际endpoint: 101个');
    console.log('- ✅ 无旧数量残留');
    
    console.log('\n🏆 结论: HTML文档中的接口数量已完全修正！');
  } else {
    console.log('\n⚠️ 验证发现问题，请检查上述错误项目');
  }
  
  return overallSuccess;
}

// 运行验证
if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { main, verifyHTMLCounts, countActualEndpoints };
