# API快速参考指南

> 视频平台API接口快速查询手册

## 🚀 快速开始

### 基础配置
```bash
Base URL: https://api.yourdomain.com
API版本: v1
认证方式: JWT Bearer Token
```

### 认证头
```http
Authorization: Bearer <your_jwt_token>
Content-Type: application/json
```

## 📋 核心接口速查

### 🔐 认证模块 (/api/auth)

| 接口 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/register` | POST | 用户注册 | ❌ |
| `/login` | POST | 用户登录 | ❌ |
| `/logout` | POST | 用户登出 | ✅ |
| `/refresh-token` | POST | 刷新Token | ❌ |
| `/me` | GET | 获取当前用户信息 | ✅ |
| `/change-password` | POST | 修改密码 | ✅ |
| `/forgot-password` | POST | 忘记密码 | ❌ |
| `/reset-password` | POST | 重置密码 | ❌ |

### 👤 用户模块 (/api/users)

| 接口 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/profile` | GET | 获取个人资料 | ✅ |
| `/profile` | PUT | 更新个人资料 | ✅ |
| `/avatar` | POST | 上传头像 | ✅ |
| `/avatar` | DELETE | 删除头像 | ✅ |
| `/list` | GET | 获取用户列表 | ❌ |
| `/:id` | GET | 获取用户详情 | 👑 |
| `/:id/ban` | PUT | 封禁用户 | 👑 |
| `/:id/unban` | PUT | 解封用户 | 👑 |

### 🎬 视频模块 (/api/videos)

| 接口 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/list` | GET | 获取视频列表 | ❌ |
| `/upload` | POST | 上传视频 | 👑 |
| `/:id` | GET | 获取视频详情 | ❌ |
| `/:id` | PUT | 更新视频信息 | ✅ |
| `/:id` | DELETE | 删除视频 | ✅ |
| `/search` | GET | 搜索视频 | ❌ |
| `/popular` | GET | 热门视频 | ❌ |
| `/recommended` | GET | 推荐视频 | ✅ |
| `/user/:userId` | GET | 获取用户视频 | ❌ |
| `/:id/processing-status` | GET | 获取处理状态 | ❌ |
| `/:id/reprocess` | POST | 重新处理视频 | 👑 |
| `/:id/stats` | GET | 视频统计 | ❌ |
| `/test` | GET | 视频测试 | 👑 |

#### 📁 分类管理 (/api/videos/categories)

| 接口 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/tree` | GET | 获取分类树 | ❌ |
| `/list` | GET | 获取分类列表 | ❌ |
| `/popular` | GET | 热门分类 | ❌ |
| `/search` | GET | 搜索分类 | ❌ |
| `/:id` | GET | 获取分类详情 | ❌ |
| `/:id/videos` | GET | 获取分类下的视频 | ❌ |
| `/` | POST | 创建分类 | 👑 |
| `/:id` | PUT | 更新分类 | 👑 |
| `/:id` | DELETE | 删除分类 | 👑 |

### 💬 互动模块 (/api/interactions)

| 接口 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/comments` | POST | 发表评论 | ✅ |
| `/comments/:id` | PUT | 编辑评论 | ✅ |
| `/comments/:id` | DELETE | 删除评论 | ✅ |
| `/videos/:id/comments` | GET | 获取视频评论 | ❌ |
| `/likes` | POST | 点赞/取消点赞 | ✅ |
| `/favorites` | POST | 收藏/取消收藏 | ✅ |
| `/users/:id/favorites` | GET | 获取用户收藏 | ✅ |
| `/batch-check` | POST | 批量检查互动状态 | ✅ |

### 💎 会员模块 (/api/members)

| 接口 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/plans` | GET | 获取会员计划 | ❌ |
| `/plans/:id` | GET | 获取计划详情 | ❌ |
| `/my-membership` | GET | 获取我的会员信息 | ✅ |
| `/subscribe` | POST | 订阅会员 | ✅ |
| `/cancel` | POST | 取消订阅 | ✅ |
| `/upgrade` | POST | 升级会员 | ✅ |

### 💳 支付模块 (/api/payment)

| 接口 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/create-order` | POST | 创建支付订单 | ✅ |
| `/orders/:orderNo` | GET | 查询订单状态 | ✅ |
| `/my-orders` | GET | 获取我的订单 | ✅ |
| `/refund` | POST | 申请退款 | ✅ |
| `/webhook/:provider` | POST | 支付回调 | ❌ |
| `/methods` | GET | 获取支付方式列表 | ❌ |
| `/statistics` | GET | 支付统计 | 👑 |
| `/test` | GET | 支付测试 | 👑 |

### 🔧 管理模块 (/api/admin)

| 接口 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/dashboard/stats` | GET | 仪表板统计 | 👑 |
| `/users` | GET | 用户管理列表 | 👑 |
| `/users/batch` | POST | 批量用户操作 | 👑 |
| `/videos` | GET | 视频管理列表 | 👑 |
| `/comments` | GET | 评论管理列表 | 👑 |
| `/system/config` | GET | 获取系统配置 | 👑 |
| `/system/config` | PUT | 更新系统配置 | 👑 |
| `/statistics/access/overview` | GET | 访问统计概览 | 👑 |
| `/statistics/revenue/overview` | GET | 收费统计概览 | 👑 |

## 🔑 认证说明

- ❌ 无需认证
- ✅ 需要用户认证
- 👑 需要管理员权限

## 📊 常用查询参数

### 分页参数
```http
?page=1&pageSize=20
```

### 排序参数
```http
?sortBy=created_at&sortOrder=desc
```

### 搜索参数
```http
?keyword=搜索关键词
```

### 筛选参数
```http
?status=active&categoryId=1
```

## 🚨 常见错误代码

| 代码 | 状态码 | 说明 |
|------|--------|------|
| `VALIDATION_ERROR` | 400 | 数据验证失败 |
| `UNAUTHORIZED` | 401 | 未认证 |
| `ACCESS_DENIED` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求过于频繁 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |

## 📝 请求示例

### 用户注册
```bash
curl -X POST https://api.yourdomain.com/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "nickname": "测试用户"
  }'
```

### 用户登录
```bash
curl -X POST https://api.yourdomain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 获取视频列表
```bash
curl -X GET "https://api.yourdomain.com/api/videos/list?page=1&pageSize=10" \
  -H "Content-Type: application/json"
```

### 上传视频
```bash
curl -X POST https://api.yourdomain.com/api/videos/upload \
  -H "Authorization: Bearer <token>" \
  -F "video=@video.mp4" \
  -F "title=我的视频" \
  -F "description=视频描述"
```

### 发表评论
```bash
curl -X POST https://api.yourdomain.com/api/interactions/comments \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "videoId": 1,
    "content": "很棒的视频！"
  }'
```

## 🔄 限流说明

| 操作类型 | 限制 |
|---------|------|
| 登录 | 5次/分钟 |
| 注册 | 3次/分钟 |
| 上传视频 | 5次/小时 |
| 发表评论 | 10次/分钟 |
| 通用API | 100次/15分钟 |

## 📞 技术支持

- **API问题**: <EMAIL>
- **技术支持**: <EMAIL>
- **文档反馈**: <EMAIL>

---

> 更多详细信息请查看完整的 [API文档](./API.md)
