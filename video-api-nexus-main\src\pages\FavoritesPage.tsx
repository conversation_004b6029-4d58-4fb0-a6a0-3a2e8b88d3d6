import React, { useState, useEffect } from 'react';
import { Heart, LogIn, Play } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Link, useNavigate } from 'react-router-dom';
import { getUserProfile, getUserFavorites } from '@/lib/api';
import VideoCard from '@/components/user/VideoCard';
import { useTranslation } from 'react-i18next';

const FavoritesPage = () => {
  const { isAuthenticated, user, isLoading: authLoading } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [userProfile, setUserProfile] = useState(null);
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [favoritesRefreshTrigger, setFavoritesRefreshTrigger] = useState(0);

  // 获取用户资料
  useEffect(() => {
    const fetchProfile = async () => {
      if (isAuthenticated && !userProfile && !authLoading) {
        try {
          const response = await getUserProfile();

          const userData = response?.data?.data?.user ||
                          response?.data?.user ||
                          response?.data ||
                          null;

          setUserProfile(userData);
          setError(null);
        } catch (err: any) {
          console.error('获取用户资料失败:', err);
          console.error('错误详情:', err?.response?.data);
          if (err?.response?.status !== 401 && err?.response?.status !== 403) {
            setError('获取用户资料失败');
          }
        }
      }
    };

    fetchProfile();
  }, [isAuthenticated, authLoading, userProfile]);

  // 获取收藏数据
  useEffect(() => {
    const fetchFavorites = async () => {
      if (isAuthenticated && !authLoading && userProfile) {
        try {
          setLoading(true);
          const response = await getUserFavorites(userProfile.id);

          const favoritesData = response?.data?.data?.data || [];

          const formattedFavorites = favoritesData.map(item => ({
            id: item.video_id || item.id,
            title: item.title,
            description: item.description,
            thumbnail: item.thumbnail,
            duration: item.duration || 0,
            username: item.nickname || item.username || '未知用户',
            category_name: item.category_name || '未分类',
            view_count: item.view_count || 0,
            like_count: item.like_count || 0,
            comment_count: item.comment_count || 0,
            created_at: item.video_created_at || item.created_at,
            favorited_at: item.created_at
          }));

          setFavorites(formattedFavorites);
          setError(null);
        } catch (err: any) {
          const errorMessage = '获取收藏列表失败';
          setError(errorMessage);
          console.error('获取收藏列表失败:', err);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchFavorites();
  }, [isAuthenticated, authLoading, userProfile, favoritesRefreshTrigger]);

  // 处理收藏状态变化
  const handleFavoriteChange = () => {
    setFavoritesRefreshTrigger(prev => prev + 1);
  };

  // 检查认证状态
  if (authLoading) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">正在检查登录状态...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="bg-card p-8 rounded-lg border max-w-md mx-auto">
            <LogIn className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">需要登录</h3>
            <p className="text-muted-foreground mb-6">请先登录以查看您的收藏视频</p>
            <Link
              to="/login"
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              <LogIn className="mr-2 h-4 w-4" />
              立即登录
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">正在加载收藏...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <div className="text-red-600 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-red-800 mb-2">加载失败</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              {t('favorites.reload')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">{t('favorites.title')}</h1>
          <div className="text-sm text-muted-foreground">
            {t('favorites.videoCount', { count: favorites.length })}
          </div>
        </div>

        {favorites.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {favorites.map((video) => (
              <VideoCard
                key={video.id}
                video={video}
                onPlay={() => {
                  // 跳转到视频详情页面
                  navigate(`/video/${video.id}`);
                }}
                onFavoriteChange={handleFavoriteChange}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Heart className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">{t('favorites.noFavorites')}</h3>
            <p className="text-muted-foreground">{t('favorites.noFavoritesDesc')}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default FavoritesPage;