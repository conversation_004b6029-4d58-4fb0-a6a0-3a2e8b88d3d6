
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, User, Heart, Crown, Settings, Menu, X, Home, History } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useMembership } from '@/hooks/useMembership';
import MembershipDialog from '../dialogs/MembershipDialog';

interface UserHeaderProps {
  currentView: string;
  setCurrentView: (view: string) => void;
}

const UserHeader: React.FC<UserHeaderProps> = ({ currentView, setCurrentView }) => {
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isMembershipDialogOpen, setIsMembershipDialogOpen] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const { isMember, isVip } = useMembership();

  // 判断是否显示升级会员按钮
  const shouldShowUpgradeButton = () => {
    if (!isAuthenticated) return false; // 未登录不显示
    if (user?.role === 'admin') return false; // 管理员不显示
    if (isVip) return false; // VIP用户不显示
    return true; // 普通用户和普通会员显示
  };

  const navItems = [
    { id: 'home', label: '首页', icon: Home },
    { id: 'favorites', label: '收藏', icon: Heart },
    { id: 'profile', label: '个人', icon: User },
  ];

  return (
    <header className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">V</span>
            </div>
            <h1 className="text-xl font-bold">视频平台</h1>
          </div>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
              <input
                type="text"
                placeholder="搜索视频..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-input rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              />
            </div>
          </div>

          {/* Navigation - Desktop */}
          <nav className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setCurrentView(item.id)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                    currentView === item.id
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  {IconComponent && <IconComponent size={18} />}
                  <span>{item.label}</span>
                </button>
              );
            })}
            
            {/* 会员用户显示会员标识（可点击查看详情） */}
            {isMember && (
              <button
                className="flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 transition-all duration-200 cursor-pointer"
                onClick={() => setIsMembershipDialogOpen(true)}
                title={t('membership.clickToViewDetails')}
              >
                <Crown size={18} className="text-yellow-200" />
                <span>{t('membership.memberUser')}</span>
              </button>
            )}

            {/* 普通用户显示升级会员按钮 */}
            {!isMember && isAuthenticated && user?.role !== 'admin' && (
              <button className="flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-md hover:from-yellow-500 hover:to-orange-600">
                <Crown size={18} />
                <span>{t('membership.upgrade')}</span>
              </button>
            )}
          </nav>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 hover:bg-accent rounded-md"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Search */}
        <div className="md:hidden mt-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
            <input
              type="text"
              placeholder="搜索视频..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background border border-input rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
            />
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 py-4 border-t">
            <div className="space-y-2">
              {navItems.map((item) => {
                const IconComponent = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      setCurrentView(item.id);
                      setIsMenuOpen(false);
                    }}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${
                      currentView === item.id
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-accent hover:text-accent-foreground'
                    }`}
                  >
                    {IconComponent && <IconComponent size={18} />}
                    <span>{item.label}</span>
                  </button>
                );
              })}
              
              {/* 会员用户显示会员标识（可点击查看详情） */}
              {isMember && (
                <button
                  className="w-full flex items-center space-x-3 px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 transition-all duration-200 cursor-pointer"
                  onClick={() => {
                    setIsMembershipDialogOpen(true);
                    setIsMenuOpen(false); // 关闭移动端菜单
                  }}
                  title={t('membership.clickToViewDetails')}
                >
                  <Crown size={18} className="text-yellow-200" />
                  <span>{t('membership.memberUser')}</span>
                </button>
              )}

              {/* 普通用户显示升级会员按钮 */}
              {!isMember && isAuthenticated && user?.role !== 'admin' && (
                <button className="w-full flex items-center space-x-3 px-3 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-md hover:from-yellow-500 hover:to-orange-600">
                  <Crown size={18} />
                  <span>{t('membership.upgrade')}</span>
                </button>
              )}
            </div>
          </nav>
        )}
      </div>

      {/* 会员信息弹窗 */}
      <MembershipDialog
        open={isMembershipDialogOpen}
        onOpenChange={setIsMembershipDialogOpen}
      />
    </header>
  );
};

export default UserHeader;
