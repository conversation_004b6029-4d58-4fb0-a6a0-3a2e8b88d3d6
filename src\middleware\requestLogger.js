const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

// 请求日志中间件
const requestLogger = (req, res, next) => {
  const { method, originalUrl, ip } = req;
  const userAgent = req.headers['user-agent'];

  // 只记录API请求, 忽略静态资源等
  if (originalUrl.startsWith('/api')) {
    logger.debug(`${method} ${originalUrl} - ${ip}`, { userAgent });
  }

  next();
};

// 错误请求日志中间件
const errorRequestLogger = (err, req, res, next) => {
  const errorLog = {
    requestId: req.requestId,
    method: req.method,
    url: req.url,
    error: {
      name: err.name,
      message: err.message,
      stack: err.stack,
      code: err.code
    },
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  };

  // 添加用户信息
  if (req.user) {
    errorLog.userId = req.user.id;
    errorLog.userRole = req.user.role;
  }

  logger.error('请求错误', errorLog);
  next(err);
};

// 操作日志记录器
class OperationLogger {
  constructor() {
    this.logger = logger;
  }

  // 记录用户操作
  logUserOperation(req, action, target = null, description = null, data = null) {
    const operationLog = {
      requestId: req.requestId,
      userId: req.user?.id,
      userRole: req.user?.role,
      action,
      target,
      description,
      data,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    };

    this.logger.info('用户操作', operationLog);
    
    // 如果是敏感操作，记录到专门的操作日志
    const sensitiveActions = [
      'user_login', 'user_logout', 'user_register',
      'password_change', 'role_change', 'user_ban',
      'video_delete', 'comment_delete', 'admin_action'
    ];

    if (sensitiveActions.includes(action)) {
      this.logger.warn('敏感操作', operationLog);
    }
  }

  // 记录管理员操作
  logAdminOperation(req, action, target, description, data = null) {
    const adminLog = {
      requestId: req.requestId,
      adminId: req.user?.id,
      adminRole: req.user?.role,
      action,
      target,
      description,
      data,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    };

    this.logger.warn('管理员操作', adminLog);
  }

  // 记录安全事件
  logSecurityEvent(req, event, severity = 'medium', description = null, data = null) {
    const securityLog = {
      requestId: req.requestId,
      userId: req.user?.id,
      event,
      severity,
      description,
      data,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    };

    if (severity === 'high' || severity === 'critical') {
      this.logger.error('安全事件', securityLog);
    } else {
      this.logger.warn('安全事件', securityLog);
    }
  }

  // 记录业务事件
  logBusinessEvent(req, event, data = null) {
    const businessLog = {
      requestId: req.requestId,
      userId: req.user?.id,
      event,
      data,
      timestamp: new Date().toISOString()
    };

    this.logger.info('业务事件', businessLog);
  }
}

// 创建操作日志记录器实例
const operationLogger = new OperationLogger();

// 操作日志中间件
const createOperationLogMiddleware = (action, getTarget = null, getDescription = null) => {
  return (req, res, next) => {
    // 保存原始的res.end方法
    const originalEnd = res.end;
    
    res.end = function(chunk, encoding) {
      // 只在成功响应时记录操作日志
      if (res.statusCode < 400) {
        const target = typeof getTarget === 'function' ? getTarget(req, res) : getTarget;
        const description = typeof getDescription === 'function' ? getDescription(req, res) : getDescription;
        
        operationLogger.logUserOperation(req, action, target, description);
      }
      
      originalEnd.call(this, chunk, encoding);
    };
    
    next();
  };
};

// 性能监控中间件
const performanceMonitor = (req, res, next) => {
  const startTime = process.hrtime();
  const startMemory = process.memoryUsage();

  res.on('finish', () => {
    const endTime = process.hrtime(startTime);
    const endMemory = process.memoryUsage();
    
    const duration = endTime[0] * 1000 + endTime[1] / 1000000; // 转换为毫秒
    const memoryDiff = {
      rss: endMemory.rss - startMemory.rss,
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal
    };

    // 记录性能数据
    const perfLog = {
      requestId: req.requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: Math.round(duration * 100) / 100,
      memory: {
        start: startMemory,
        end: endMemory,
        diff: memoryDiff
      },
      timestamp: new Date().toISOString()
    };

    // 如果响应时间过长或内存使用异常，记录警告
    if (duration > 5000 || memoryDiff.heapUsed > 50 * 1024 * 1024) { // 5秒或50MB
      logger.warn('性能异常', perfLog);
    } else {
      logger.debug('性能监控', perfLog);
    }
  });

  next();
};

module.exports = {
  requestLogger,
  errorRequestLogger,
  OperationLogger,
  operationLogger,
  createOperationLogMiddleware,
  performanceMonitor
};
