/**
 * 支付模块初始化
 */

const paymentServiceFactory = require('./services/PaymentServiceFactory');
const { paymentConfig, validatePaymentConfig } = require('../../config/payment');
const { logger } = require('../../utils/advancedLogger');
const cron = require('node-cron');
const Order = require('../../database/models/Order');

/**
 * 初始化支付模块
 */
async function initializePaymentModule() {
  try {
    logger.info('开始初始化支付模块...');

    // 初始化支付服务工厂，现在它会从数据库加载配置
    await paymentServiceFactory.initialize();

    // 启动定时任务
    startScheduledTasks();

    logger.info('支付模块初始化完成');

    return {
      success: true,
      status: paymentServiceFactory.getStatus()
    };

  } catch (error) {
    logger.error('支付模块初始化失败:', error);
    throw error;
  }
}

/**
 * 启动定时任务
 */
function startScheduledTasks() {
  // 每5分钟检查一次过期订单
  cron.schedule('*/5 * * * *', async () => {
    try {
      await handleExpiredOrders();
    } catch (error) {
      logger.error('处理过期订单失败:', error);
    }
  });

  // 每天凌晨2点清理过期的支付日志
  cron.schedule('0 2 * * *', async () => {
    try {
      await cleanupPaymentLogs();
    } catch (error) {
      logger.error('清理支付日志失败:', error);
    }
  });

  logger.info('支付模块定时任务已启动');
}

/**
 * 处理过期订单
 */
async function handleExpiredOrders() {
  try {
    const expiredOrders = await Order.getExpiredOrders();
    
    if (expiredOrders.length > 0) {
      logger.info(`发现 ${expiredOrders.length} 个过期订单，开始处理...`);

      for (const order of expiredOrders) {
        await Order.markOrderExpired(order.order_no);
        logger.debug(`订单 ${order.order_no} 已标记为过期`);
      }

      logger.info(`已处理 ${expiredOrders.length} 个过期订单`);
    }
  } catch (error) {
    logger.error('处理过期订单失败:', error);
  }
}

/**
 * 清理支付日志
 */
async function cleanupPaymentLogs() {
  try {
    // 删除30天前的支付日志
    const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    // 这里可以添加清理逻辑，比如删除旧的日志文件或数据库记录
    logger.info('支付日志清理完成');
  } catch (error) {
    logger.error('清理支付日志失败:', error);
  }
}

/**
 * 获取支付模块状态
 */
function getPaymentModuleStatus() {
  return {
    initialized: true,
    factory: paymentServiceFactory.getStatus(),
    config: {
      enabledMethods: Object.keys(paymentConfig).filter(key => 
        key !== 'common' && paymentConfig[key].enabled
      ),
      orderExpireMinutes: paymentConfig.common.orderExpireMinutes,
      minAmount: paymentConfig.common.minAmount,
      maxAmount: paymentConfig.common.maxAmount,
      currency: paymentConfig.common.currency
    }
  };
}

/**
 * 重新加载支付配置
 */
async function reloadPaymentConfig() {
  try {
    logger.info('重新加载支付配置...');
    
    // 直接调用工厂的reload方法
    await paymentServiceFactory.reload();

    logger.info('支付配置重新加载完成');
    return true;
  } catch (error) {
    logger.error('重新加载支付配置失败:', error);
    throw error;
  }
}

/**
 * 健康检查
 */
async function healthCheck() {
  try {
    const status = getPaymentModuleStatus();
    const availableMethods = paymentServiceFactory.getAvailablePaymentMethods();
    
    return {
      healthy: availableMethods.length > 0,
      status,
      availableMethods,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('支付模块健康检查失败:', error);
    return {
      healthy: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 优雅关闭
 */
async function shutdown() {
  try {
    logger.info('正在关闭支付模块...');
    
    // 这里可以添加清理逻辑，比如关闭数据库连接、保存状态等
    
    logger.info('支付模块已关闭');
  } catch (error) {
    logger.error('关闭支付模块失败:', error);
  }
}

module.exports = {
  initializePaymentModule,
  getPaymentModuleStatus,
  reloadPaymentConfig,
  healthCheck,
  shutdown,
  paymentServiceFactory
};
