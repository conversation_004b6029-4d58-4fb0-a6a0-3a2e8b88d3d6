CREATE TABLE `withdrawals` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `amount` DECIMAL(15, 2) NOT NULL,
  `status` ENUM('pending', 'approved', 'rejected', 'completed', 'failed') NOT NULL DEFAULT 'pending',
  `wallet_type` VARCHAR(50) NOT NULL COMMENT '提现钱包类型, e.g., TRON_USDT',
  `wallet_address` VARCHAR(255) NOT NULL COMMENT '收款钱包地址',
  `transaction_hash` VARCHAR(255) NULL COMMENT '链上交易哈希',
  `rejection_reason` VARCHAR(255) NULL COMMENT '管理员拒绝原因',
  `notes` TEXT NULL COMMENT '管理员备注',
  `requested_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '用户申请时间',
  `processed_at` TIMESTAMP NULL COMMENT '管理员处理时间',
  `processed_by` INT NULL COMMENT '处理该请求的管理员ID',
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) COMMENT='用户提现记录表'; 