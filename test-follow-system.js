const mysql = require('mysql2/promise');
const config = require('./src/config/database');

async function testFollowSystem() {
  try {
    const connection = await mysql.createConnection(config);
    
    console.log('=== 测试关注系统数据 ===');
    
    // 检查follows表数据
    const [follows] = await connection.execute('SELECT * FROM follows LIMIT 10');
    console.log('Follows表数据:', follows);
    
    // 检查user_stats表数据
    const [userStats] = await connection.execute('SELECT * FROM user_stats LIMIT 10');
    console.log('User_stats表数据:', userStats);
    
    // 手动计算关注统计
    const [followCounts] = await connection.execute(`
      SELECT 
        u.id,
        u.username,
        COALESCE(followers.count, 0) as follower_count,
        COALESCE(following.count, 0) as following_count
      FROM users u
      LEFT JOIN (
        SELECT followed_id, COUNT(*) as count 
        FROM follows 
        WHERE status = 'active' 
        GROUP BY followed_id
      ) followers ON u.id = followers.followed_id
      LEFT JOIN (
        SELECT follower_id, COUNT(*) as count 
        FROM follows 
        WHERE status = 'active' 
        GROUP BY follower_id
      ) following ON u.id = following.follower_id
      ORDER BY u.id
      LIMIT 10
    `);
    console.log('实际关注统计:', followCounts);
    
    // 更新user_stats表
    console.log('=== 更新user_stats表 ===');
    for (const user of followCounts) {
      await connection.execute(`
        INSERT INTO user_stats (user_id, follower_count, following_count, video_count, total_views, total_likes)
        VALUES (?, ?, ?, 0, 0, 0)
        ON DUPLICATE KEY UPDATE
        follower_count = VALUES(follower_count),
        following_count = VALUES(following_count)
      `, [user.id, user.follower_count, user.following_count]);
    }
    
    console.log('用户统计数据已更新');
    
    await connection.end();
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testFollowSystem();