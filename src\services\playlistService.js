const Playlist = require('../models/Playlist');
const PlayHistory = require('../models/PlayHistory');
const logger = require('../utils/logger');

class PlaylistService {
  // 创建播放列表
  static async createPlaylist(userId, playlistData) {
    try {
      const { name, description, isPublic = false, playMode = 'sequence' } = playlistData;
      
      if (!name || name.trim().length === 0) {
        throw new Error('播放列表名称不能为空');
      }
      
      if (name.length > 255) {
        throw new Error('播放列表名称不能超过255个字符');
      }
      
      const playlist = await Playlist.create(userId, name.trim(), description, isPublic, playMode);
      
      logger.info(`用户 ${userId} 创建播放列表: ${name}`, { playlistId: playlist.id });
      
      return playlist;
    } catch (error) {
      logger.error('创建播放列表失败:', error);
      throw error;
    }
  }

  // 获取用户播放列表
  static async getUserPlaylists(userId, includeItems = false) {
    try {
      const playlists = await Playlist.findByUserId(userId, includeItems);
      
      logger.debug(`获取用户 ${userId} 的播放列表，数量: ${playlists.length}`);
      
      return playlists;
    } catch (error) {
      logger.error('获取用户播放列表失败:', error);
      throw error;
    }
  }

  // 获取播放列表详情
  static async getPlaylistById(playlistId, userId = null) {
    try {
      const playlist = await Playlist.findById(playlistId, userId);
      
      if (!playlist) {
        throw new Error('播放列表不存在或无权限访问');
      }
      
      logger.debug(`获取播放列表详情: ${playlistId}`);
      
      return playlist;
    } catch (error) {
      logger.error('获取播放列表详情失败:', error);
      throw error;
    }
  }

  // 更新播放列表
  static async updatePlaylist(playlistId, userId, updates) {
    try {
      // 验证更新数据
      if (updates.name !== undefined) {
        if (!updates.name || updates.name.trim().length === 0) {
          throw new Error('播放列表名称不能为空');
        }
        if (updates.name.length > 255) {
          throw new Error('播放列表名称不能超过255个字符');
        }
        updates.name = updates.name.trim();
      }
      
      if (updates.play_mode && !['sequence', 'loop', 'random'].includes(updates.play_mode)) {
        throw new Error('无效的播放模式');
      }
      
      const playlist = await Playlist.update(playlistId, userId, updates);
      
      logger.info(`用户 ${userId} 更新播放列表: ${playlistId}`, { updates });
      
      return playlist;
    } catch (error) {
      logger.error('更新播放列表失败:', error);
      throw error;
    }
  }

  // 删除播放列表
  static async deletePlaylist(playlistId, userId) {
    try {
      const result = await Playlist.delete(playlistId, userId);
      
      logger.info(`用户 ${userId} 删除播放列表: ${playlistId}`);
      
      return result;
    } catch (error) {
      logger.error('删除播放列表失败:', error);
      throw error;
    }
  }

  // 添加视频到播放列表
  static async addVideoToPlaylist(playlistId, videoId, userId, position = null) {
    try {
      const result = await Playlist.addItem(playlistId, videoId, userId, position);
      
      logger.info(`用户 ${userId} 添加视频 ${videoId} 到播放列表 ${playlistId}`);
      
      return result;
    } catch (error) {
      logger.error('添加视频到播放列表失败:', error);
      throw error;
    }
  }

  // 从播放列表移除视频
  static async removeVideoFromPlaylist(playlistId, videoId, userId) {
    try {
      const result = await Playlist.removeItem(playlistId, videoId, userId);
      
      logger.info(`用户 ${userId} 从播放列表 ${playlistId} 移除视频 ${videoId}`);
      
      return result;
    } catch (error) {
      logger.error('从播放列表移除视频失败:', error);
      throw error;
    }
  }

  // 重新排序播放列表项目
  static async reorderPlaylistItems(playlistId, userId, itemOrders) {
    try {
      if (!Array.isArray(itemOrders) || itemOrders.length === 0) {
        throw new Error('无效的排序数据');
      }
      
      // 验证排序数据格式
      for (const item of itemOrders) {
        if (!item.videoId || typeof item.position !== 'number') {
          throw new Error('排序数据格式错误');
        }
      }
      
      const result = await Playlist.reorderItems(playlistId, userId, itemOrders);
      
      logger.info(`用户 ${userId} 重新排序播放列表 ${playlistId}`);
      
      return result;
    } catch (error) {
      logger.error('重新排序播放列表项目失败:', error);
      throw error;
    }
  }

  // 批量添加视频到播放列表
  static async addMultipleVideos(playlistId, videoIds, userId) {
    try {
      if (!Array.isArray(videoIds) || videoIds.length === 0) {
        throw new Error('视频ID列表不能为空');
      }
      
      const results = [];
      let position = null;
      
      for (const videoId of videoIds) {
        try {
          const result = await Playlist.addItem(playlistId, videoId, userId, position);
          results.push({ videoId, success: true });
          if (position !== null) position++;
        } catch (error) {
          results.push({ videoId, success: false, error: error.message });
        }
      }
      
      logger.info(`用户 ${userId} 批量添加 ${videoIds.length} 个视频到播放列表 ${playlistId}`);
      
      return results;
    } catch (error) {
      logger.error('批量添加视频到播放列表失败:', error);
      throw error;
    }
  }

  // 复制播放列表
  static async duplicatePlaylist(sourcePlaylistId, userId, newName = null) {
    try {
      const sourcePlaylist = await Playlist.findById(sourcePlaylistId, userId);
      if (!sourcePlaylist) {
        throw new Error('源播放列表不存在或无权限访问');
      }
      
      const name = newName || `${sourcePlaylist.name} - 副本`;
      const newPlaylist = await Playlist.create(
        userId, 
        name, 
        sourcePlaylist.description, 
        false, // 副本默认为私有
        sourcePlaylist.play_mode
      );
      
      // 复制播放列表项目
      if (sourcePlaylist.items && sourcePlaylist.items.length > 0) {
        for (let i = 0; i < sourcePlaylist.items.length; i++) {
          const item = sourcePlaylist.items[i];
          await Playlist.addItem(newPlaylist.id, item.video_id, userId, i);
        }
      }
      
      logger.info(`用户 ${userId} 复制播放列表 ${sourcePlaylistId} 为 ${newPlaylist.id}`);
      
      return await Playlist.findById(newPlaylist.id, userId);
    } catch (error) {
      logger.error('复制播放列表失败:', error);
      throw error;
    }
  }

  // 获取播放列表统计信息
  static async getPlaylistStats(userId) {
    try {
      const playlists = await Playlist.findByUserId(userId, true);
      
      const stats = {
        totalPlaylists: playlists.length,
        totalItems: playlists.reduce((sum, playlist) => sum + (playlist.items?.length || 0), 0),
        publicPlaylists: playlists.filter(p => p.is_public).length,
        privatePlaylists: playlists.filter(p => !p.is_public).length,
        playModeStats: {
          sequence: playlists.filter(p => p.play_mode === 'sequence').length,
          loop: playlists.filter(p => p.play_mode === 'loop').length,
          random: playlists.filter(p => p.play_mode === 'random').length
        }
      };
      
      logger.debug(`获取用户 ${userId} 播放列表统计信息`);
      
      return stats;
    } catch (error) {
      logger.error('获取播放列表统计信息失败:', error);
      throw error;
    }
  }

  // 记录播放历史
  static async recordPlayHistory(userId, videoId, watchDuration, videoDuration, completed = false) {
    try {
      const result = await PlayHistory.record(userId, videoId, watchDuration, videoDuration, completed);
      
      logger.debug(`记录用户 ${userId} 播放历史: 视频 ${videoId}`);
      
      return result;
    } catch (error) {
      logger.error('记录播放历史失败:', error);
      throw error;
    }
  }

  // 获取播放历史
  static async getPlayHistory(userId, limit = 50, offset = 0) {
    try {
      const history = await PlayHistory.findByUserId(userId, limit, offset);
      
      logger.debug(`获取用户 ${userId} 播放历史，数量: ${history.length}`);
      
      return history;
    } catch (error) {
      logger.error('获取播放历史失败:', error);
      throw error;
    }
  }

  // 获取最近播放
  static async getRecentlyPlayed(userId, limit = 10) {
    try {
      const recentVideos = await PlayHistory.getRecentlyPlayed(userId, limit);
      
      logger.debug(`获取用户 ${userId} 最近播放，数量: ${recentVideos.length}`);
      
      return recentVideos;
    } catch (error) {
      logger.error('获取最近播放失败:', error);
      throw error;
    }
  }

  // 清除播放历史
  static async clearPlayHistory(userId, videoId = null) {
    try {
      const deletedCount = await PlayHistory.clearHistory(userId, videoId);
      
      logger.info(`用户 ${userId} 清除播放历史，删除 ${deletedCount} 条记录`);
      
      return { deletedCount };
    } catch (error) {
      logger.error('清除播放历史失败:', error);
      throw error;
    }
  }

  // 获取播放统计
  static async getPlayStats(userId) {
    try {
      const stats = await PlayHistory.getPlayStats(userId);
      
      logger.debug(`获取用户 ${userId} 播放统计信息`);
      
      return stats;
    } catch (error) {
      logger.error('获取播放统计失败:', error);
      throw error;
    }
  }
}

module.exports = PlaylistService;
