import axios from 'axios';

const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include the auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    console.log('🚀 API请求:', config.method?.toUpperCase(), config.url, config.headers);
    return config;
  },
  (error) => {
    console.error('❌ API请求错误:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor to debug API responses
api.interceptors.response.use(
  (response) => {
    console.log('✅ API响应成功:', response.config.url, response.status, response.data);
    return response;
  },
  (error) => {
    console.error('❌ API响应错误:', error.config?.url, error.response?.status, error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const getVideos = (params) => api.get('/video/list', { params });
export const getPopularVideos = () => api.get('/video/popular');
export const getRecommendedVideos = () => api.get('/video/recommended');
export const getVideoDetails = (id) => api.get(`/video/${id}`);
export const searchVideos = (query) => api.get('/video/search', { params: { q: query } });

export const getCategories = () => api.get('/video/categories/list');
export const getCategoryTree = () => api.get('/video/categories/tree');
export const getCategoryDetails = (id) => api.get(`/video/categories/${id}`);
export const searchCategories = (keyword) => api.get('/video/categories/search', { params: { keyword } });

// Category Management API (Admin only)
export const createCategory = (categoryData) => api.post('/video/categories', categoryData);
export const updateCategory = (id, categoryData) => api.put(`/video/categories/${id}`, categoryData);
export const deleteCategory = (id) => api.delete(`/video/categories/${id}`);

// User API
export const getUserProfile = () => api.get('/user/profile');
export const getUserBasicInfo = () => api.get('/auth/me');
export const updateUserProfile = (profileData) => api.put('/user/profile', profileData);
export const uploadAvatar = (formData) => api.post('/user/avatar', formData, {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
});
export const deleteAvatar = () => api.delete('/user/avatar');
export const deleteAccount = () => api.delete('/user/account');

// Auth API
export const login = (credentials) => api.post('/auth/login', credentials);
export const register = (userInfo) => api.post('/auth/register', userInfo);
export const logout = () => api.post('/auth/logout');
export const changePassword = (passwordData) => api.post('/auth/change-password', passwordData);

// Interaction API
export const toggleLike = (targetId, targetType) => api.post('/interaction/likes', { targetId, targetType });
export const toggleFavorite = (videoId) => api.post('/interaction/favorites', { videoId });
export const getUserFavorites = (userId) => api.get(`/interaction/users/${userId}/favorites`);
export const batchCheckInteractions = (videoIds) => api.post('/interaction/batch-check', { videoIds });

// Watch API
export const recordWatch = (watchData) => api.post('/watch/record', watchData);
export const getUserWatchHistory = (params) => api.get('/watch/history', { params });
export const getUserWatchStats = () => api.get('/watch/stats');
export const clearWatchHistory = () => api.delete('/watch/history/clear');

// Admin API
export const getDashboardStats = () => api.get('/admin/dashboard/stats');
export const getAdminUsers = (params) => api.get('/admin/users', { params });
export const batchUserAction = (action, userIds) => api.post('/admin/users/batch', { action, userIds });

export const getAdminVideos = (params) => api.get('/admin/videos', { params });
export const batchVideoAction = (action, videoIds) => api.post('/admin/videos/batch', { action, videoIds });

export const getAdminComments = (params) => api.get('/admin/comments', { params });
export const deleteCommentAsAdmin = (commentId) => api.delete(`/admin/comments/${commentId}`);

export const getUserVideos = () => api.get('/video/my-videos');

export const uploadVideo = (formData, onUploadProgress) => api.post('/video/upload', formData, {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  onUploadProgress,
});

export const uploadVideoFromUrl = (data) => api.post('/video/upload-from-url', data);

// Notifications
export const getNotifications = (params) => api.get('/notifications', { params });
export const getNotificationStatus = async () => {
  const response = await api.get('/notifications/status');
  return response.data; // 直接返回后端响应的核心数据
};
export const markNotificationAsRead = (id) => api.post(`/notifications/${id}/read`);
export const markAllNotificationsAsRead = () => api.post('/notifications/read-all');

export const uploadAudio = (formData, onUploadProgress) => api.post('/video/upload-audio', formData, {
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  onUploadProgress,
});

// Member API
export const getMembershipOverview = () => api.get('/member/admin/overview');
export const getMyMembership = () => api.get('/member/my-membership');
export const getMembershipPlans = () => api.get('/member/plans');
export const subscribeMembership = (planId, paymentMethod, paymentData = {}) =>
  api.post('/member/subscribe', { planId, paymentMethod, paymentData });
export const cancelMembership = () => api.post('/member/cancel');
export const upgradeMembership = (planId, paymentMethod, paymentData = {}) =>
  api.post('/member/upgrade', { planId, paymentMethod, paymentData });

// Order API
export const getMyOrders = (params) => api.get('/user/orders', { params });
export const createPaymentForRecharge = (amount, paymentMethod) => 
  api.post('/payment/create-order', {
    orderType: 'recharge',
    amount,
    paymentMethod,
  });
export const createVideoOrder = (videoId) => api.post('/payment/orders/video', { videoId });
export const payOrderWithBalance = (orderId) => api.post(`/payment/orders/${orderId}/pay`);

// You can add other API functions for user, comments, etc. here
// For example:
// export const getComments = (videoId) => api.get(`/video/${videoId}/comments`);

export default api;