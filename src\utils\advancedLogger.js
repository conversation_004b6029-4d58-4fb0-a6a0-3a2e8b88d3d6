const winston = require('winston');
require('winston-daily-rotate-file');
const path = require('path');
const { toAbsolutePath } = require('./pathResolver');
const fs = require('fs');

// 确保日志目录存在
const logDir = toAbsolutePath('logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` | Meta: ${JSON.stringify(meta)}`;
    }
    
    if (stack) {
      log += `\nStack: ${stack}`;
    }
    
    return log;
  })
);

// 控制台格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} ${level}: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    
    return log;
  })
);

// 创建日志传输器
const transports = [
  // 控制台输出
  new winston.transports.Console({
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    format: consoleFormat
  }),
  
  // 错误日志文件
  new winston.transports.File({
    filename: path.join(logDir, 'error.log'),
    level: 'error',
    format: logFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5
  }),
  
  // 警告日志文件
  new winston.transports.File({
    filename: path.join(logDir, 'warn.log'),
    level: 'warn',
    format: logFormat,
    maxsize: 10 * 1024 * 1024,
    maxFiles: 3
  }),
  
  // 综合日志文件
  new winston.transports.File({
    filename: path.join(logDir, 'combined.log'),
    format: logFormat,
    maxsize: 20 * 1024 * 1024, // 20MB
    maxFiles: 10
  }),
  
  // 访问日志文件
  new winston.transports.File({
    filename: path.join(logDir, 'access.log'),
    level: 'info',
    format: logFormat,
    maxsize: 50 * 1024 * 1024, // 50MB
    maxFiles: 7
  })
];

// 生产环境添加日志轮转
if (process.env.NODE_ENV === 'production') {
  const DailyRotateFile = require('winston-daily-rotate-file');
  
  transports.push(
    new DailyRotateFile({
      filename: path.join(logDir, 'application-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
      format: logFormat
    })
  );
}

// 创建主日志器
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports,
  exitOnError: false
});

// 创建专用日志器
const accessLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'access.log'),
      maxsize: 50 * 1024 * 1024,
      maxFiles: 7
    })
  ]
});

const securityLogger = winston.createLogger({
  level: 'warn',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'security.log'),
      maxsize: 10 * 1024 * 1024,
      maxFiles: 10
    }),
    new winston.transports.Console({
      level: 'error',
      format: consoleFormat
    })
  ]
});

const performanceLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'performance.log'),
      maxsize: 20 * 1024 * 1024,
      maxFiles: 5
    })
  ]
});

// 日志工具函数
const logUtils = {
  // 记录API访问
  logAccess: (req, res, responseTime) => {
    const logData = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userId: req.user?.id || null,
      timestamp: new Date().toISOString()
    };
    
    accessLogger.info('API Access', logData);
  },
  
  // 记录安全事件
  logSecurity: (event, details, req = null) => {
    const logData = {
      event,
      details,
      ip: req?.ip || req?.connection?.remoteAddress || 'unknown',
      userAgent: req?.get('User-Agent') || 'unknown',
      userId: req?.user?.id || null,
      timestamp: new Date().toISOString()
    };
    
    securityLogger.warn('Security Event', logData);
  },
  
  // 记录性能指标
  logPerformance: (operation, duration, metadata = {}) => {
    const logData = {
      operation,
      duration: `${duration}ms`,
      metadata,
      timestamp: new Date().toISOString()
    };
    
    performanceLogger.info('Performance Metric', logData);
  },
  
  // 记录数据库操作
  logDatabase: (operation, table, duration, error = null) => {
    const logData = {
      type: 'database',
      operation,
      table,
      duration: `${duration}ms`,
      error: error?.message || null,
      timestamp: new Date().toISOString()
    };
    
    if (error) {
      logger.error('Database Error', logData);
    } else {
      logger.debug('Database Operation', logData);
    }
  },
  
  // 记录缓存操作
  logCache: (operation, key, hit = null, duration = null) => {
    const logData = {
      type: 'cache',
      operation,
      key,
      hit,
      duration: duration ? `${duration}ms` : null,
      timestamp: new Date().toISOString()
    };
    
    logger.debug('Cache Operation', logData);
  },
  
  // 记录用户操作
  logUserAction: (userId, action, resource, metadata = {}) => {
    const logData = {
      type: 'user_action',
      userId,
      action,
      resource,
      metadata,
      timestamp: new Date().toISOString()
    };
    
    logger.info('User Action', logData);
  },
  
  // 记录系统事件
  logSystem: (event, level = 'info', metadata = {}) => {
    const logData = {
      type: 'system',
      event,
      metadata,
      timestamp: new Date().toISOString()
    };
    
    logger[level]('System Event', logData);
  }
};

// 错误处理
logger.on('error', (error) => {
  console.error('Logger error:', error);
});

// 进程退出时关闭日志
process.on('exit', () => {
  logger.end();
  accessLogger.end();
  securityLogger.end();
  performanceLogger.end();
});

// 导出日志器和工具
module.exports = {
  logger,
  accessLogger,
  securityLogger,
  performanceLogger,
  logUtils
};

// 兼容原有的logger
module.exports.info = logger.info.bind(logger);
module.exports.error = logger.error.bind(logger);
module.exports.warn = logger.warn.bind(logger);
module.exports.debug = logger.debug.bind(logger);
