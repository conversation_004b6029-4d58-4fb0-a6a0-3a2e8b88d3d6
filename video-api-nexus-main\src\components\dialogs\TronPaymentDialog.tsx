import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Copy } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface TronPaymentInfo {
  wallet_address: string;
  final_amount: number; // TRX amount
  final_amount_usdt: number; // USDT amount
  order_no: string;
  qr_code_url: string;
}

interface TronPaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: TronPaymentInfo | null;
}

const TronPaymentDialog: React.FC<TronPaymentDialogProps> = ({ open, onOpenChange, order }) => {
  const { toast } = useToast();

  const handleCopy = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({ title: "已复制", description: `${label}已复制到剪贴板。` });
  };

  if (!order) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95%] sm:max-w-md max-h-[90vh] flex flex-col p-6">
        <div className="py-4 text-center">
          <DialogHeader>
            <DialogTitle>TRON (TRX) 支付</DialogTitle>
            <DialogDescription>
              为确保订单被正确识别，请精确转账下方所示的TRX金额。
            </DialogDescription>
          </DialogHeader>
          <div className="my-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
            <div className="flex flex-col items-center">
              <img src={order.qr_code_url} alt="TRON Wallet Address QR Code" className="w-40 h-40 mb-4 border rounded-md" />
              
              <Label htmlFor="walletAddress" className="text-base font-semibold">收款地址 (TRC20)</Label>
              <div className="mt-2 flex items-center justify-center p-3 bg-gray-100 dark:bg-gray-700 rounded-md w-full">
                <p id="walletAddress" className="text-sm md:text-base break-all font-mono mr-4 text-gray-700 dark:text-gray-300">{order.wallet_address}</p>
                <Button variant="ghost" size="icon" onClick={() => handleCopy(order.wallet_address, '收款地址')}>
                  <Copy className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
          <div className="my-6 space-y-4">
            <div>
              <Label className="text-lg font-semibold text-purple-600">支付金额 (TRX)</Label>
              <p className="text-2xl font-bold text-purple-600 mt-2 tracking-wider">{parseFloat(order.final_amount)} <span className="text-xl">TRX</span></p>
              <p className="text-xs text-muted-foreground mt-1">请确保转账金额完全一致，包括所有小数位。</p>
            </div>
            
            <div className="border-t pt-4">
              <Label className="text-base font-semibold">支付金额 (USDT)</Label>
              <p className="text-xl font-bold text-gray-700 dark:text-gray-300 mt-1">{parseFloat(order.final_amount_usdt)} <span className="text-lg">USDT</span></p>
              <p className="text-xs text-muted-foreground mt-1">此笔交易等值于所选会员计划的价格。</p>
            </div>
          </div>
          <DialogFooter className="mt-8">
            <Button onClick={() => onOpenChange(false)} className="w-full" variant="outline">我已完成支付</Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TronPaymentDialog; 