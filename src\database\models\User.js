const BaseModel = require('../BaseModel');
const bcrypt = require('bcryptjs');
const { AppError } = require('../../middleware/errorHandler');

class User extends BaseModel {
  constructor() {
    super('users');
  }

  // 根据邮箱查找用户
  async findByEmail(email) {
    return await this.findOne({ email });
  }

  // 根据用户名查找用户
  async findByUsername(username) {
    return await this.findOne({ username });
  }

  // 创建用户
  async createUser(userData) {
    // 检查邮箱是否已存在
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new AppError('邮箱已被注册', 409, 'EMAIL_EXISTS');
    }

    // 检查用户名是否已存在
    if (userData.username) {
      const existingUsername = await this.findByUsername(userData.username);
      if (existingUsername) {
        throw new AppError('用户名已被使用', 409, 'USERNAME_EXISTS');
      }
    }

    // 加密密码
    if (userData.password) {
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
      userData.password = await bcrypt.hash(userData.password, saltRounds);
    }

    // 设置默认值
    const userToCreate = {
      ...userData,
      role: userData.role || 'user',
      status: userData.status || 'active',
      email_verified: userData.email_verified || false
    };

    return await this.create(userToCreate);
  }

  // 验证密码
  async verifyPassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  // 用户登录验证
  async authenticate(email, password) {
    const logger = require('../../utils/logger'); // Local require for logging
    logger.info(`[Auth] 开始验证用户: ${email}`);

    const user = await this.findByEmail(email);
    if (!user) {
      logger.warn(`[Auth] 验证失败: 用户未找到. Email: ${email}`);
      throw new AppError('用户不存在或密码错误', 401, 'AUTHENTICATION_FAILED');
    }
    
    logger.info(`[Auth] 用户已找到: ${user.username} (ID: ${user.id})`);
    logger.info(`[Auth] 数据库存储的密码哈希: ${user.password}`);

    if (user.status !== 'active') {
      logger.warn(`[Auth] 验证失败: 账户被禁用. UserID: ${user.id}, Status: ${user.status}`);
      throw new AppError('账户已被禁用', 401, 'ACCOUNT_DISABLED');
    }

    const isValidPassword = await this.verifyPassword(password, user.password);
    logger.info(`[Auth] 密码比对结果 (isValidPassword): ${isValidPassword}`);

    if (!isValidPassword) {
      logger.warn(`[Auth] 验证失败: 密码错误. UserID: ${user.id}`);
      throw new AppError('用户不存在或密码错误', 401, 'AUTHENTICATION_FAILED');
    }

    logger.info(`[Auth] 用户 ${email} 验证成功`);
    // 返回完整的用户信息，由Controller决定如何处理
    return user;
  }

  // 更新最后登录时间
  async updateLastLogin(userId, ipAddress = null) {
    const updateData = {
      last_login_at: new Date()
    };

    if (ipAddress) {
      updateData.last_login_ip = ipAddress;
    }

    return await this.update(userId, updateData);
  }

  // 更新密码
  async updatePassword(userId, newPassword) {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    return await this.update(userId, { password: hashedPassword });
  }

  // 验证邮箱
  async verifyEmail(userId) {
    return await this.update(userId, { 
      email_verified: true,
      updated_at: new Date()
    });
  }

  // 更新用户资料
  async updateProfile(userId, profileData) {
    // 过滤敏感字段
    const allowedFields = [
      'nickname', 'avatar', 'phone', 'gender', 
      'birthday', 'bio'
    ];
    
    const updateData = {};
    for (const field of allowedFields) {
      if (profileData[field] !== undefined) {
        updateData[field] = profileData[field];
      }
    }

    if (Object.keys(updateData).length === 0) {
      throw new AppError('没有可更新的字段', 400, 'NO_UPDATE_FIELDS');
    }

    return await this.update(userId, updateData);
  }

  // 获取用户统计信息
  async getUserStats(userId) {
    const sql = `
      SELECT
        u.id,
        u.username,
        u.nickname,
        u.email,
        u.avatar,
        u.role,
        u.status,
        u.balance,
        u.created_at,
        COUNT(DISTINCT v.id) as video_count,
        COUNT(DISTINCT f.id) as favorite_count,
        COUNT(DISTINCT c.id) as comment_count,
        COUNT(DISTINCT l.id) as like_count,
        COALESCE(ROUND(SUM(wh.watch_duration) / 3600, 1), 0) as watch_time
      FROM users u
      LEFT JOIN videos v ON u.id = v.user_id AND v.status != 'deleted'
      LEFT JOIN favorites f ON u.id = f.user_id
      LEFT JOIN comments c ON u.id = c.user_id AND c.status = 'active'
      LEFT JOIN likes l ON u.id = l.user_id
      LEFT JOIN watch_history wh ON u.id = wh.user_id
      WHERE u.id = ?
      GROUP BY u.id
    `;

    const result = await this.query(sql, [userId]);
    if (result[0]) {
      // 确保数值字段为数字类型
      result[0].video_count = parseInt(result[0].video_count) || 0;
      result[0].favorite_count = parseInt(result[0].favorite_count) || 0;
      result[0].comment_count = parseInt(result[0].comment_count) || 0;
      result[0].like_count = parseInt(result[0].like_count) || 0;
      result[0].watch_time = parseFloat(result[0].watch_time) || 0;
      result[0].balance = parseFloat(result[0].balance) || 0;
    }
    return result[0] || null;
  }

  // 获取用户列表（管理员用）
  async getUserList(page = 1, pageSize = 20, filters = {}, options = {}) {
    const { sortBy = 'created_at', sortOrder = 'DESC' } = options;
    const offset = (page - 1) * pageSize;

    let whereClauses = [];
    const params = [];

    if (filters.role && filters.role !== 'all') {
      whereClauses.push('u.role = ?');
      params.push(filters.role);
    }
    if (filters.status && filters.status !== 'all') {
      if(Array.isArray(filters.status)) {
        whereClauses.push(`u.status IN (${filters.status.map(() => '?').join(',')})`);
        params.push(...filters.status);
      } else {
        whereClauses.push('u.status = ?');
        params.push(filters.status);
      }
    } else {
      whereClauses.push("u.status IN ('active', 'banned', 'inactive')");
    }

    const whereSql = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

    const dataSql = `
      SELECT 
        u.*,
        (SELECT COUNT(*) FROM videos v WHERE v.user_id = u.id AND v.status != 'deleted') as video_count,
        (SELECT COUNT(*) FROM comments c WHERE c.user_id = u.id AND c.status = 'active') as comment_count
      FROM users u
      ${whereSql}
      ORDER BY u.${sortBy} ${sortOrder}
      LIMIT ${parseInt(pageSize, 10)} OFFSET ${parseInt(offset, 10)}
    `;
    
    const users = await this.query(dataSql, params);

    const countSql = `SELECT COUNT(*) as total FROM users u ${whereSql}`;
    const countResult = await this.query(countSql, params);
    const total = countResult[0].total;

    const formattedUsers = users.map(user => {
      const { password, video_count, comment_count, last_login_at, ...userWithoutPassword } = user;
      return {
        ...userWithoutPassword,
        last_login: last_login_at, // 映射字段名
        content_counts: {
          videos: video_count,
          comments: comment_count
        }
      };
    });

    return {
      data: formattedUsers,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total,
        totalPages: Math.ceil(total / parseInt(pageSize))
      }
    };
  }

  // 搜索用户
  async searchUsers(keyword, page = 1, pageSize = 20, filters = {}) {
    const offset = (page - 1) * pageSize;
    const searchTerm = `%${keyword}%`;
    
    let whereClauses = ['(u.username LIKE ? OR u.nickname LIKE ? OR u.email LIKE ?)'];
    const params = [searchTerm, searchTerm, searchTerm];

    if (filters.role && filters.role !== 'all') {
      whereClauses.push('u.role = ?');
      params.push(filters.role);
    }
    if (filters.status && filters.status !== 'all') {
      if(Array.isArray(filters.status)) {
        whereClauses.push(`u.status IN (${filters.status.map(() => '?').join(',')})`);
        params.push(...filters.status);
      } else {
        whereClauses.push('u.status = ?');
        params.push(filters.status);
      }
    } else {
      whereClauses.push("u.status IN ('active', 'banned', 'inactive')");
    }

    const whereSql = `WHERE ${whereClauses.join(' AND ')}`;

    const sql = `
      SELECT 
        u.*,
        (SELECT COUNT(*) FROM videos v WHERE v.user_id = u.id AND v.status != 'deleted') as video_count,
        (SELECT COUNT(*) FROM comments c WHERE c.user_id = u.id AND c.status = 'active') as comment_count
      FROM users u
      ${whereSql}
      ORDER BY u.created_at DESC
      LIMIT ${parseInt(pageSize, 10)} OFFSET ${parseInt(offset, 10)}
    `;
    
    const users = await this.query(sql, params);

    const countSql = `SELECT COUNT(*) as total FROM users u ${whereSql}`;
    const countResult = await this.query(countSql, params);
    const total = countResult[0].total;
    
    const formattedUsers = users.map(user => {
      const { password, video_count, comment_count, last_login_at, ...userWithoutPassword } = user;
      return {
        ...userWithoutPassword,
        last_login: last_login_at, // 映射字段名
        content_counts: {
          videos: video_count,
          comments: comment_count
        }
      };
    });

    return {
      data: formattedUsers,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 禁用用户
  async banUser(userId, reason = null) {
    return await this.update(userId, { 
      status: 'banned',
      updated_at: new Date()
    });
  }

  // 启用用户
  async unbanUser(userId) {
    return await this.update(userId, { 
      status: 'active',
      updated_at: new Date()
    });
  }
}

module.exports = new User();
