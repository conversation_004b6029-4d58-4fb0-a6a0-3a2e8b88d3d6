const { mysql } = require('../config/database');
const logger = require('../utils/logger');
const { pool } = require('./ConnectionManager');
const { Op } = require('sequelize'); // 确保Op也被引入

class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
    this.connection = mysql;
  }

  // 执行查询
  async query(sql, params = [], connection = null) {
    try {
      // logger.database(`执行查询: ${sql}`, { params });
      const executor = connection || this.connection;
      const [rows] = await executor.query(sql, params);
      return rows;
    } catch (error) {
      logger.error(`查询失败: ${sql}`, { params, error: error.message });
      throw error;
    }
  }

  // 查找单条记录
  async findById(id, lock = null, connection = null) {
    let sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
    const params = [id];

    if (lock && typeof lock === 'string') {
      sql += ` ${lock}`;
    }

    const executor = connection || this.connection;

    try {
      // 注意：这里的 query 方法是实例方法，它期望 executor 有 query 方法
      const [rows] = await executor.query(sql, params);
    return rows[0] || null;
    } catch (error) {
      // 错误日志记录了SQL和参数，但可能需要更具体的上下文
      logger.error(`通过ID查找记录失败: ${this.tableName}`, { id, sql, error: error.message });
      // 重新抛出错误，以便上层调用栈可以捕获
      throw error;
    }
  }
  
  // 查找单条记录
  async findOne(where, connection = null) {
    // 兼容 { where: { ... } } 的写法
    const conditions = where.where && typeof where.where === 'object' ? where.where : where;
    const rows = await this.findAll(conditions, { limit: 1 }, connection);
    return rows[0] || null;
  }


  // 查找多条记录
  async findAll(where = {}, options = {}) {
    const { orderBy, orderDirection = 'ASC', limit, offset } = options;

    // 兼容 { where: { ... } } 的写法
    const conditions = where.where && typeof where.where === 'object' ? where.where : where;

    let sql = `SELECT * FROM ??`;
    const params = [this.tableName];

    if (Object.keys(conditions).length > 0) {
      sql += ' WHERE ' + Object.keys(conditions).map(key => `?? = ?`).join(' AND ');
      Object.keys(conditions).forEach(key => {
        params.push(key, conditions[key]);
      });
    }

    if (orderBy) {
      sql += ` ORDER BY ?? ${orderDirection.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'}`;
      params.push(orderBy);
    }

    if (typeof limit === 'number') {
      sql += ' LIMIT ?';
      params.push(limit);
    }

    if (typeof offset === 'number') {
      sql += ' OFFSET ?';
      params.push(offset);
    }

    return this.query(sql, params);
  }

  // 查找一条记录
  async findOne(where = {}) {
    const results = await this.findAll(where, { limit: 1 });
    return results.length > 0 ? results[0] : null;
  }

  // 创建记录
  async create(data, connection = null) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map(() => '?').join(', ');
    
    const sql = `INSERT INTO ${this.tableName} (\`${fields.join('`, `')}\`) VALUES (${placeholders})`;
    
    const executor = connection || this.connection;
    
    try {
      const [result] = await executor.execute(sql, values);
      // logger.database(`创建记录成功: ${this.tableName}`, { id: result.insertId });
      return result.insertId;
    } catch (error) {
      logger.error(`创建记录失败: ${this.tableName}`, { data, error: error.message });
      throw error;
    }
  }

  // 更新记录
  async update(id, data, connection = null) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const setClause = fields.map(field => `\`${field}\` = ?`).join(', ');

    const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`;
    values.push(id);

    const executor = connection || this.connection;

    try {
      const [result] = await executor.execute(sql, values);
      // logger.database(`更新记录成功: ${this.tableName}`, { id, affectedRows: result.affectedRows });
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`更新记录失败: ${this.tableName}`, { id, data, error: error.message });
      throw error;
    }
  }
  
    // 更新记录
  async updateBy(where, data, connection = null) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const setClause = fields.map(field => `\`${field}\` = ?`).join(', ');
    
    const whereFields = Object.keys(where);
    const whereValues = Object.values(where);
    const whereClause = whereFields.map(field => `\`${field}\` = ?`).join(' AND ');

    const sql = `UPDATE ${this.tableName} SET ${setClause} WHERE ${whereClause}`;
    const allValues = [...values, ...whereValues];

    const executor = connection || this.connection;

    try {
      const [result] = await executor.execute(sql, allValues);
      // logger.database(`更新记录成功: ${this.tableName}`, { where, affectedRows: result.affectedRows });
      return result.affectedRows;
    } catch (error) {
      logger.error(`更新记录失败: ${this.tableName}`, { where, data, error: error.message });
      throw error;
    }
  }


  // 删除记录
  async delete(id, connection = null) {
    const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
    const executor = connection || this.connection;
    try {
      const [result] = await executor.execute(sql, [id]);
      // logger.database(`删除记录成功: ${this.tableName}`, { id, affectedRows: result.affectedRows });
      return result.affectedRows > 0;
    } catch (error) {
      logger.error(`删除记录失败: ${this.tableName}`, { id, error: error.message });
      throw error;
    }
  }

  // 软删除（如果表有deleted_at字段）
  async softDelete(id, connection = null) {
    return await this.update(id, { 
      status: 'deleted',
      deleted_at: new Date()
    }, connection);
  }

  // 统计记录数
  async count(where = {}, connection = null) {
    let sql = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const params = [];

    if (Object.keys(where).length > 0) {
      const whereClauses = Object.keys(where)
        .map(key => `\`${key}\` = ?`)
        .join(' AND ');
      sql += ` WHERE ${whereClauses}`;
      params.push(...Object.values(where));
    }
    
    const rows = await this.query(sql, params, connection);
    return rows[0].count;
  }

  /**
   * 执行一个SQL查询
   * @param {string} sql - The SQL query.
   * @param {Array<any>} params - The parameters for the query.
   * @returns {Promise<any>}
   */
  static async query(sql, params) {
    try {
      const result = await pool.query(sql, params);
      return result;
    } catch (error) {
      // 增强错误日志，打印出SQL和参数
      console.error('查询失败:', sql, '\n参数:', JSON.stringify({ params, error: error.message }, null, 2));
      throw error;
    }
  }

  /**
   * 按主键查找单个记录
   * @param {number|string} id - The primary key value.
   * @returns {Promise<BaseModel|null>}
   */
  static async findByPk(id) {
    const [rows] = await this.query(`SELECT * FROM ${this.tableName} WHERE id = ? LIMIT 1`, [id]);
    return rows[0] ? new this(rows[0]) : null;
  }

  /**
   * 查找单个记录
   * @param {object} options - The query options.
   * @returns {Promise<BaseModel|null>}
   */
  static async findOne(options) {
    const records = await this.findAll({ ...options, limit: 1 });
    return records.length > 0 ? records[0] : null;
  }

  /**
   * 查找多个记录
   * @param {object} options - The query options.
   * @returns {Promise<BaseModel[]>}
   */
  static async findAll(options = {}) {
    const { where = {}, order, limit, offset, attributes } = options;
    
    let conditions = '1=1';
    const params = [];

    // 重构WHERE条件构建逻辑以支持 [Op.between]
    const conditionParts = [];
    for (const key in where) {
      const value = where[key];
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        if (value[Op.between] && Array.isArray(value[Op.between]) && value[Op.between].length === 2) {
          conditionParts.push(`\`${key}\` BETWEEN ? AND ?`);
          params.push(value[Op.between][0], value[Op.between][1]);
        } else {
          // 可以扩展支持其他 Op, 但目前只处理简单相等
          conditionParts.push(`\`${key}\` = ?`);
          params.push(value);
        }
      } else {
        conditionParts.push(`\`${key}\` = ?`);
        params.push(value);
      }
    }

    if (conditionParts.length > 0) {
      conditions = conditionParts.join(' AND ');
    }
    
    const selectedAttributes = attributes ? attributes.map(attr => `\`${attr}\``).join(', ') : '*';

    let sql = `SELECT ${selectedAttributes} FROM ${this.tableName} WHERE ${conditions}`;

    if (order) {
      sql += ` ORDER BY ${order}`;
    }

    if (limit) {
      sql += ` LIMIT ${limit}`;
    }

    if (offset) {
      sql += ` OFFSET ${offset}`;
    }

    const [rows] = await this.query(sql, params);
    return rows.map(row => new this(row));
  }
}

module.exports = BaseModel;