#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');

// API文档生成器
class APIDocGenerator {
  constructor() {
    this.routes = [];
    this.modules = [
      'auth',
      'user', 
      'video',
      'interaction',
      'member',
      'admin'
    ];
  }

  // 扫描路由文件
  async scanRoutes() {
    console.log('扫描API路由...');
    
    for (const module of this.modules) {
      try {
        const routePath = path.join(__dirname, '..', 'src', 'modules', module, 'routes.js');
        const routeContent = await fs.readFile(routePath, 'utf8');
        
        // 简单的路由解析（实际项目中可能需要更复杂的解析）
        const routes = this.parseRoutes(routeContent, module);
        this.routes.push(...routes);
        
        console.log(`✅ ${module} 模块: ${routes.length} 个路由`);
      } catch (error) {
        console.warn(`⚠️  跳过 ${module} 模块: ${error.message}`);
      }
    }
    
    console.log(`总计扫描到 ${this.routes.length} 个API路由`);
  }

  // 解析路由（简化版）
  parseRoutes(content, module) {
    const routes = [];
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 匹配路由定义
      const routeMatch = line.match(/router\.(get|post|put|delete|patch)\s*\(\s*['"`]([^'"`]+)['"`]/);
      if (routeMatch) {
        const [, method, path] = routeMatch;
        
        // 查找注释
        let description = '';
        for (let j = i - 1; j >= 0; j--) {
          const prevLine = lines[j].trim();
          if (prevLine.startsWith('//')) {
            description = prevLine.replace('//', '').trim();
            break;
          }
          if (prevLine && !prevLine.startsWith('//')) {
            break;
          }
        }
        
        routes.push({
          module,
          method: method.toUpperCase(),
          path: `/api/${module}${path}`,
          description: description || `${method.toUpperCase()} ${path}`,
          auth: this.detectAuthRequirement(content, i),
          admin: this.detectAdminRequirement(content, i)
        });
      }
    }
    
    return routes;
  }

  // 检测认证要求
  detectAuthRequirement(content, lineIndex) {
    const lines = content.split('\n');
    
    // 检查前面几行是否有认证中间件
    for (let i = Math.max(0, lineIndex - 10); i < lineIndex; i++) {
      const line = lines[i];
      if (line.includes('verifyToken') || line.includes('requireAuth')) {
        return true;
      }
    }
    
    return false;
  }

  // 检测管理员要求
  detectAdminRequirement(content, lineIndex) {
    const lines = content.split('\n');
    
    for (let i = Math.max(0, lineIndex - 10); i < lineIndex; i++) {
      const line = lines[i];
      if (line.includes('requireAdmin')) {
        return true;
      }
    }
    
    return false;
  }

  // 生成Markdown文档
  generateMarkdown() {
    let markdown = `# API接口文档

> 自动生成于 ${new Date().toLocaleString('zh-CN')}

## 概述

本文档包含所有API接口的详细信息，共计 **${this.routes.length}** 个接口。

### 基础信息

- **Base URL**: \`https://api.yourdomain.com\`
- **API版本**: v1
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 认证说明

需要认证的接口请在请求头中包含：
\`\`\`http
Authorization: Bearer <your_jwt_token>
\`\`\`

### 通用响应格式

成功响应：
\`\`\`json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
\`\`\`

错误响应：
\`\`\`json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": {
    // 错误详情（可选）
  }
}
\`\`\`

### HTTP状态码

- \`200\` - 成功
- \`201\` - 创建成功
- \`400\` - 请求参数错误
- \`401\` - 未认证
- \`403\` - 权限不足
- \`404\` - 资源不存在
- \`429\` - 请求过于频繁
- \`500\` - 服务器内部错误

## API接口列表

`;

    // 按模块分组
    const groupedRoutes = this.groupRoutesByModule();
    
    for (const [module, routes] of Object.entries(groupedRoutes)) {
      markdown += `### ${this.getModuleName(module)}\n\n`;
      
      for (const route of routes) {
        markdown += this.generateRouteDoc(route);
      }
      
      markdown += '\n';
    }

    // 添加错误代码说明
    markdown += this.generateErrorCodes();
    
    return markdown;
  }

  // 按模块分组路由
  groupRoutesByModule() {
    const grouped = {};
    
    for (const route of this.routes) {
      if (!grouped[route.module]) {
        grouped[route.module] = [];
      }
      grouped[route.module].push(route);
    }
    
    // 排序
    for (const module in grouped) {
      grouped[module].sort((a, b) => {
        if (a.path < b.path) return -1;
        if (a.path > b.path) return 1;
        return 0;
      });
    }
    
    return grouped;
  }

  // 获取模块中文名
  getModuleName(module) {
    const names = {
      auth: '认证模块',
      user: '用户模块', 
      video: '视频模块',
      interaction: '互动模块',
      member: '会员模块',
      admin: '管理模块'
    };
    
    return names[module] || module;
  }

  // 生成单个路由文档
  generateRouteDoc(route) {
    let doc = `#### ${route.method} ${route.path}\n\n`;
    doc += `**描述**: ${route.description}\n\n`;
    
    // 认证要求
    if (route.auth) {
      doc += `**认证**: 🔐 需要认证\n\n`;
    }
    
    if (route.admin) {
      doc += `**权限**: 👑 需要管理员权限\n\n`;
    }
    
    // 示例请求
    doc += `**请求示例**:\n`;
    doc += `\`\`\`http\n`;
    doc += `${route.method} ${route.path}\n`;
    if (route.auth) {
      doc += `Authorization: Bearer <token>\n`;
    }
    doc += `Content-Type: application/json\n`;
    doc += `\`\`\`\n\n`;
    
    return doc;
  }

  // 生成错误代码说明
  generateErrorCodes() {
    return `## 错误代码说明

| 错误代码 | 说明 |
|---------|------|
| \`VALIDATION_ERROR\` | 数据验证失败 |
| \`UNAUTHORIZED\` | 未认证 |
| \`ACCESS_DENIED\` | 权限不足 |
| \`USER_NOT_FOUND\` | 用户不存在 |
| \`VIDEO_NOT_FOUND\` | 视频不存在 |
| \`EMAIL_EXISTS\` | 邮箱已存在 |
| \`USERNAME_EXISTS\` | 用户名已存在 |
| \`INVALID_CREDENTIALS\` | 登录凭据无效 |
| \`TOKEN_EXPIRED\` | Token已过期 |
| \`RATE_LIMIT_EXCEEDED\` | 请求频率超限 |

## HTTP状态码

| 状态码 | 说明 |
|-------|------|
| 200 | 成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

---

*文档最后更新: ${new Date().toLocaleString()}*
`;
  }

  // 生成Postman集合
  generatePostmanCollection() {
    const collection = {
      info: {
        name: "视频平台API",
        description: "视频平台API接口集合",
        version: "1.0.0"
      },
      auth: {
        type: "bearer",
        bearer: [
          {
            key: "token",
            value: "{{auth_token}}",
            type: "string"
          }
        ]
      },
      variable: [
        {
          key: "base_url",
          value: "http://localhost:3000",
          type: "string"
        },
        {
          key: "auth_token",
          value: "",
          type: "string"
        }
      ],
      item: []
    };

    const groupedRoutes = this.groupRoutesByModule();
    
    for (const [module, routes] of Object.entries(groupedRoutes)) {
      const folder = {
        name: this.getModuleName(module),
        item: []
      };
      
      for (const route of routes) {
        const item = {
          name: route.description,
          request: {
            method: route.method,
            header: [
              {
                key: "Content-Type",
                value: "application/json"
              }
            ],
            url: {
              raw: "{{base_url}}" + route.path,
              host: ["{{base_url}}"],
              path: route.path.split('/').filter(p => p)
            }
          }
        };
        
        if (route.auth) {
          item.request.auth = {
            type: "bearer",
            bearer: [
              {
                key: "token",
                value: "{{auth_token}}",
                type: "string"
              }
            ]
          };
        }
        
        folder.item.push(item);
      }
      
      collection.item.push(folder);
    }
    
    return JSON.stringify(collection, null, 2);
  }

  // 保存文档
  async saveDocs() {
    const docsDir = path.join(__dirname, '..', 'docs');
    
    try {
      await fs.access(docsDir);
    } catch {
      await fs.mkdir(docsDir, { recursive: true });
    }
    
    // 保存Markdown文档
    const markdown = this.generateMarkdown();
    await fs.writeFile(path.join(docsDir, 'API_AUTO.md'), markdown);
    console.log('✅ Markdown文档已生成: docs/API_AUTO.md');
    
    // 保存Postman集合
    const postman = this.generatePostmanCollection();
    await fs.writeFile(path.join(docsDir, 'postman_collection.json'), postman);
    console.log('✅ Postman集合已生成: docs/postman_collection.json');
  }

  // 运行生成器
  async run() {
    console.log('🚀 开始生成API文档...\n');
    
    try {
      await this.scanRoutes();
      console.log('');
      await this.saveDocs();
      
      console.log('\n🎉 API文档生成完成！');
      console.log('\n使用方法:');
      console.log('1. 查看 docs/API_AUTO.md 获取完整API文档');
      console.log('2. 导入 docs/postman_collection.json 到Postman进行接口测试');
      
    } catch (error) {
      console.error('❌ 文档生成失败:', error);
      process.exit(1);
    }
  }
}

// 运行生成器
if (require.main === module) {
  const generator = new APIDocGenerator();
  generator.run();
}

module.exports = APIDocGenerator;
