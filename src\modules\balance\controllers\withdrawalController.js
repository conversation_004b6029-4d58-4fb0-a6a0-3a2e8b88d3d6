const asyncHandler = require('express-async-handler');
const withdrawalService = require('../services/withdrawalService');

class WithdrawalController {
  
  /**
   * @desc    用户申请提现
   * @route   POST /api/balance/withdrawals
   * @access  Private
   */
  requestWithdrawal = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { amount, walletType, walletAddress } = req.body;
    
    const withdrawalRequest = await withdrawalService.createWithdrawalRequest(
      userId, 
      amount, 
      walletType, 
      walletAddress
    );
    
    res.status(201).json({
      success: true,
      message: '提现申请已提交，等待审核。',
      data: withdrawalRequest,
    });
  });

  /**
   * @desc    获取用户提现历史
   * @route   GET /api/balance/withdrawals
   * @access  Private
   */
  getWithdrawalHistory = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { page = 1, limit = 10, status } = req.query;
    
    const history = await withdrawalService.getWithdrawalHistory(userId, { page, limit, status });
    
    res.json({
      success: true,
      data: history,
    });
  });
}

module.exports = new WithdrawalController(); 