const db = require('../../database/ConnectionManager');

// 根据视频ID获取评论
async function getCommentsByVideoId(videoId) {
  // 使用 executeQuery 方法
  return await db.executeQuery(
    `SELECT c.id, c.content, c.created_at, c.parent_id, 
            u.id as user_id, u.username, u.nickname, u.avatar 
     FROM comments c
     JOIN users u ON c.user_id = u.id
     WHERE c.video_id = ? AND c.status = 'active'
     ORDER BY c.created_at DESC`,
    [videoId]
  );
}

// 创建新评论（带事务）
async function createComment({ videoId, userId, content, parentId = null }) {
  const transactionId = await db.beginTransaction();
  try {
    // 1. 插入评论
    const result = await db.executeInTransaction(
      transactionId,
      'INSERT INTO comments (video_id, user_id, content, parent_id) VALUES (?, ?, ?, ?)',
      [videoId, userId, content, parentId]
    );
    const commentId = result.insertId;

    // 2. 更新视频表中的评论数
    await db.executeInTransaction(
      transactionId,
      'UPDATE videos SET comment_count = comment_count + 1 WHERE id = ?',
      [videoId]
    );
    
    // 3. 如果是回复，更新父评论的回复数
    if (parentId) {
      await db.executeInTransaction(
        transactionId,
        'UPDATE comments SET reply_count = reply_count + 1 WHERE id = ?',
        [parentId]
      );
    }

    // 4. 获取刚创建的完整评论信息并返回
    const newCommentResult = await db.executeInTransaction(
      transactionId,
      `SELECT c.id, c.content, c.created_at, c.parent_id, 
              u.id as user_id, u.username, u.nickname, u.avatar 
       FROM comments c
       JOIN users u ON c.user_id = u.id
       WHERE c.id = ?`,
      [commentId]
    );

    await db.commitTransaction(transactionId);
    
    return newCommentResult[0];

  } catch (error) {
    await db.rollbackTransaction(transactionId);
    // 重新抛出错误，以便上层可以捕获
    throw error;
  }
}

module.exports = {
  getCommentsByVideoId,
  createComment,
}; 