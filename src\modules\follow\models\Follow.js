const BaseModel = require('../../../database/BaseModel');
const { AppError } = require('../../../middleware/errorHandler');

class Follow extends BaseModel {
  constructor() {
    super('follows');
  }

  // 关注用户
  async followUser(followerId, followedId) {
    if (followerId === followedId) {
      throw new AppError('不能关注自己', 400, 'CANNOT_FOLLOW_SELF');
    }

    // 检查是否已经关注
    const existing = await this.findOne({
      follower_id: followerId,
      followed_id: followedId
    });

    if (existing) {
      throw new AppError('已经关注过该用户', 409, 'ALREADY_FOLLOWING');
    }

    const followId = await this.create({
      follower_id: followerId,
      followed_id: followedId,
      status: 'active'
    });

    // 更新统计数据
    await this.updateUserStats(followerId);
    await this.updateUserStats(followedId);
    
    return followId;
  }

  // 取消关注
  async unfollowUser(followerId, followedId) {
    const follow = await this.findOne({
      follower_id: followerId,
      followed_id: followedId
    });

    if (!follow) {
      throw new AppError('未关注该用户', 404, 'NOT_FOLLOWING');
    }

    await this.delete(follow.id);
    
    // 更新统计数据
    await this.updateUserStats(followerId);
    await this.updateUserStats(followedId);
    
    return true;
  }

  // 更新关注统计
  async updateFollowStats(followerId, followedId, action) {
    const increment = action === 'follow' ? 1 : -1;
    
    // 更新关注者的关注数
    await this.query(`
      INSERT INTO user_stats (user_id, following_count) 
      VALUES (?, 1) 
      ON DUPLICATE KEY UPDATE following_count = GREATEST(following_count + ?, 0)
    `, [followerId, increment]);

    // 更新被关注者的粉丝数
    await this.query(`
      INSERT INTO user_stats (user_id, follower_count) 
      VALUES (?, 1) 
      ON DUPLICATE KEY UPDATE follower_count = GREATEST(follower_count + ?, 0)
    `, [followedId, increment]);
  }

  // 获取粉丝列表
  async getFollowers(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const sql = `
      SELECT 
        f.id as follow_id,
        f.created_at as follow_time,
        u.id, u.username, u.nickname, u.avatar, u.bio,
        us.follower_count, us.following_count, us.video_count
      FROM follows f
      JOIN users u ON f.follower_id = u.id
      LEFT JOIN user_stats us ON u.id = us.user_id
      WHERE f.followed_id = ? AND f.status = 'active'
      ORDER BY f.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const followers = await this.query(sql, [userId, limit, offset]);

    const countSql = `
      SELECT COUNT(*) as total 
      FROM follows 
      WHERE followed_id = ? AND status = 'active'
    `;
    const countResult = await this.query(countSql, [userId]);

    return {
      data: followers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / limit)
      }
    };
  }

  // 获取关注列表
  async getFollowing(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const sql = `
      SELECT 
        f.id as follow_id,
        f.created_at as follow_time,
        u.id, u.username, u.nickname, u.avatar, u.bio,
        us.follower_count, us.following_count, us.video_count
      FROM follows f
      JOIN users u ON f.followed_id = u.id
      LEFT JOIN user_stats us ON u.id = us.user_id
      WHERE f.follower_id = ? AND f.status = 'active'
      ORDER BY f.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const following = await this.query(sql, [userId, limit, offset]);

    const countSql = `
      SELECT COUNT(*) as total 
      FROM follows 
      WHERE follower_id = ? AND status = 'active'
    `;
    const countResult = await this.query(countSql, [userId]);

    return {
      data: following,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / limit)
      }
    };
  }

  // 检查关注状态
  async checkFollowStatus(followerId, followedId) {
    const follow = await this.findOne({
      follower_id: followerId,
      followed_id: followedId,
      status: 'active'
    });
    
    return !!follow;
  }

  // 批量检查关注状态
  async batchCheckFollowStatus(followerId, userIds) {
    if (!userIds || userIds.length === 0) return {};

    const placeholders = userIds.map(() => '?').join(',');
    const sql = `
      SELECT followed_id
      FROM follows 
      WHERE follower_id = ? AND followed_id IN (${placeholders}) AND status = 'active'
    `;

    const results = await this.query(sql, [followerId, ...userIds]);
    const followedIds = new Set(results.map(r => r.followed_id));

    const statusMap = {};
    userIds.forEach(id => {
      statusMap[id] = followedIds.has(id);
    });

    return statusMap;
  }

  // 获取关注者的最新视频 (用于推荐)
  async getFollowingVideos(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const sql = `
      SELECT 
        v.id, v.title, v.description, v.thumbnail_url, v.url, 
        v.duration, v.view_count, v.like_count, v.comment_count,
        v.published_at, v.created_at,
        u.id as uploader_id, u.username, u.nickname, u.avatar
      FROM videos v
      JOIN users u ON v.user_id = u.id
      JOIN follows f ON u.id = f.followed_id
      WHERE f.follower_id = ? 
        AND f.status = 'active'
        AND v.status = 'published'
        AND v.visibility IN ('public', 'member_only')
      ORDER BY v.published_at DESC
      LIMIT ? OFFSET ?
    `;

    const videos = await this.query(sql, [userId, limit, offset]);

    const countSql = `
      SELECT COUNT(*) as total
      FROM videos v
      JOIN follows f ON v.user_id = f.followed_id
      WHERE f.follower_id = ? 
        AND f.status = 'active'
        AND v.status = 'published'
        AND v.visibility IN ('public', 'member_only')
    `;
    const countResult = await this.query(countSql, [userId]);

    return {
      data: videos,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / limit)
      }
    };
  }

  // 获取用户关注统计
  async getUserFollowStats(userId) {
    const sql = `
      SELECT 
        COALESCE(us.follower_count, 0) as follower_count,
        COALESCE(us.following_count, 0) as following_count,
        COALESCE(us.video_count, 0) as video_count,
        COALESCE(us.total_views, 0) as total_views,
        COALESCE(us.total_likes, 0) as total_likes
      FROM users u
      LEFT JOIN user_stats us ON u.id = us.user_id
      WHERE u.id = ?
    `;

    const result = await this.query(sql, [userId]);
    return result[0] || {
      follower_count: 0,
      following_count: 0,
      video_count: 0,
      total_views: 0,
      total_likes: 0
    };
  }

  // 更新单个用户的统计数据
  async updateUserStats(userId) {
    const sql = `
      INSERT INTO user_stats (user_id, follower_count, following_count, video_count, total_views, total_likes)
      SELECT 
        u.id,
        COALESCE(followers.count, 0) as follower_count,
        COALESCE(following.count, 0) as following_count,
        COALESCE(videos.count, 0) as video_count,
        0 as total_views,
        0 as total_likes
      FROM users u
      LEFT JOIN (
        SELECT followed_id, COUNT(*) as count 
        FROM follows 
        WHERE status = 'active' AND followed_id = ?
        GROUP BY followed_id
      ) followers ON u.id = followers.followed_id
      LEFT JOIN (
        SELECT follower_id, COUNT(*) as count 
        FROM follows 
        WHERE status = 'active' AND follower_id = ?
        GROUP BY follower_id
      ) following ON u.id = following.follower_id
      LEFT JOIN (
        SELECT user_id, COUNT(*) as count 
        FROM videos 
        WHERE status = 'published' AND user_id = ?
        GROUP BY user_id
      ) videos ON u.id = videos.user_id
      WHERE u.id = ?
      ON DUPLICATE KEY UPDATE
      follower_count = VALUES(follower_count),
      following_count = VALUES(following_count),
      video_count = VALUES(video_count)
    `;

    await this.query(sql, [userId, userId, userId, userId]);
  }
}

module.exports = new Follow();