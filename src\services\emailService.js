const nodemailer = require('nodemailer');
const logger = require('../utils/logger');
const SystemConfig = require('../database/models/SystemConfig');
const { SocksProxyAgent } = require('socks-proxy-agent');

class EmailService {
  constructor() {
    this.transporter = null;
    this.config = null;
  }

  resetTransporter() {
    this.transporter = null;
    this.config = null;
    logger.info('邮件服务配置已重置，将在下次使用时重新加载。');
  }

  isConfigured() {
    return !!this.transporter;
  }

  async getTransporter() {
    if (this.transporter) {
      return this.transporter;
    }

    try {
      // 从数据库获取 'email' 分组的所有配置
      const emailSettingsArray = await SystemConfig.getByGroup('email');

      // 将数组转换为对象以便使用
      const settings = emailSettingsArray.reduce((acc, item) => {
        acc[item.config_key] = item.config_value;
        return acc;
      }, {});

      // 增加详细日志，打印从数据库获取的原始配置
      logger.info('从数据库加载的邮件配置:', settings);

      if (settings && settings.smtpHost && settings.smtpUser && settings.smtpPassword) {
        this.config = settings;
        
        const transportOptions = {
          host: this.config.smtpHost,
          port: this.config.smtpPort || 587,
          secure: Number(this.config.smtpPort) === 465,
          auth: {
            user: this.config.smtpUser,
            pass: this.config.smtpPassword,
          },
          tls: {
            rejectUnauthorized: false,
          },
        };

        // 处理代理配置
        const proxyEnabled = this.config.proxyEnabled === 'true' || this.config.proxyEnabled === true;
        const proxyUrl = this.config.proxy;

        if (proxyEnabled && proxyUrl && proxyUrl.trim() !== '' && proxyUrl !== 'disabled') {
          logger.info(`邮件服务正在使用代理: ${proxyUrl}`);

          if (proxyUrl.startsWith('socks5://') || proxyUrl.startsWith('socks4://')) {
            // SOCKS 代理配置
            try {
              const agent = new SocksProxyAgent(proxyUrl);
              transportOptions.socketOptions = {
                agent: agent
              };
              logger.info('SOCKS 代理配置成功');
            } catch (error) {
              logger.error('SOCKS 代理配置失败:', error.message);
              // 在开发环境下，如果代理配置失败，可以选择继续不使用代理
              if (process.env.NODE_ENV === 'development') {
                logger.warn('开发环境下代理配置失败，将尝试直连');
              } else {
                throw new Error(`SOCKS 代理配置失败: ${error.message}`);
              }
            }
          } else if (proxyUrl.startsWith('http://') || proxyUrl.startsWith('https://')) {
            // HTTP 代理配置
            transportOptions.proxy = proxyUrl;
            logger.info('HTTP 代理配置成功');
          } else {
            logger.warn('不支持的代理协议，将忽略代理设置');
          }
        } else {
          if (proxyEnabled) {
            logger.warn('代理已启用但未配置有效的代理URL');
          } else {
            logger.info('邮件服务代理已禁用，使用直连');
          }
        }

        this.transporter = nodemailer.createTransport(transportOptions);

        // 验证邮件服务配置
        try {
          await this.transporter.verify();
          logger.info('邮件服务已根据数据库配置动态就绪');
          return this.transporter;
        } catch (verifyError) {
          logger.error('邮件服务验证失败:', {
            error: verifyError.message,
            host: this.config.smtpHost,
            port: this.config.smtpPort,
            user: this.config.smtpUser,
            proxy: this.config.proxy || 'none'
          });

          // 在开发环境下提供更详细的错误信息
          if (process.env.NODE_ENV === 'development') {
            logger.warn('开发环境邮件服务验证失败，但将继续尝试发送（可能会失败）');
            return this.transporter; // 在开发环境下即使验证失败也返回 transporter
          } else {
            throw verifyError;
          }
        }

      } else {
        logger.warn('数据库中邮件服务配置不完整，无法初始化邮件服务');
        this.transporter = null;
        return null;
      }
    } catch (error) {
      logger.error('动态初始化邮件服务失败:', { message: error.message, stack: error.stack });
      this.transporter = null;
      return null;
    }
  }

  async sendMail(mailOptions) {
    const transporter = await this.getTransporter();
    
    if (!transporter) {
      logger.error('邮件发送失败：邮件服务未配置或初始化失败。');
      // 返回一个包含错误信息的对象，而不是简单的false
      return { success: false, error: 'Mail service not configured' };
    }

    try {
      // 确保发件人信息存在
      const from = this.config.fromName 
        ? `"${this.config.fromName}" <${this.config.fromEmail}>` 
        : this.config.smtpUser;

      const fullMailOptions = {
        ...mailOptions,
        from,
        text: mailOptions.text || this.stripHtml(mailOptions.html),
      };

      const result = await transporter.sendMail(fullMailOptions);
      logger.info(`邮件发送成功: ${mailOptions.to}`, { messageId: result.messageId });
      return { success: true, messageId: result.messageId };
    } catch (error) {
      logger.error(`邮件发送失败: ${mailOptions.to}`, { message: error.message });
      return { success: false, error: error.message };
    }
  }

  // 发送注册验证码
  async sendRegistrationCode(email, code) {
    const subject = '您的注册验证码';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; padding: 20px; border: 1px solid #ddd;">
        <h2 style="color: #333;">欢迎注册！</h2>
        <p>您正在进行注册操作，您的验证码是：</p>
        <p style="font-size: 24px; font-weight: bold; color: #007bff; letter-spacing: 2px; text-align: center;">${code}</p>
        <p>此验证码将在5分钟内有效。请勿将验证码泄露给他人。</p>
        <hr>
        <p style="font-size: 12px; color: #999;">如果这不是您本人操作，请忽略此邮件。</p>
      </div>
    `;
    return this.sendMail({
      to: email,
      subject,
      html
    });
  }

  // 发送邮箱验证邮件
  async sendVerificationEmail(email, verificationCode, username) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?code=${verificationCode}`;
    
    const subject = '验证您的邮箱地址';
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>邮箱验证</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>欢迎注册视频平台</h1>
          </div>
          <div class="content">
            <h2>您好，${username}！</h2>
            <p>感谢您注册我们的视频平台。为了确保您的账户安全，请点击下面的按钮验证您的邮箱地址：</p>
            <p style="text-align: center;">
              <a href="${verificationUrl}" class="button">验证邮箱</a>
            </p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all; background: #eee; padding: 10px;">${verificationUrl}</p>
            <p><strong>注意：</strong>此验证链接将在24小时后过期。</p>
          </div>
          <div class="footer">
            <p>如果您没有注册我们的服务，请忽略此邮件。</p>
            <p>&copy; 2025 视频平台. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendMail({ to: email, subject, html });
  }

  // 发送密码重置邮件
  async sendPasswordResetEmail(email, resetToken, username) {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}`;
    
    const subject = '重置您的密码';
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>密码重置</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>密码重置请求</h1>
          </div>
          <div class="content">
            <h2>您好，${username}！</h2>
            <p>我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的按钮重置密码：</p>
            <p style="text-align: center;">
              <a href="${resetUrl}" class="button">重置密码</a>
            </p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all; background: #eee; padding: 10px;">${resetUrl}</p>
            <div class="warning">
              <strong>安全提醒：</strong>
              <ul>
                <li>此重置链接将在1小时后过期</li>
                <li>如果您没有请求重置密码，请忽略此邮件</li>
                <li>请不要将此链接分享给他人</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>如果您有任何疑问，请联系我们的客服团队。</p>
            <p>&copy; 2025 视频平台. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendMail({ to: email, subject, html });
  }

  // 发送欢迎邮件
  async sendWelcomeEmail(email, username) {
    const subject = '欢迎加入视频平台';
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>欢迎加入</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .feature { background: white; padding: 15px; margin: 10px 0; border-radius: 4px; border-left: 4px solid #28a745; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>欢迎加入视频平台！</h1>
          </div>
          <div class="content">
            <h2>您好，${username}！</h2>
            <p>恭喜您成功注册并验证了邮箱！现在您可以享受我们平台的所有功能：</p>
            
            <div class="feature">
              <h3>🎬 观看精彩视频</h3>
              <p>浏览海量高质量视频内容，发现您感兴趣的内容。</p>
            </div>
            
            <div class="feature">
              <h3>📤 上传您的作品</h3>
              <p>分享您的创意视频，与更多用户互动交流。</p>
            </div>
            
            <div class="feature">
              <h3>💬 参与互动</h3>
              <p>评论、点赞、收藏，与其他用户建立联系。</p>
            </div>
            
            <div class="feature">
              <h3>⭐ 会员特权</h3>
              <p>升级会员享受更多专属内容和特权功能。</p>
            </div>
            
            <p>如果您有任何问题或建议，随时联系我们的客服团队。</p>
            <p>祝您使用愉快！</p>
          </div>
          <div class="footer">
            <p>感谢您选择我们的平台！</p>
            <p>&copy; 2025 视频平台. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendMail({ to: email, subject, html });
  }

  // 发送安全警告邮件
  async sendSecurityAlert(email, username, alertType, details) {
    const subject = '账户安全警告';
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>安全警告</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #ffc107; color: #212529; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .alert { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 4px; margin: 15px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⚠️ 账户安全警告</h1>
          </div>
          <div class="content">
            <h2>您好，${username}！</h2>
            <p>我们检测到您的账户存在以下安全事件：</p>
            
            <div class="alert">
              <strong>警告类型：</strong> ${alertType}<br>
              <strong>详细信息：</strong> ${details}<br>
              <strong>时间：</strong> ${new Date().toLocaleString('zh-CN')}
            </div>
            
            <p><strong>建议采取的措施：</strong></p>
            <ul>
              <li>立即修改您的密码</li>
              <li>检查账户的登录记录</li>
              <li>确保您的邮箱安全</li>
              <li>如有疑问，请立即联系客服</li>
            </ul>
            
            <p>如果这不是您本人的操作，请立即联系我们的客服团队。</p>
          </div>
          <div class="footer">
            <p>您的账户安全是我们的首要关注。</p>
            <p>&copy; 2025 视频平台. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendMail({ to: email, subject, html });
  }

  // 移除HTML标签（用于纯文本版本）
  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  // 测试邮件服务
  async testEmailService() {
    const transporter = await this.getTransporter();
    if (!transporter) {
      return { success: false, message: '邮件服务未配置' };
    }

    try {
      await transporter.verify();
      const proxyEnabled = this.config.proxyEnabled === 'true' || this.config.proxyEnabled === true;

      return {
        success: true,
        message: '邮件服务连接正常',
        config: {
          host: this.config.smtpHost,
          port: this.config.smtpPort,
          user: this.config.smtpUser,
          proxyEnabled: proxyEnabled,
          proxy: proxyEnabled ? (this.config.proxy || 'none') : 'disabled',
          secure: Number(this.config.smtpPort) === 465
        }
      };
    } catch (error) {
      return { success: false, message: `邮件服务连接失败: ${error.message}` };
    }
  }
}

// 创建邮件服务实例
const emailService = new EmailService();

module.exports = emailService;
