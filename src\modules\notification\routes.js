const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../middleware/auth');
const NotificationController = require('./controllers/notificationController');

// 所有通知相关路由都需要认证
router.use(verifyToken);

/**
 * @swagger
 * /api/notifications:
 *   get:
 *     summary: 获取当前用户的通知列表
 *     tags: [Notifications]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: unreadOnly
 *         schema:
 *           type: boolean
 *         description: 只看未读
 *     responses:
 *       200:
 *         description: 成功获取通知列表
 */
router.get('/', NotificationController.getNotifications);

/**
 * @swagger
 * /api/notifications/status:
 *   get:
 *     summary: 获取通知状态（如未读数量）
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: 成功获取通知状态
 */
router.get('/status', NotificationController.getNotificationStatus);

/**
 * @swagger
 * /api/notifications/read-all:
 *   post:
 *     summary: 将所有未读通知标记为已读
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: 操作成功
 */
router.post('/read-all', NotificationController.markAllAsRead);

/**
 * @swagger
 * /api/notifications/{id}/read:
 *   post:
 *     summary: 将单个通知标记为已读
 *     tags: [Notifications]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 通知ID
 *     responses:
 *       200:
 *         description: 操作成功
 */
router.post('/:id/read', NotificationController.markAsRead);

module.exports = router; 