const db = require('../../../database/ConnectionManager');
const logger = require('../../../utils/logger');

class AdminService {
  // ... existing code from the file ...

  async getCommentList({ page = 1, pageSize = 10, search = '', status = '', userId = null, videoId = null }) {
    // Sanitize pagination values to ensure they are integers
    const limit = parseInt(pageSize, 10) || 10;
    const sanitizedPage = parseInt(page, 10) || 1;
    const offset = sanitizedPage > 0 ? (sanitizedPage - 1) * limit : 0;

    let whereClauses = [];
    let params = [];

    if (search) {
        whereClauses.push(`c.content LIKE ?`);
        params.push(`%${search}%`);
    }
    if (status && status !== 'all') {
        whereClauses.push(`c.status = ?`);
        params.push(status);
    }
    if (userId) {
        whereClauses.push(`c.user_id = ?`);
        params.push(userId);
    }
    if (videoId) {
        whereClauses.push(`c.video_id = ?`);
        params.push(videoId);
    }

    const whereSql = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

    const commentsSql = `
        SELECT 
            c.id, c.content, c.status, c.created_at, c.like_count, c.reply_count,
            u.id as user_id, u.username, u.nickname,
            v.id as video_id, v.title as video_title
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN videos v ON c.video_id = v.id
        ${whereSql}
        ORDER BY c.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
    `;
    
    const countSql = `SELECT COUNT(*) as total FROM comments c ${whereSql}`;

    try {
        const [comments, [{ total }]] = await Promise.all([
            db.executeQuery(commentsSql, params), // No longer passing limit/offset as params
            db.executeQuery(countSql, params),
        ]);

        return {
            data: comments,
            pagination: {
                page: sanitizedPage,
                pageSize: limit,
                total,
                totalPages: Math.ceil(total / limit),
            }
        };
    } catch (error) {
        logger.error('获取评论列表失败 (Admin):', error);
        throw error;
    }
  }

  async getCommentStats() {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 设置为今天零点

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1); // 设置为明天零点

    const totalSql = `SELECT COUNT(*) as total FROM comments`;
    const todaySql = `SELECT COUNT(*) as total FROM comments WHERE created_at >= ? AND created_at < ?`;
    const pendingSql = `SELECT COUNT(*) as total FROM comments WHERE status = 'pending'`;
    const flaggedSql = `SELECT COUNT(*) as total FROM comments WHERE status = 'flagged'`;

    try {
      const [[{ total }], [todayStats], [{ pending }], [{ flagged }]] = await Promise.all([
        db.executeQuery(totalSql),
        db.executeQuery(todaySql, [today, tomorrow]),
        db.executeQuery(pendingSql),
        db.executeQuery(flaggedSql),
      ]);

      return {
        total,
        today: todayStats ? todayStats.total : 0,
        pending,
        flagged,
      };
    } catch (error) {
        logger.error('获取评论统计失败 (Admin):', error);
        throw error;
    }
  }

  async deleteComment(commentId) {
    try {
      const result = await db.executeQuery('DELETE FROM comments WHERE id = ?', [commentId]);
      if (result.affectedRows === 0) {
        throw new Error('Comment not found or already deleted.');
      }
      logger.info(`管理员删除了评论 #${commentId}`);
      return { success: true };
    } catch (error) {
      logger.error(`管理员删除评论 #${commentId} 失败:`, error);
      throw error;
    }
  }

  // ... other existing methods from the file ...
}

module.exports = new AdminService(); 