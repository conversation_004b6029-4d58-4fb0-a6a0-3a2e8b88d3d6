const express = require('express');
const router = express.Router();
const followController = require('./controllers/followController');
const { verifyToken } = require('../../middleware/auth');
const { body, param, query, validationResult } = require('express-validator');

// 验证结果处理中间件
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '请求数据验证失败',
      errors: errors.array()
    });
  }
  next();
};

// 验证中间件
const validateUserId = [
  param('userId').isInt({ min: 1 }).withMessage('用户ID必须是正整数'),
  handleValidationErrors
];

const validatePagination = [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  handleValidationErrors
];

const validateUserIds = [
  body('userIds').isArray({ min: 1, max: 100 }).withMessage('用户ID列表必须是数组且长度在1-100之间'),
  body('userIds.*').isInt({ min: 1 }).withMessage('用户ID必须是正整数'),
  handleValidationErrors
];

// 需要登录的路由
router.use(verifyToken);

// 关注相关路由
router.post('/follow/:userId', validateUserId, followController.followUser);        // 关注用户
router.delete('/follow/:userId', validateUserId, followController.unfollowUser);    // 取消关注
router.get('/follow/:userId/status', validateUserId, followController.checkFollowStatus); // 检查关注状态

// 粉丝和关注列表
router.get('/users/:userId/followers', validateUserId, validatePagination, followController.getFollowers);  // 获取粉丝列表
router.get('/users/:userId/following', validateUserId, validatePagination, followController.getFollowing);  // 获取关注列表
router.get('/users/:userId/stats', validateUserId, followController.getFollowStats);    // 获取关注统计
router.get('/users/:userId/mutual', validateUserId, validatePagination, followController.getMutualFollows); // 获取相互关注列表

// 关注动态和推荐
router.get('/following/videos', validatePagination, followController.getFollowingVideos);   // 获取关注者视频动态
router.get('/recommendations/users', [
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('推荐数量必须在1-50之间'),
  handleValidationErrors
], followController.getRecommendedUsers); // 获取推荐关注用户

// 批量操作
router.post('/follow/batch/status', validateUserIds, followController.batchCheckFollowStatus); // 批量检查关注状态

// 管理员功能
router.post('/admin/update-stats', followController.updateUserStats); // 更新用户统计数据

module.exports = router;