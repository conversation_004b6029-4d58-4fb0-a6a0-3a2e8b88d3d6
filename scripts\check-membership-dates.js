#!/usr/bin/env node

/**
 * 检查会员日期数据
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const Membership = require('../src/database/models/Membership');
const logger = require('../src/utils/logger');

async function checkMembershipDates() {
  try {
    logger.info('检查会员日期数据...');
    
    const sql = `
      SELECT 
        u.username,
        m.start_date,
        m.end_date,
        m.created_at,
        mp.name as plan_name
      FROM users u
      JOIN memberships m ON u.id = m.user_id 
      JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.status = 'active'
      ORDER BY m.created_at DESC
    `;
    
    const results = await Membership.query(sql);
    logger.info('数据库中的会员日期数据:');
    results.forEach(row => {
      logger.info(`用户: ${row.username}`);
      logger.info(`  计划: ${row.plan_name}`);
      logger.info(`  开始日期: ${row.start_date}`);
      logger.info(`  结束日期: ${row.end_date}`);
      logger.info(`  创建时间: ${row.created_at}`);
      logger.info('---');
    });
    
  } catch (error) {
    logger.error('查询失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkMembershipDates()
    .then(() => {
      logger.info('检查完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('检查失败:', error);
      process.exit(1);
    });
}

module.exports = { checkMembershipDates };
