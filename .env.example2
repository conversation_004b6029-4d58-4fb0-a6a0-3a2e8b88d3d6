        # ==============================================================================
        #                      *** 应用程序环境变量 (Application Environment Variables) ***
        # 
        # 本文件是 .env 文件所需的模板。
        # 请将此文件复制为 .env，并根据您的环境填写相应的值。
        # ==============================================================================

        # ------------------------------------------------------------------------------
        # A. 核心应用设置 (Core Application Settings)
        # ------------------------------------------------------------------------------
        # 设置应用环境: 'development' (开发) 或 'production' (生产)。
        NODE_ENV=development

        # 应用程序运行的端口。
        PORT=3000

        # 如果启用SSL，则为HTTPS端口。
        HTTPS_PORT=3443

        # CORS允许的来源列表，以逗号分隔。
        ALLOWED_ORIGINS=http://localhost:8081,http://localhost:5173

        # 后端API的基础URL，用于生成webhook URL。
        API_BASE_URL=http://localhost:3000

        # 前端应用的基础URL，用于生成重定向URL。
        FRONTEND_URL=http://localhost:8081

        # 应用日志记录器的级别: 'info', 'debug', 'warn', 'error'。
        LOG_LEVEL=info


        # ------------------------------------------------------------------------------
        # B. 数据库与缓存 (Database & Cache - MySQL, Redis)
        # ------------------------------------------------------------------------------
        # MySQL数据库连接详情。
        DB_HOST=localhost
        DB_PORT=3306
        DB_USER=video_user
        DB_PASSWORD=secure_password
        DB_DATABASE=video_platform # 重要: 迁移脚本也会使用此数据库名。

        # Redis连接详情。
        REDIS_HOST=127.0.0.1
        REDIS_PORT=6379
        REDIS_PASSWORD=


        # ------------------------------------------------------------------------------
        # C. 安全设置 (Security - JWT, SSL, Rate Limiting, Admin)
        # ------------------------------------------------------------------------------
        # 用于签发JWT的密钥。请使用长的、随机的字符串。
        JWT_SECRET=your_super_secret_jwt_key
        JWT_REFRESH_SECRET=your_super_secret_refresh_key
        JWT_EXPIRES_IN=1d
        JWT_REFRESH_EXPIRES_IN=7d

        # 初始管理员账户凭据 (在数据库初始化时使用)。
        ADMIN_EMAIL=<EMAIL>
        ADMIN_USERNAME=admin
        ADMIN_PASSWORD=admin123456

        # Bcrypt密码哈希的加盐轮数。
        BCRYPT_ROUNDS=12

        # 启用HTTPS。设为 'true' 以尝试使用SSL证书。
        ENABLE_HTTPS=false

        # 您的SSL证书和私钥文件的路径。
        SSL_CERT_PATH=./certs/certificate.crt
        SSL_KEY_PATH=./certs/private.key

        # API的请求速率限制设置。
        RATE_LIMIT_WINDOW=15 # 时间窗口 (分钟)
        RATE_LIMIT_MAX=100   # 每个IP在时间窗口内的最大请求数


        # ------------------------------------------------------------------------------
        # D. 支付网关配置 (Payment Gateway Configurations)
        # ------------------------------------------------------------------------------

        # --- Creem.io 设置 ---
        CREEM_ENABLED=true
        CREEM_API_KEY=creem_test_1F37shEDMhcbSkNzoccldE
        CREEM_API_URL=https://test-api.creem.io
        CREEM_WEBHOOK_SECRET=whsec_7MIpdfbKSqEKKHhk1QwIqB

        # --- Epay (易支付) 设置 ---
        EPAY_ENABLED=false
        EPAY_API_URL=
        EPAY_PARTNER_ID=
        EPAY_PARTNER_KEY=

        # --- Alipay (支付宝) 设置 ---
        ALIPAY_ENABLED=false
        ALIPAY_APP_ID=
        ALIPAY_PRIVATE_KEY=
        ALIPAY_PUBLIC_KEY=
        ALIPAY_GATEWAY_URL=https://openapi.alipaydev.com/gateway.do # 开发/沙箱环境URL

        # --- WeChat Pay (微信支付) 设置 ---
        WECHAT_PAY_ENABLED=false
        WECHAT_APP_ID=
        WECHAT_MCH_ID=
        WECHAT_API_KEY=
        WECHAT_CERT_PATH=
        WECHAT_KEY_PATH=


        # ------------------------------------------------------------------------------
        # E. 通用支付模块设置 (General Payment Module Settings)
        # ------------------------------------------------------------------------------
        # 默认支付货币 (例如 'CNY', 'USD')。
        PAYMENT_CURRENCY=USD

        # 允许的最小和最大支付金额。
        MIN_PAYMENT_AMOUNT=0.01
        MAX_PAYMENT_AMOUNT=50000

        # 订单过期时间 (分钟)。
        ORDER_EXPIRE_MINUTES=30

        # 支付超时时间 (秒)。
        PAYMENT_TIMEOUT_SECONDS=300

        # 启用的支付方式列表，以逗号分隔 (例如 'epay,alipay,creem')。
        # 必须与 paymentConfig.js 中的键名匹配。
        SUPPORTED_PAYMENT_METHODS=creem

        # 未指定时使用的默认支付方式。
        DEFAULT_PAYMENT_METHOD=creem

        # 是否启用详细的支付日志。
        ENABLE_PAYMENT_LOG=true