import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useVideoDetail } from '../hooks/queries/useVideos';
import { AccessDeniedPrompt } from '@/components/ui/AccessDeniedPrompt';
import MembershipDialog from '@/components/dialogs/MembershipDialog';
import PurchaseDialog from '@/components/dialogs/PurchaseDialog';
import VideoPlayerWithFeatures, { Video } from '@/components/video/VideoPlayerWithFeatures';

const VideoDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  const { data: video, isLoading: loading, error: videoError } = useVideoDetail(id!) as { data: Video | undefined; isLoading: boolean; error: any };

  if (loading || authLoading) {
    return <div className="text-center p-10">正在加载...</div>;
  }

  if (videoError) {
    const errorCode = (videoError as any).response?.data?.code;
    const errorDetails = (videoError as any).response?.data?.details;

    if (errorCode === 'NEEDS_PURCHASE') {
      return (
        <div className="container mx-auto px-4 py-8">
          <PurchaseDialog
            open={true}
            onOpenChange={() => navigate('/')}
            price={(videoError as any).response?.data?.price}
            videoId={id!}
            onPurchaseSuccess={() => {
              window.location.reload();
            }}
          />
        </div>
      );
    }

    if (errorCode === 'ACCESS_DENIED' || errorCode === 'NEEDS_MEMBERSHIP') {
        return (
          <div className="container mx-auto px-4 py-8">
            <AccessDeniedPrompt
              isLoggedIn={isAuthenticated}
              onNavigateToMembership={() => navigate('/membership')}
            />
            <MembershipDialog
              open={false} // This can be managed internally by AccessDeniedPrompt or a state
              onOpenChange={() => {}}
            />
          </div>
        );
      }

    return <div className="text-center p-10">加载视频失败，请稍后再试。</div>;
  }

  if (!video) {
    return <div className="text-center p-10">未找到视频。</div>;
  }

  return <VideoPlayerWithFeatures video={video} />;
};

export default VideoDetail; 