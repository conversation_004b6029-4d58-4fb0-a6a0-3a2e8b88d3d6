import React, { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useMembership } from '@/hooks/useMembership';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Crown, CheckCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const MembershipPage = () => {
  const [searchParams] = useSearchParams();
  const { membershipInfo, membershipLevel, isMember, isVip, updateMembershipAfterPayment } = useMembership();
  const { toast } = useToast();
  const { t } = useTranslation();

  // 处理支付成功返回
  useEffect(() => {
    const paymentStatus = searchParams.get('status');
    const paymentSuccess = searchParams.get('success');

    if (paymentStatus === 'success' || paymentSuccess === 'true') {
      // 支付成功，刷新会员状态
      updateMembershipAfterPayment().then(() => {
        toast({
          title: "支付成功！",
          description: "您的会员状态已更新，感谢您的订阅！",
          className: "bg-green-500 text-white",
        });
      });
    }
  }, [searchParams, updateMembershipAfterPayment, toast]);

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8">
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-6 w-6 text-yellow-500" />
              {t('membership.center')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-lg font-medium">{t('membership.currentStatus')}</span>
                <Badge variant={isMember ? "default" : "outline"}>
                  {isMember ? t('membership.memberUser') : t('membership.freeUser')}
                </Badge>
              </div>

              {membershipInfo && (
                <>
                  <div className="flex items-center justify-between">
                    <span>{t('membership.plan')}</span>
                    <span className="font-medium">{membershipInfo.plan_name}</span>
                  </div>

                  {membershipInfo.end_date && (
                    <div className="flex items-center justify-between">
                      <span>{t('membership.expiryDate')}</span>
                      <span className="font-medium">
                        {new Date(membershipInfo.end_date).toLocaleDateString()}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <span>{t('membership.autoRenew')}</span>
                    <span className="font-medium">
                      {membershipInfo.auto_renew ? t('membership.enabled') : t('membership.disabled')}
                    </span>
                  </div>
                </>
              )}

              {!isMember && (
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-800">
                    {t('membership.upgradePrompt')}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MembershipPage; 