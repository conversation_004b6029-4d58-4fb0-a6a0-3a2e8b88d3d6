#!/usr/bin/env node

/**
 * HTTPS配置验证脚本
 * 验证SSL证书配置和HTTPS功能
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

console.log('🔒 验证HTTPS配置\n');

/**
 * 检查环境变量配置
 */
function checkEnvironmentConfig() {
  console.log('📋 检查环境变量配置:');
  console.log('='.repeat(50));
  
  const envVars = [
    'ENABLE_HTTPS',
    'HTTPS_PORT',
    'SSL_CERT_PATH',
    'SSL_KEY_PATH',
    'FORCE_HTTPS',
    'AUTO_REDIRECT_HTTPS'
  ];
  
  let allConfigured = true;
  
  envVars.forEach(varName => {
    const value = process.env[varName];
    if (value !== undefined) {
      console.log(`✅ ${varName}: ${value}`);
    } else {
      console.log(`⚠️ ${varName}: 未设置 (将使用默认值)`);
    }
  });
  
  return allConfigured;
}

/**
 * 检查SSL证书文件
 */
function checkSSLCertificates() {
  console.log('\n🔐 检查SSL证书文件:');
  console.log('='.repeat(50));
  
  const certPath = process.env.SSL_CERT_PATH || './certs/certificate.crt';
  const keyPath = process.env.SSL_KEY_PATH || './certs/private.key';
  
  const certExists = fs.existsSync(certPath);
  const keyExists = fs.existsSync(keyPath);
  
  console.log(`证书文件 (${certPath}): ${certExists ? '✅ 存在' : '❌ 不存在'}`);
  console.log(`私钥文件 (${keyPath}): ${keyExists ? '✅ 存在' : '❌ 不存在'}`);
  
  if (certExists && keyExists) {
    try {
      const cert = fs.readFileSync(certPath, 'utf8');
      const key = fs.readFileSync(keyPath, 'utf8');
      
      const certValid = cert.includes('BEGIN CERTIFICATE');
      const keyValid = key.includes('BEGIN PRIVATE KEY') || key.includes('BEGIN RSA PRIVATE KEY');
      
      console.log(`证书格式: ${certValid ? '✅ 有效' : '❌ 无效'}`);
      console.log(`私钥格式: ${keyValid ? '✅ 有效' : '❌ 无效'}`);
      
      if (certValid && keyValid) {
        console.log('✅ SSL证书文件检查通过');
        return true;
      }
    } catch (error) {
      console.log(`❌ 读取证书文件失败: ${error.message}`);
    }
  }
  
  console.log('❌ SSL证书文件检查失败');
  return false;
}

/**
 * 测试SSL配置
 */
async function testSSLConfiguration() {
  console.log('\n🧪 测试SSL配置:');
  console.log('='.repeat(50));
  
  try {
    const { autoConfigureSSL } = require('../src/config/ssl');
    const sslOptions = await autoConfigureSSL();
    
    if (sslOptions) {
      console.log('✅ SSL自动配置成功');
      
      // 尝试创建HTTPS服务器
      const testServer = https.createServer(sslOptions);
      
      return new Promise((resolve) => {
        testServer.listen(0, () => {
          const port = testServer.address().port;
          console.log(`✅ HTTPS测试服务器启动成功 (端口: ${port})`);
          testServer.close(() => {
            console.log('✅ HTTPS测试服务器关闭成功');
            resolve(true);
          });
        });
        
        testServer.on('error', (error) => {
          console.log(`❌ HTTPS测试服务器启动失败: ${error.message}`);
          resolve(false);
        });
      });
    } else {
      console.log('❌ SSL自动配置失败');
      return false;
    }
  } catch (error) {
    console.log(`❌ SSL配置测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 检查HTTPS中间件
 */
function checkHTTPSMiddleware() {
  console.log('\n🔧 检查HTTPS中间件:');
  console.log('='.repeat(50));
  
  try {
    const middlewarePath = path.join(__dirname, '..', 'src/middleware/httpsRedirect.js');
    
    if (fs.existsSync(middlewarePath)) {
      console.log('✅ HTTPS中间件文件存在');
      
      const middleware = require('../src/middleware/httpsRedirect');
      const requiredFunctions = ['httpsRedirect', 'securityHeaders', 'httpsStatus', 'httpsDebug'];
      
      let allFunctionsExist = true;
      requiredFunctions.forEach(funcName => {
        if (typeof middleware[funcName] === 'function') {
          console.log(`✅ ${funcName} 函数存在`);
        } else {
          console.log(`❌ ${funcName} 函数缺失`);
          allFunctionsExist = false;
        }
      });
      
      return allFunctionsExist;
    } else {
      console.log('❌ HTTPS中间件文件不存在');
      return false;
    }
  } catch (error) {
    console.log(`❌ 检查HTTPS中间件失败: ${error.message}`);
    return false;
  }
}

/**
 * 生成自签名证书（仅用于测试）
 */
function generateTestCertificate() {
  console.log('\n🔨 生成测试证书:');
  console.log('='.repeat(50));
  
  if (process.env.NODE_ENV === 'production') {
    console.log('⚠️ 生产环境不应生成测试证书');
    return false;
  }
  
  try {
    const { generateSelfSignedCert } = require('../src/config/ssl');
    const success = generateSelfSignedCert();
    
    if (success) {
      console.log('✅ 测试证书生成成功');
      console.log('⚠️ 注意: 这是自签名证书，仅用于开发测试');
      return true;
    } else {
      console.log('❌ 测试证书生成失败');
      return false;
    }
  } catch (error) {
    console.log(`❌ 生成测试证书失败: ${error.message}`);
    return false;
  }
}

/**
 * 检查文档中的HTTPS URL
 */
function checkDocumentationURLs() {
  console.log('\n📚 检查文档中的URL:');
  console.log('='.repeat(50));
  
  const docFiles = [
    'docs/api.html',
    'docs/API.md',
    'README.md'
  ];
  
  let httpsSupported = true;
  
  docFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const hasHttps = content.includes('https://');
      const hasHttp = content.includes('http://localhost') || content.includes('http://127.0.0.1');
      
      console.log(`${filePath}:`);
      console.log(`  HTTPS URL: ${hasHttps ? '✅ 包含' : '❌ 不包含'}`);
      console.log(`  HTTP URL: ${hasHttp ? '⚠️ 包含' : '✅ 不包含'}`);
      
      if (!hasHttps) {
        httpsSupported = false;
      }
    } else {
      console.log(`${filePath}: ❌ 文件不存在`);
    }
  });
  
  return httpsSupported;
}

/**
 * 主验证函数
 */
async function main() {
  console.log('🚀 开始HTTPS配置验证');
  console.log('='.repeat(50));
  
  const envConfigOK = checkEnvironmentConfig();
  const certFilesOK = checkSSLCertificates();
  const middlewareOK = checkHTTPSMiddleware();
  const docsOK = checkDocumentationURLs();
  
  let sslTestOK = false;
  if (certFilesOK) {
    sslTestOK = await testSSLConfiguration();
  } else {
    console.log('\n⚠️ 证书文件不存在，是否生成测试证书？');
    if (process.env.NODE_ENV !== 'production') {
      const testCertGenerated = generateTestCertificate();
      if (testCertGenerated) {
        sslTestOK = await testSSLConfiguration();
      }
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 HTTPS配置验证结果:');
  console.log('='.repeat(50));
  
  console.log(`环境变量配置: ${envConfigOK ? '✅ 正常' : '⚠️ 部分缺失'}`);
  console.log(`SSL证书文件: ${certFilesOK ? '✅ 正常' : '❌ 缺失'}`);
  console.log(`HTTPS中间件: ${middlewareOK ? '✅ 正常' : '❌ 异常'}`);
  console.log(`SSL配置测试: ${sslTestOK ? '✅ 通过' : '❌ 失败'}`);
  console.log(`文档URL检查: ${docsOK ? '✅ 支持HTTPS' : '⚠️ 需要更新'}`);
  
  const overallSuccess = middlewareOK && (certFilesOK || sslTestOK);
  
  if (overallSuccess) {
    console.log('\n🎉 HTTPS配置验证通过！');
    console.log('\n📋 使用说明:');
    console.log('1. 设置环境变量 ENABLE_HTTPS=true 启用HTTPS');
    console.log('2. 配置SSL证书路径 SSL_CERT_PATH 和 SSL_KEY_PATH');
    console.log('3. 可选设置 FORCE_HTTPS=true 强制使用HTTPS');
    console.log('4. 可选设置 AUTO_REDIRECT_HTTPS=true 自动重定向到HTTPS');
    
    console.log('\n🔗 访问地址:');
    console.log(`- HTTP: http://localhost:${process.env.PORT || 3000}`);
    console.log(`- HTTPS: https://localhost:${process.env.HTTPS_PORT || 3443}`);
    
  } else {
    console.log('\n⚠️ HTTPS配置验证发现问题，请检查上述错误');
  }
  
  return overallSuccess;
}

// 运行验证
if (require.main === module) {
  main().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('验证过程出错:', error);
    process.exit(1);
  });
}

module.exports = { 
  main, 
  checkEnvironmentConfig, 
  checkSSLCertificates, 
  testSSLConfiguration,
  generateTestCertificate 
};
