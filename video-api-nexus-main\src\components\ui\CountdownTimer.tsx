import React, { useState, useEffect } from 'react';

interface CountdownTimerProps {
  targetDate: string;
  className?: string;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ targetDate, className }) => {
  const calculateTimeLeft = () => {
    const difference = +new Date(targetDate) - +new Date();
    let timeLeft = {};

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    }

    return timeLeft;
  };

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  useEffect(() => {
    const timer = setTimeout(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearTimeout(timer);
  });

  const timerComponents: JSX.Element[] = [];

  Object.keys(timeLeft).forEach((interval) => {
    if (!timeLeft[interval as keyof typeof timeLeft]) {
      return;
    }

    timerComponents.push(
      <span key={interval}>
        {timeLeft[interval as keyof typeof timeLeft]}{interval.slice(0,1)}{' '}
      </span>
    );
  });
  
  const formatTime = (time: number) => time.toString().padStart(2, '0');

  const { days, hours, minutes, seconds } = timeLeft as { [key: string]: number };

  return (
    <div className={className}>
      {timerComponents.length ? (
        <div className="text-sm text-red-600 font-medium">
          <span>优惠剩余: </span>
          {days > 0 && <span>{days}天 </span>}
          <span>{formatTime(hours || 0)}:</span>
          <span>{formatTime(minutes || 0)}:</span>
          <span>{formatTime(seconds || 0)}</span>
        </div>
      ) : (
        <span className="text-sm text-gray-500">优惠已结束</span>
      )}
    </div>
  );
};

export default CountdownTimer; 