const axios = require('axios');
const { AppError } = require('../../../middleware/errorHandler');
const logger = require('../../../utils/logger');
const { getPaymentConfig } = require('../../../config/payment');

/**
 * Creem.io 服务
 * 负责与Creem.io API的所有交互
 */
class CreemIoService {
  constructor() {
    const config = getPaymentConfig().creem;
    if (!config || !config.enabled) {
      // 服务未启用时，可以打印一个日志，但不应抛出错误，
      // 因为系统可能在不使用它的情况下正常运行。
      logger.info('Creem.io服务未启用或未配置。');
      this.enabled = false;
      return;
    }
    this.enabled = true;
    this.apiKey = config.apiKey;
    this.apiUrl = config.apiUrl;
    this.apiClient = axios.create({
      baseURL: this.apiUrl,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 15000, // 15秒超时
    });

    // 请求拦截器
    this.apiClient.interceptors.request.use(request => {
      const currentApiKey = getPaymentConfig().creem.apiKey;
      if (!currentApiKey) {
        logger.error('[CreemIO] API Key is missing. Request cannot be authenticated.');
        return Promise.reject(new AppError('Creem.io API Key未配置', 500, 'CONFIGURATION_ERROR'));
      }
      
      // 诊断日志：打印部分API密钥以供验证
      const maskedKey = `${currentApiKey.substring(0, 12)}...${currentApiKey.substring(currentApiKey.length - 4)}`;
      logger.info(`[CreemIO Auth] Using API Key: ${maskedKey}`);

      request.headers['x-api-key'] = currentApiKey;
      logger.info(`[CreemIO Request] -> ${request.method.toUpperCase()} ${request.url}`, { data: request.data });
      return request;
    });

    // 响应拦截器
    this.apiClient.interceptors.response.use(
      response => {
        logger.info(`[CreemIO Response] <- ${response.status} ${response.config.url}`, { data: response.data });
        return response.data; // 直接返回响应的data部分
      },
      error => {
        const status = error.response ? error.response.status : 'NETWORK_ERROR';
        const message = error.response ? error.response.data : error.message;
        logger.error(`[CreemIO Error] <- ${status} ${error.config.url}`, { message });
        
        // 将其包装在AppError中，以便全局错误处理器可以处理它
        throw new AppError(message.error || '与Creem.io通信时发生错误', status, 'CREEM_API_ERROR', { details: message });
      }
    );
  }

  /**
   * 在Creem.io上创建一个新产品
   * @param {object} productData - 产品数据
   * @param {string} productData.name - 产品名称
   * @param {string} productData.description - 产品描述
   * @param {number} productData.price - 价格 (以最小货币单位，如美分)
   * @param {string} productData.currency - 货币 (例如 'usd')
   * @returns {Promise<object>} 在Creem.io上创建的产品对象
   */
  async createProduct(productData) {
    if (!this.enabled) {
      throw new AppError('Creem.io服务未启用', 503, 'SERVICE_DISABLED');
    }
    
    const priceInCents = Math.round(parseFloat(productData.price) * 100);

    // 根据API文档，价格不能低于100美分（$1.00）
    if (priceInCents < 100) {
        throw new AppError('价格不能低于1.00', 400, 'VALIDATION_ERROR');
    }

    // 确保billing_type是有效的枚举值
    let billingType = 'recurring'; // 默认值
    if (productData.billing_type === 'subscription') {
      billingType = 'recurring';
    } else if (productData.billing_type === 'one_time') {
      billingType = 'onetime';
    }

    // 直接使用前端传递的、经过验证的tax_category，但转换为小写
    const taxCategory = productData.tax_category ? productData.tax_category.toLowerCase() : 'saas';
    logger.info(`[CreemIO] 使用税类: ${taxCategory}`);


    const payload = {
      name: productData.name,
      description: productData.description,
      price: priceInCents,
      currency: productData.currency.toUpperCase(),
      billing_type: billingType,
      tax_mode: 'inclusive',
      tax_category: taxCategory,
    };

    // 只有当default_success_url有值且不为空字符串时才添加到payload
    if (productData.default_success_url && productData.default_success_url.trim() !== '') {
      payload.default_success_url = productData.default_success_url;
    }

    if (payload.billing_type === 'recurring' && productData.billing_period) {
      payload.billing_period = productData.billing_period;
    }

    return this.apiClient.post('/products', payload);
  }

  /**
   * 在Creem.io上更新一个产品
   * @param {string} productId - Creem.io上的产品ID
   * @param {object} updateData - 要更新的数据
   * @returns {Promise<object>} 更新后的产品对象
   */
  async updateProduct(productId, updateData) {
     if (!this.enabled) {
      throw new AppError('Creem.io服务未启用', 503, 'SERVICE_DISABLED');
    }

    if (updateData.price) {
        updateData.price = Math.round(parseFloat(updateData.price) * 100);
    }
    if(updateData.currency) {
        updateData.currency = updateData.currency.toLowerCase();
    }

    return this.apiClient.put(`/products/${productId}`, updateData);
  }

  /**
   * 从Creem.io删除一个产品
   * @param {string} productId - Creem.io上的产品ID
   * @returns {Promise<object>}
   */
  async deleteProduct(productId) {
     if (!this.enabled) {
      throw new AppError('Creem.io服务未启用', 503, 'SERVICE_DISABLED');
    }
    return this.apiClient.delete(`/products/${productId}`);
  }

  /**
   * 获取所有产品
   * @returns {Promise<object>}
   */
  async listProducts() {
     if (!this.enabled) {
      throw new AppError('Creem.io服务未启用', 503, 'SERVICE_DISABLED');
    }
    return this.apiClient.get('/products/search');
  }
}

module.exports = new CreemIoService(); 