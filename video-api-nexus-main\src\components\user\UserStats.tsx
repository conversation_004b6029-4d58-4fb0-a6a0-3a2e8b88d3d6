import React, { useEffect, useState } from 'react';
import { followApi, FollowStats } from '../../services/followApi';
import { Users, Video, Heart, Eye } from 'lucide-react';
import { Card, CardContent } from '../ui/card';

interface UserStatsProps {
  userId: number;
  className?: string;
  showDetailedStats?: boolean;
}

interface StatItemProps {
  icon: React.ReactNode;
  label: string;
  value: number;
  className?: string;
}

const StatItem: React.FC<StatItemProps> = ({ icon, label, value, className = '' }) => (
  <div className={`flex flex-col items-center space-y-1 ${className}`}>
    <div className="flex items-center space-x-1 text-gray-600">
      {icon}
      <span className="text-sm font-medium">{label}</span>
    </div>
    <div className="text-2xl font-bold text-gray-900">
      {value.toLocaleString()}
    </div>
  </div>
);

const StatItemCompact: React.FC<StatItemProps> = ({ label, value, className = '' }) => (
  <div className={`text-center ${className}`}>
    <div className="text-xl font-bold text-gray-900">
      {value.toLocaleString()}
    </div>
    <div className="text-sm text-gray-600">{label}</div>
  </div>
);

export const UserStats: React.FC<UserStatsProps> = ({ 
  userId, 
  className = '',
  showDetailedStats = false 
}) => {
  const [stats, setStats] = useState<FollowStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await followApi.getFollowStats(userId);
        setStats(response.data);
      } catch (error: any) {
        console.error('获取用户统计失败:', error);
        setError('获取统计数据失败');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchStats();
    }
  }, [userId]);

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="flex justify-center space-x-8">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="text-center">
              <div className="h-6 bg-gray-200 rounded w-12 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-16"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className={`text-center text-gray-500 ${className}`}>
        <p className="text-sm">{error || '暂无统计数据'}</p>
      </div>
    );
  }

  if (showDetailedStats) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <StatItem
              icon={<Video className="w-4 h-4" />}
              label="作品"
              value={stats.video_count}
            />
            <StatItem
              icon={<Users className="w-4 h-4" />}
              label="粉丝"
              value={stats.follower_count}
            />
            <StatItem
              icon={<Users className="w-4 h-4" />}
              label="关注"
              value={stats.following_count}
            />
            <StatItem
              icon={<Heart className="w-4 h-4" />}
              label="获赞"
              value={stats.total_likes}
            />
          </div>
          
          {stats.total_views > 0 && (
            <div className="mt-4 pt-4 border-t">
              <StatItem
                icon={<Eye className="w-4 h-4" />}
                label="总播放量"
                value={stats.total_views}
                className="justify-center"
              />
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // 紧凑模式
  return (
    <div className={`flex justify-center space-x-8 ${className}`}>
      <StatItemCompact
        label="作品"
        value={stats.video_count}
      />
      <StatItemCompact
        label="粉丝"
        value={stats.follower_count}
      />
      <StatItemCompact
        label="关注"
        value={stats.following_count}
      />
      <StatItemCompact
        label="获赞"
        value={stats.total_likes}
      />
    </div>
  );
};

// 简化版统计组件，只显示核心数据
export const UserStatsSimple: React.FC<{ userId: number; className?: string }> = ({ 
  userId, 
  className = '' 
}) => {
  const [stats, setStats] = useState<Pick<FollowStats, 'follower_count' | 'following_count' | 'video_count'> | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await followApi.getFollowStats(userId);
        setStats({
          follower_count: response.data.follower_count,
          following_count: response.data.following_count,
          video_count: response.data.video_count
        });
      } catch (error) {
        console.error('获取用户统计失败:', error);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchStats();
    }
  }, [userId]);

  if (loading) {
    return (
      <div className={`flex space-x-4 ${className}`}>
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse text-center">
            <div className="h-5 bg-gray-200 rounded w-8 mb-1"></div>
            <div className="h-3 bg-gray-200 rounded w-10"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <div className={`flex space-x-6 text-sm ${className}`}>
      <div className="text-center">
        <div className="font-semibold text-gray-900">{stats.video_count}</div>
        <div className="text-gray-600">作品</div>
      </div>
      <div className="text-center">
        <div className="font-semibold text-gray-900">{stats.follower_count}</div>
        <div className="text-gray-600">粉丝</div>
      </div>
      <div className="text-center">
        <div className="font-semibold text-gray-900">{stats.following_count}</div>
        <div className="text-gray-600">关注</div>
      </div>
    </div>
  );
};