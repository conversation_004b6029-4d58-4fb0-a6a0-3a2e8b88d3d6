import { useQuery, useQueryClient } from '@tanstack/react-query';
import { earningsApi } from '@/services/earningsApi';
import { getWithdrawalHistory } from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Terminal, Banknote } from 'lucide-react';
import { format } from 'date-fns';
import { keepPreviousData } from '@tanstack/react-query';
import WithdrawalDialog from '@/components/dialogs/WithdrawalDialog';
import { useTranslation } from 'react-i18next';


// --- TypeScript Interfaces ---

interface EarningsSummaryData {
  totalEarnings: number;
  monthEarnings: number;
  balance: number;
}

interface EarningDetail {
  id: number;
  total_amount: number;
  creator_earning: number;
  created_at: string;
  video_title: string | null;
}

interface BalanceLog {
  id: number;
  amount: number;
  balance_after: number;
  type: string;
  description: string;
  created_at: string;
}

interface PaginatedResponse<T> {
  data: T[];
  totalPages: number;
  currentPage: number;
  totalItems: number;
}

interface Withdrawal {
  id: number;
  amount: number;
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed';
  wallet_type: string;
  wallet_address: string;
  requested_at: string;
  processed_at: string | null;
}


// --- Helper Functions & Components ---

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
};

const formatDate = (dateString) => {
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm:ss');
};

const WITHDRAWAL_STATUS_MAP = {
  pending: { label: 'earnings.withdrawalStatus.pending', variant: 'secondary' },
  approved: { label: 'earnings.withdrawalStatus.approved', variant: 'primary' },
  completed: { label: 'earnings.withdrawalStatus.completed', variant: 'success' },
  rejected: { label: 'earnings.withdrawalStatus.rejected', variant: 'destructive' },
  failed: { label: 'earnings.withdrawalStatus.failed', variant: 'destructive' },
};

const getStatusBadge = (status, t) => {
  const statusInfo = WITHDRAWAL_STATUS_MAP[status];
  if (!statusInfo) return <Badge variant="outline">{t('earnings.withdrawalStatus.unknown')}</Badge>;
  return <Badge variant={statusInfo.variant as any}>{t(statusInfo.label)}</Badge>;
};

const LoadingSpinner = () => (
    <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
    </div>
);

const ErrorDisplay = ({ message, t }) => (
    <Alert variant="destructive" className="my-4">
        <Terminal className="h-4 w-4" />
        <AlertTitle>{t('earnings.loadError')}</AlertTitle>
        <AlertDescription>{message || t('earnings.loadErrorDesc')}</AlertDescription>
    </Alert>
);

// --- Child Components ---

const EarningsSummary = () => {
  const { t } = useTranslation();
  const { data: response, isLoading, error } = useQuery<{ data: { data: EarningsSummaryData } }>({
    queryKey: ['earningsSummary'],
    queryFn: earningsApi.getSummary,
  });

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorDisplay message={error.message} t={t} />;

  const stats = response?.data?.data;

  return (
    <div className="grid gap-4 md:grid-cols-3">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('earnings.totalEarnings')}</CardTitle>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" className="h-4 w-4 text-muted-foreground"><path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" /></svg>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(stats?.totalEarnings || 0)}</div>
          <p className="text-xs text-muted-foreground">{t('earnings.totalEarningsDesc')}</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('earnings.monthEarnings')}</CardTitle>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" className="h-4 w-4 text-muted-foreground"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" /><circle cx="9" cy="7" r="4" /><path d="M22 21v-2a4 4 0 0 0-3-3.87" /><path d="M16 3.13a4 4 0 0 1 0 7.75" /></svg>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(stats?.monthEarnings || 0)}</div>
          <p className="text-xs text-muted-foreground">{t('earnings.monthEarningsDesc')}</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('earnings.availableBalance')}</CardTitle>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" className="h-4 w-4 text-muted-foreground"><rect width="20" height="14" x="2" y="5" rx="2" /><line x1="2" x2="22" y1="10" y2="10" /></svg>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(stats?.balance || 0)}</div>
          <p className="text-xs text-muted-foreground">{t('earnings.availableBalanceDesc')}</p>
        </CardContent>
      </Card>
    </div>
  );
};

const EarningsTable = () => {
    const { t } = useTranslation();
    const [page, setPage] = useState(1);
    const { data: response, isLoading, error } = useQuery<PaginatedResponse<EarningDetail>>({
        queryKey: ['earningsDetails', page],
        queryFn: () => earningsApi.getDetails(page, 10).then(res => res.data),
        placeholderData: keepPreviousData,
    });

    if (isLoading) return <LoadingSpinner />;
    if (error) return <ErrorDisplay message={error.message} t={t} />;

    const earnings = response?.data || [];
    const totalPages = response?.totalPages || 1;

    return (
        <Card>
            <CardHeader><CardTitle>{t('earnings.tabs.earnings')}</CardTitle></CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>{t('earnings.earningsTable.videoTitle')}</TableHead>
                            <TableHead className="text-right">{t('earnings.earningsTable.orderAmount')}</TableHead>
                            <TableHead className="text-right">{t('earnings.earningsTable.myEarnings')}</TableHead>
                            <TableHead>{t('earnings.earningsTable.earningTime')}</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {earnings.length > 0 ? earnings.map((earning) => (
                            <TableRow key={earning.id}>
                                <TableCell className="font-medium">{earning.video_title || t('earnings.earningsTable.videoDeleted')}</TableCell>
                                <TableCell className="text-right">{formatCurrency(earning.total_amount)}</TableCell>
                                <TableCell className="text-right text-green-500 font-semibold">+{formatCurrency(earning.creator_earning)}</TableCell>
                                <TableCell>{formatDate(earning.created_at)}</TableCell>
                            </TableRow>
                        )) : <TableRow><TableCell colSpan={4} className="text-center">{t('common.noData')}</TableCell></TableRow>}
                    </TableBody>
                </Table>
                <CustomPagination currentPage={page} totalPages={totalPages} onPageChange={setPage} />
            </CardContent>
        </Card>
    );
};

const BalanceHistoryTable = () => {
    const { t } = useTranslation();
    const [page, setPage] = useState(1);
    const { data: response, isLoading, error } = useQuery<PaginatedResponse<BalanceLog>>({
        queryKey: ['balanceHistory', page],
        queryFn: () => earningsApi.getBalanceHistory(page, 10).then(res => res.data),
        placeholderData: keepPreviousData,
    });

    if (isLoading) return <LoadingSpinner />;
    if (error) return <ErrorDisplay message={error.message} t={t} />;

    const history = response?.data || [];
    const totalPages = response?.totalPages || 1;

    return (
        <Card>
            <CardHeader><CardTitle>{t('earnings.tabs.balance')}</CardTitle></CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>{t('earnings.balanceTable.type')}</TableHead>
                            <TableHead>描述</TableHead>
                            <TableHead className="text-right">{t('earnings.balanceTable.amount')}</TableHead>
                            <TableHead className="text-right">{t('earnings.balanceTable.balance')}</TableHead>
                            <TableHead>{t('earnings.balanceTable.time')}</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {history.length > 0 ? history.map((log) => (
                            <TableRow key={log.id}>
                                <TableCell>{log.type}</TableCell>
                                <TableCell className="font-medium">{log.description}</TableCell>
                                <TableCell className={`text-right font-semibold ${log.amount > 0 ? 'text-green-500' : 'text-red-500'}`}>{log.amount > 0 ? '+' : ''}{formatCurrency(log.amount)}</TableCell>
                                <TableCell className="text-right">{formatCurrency(log.balance_after)}</TableCell>
                                <TableCell>{formatDate(log.created_at)}</TableCell>
                            </TableRow>
                        )) : <TableRow><TableCell colSpan={5} className="text-center">暂无余额变动记录</TableCell></TableRow>}
                    </TableBody>
                </Table>
                <CustomPagination currentPage={page} totalPages={totalPages} onPageChange={setPage} />
            </CardContent>
        </Card>
    );
};

const WithdrawalsTable = () => {
    const { t } = useTranslation();
    const [page, setPage] = useState(1);
    const { data: response, isLoading, error } = useQuery<PaginatedResponse<Withdrawal>>({
        queryKey: ['withdrawalHistory', page],
        queryFn: () => getWithdrawalHistory({ page, limit: 10 }).then(res => res.data),
        placeholderData: keepPreviousData,
    });

    if (isLoading) return <LoadingSpinner />;
    if (error) return <ErrorDisplay message={error.message} t={t} />;

    const withdrawals = response?.data || [];
    const totalPages = response?.totalPages || 1;

    return (
        <Card>
            <CardHeader><CardTitle>{t('earnings.tabs.withdrawals')}</CardTitle></CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>{t('earnings.withdrawalTable.applyTime')}</TableHead>
                            <TableHead>{t('earnings.withdrawalTable.amount')}</TableHead>
                            <TableHead>{t('earnings.withdrawalTable.status')}</TableHead>
                            <TableHead>{t('earnings.withdrawalTable.wallet')}</TableHead>
                            <TableHead>{t('earnings.withdrawalTable.processTime')}</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {withdrawals.length > 0 ? withdrawals.map((w) => (
                            <TableRow key={w.id}>
                                <TableCell>{formatDate(w.requested_at)}</TableCell>
                                <TableCell className="font-medium">{formatCurrency(w.amount)}</TableCell>
                                <TableCell>{getStatusBadge(w.status, t)}</TableCell>
                                <TableCell>{w.wallet_address}</TableCell>
                                <TableCell>{w.processed_at ? formatDate(w.processed_at) : '-'}</TableCell>
                            </TableRow>
                        )) : <TableRow><TableCell colSpan={5} className="text-center">{t('earnings.withdrawalTable.noRecords')}</TableCell></TableRow>}
                    </TableBody>
                </Table>
                <CustomPagination currentPage={page} totalPages={totalPages} onPageChange={setPage} />
            </CardContent>
        </Card>
    );
};

const CustomPagination = ({ currentPage, totalPages, onPageChange }) => {
    return (
        <Pagination className="mt-4">
            <PaginationContent>
                <PaginationItem>
                    <PaginationPrevious href="#" onClick={(e) => { e.preventDefault(); if (currentPage > 1) onPageChange(currentPage - 1); }} />
                </PaginationItem>
                {[...Array(totalPages).keys()].map(num => (
                    <PaginationItem key={num + 1}>
                        <PaginationLink href="#" isActive={currentPage === num + 1} onClick={(e) => { e.preventDefault(); onPageChange(num + 1); }}>
                            {num + 1}
                        </PaginationLink>
                    </PaginationItem>
                ))}
                <PaginationItem>
                    <PaginationNext href="#" onClick={(e) => { e.preventDefault(); if (currentPage < totalPages) onPageChange(currentPage + 1); }} />
                </PaginationItem>
            </PaginationContent>
        </Pagination>
    );
};


// --- Main Page Component ---

export default function MyEarningsPage() {
  const { t } = useTranslation();
  const [isWithdrawalDialogOpen, setIsWithdrawalDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="flex items-center justify-between">
      <h1 className="text-3xl font-bold tracking-tight">{t('earnings.title')}</h1>
        <Button onClick={() => setIsWithdrawalDialogOpen(true)}>
          <Banknote className="mr-2 h-4 w-4" />
          {t('earnings.withdraw')}
        </Button>
      </div>
      
      <EarningsSummary />

      <Tabs defaultValue="earnings" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="earnings">{t('earnings.tabs.earnings')}</TabsTrigger>
          <TabsTrigger value="balance">{t('earnings.tabs.balance')}</TabsTrigger>
          <TabsTrigger value="withdrawals">{t('earnings.tabs.withdrawals')}</TabsTrigger>
        </TabsList>
        <TabsContent value="earnings">
          <EarningsTable />
        </TabsContent>
        <TabsContent value="balance">
          <BalanceHistoryTable />
        </TabsContent>
        <TabsContent value="withdrawals">
          <WithdrawalsTable />
        </TabsContent>
      </Tabs>

      <WithdrawalDialog
        open={isWithdrawalDialogOpen}
        onOpenChange={setIsWithdrawalDialogOpen}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: ['earningsSummary'] });
          queryClient.invalidateQueries({ queryKey: ['withdrawalHistory'] });
        }}
      />
    </div>
  );
} 