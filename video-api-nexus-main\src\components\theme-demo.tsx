import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Heart, 
  Star, 
  Play, 
  Download, 
  Share, 
  Bookmark,
  Zap,
  Waves,
  TreePine,
  Sunset,
  Rainbow,
  Palette
} from "lucide-react"
import { useTheme } from "./theme-provider"

const themeIcons = {
  neon: Zap,
  ocean: Waves,
  forest: TreePine,
  sunset: Sunset,
  rainbow: Rainbow,
  light: Palette,
  dark: Palette,
  system: Palette
}

export function ThemeDemo() {
  const { theme } = useTheme()
  const ThemeIcon = themeIcons[theme as keyof typeof themeIcons] || Palette

  return (
    <div className="space-y-6 p-6">
      {/* 主题标识 */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <ThemeIcon className="h-6 w-6" />
          <h2 className="text-2xl font-bold">当前主题演示</h2>
        </div>
        <p className="text-muted-foreground">体验不同主题下的界面效果</p>
      </div>

      {/* 演示卡片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        
        {/* 视频卡片演示 */}
        <Card className="overflow-hidden">
          <div className="aspect-video bg-muted flex items-center justify-center">
            <Play className="h-12 w-12 text-muted-foreground" />
          </div>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">示例视频标题</CardTitle>
            <CardDescription>这是一个演示视频的描述文本</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
              <span>1.2万 观看</span>
              <span>2天前</span>
            </div>
            <div className="flex items-center gap-2">
              <Button size="sm" className="flex-1">
                <Play className="h-4 w-4 mr-1" />
                播放
              </Button>
              <Button size="sm" variant="outline">
                <Heart className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="outline">
                <Bookmark className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 统计卡片演示 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              用户统计
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>观看时长</span>
                <span>128小时</span>
              </div>
              <Progress value={75} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>收藏数量</span>
                <span>45个</span>
              </div>
              <Progress value={60} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>点赞数量</span>
                <span>89个</span>
              </div>
              <Progress value={85} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* 功能按钮演示 */}
        <Card>
          <CardHeader>
            <CardTitle>操作按钮</CardTitle>
            <CardDescription>不同样式的按钮演示</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full">主要按钮</Button>
            <Button variant="secondary" className="w-full">次要按钮</Button>
            <Button variant="outline" className="w-full">边框按钮</Button>
            <Button variant="ghost" className="w-full">幽灵按钮</Button>
            <div className="flex gap-2">
              <Button size="sm" className="flex-1">
                <Download className="h-4 w-4 mr-1" />
                下载
              </Button>
              <Button size="sm" variant="outline" className="flex-1">
                <Share className="h-4 w-4 mr-1" />
                分享
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 标签演示 */}
        <Card>
          <CardHeader>
            <CardTitle>标签样式</CardTitle>
            <CardDescription>各种标签和徽章效果</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex flex-wrap gap-2">
              <Badge>默认</Badge>
              <Badge variant="secondary">次要</Badge>
              <Badge variant="outline">边框</Badge>
              <Badge variant="destructive">警告</Badge>
            </div>
            <Separator />
            <div className="flex flex-wrap gap-2">
              <Badge className="bg-blue-500">蓝色</Badge>
              <Badge className="bg-green-500">绿色</Badge>
              <Badge className="bg-purple-500">紫色</Badge>
              <Badge className="bg-orange-500">橙色</Badge>
            </div>
          </CardContent>
        </Card>

        {/* 文本演示 */}
        <Card>
          <CardHeader>
            <CardTitle>文本样式</CardTitle>
            <CardDescription>不同层级的文本展示</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <h1 className="text-2xl font-bold">一级标题</h1>
            <h2 className="text-xl font-semibold">二级标题</h2>
            <h3 className="text-lg font-medium">三级标题</h3>
            <p className="text-base">正文内容，这是一段普通的文本内容。</p>
            <p className="text-sm text-muted-foreground">
              辅助文本，通常用于说明或补充信息。
            </p>
            <p className="text-xs text-muted-foreground">
              小号文本，用于时间戳或版权信息。
            </p>
          </CardContent>
        </Card>

        {/* 特殊效果演示 */}
        <Card className={theme === 'neon' ? 'glow' : ''}>
          <CardHeader>
            <CardTitle className={theme === 'neon' ? 'text-glow' : ''}>
              特殊效果
            </CardTitle>
            <CardDescription>主题特有的视觉效果</CardDescription>
          </CardHeader>
          <CardContent>
            <div className={`p-4 rounded-lg ${
              theme === 'rainbow' ? 'rainbow-border' : 
              theme === 'ocean' ? 'wave-effect' : 
              'bg-muted'
            }`}>
              <p className="text-center font-medium">
                {theme === 'neon' && '✨ 霓虹发光效果'}
                {theme === 'rainbow' && '🌈 彩虹渐变效果'}
                {theme === 'ocean' && '🌊 海洋波浪效果'}
                {theme === 'forest' && '🌲 森林自然效果'}
                {theme === 'sunset' && '🌅 日落温暖效果'}
                {!['neon', 'rainbow', 'ocean', 'forest', 'sunset'].includes(theme) && '🎨 主题效果演示'}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
