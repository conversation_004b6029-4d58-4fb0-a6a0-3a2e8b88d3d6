const express = require('express');
const router = express.Router();

// 导入控制器和中间件
const interactionController = require('./controllers/interactionController');
const { verifyToken, optionalAuth } = require('../../middleware/auth');
const { disabledRateLimiter } = require('../../middleware/rateLimiter');
const {
  validateCommentCreate,
  validateCommentList,
  validateId,
  validateComment,
  validateLike
} = require('../../middleware/validation');
const { trackActivity } = require('../user/middleware/activityTracker');

// 公开路由（无需认证）
router.get('/videos/:videoId/comments',
  validateId,
  validateCommentList,
  interactionController.getVideoComments
);

router.get('/videos/:videoId/comments/popular',
  validateId,
  interactionController.getPopularComments
);

router.get('/comments/:commentId/replies',
  validateId,
  interactionController.getCommentReplies
);

router.get('/comments/search',
  interactionController.searchComments
);

router.get('/videos/:videoId/stats',
  validateId,
  interactionController.getInteractionStats
);

// 需要认证的路由
router.use(verifyToken);

// 评论相关路由
router.post('/comments',
  disabledRateLimiter,
  validateCommentCreate,
  trackActivity.comment,
  interactionController.createComment
);

router.put('/comments/:id',
  validateId,
  interactionController.updateComment
);

router.delete('/comments/:id',
  validateId,
  interactionController.deleteComment
);

// 点赞相关路由
router.post('/likes',
  disabledRateLimiter,
  trackActivity.like,
  interactionController.toggleLike
);

// 收藏相关路由
router.post('/favorites',
  trackActivity.general,
  interactionController.toggleFavorite
);

// 用户互动记录路由
router.get('/users/:id/favorites',
  validateId,
  interactionController.getUserFavorites
);

router.get('/users/:id/likes',
  validateId,
  interactionController.getUserLikes
);

router.get('/users/:id/comments',
  validateId,
  interactionController.getUserComments
);

// 批量检查互动状态
router.post('/batch-check',
  disabledRateLimiter,
  interactionController.batchCheckInteractions
);

// 测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '互动模块测试接口',
    module: 'interaction',
    user: req.user || null
  });
});

module.exports = router;
