const BaseModel = require('../BaseModel');
const logger = require('../../utils/logger');

class Order extends BaseModel {
  constructor() {
    super('orders');
  }

  async createOrder(orderData, connection = null) {
    const query = `
      INSERT INTO orders (
        user_id, type, target_id, amount, final_amount, final_amount_usdt,
        final_amount_trx_integer, final_amount_usdt_integer,
        payment_method, payment_status, order_no, expires_at, description
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      orderData.user_id,
      orderData.type,
      orderData.target_id,
      orderData.amount,
      orderData.final_amount,
      orderData.final_amount_usdt,
      orderData.final_amount_trx_integer,
      orderData.final_amount_usdt_integer,
      orderData.payment_method,
      orderData.payment_status || 'pending',
      orderData.order_no,
      orderData.expires_at,
      orderData.description || null
    ];

    if (connection) {
      const [result] = await connection.query(query, values);
      return result.insertId;
    } else {
      const result = await this.query(query, values);
      return result.insertId;
    }
  }

  async getOrderByNo(orderNo) {
    const query = `
      SELECT o.*, u.username, u.email
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.order_no = ?
    `;

    const results = await this.query(query, [orderNo]);
    return results[0] || null;
  }

  async getOrderById(orderId) {
    const query = `
      SELECT o.*, u.username, u.email
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.id = ?
    `;

    const results = await this.query(query, [orderId]);
    return results[0] || null;
  }

  async updateById(orderId, updateData, connection = null) {
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);

    if (fields.length === 0) {
      return { affectedRows: 0 };
    }

    const setClause = fields.map(field => `\`${field}\` = ?`).join(', ');
    const query = `UPDATE \`${this.tableName}\` SET ${setClause} WHERE id = ?`;
    
    values.push(orderId);

    const executor = connection || this;
    const [result] = await executor.query(query, values);
    return result;
  }

  async updateOrderStatus(orderNo, status, paymentData = {}, connection = null) {
    const updateFields = ['payment_status = ?'];
    const values = [status];
    
    if (paymentData.transaction_id) {
      updateFields.push('transaction_id = ?');
      values.push(paymentData.transaction_id);
    }
    
    if (paymentData.payment_time) {
      updateFields.push('payment_time = ?');
      values.push(paymentData.payment_time);
    }
    
    if (paymentData.payment_data) {
      updateFields.push('payment_data = ?');
      values.push(JSON.stringify(paymentData.payment_data));
    }
    
    if (paymentData.refund_amount) {
      updateFields.push('refund_amount = ?');
      values.push(paymentData.refund_amount);
    }
    
    if (paymentData.refund_time) {
      updateFields.push('refund_time = ?');
      values.push(paymentData.refund_time);
    }
    
    updateFields.push('updated_at = NOW()');
    values.push(orderNo);
    
    const query = `UPDATE orders SET ${updateFields.join(', ')} WHERE order_no = ?`;
    const executor = connection || this;
    return await executor.query(query, values);
  }

  async getUserOrders(userId, options = {}) {
    logger.info('[OrderModel] getUserOrders收到请求', { userId, options }); // 添加诊断日志
    const { page = 1, pageSize = 20, status, type } = options;
    
    // 确保分页参数是数字
    const pageNum = parseInt(page, 10) || 1;
    const pageSizeNum = parseInt(pageSize, 10) || 20;
    const offset = (pageNum - 1) * pageSizeNum;
    
    let whereConditions = ['user_id = ?'];
    let values = [userId];
    
    // 只有当提供了有效的过滤器时，才添加到查询中
    if (status && status !== 'all') {
      whereConditions.push('payment_status = ?');
      values.push(status);
    }
    
    if (type && type !== 'all') {
      whereConditions.push('type = ?');
      values.push(type);
    }
    
    const whereClause = whereConditions.join(' AND ');

    // 1. 获取订单列表的查询 (LIMIT 和 OFFSET 直接嵌入)
    const dataQuery = `
      SELECT * FROM orders
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;
    
    logger.info('[OrderModel] 执行最终查询', { query: dataQuery, params: values }); // 添加诊断日志
    // 只传递WHERE子句需要的参数
    const orders = await this.query(dataQuery, values);
    
    // 2. 获取总数的查询
    const countQuery = `
      SELECT COUNT(*) as total FROM orders
      WHERE ${whereClause}
    `;

    // countQuery 使用原始的 values 数组，不包含分页参数
    const countResult = await this.query(countQuery, values);
    const total = countResult[0]?.total || 0;
    
    return {
      data: orders,
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  async getAllOrders(options = {}) {
    const { page, pageSize, status, type, userId, keyword, sortBy = 'created_at', sortOrder = 'DESC' } = options;
    
    const validPage = Math.max(1, parseInt(page, 10) || 1);
    const limit = Math.max(1, parseInt(pageSize, 10) || 20);
    const offset = (validPage - 1) * limit;

    let whereConditions = ['1 = 1'];
    let queryParams = [];

    if (status) {
        whereConditions.push('o.payment_status = ?');
        queryParams.push(status);
    }
    if (type) {
        whereConditions.push('o.type = ?');
        queryParams.push(type);
    }
    if (userId) {
        whereConditions.push('o.user_id = ?');
        queryParams.push(userId);
    }
    if (keyword) {
        whereConditions.push('(o.order_no LIKE ? OR u.username LIKE ? OR u.email LIKE ?)');
        const searchTerm = `%${keyword}%`;
        queryParams.push(searchTerm, searchTerm, searchTerm);
    }
    
    const whereClause = whereConditions.join(' AND ');

    const dataQuery = `
      SELECT o.*, u.username, u.nickname, u.email
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      WHERE ${whereClause}
      ORDER BY o.${sortBy} ${sortOrder}
      LIMIT ${limit} OFFSET ${offset}
    `;

    const countQuery = `
      SELECT COUNT(*) as total
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      WHERE ${whereClause}
    `;
    
    // 使用更安全的方式执行查询，避免 Promise.all 的解构问题
    const data = await this.query(dataQuery, queryParams);
    const countResult = await this.query(countQuery, queryParams);
    
    const total = countResult?.[0]?.total || 0;
    
    return {
      data,
      pagination: {
        page: validPage,
        pageSize: limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  async getExpiredOrders() {
    const query = `
      SELECT * FROM orders
      WHERE payment_status = 'pending' AND expires_at < NOW()
    `;

    return await this.query(query);
  }

  async markOrderExpired(orderNo) {
    const query = `
      UPDATE orders
      SET payment_status = 'expired', updated_at = NOW()
      WHERE order_no = ? AND payment_status = 'pending'
    `;

    return await this.query(query, [orderNo]);
  }

  async getOrderStats(startDate, endDate) {
    const query = `
      SELECT
        COUNT(*) as total_orders,
        SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_orders,
        SUM(CASE WHEN payment_status = 'paid' THEN final_amount ELSE 0 END) as total_revenue,
        AVG(CASE WHEN payment_status = 'paid' THEN final_amount ELSE NULL END) as avg_order_value
      FROM orders
      WHERE created_at BETWEEN ? AND ?
    `;

    const results = await this.query(query, [startDate, endDate]);
    return results[0];
  }

  /**
   * 检查用户是否已购买特定视频
   * @param {number} userId 用户ID
   * @param {number} videoId 视频ID
   * @returns {Promise<boolean>} 如果用户已成功购买，则返回 true
   */
  async hasUserPurchasedVideo(userId, videoId) {
    const query = `
      SELECT COUNT(*) as count
      FROM orders
      WHERE user_id = ?
        AND target_id = ?
        AND type = 'video'
        AND payment_status = 'paid'
    `;
    const result = await this.query(query, [userId, videoId]);
    return result[0].count > 0;
  }

  /**
   * 由管理员更新订单状态
   * @param {string} orderNo - 订单号
   * @param {string} newStatus - 新的订单状态
   * @param {object} adminData - 管理员操作数据，如 { admin_id }
   * @param {object} connection - 数据库连接对象，用于事务
   * @returns {Promise<object>}
   */
  async adminUpdateOrderStatus(orderNo, newStatus, adminData = {}, connection = null) {
    // 允许管理员手动改为 'paid' 或 'cancelled'
    if (!['paid', 'cancelled'].includes(newStatus)) {
      throw new Error('无效的管理员操作状态');
    }

    const order = await this.getOrderByNo(orderNo);

    const query = `
      UPDATE orders
      SET 
        payment_status = ?,
        updated_at = NOW()
      WHERE order_no = ? AND payment_status = 'pending'
    `;
    const values = [newStatus, orderNo];

    const executor = connection || this;
    const [result] = await executor.query(query, values);

    // 直接返回受影响的行数，让调用者判断是否更新成功
    return result.affectedRows;
  }

  /**
   * 获取订单详情（包含用户信息）
   * @param {string} orderNo 订单号
   * @returns {Promise<Object>} 订单详情
   */
  async getOrderDetailByNo(orderNo) {
    const query = `
      SELECT
        o.*,
        u.username,
        u.nickname,
        u.email,
        u.phone,
        CASE
          WHEN o.type = 'membership' THEN mp.name
          WHEN o.type = 'video' THEN v.title
          ELSE o.description
        END as target_name
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN membership_plans mp ON o.type = 'membership' AND o.target_id = mp.id
      LEFT JOIN videos v ON o.type = 'video' AND o.target_id = v.id
      WHERE o.order_no = ?
    `;

    const results = await this.query(query, [orderNo]);
    return results[0] || null;
  }
}

module.exports = new Order();
