const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { AppError, asyncHandler } = require('../../../middleware/errorHandler');
const { generateToken, generateRefreshToken, blacklistToken } = require('../../../middleware/auth');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const { operationLogger } = require('../../../middleware/requestLogger');
const logger = require('../../../utils/logger');
const User = require('../../../database/models/User');
const emailService = require('../../../services/emailService');
const jwtConfig = require('../../../config/jwt'); // 导入新的JWT配置
const VerificationCodeUtil = require('../../../utils/verificationCode');
const settingService = require('../../../services/settingService');
const authService = require('../services/authService');

class AuthController {
  // 发送注册验证码
  sendRegistrationCode = asyncHandler(async (req, res) => {
    const { email } = req.body;
    if (!email) {
      throw new AppError('需要提供邮箱地址', 400, 'EMAIL_REQUIRED');
    }

    // 1. 检查是否需要邮箱验证
    const requireVerification = await settingService.getSetting('user.requireEmailVerification');
    if (String(requireVerification) !== 'true') {
      throw new AppError('当前无需邮箱验证', 400, 'VERIFICATION_NOT_REQUIRED');
    }
    
    // 2. 检查邮箱是否已被注册
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      throw new AppError('该邮箱已被注册', 409, 'EMAIL_ALREADY_EXISTS');
    }

    // 3. 生成并发送验证码
    const codeConfig = VerificationCodeUtil.generateCodeConfig('registration');
    const { code, ttl } = codeConfig;
    
    try {
      await emailService.sendRegistrationCode(email, code);
      logger.info(`已向 ${email} 发送注册验证码`, { code, ttl });
    } catch (error) {
      logger.error(`向 ${email} 发送注册验证码失败`, { error: error.message });
      throw new AppError('发送验证码邮件失败，请检查邮件服务配置或联系管理员', 500, 'EMAIL_SEND_FAILED');
    }

    // 4. 存储验证码到Redis
    const verificationKey = cache.generateKey(CACHE_KEYS.USER, 'registration_code', email);
    await cache.set(verificationKey, code, ttl);

    res.status(200).json({
      success: true,
      message: '验证码已发送，请注意查收。',
      data: {
        ttl, // 告知前端验证码有效期
      }
    });
  });

  // 用户注册
  register = asyncHandler(async (req, res) => {
    const { email, password, username, verificationCode } = req.body;

    // 检查是否允许新用户注册
    const allowRegistration = await settingService.getSetting('user.enableRegistration');
    if (String(allowRegistration) !== 'true') {
      throw new AppError('系统当前未开放注册', 403, 'REGISTRATION_DISABLED');
    }
    
    // 检查是否需要邮箱验证码
    const requireVerification = await settingService.getSetting('user.requireEmailVerification');
    if (String(requireVerification) === 'true') {
      if (!verificationCode) {
        throw new AppError('需要提供邮箱验证码', 400, 'VERIFICATION_CODE_REQUIRED');
      }

      const verificationKey = cache.generateKey(CACHE_KEYS.USER, 'registration_code', email);
      const storedCode = await cache.get(verificationKey);

      if (!storedCode) {
        throw new AppError('验证码已过期或不存在，请重新获取', 400, 'INVALID_OR_EXPIRED_CODE');
      }

      if (!VerificationCodeUtil.compareCode(verificationCode, storedCode)) {
        throw new AppError('验证码不正确', 400, 'INVALID_CODE');
      }
      
      // 验证成功后立即删除，防止重用
      await cache.del(verificationKey);
    }


    // 基础验证
    if (!email || !password || !username) {
      throw new AppError('邮箱、密码和用户名不能为空', 400, 'MISSING_CREDENTIALS');
    }

    // 只要能通过上面的逻辑，邮箱都应被视为已验证
    const userId = await User.createUser({ email, password, username, email_verified: true });

    if (!userId) {
      throw new AppError('用户创建失败', 500, 'USER_CREATION_FAILED');
    }

    // 创建成功后，直接帮用户登录
    const token = jwt.sign({ id: userId }, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || '1d',
    });

    operationLogger.logUserOperation(
      req,
      'user_register',
      userId,
      '新用户注册成功'
    );
    
    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        token,
        user: { id: userId, email, username }
      },
    });
  });
  
  // 用户登录
  login = asyncHandler(async (req, res) => {
    const { email, password, rememberMe = false, isAdminLogin = false } = req.body;
    const clientIP = req.ip;
    const userAgent = req.get('User-Agent');

    // 检查登录尝试次数
    const attemptCheck = await authService.checkLoginAttempts(email);
    if (!attemptCheck.allowed) {
      throw new AppError(attemptCheck.message, 429, 'TOO_MANY_ATTEMPTS');
    }

    // 验证用户凭据
    let user;
    try {
      user = await User.authenticate(email, password);
    } catch (error) {
      // 记录失败的登录尝试
      await authService.recordLoginAttempt(email);
      throw error;
    }
    
    // 🔧 增加管理员登录角色检查
    if (isAdminLogin && user.role !== 'admin') {
      // 记录尝试用非管理员账号登录管理后台的行为
      operationLogger.logSecurityEvent(req, 'admin_login_attempt_failed', user.id, '非管理员尝试登录后台', {
        email: user.email,
        role: user.role
      });
      throw new AppError('需要管理员权限才能登录', 403, 'ADMIN_REQUIRED');
    }
    
    // 登录成功，清除失败的登录尝试记录
    await authService.clearLoginAttempts(email);

    // 更新最后登录信息
    await User.updateLastLogin(user.id, clientIP);

    // 生成JWT令牌
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      username: user.username,
      nickname: user.nickname,
      avatar: user.avatar,
      balance: user.balance
    };
    
    const accessTokenExpiry = rememberMe ? '30d' : '7d';
    const accessToken = generateToken(tokenPayload, accessTokenExpiry);
    const refreshToken = generateRefreshToken(tokenPayload);
    
    // 将refresh token存储到Redis
    const refreshKey = cache.generateKey(CACHE_KEYS.USER, 'refresh_token', user.id);
    await cache.set(refreshKey, refreshToken, 30 * 24 * 3600); // 30天
    
    // 记录登录日志
    operationLogger.logUserOperation(req, 'user_login', user.id, '用户登录', {
      ip: clientIP,
      userAgent,
      rememberMe
    });
    
    // 设置安全的cookie（可选）
    if (rememberMe) {
      res.cookie('refreshToken', refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 30 * 24 * 60 * 60 * 1000 // 30天
      });
    }
    
    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          nickname: user.nickname,
          avatar: user.avatar,
          role: user.role,
          email_verified: user.email_verified
        },
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: rememberMe ? '30d' : '7d'
        }
      }
    });
  });
  
  // 用户登出
  logout = asyncHandler(async (req, res) => {
    const token = req.headers.authorization?.split(' ')[1];
    const userId = req.user.id;
    
    // 将当前token加入黑名单
    if (token) {
      await blacklistToken(token);
    }
    
    // 删除refresh token
    const refreshKey = cache.generateKey(CACHE_KEYS.USER, 'refresh_token', userId);
    await cache.del(refreshKey);
    
    // 清除cookie
    res.clearCookie('refreshToken');
    
    // 记录登出日志
    operationLogger.logUserOperation(req, 'user_logout', userId, '用户登出');
    
    res.json({
      success: true,
      message: '登出成功'
    });
  });
  
  // 刷新令牌
  refreshToken = asyncHandler(async (req, res) => {
    const { refreshToken } = req.body || {};
    const cookieRefreshToken = req.cookies?.refreshToken;
    
    const token = refreshToken || cookieRefreshToken;
    if (!token) {
      throw new AppError('刷新令牌缺失', 401, 'REFRESH_TOKEN_MISSING');
    }
    
    try {
      // 验证refresh token
      const decoded = jwt.verify(token, jwtConfig.refreshSecret);
      
      // 检查用户是否存在
      const user = await User.findById(decoded.userId);
      if (!user) {
        throw new AppError('用户不存在', 401, 'USER_NOT_FOUND');
      }
      
      // 检查存储的refresh token
      const refreshKey = cache.generateKey(CACHE_KEYS.USER, 'refresh_token', user.id);
      const storedToken = await cache.get(refreshKey);
      
      if (storedToken !== token) {
        throw new AppError('无效的刷新令牌', 401, 'INVALID_REFRESH_TOKEN');
      }
      
      // 生成新的access token
      const tokenPayload = {
        userId: user.id,
        email: user.email,
        role: user.role,
        username: user.username,
        nickname: user.nickname,
        avatar: user.avatar,
        balance: user.balance
      };
      
      const newAccessToken = generateToken(tokenPayload);
      
      res.json({
        success: true,
        message: '令牌刷新成功',
        data: {
          accessToken: newAccessToken,
          expiresIn: '7d'
        }
      });
      
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new AppError('刷新令牌已过期，请重新登录', 401, 'REFRESH_TOKEN_EXPIRED');
      } else if (error.name === 'JsonWebTokenError') {
        throw new AppError('无效的刷新令牌', 401, 'INVALID_REFRESH_TOKEN');
      }
      throw error;
    }
  });
  
  // 邮箱验证
  verifyEmail = asyncHandler(async (req, res) => {
    const { code } = req.body;
    const userId = req.user.id;

    // 检查验证码
    const verificationKey = cache.generateKey(CACHE_KEYS.USER, 'email_verification', userId);
    const storedCode = await cache.get(verificationKey);

    if (!storedCode) {
      logger.warn(`邮箱验证失败 - 验证码不存在或已过期`, { userId });
      throw new AppError('验证码无效或已过期', 400, 'INVALID_VERIFICATION_CODE');
    }

    // 使用验证码工具进行比较（邮箱验证使用令牌，区分大小写）
    if (!VerificationCodeUtil.compareCode(code, storedCode, true)) {
      logger.warn(`邮箱验证失败 - 验证码不匹配`, {
        userId,
        providedCodeLength: code ? code.length : 0
      });
      throw new AppError('验证码无效或已过期', 400, 'INVALID_VERIFICATION_CODE');
    }

    // 更新用户邮箱验证状态
    await User.verifyEmail(userId);

    // 删除验证码
    await cache.del(verificationKey);

    // 记录操作日志
    operationLogger.logUserOperation(req, 'email_verified', userId, '邮箱验证成功');
    logger.info(`用户 ${userId} 邮箱验证成功`);

    res.json({
      success: true,
      message: '邮箱验证成功'
    });
  });
  
  // 重新发送验证邮件
  resendVerification = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const user = await User.findById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    if (user.email_verified) {
      throw new AppError('邮箱已验证', 400, 'EMAIL_ALREADY_VERIFIED');
    }
    
    // 生成新的验证码
    const codeConfig = VerificationCodeUtil.generateCodeConfig('email_verification');
    const verificationCode = codeConfig.code;
    const verificationKey = cache.generateKey(CACHE_KEYS.USER, 'email_verification', userId);
    await cache.set(verificationKey, verificationCode, codeConfig.ttl);

    // 发送验证邮件
    try {
      await emailService.sendVerificationEmail(user.email, verificationCode, user.username);
      logger.info(`已重新向 ${user.email} 发送邮箱验证邮件`, {
        userId,
        codeType: 'email_verification'
      });
    } catch (error) {
      logger.error('重新发送验证邮件失败:', {
        error: error.message,
        email: user.email,
        userId
      });
      throw new AppError('发送验证邮件失败，请稍后重试', 500);
    }
    
    res.json({
      success: true,
      message: '验证邮件已重新发送'
    });
  });
  
  // 忘记密码
  forgotPassword = asyncHandler(async (req, res) => {
    const { email } = req.body;
    
    const user = await User.findByEmail(email);
    if (!user) {
      // 为了安全，不暴露用户是否存在
      res.json({
        success: true,
        message: '如果邮箱存在，重置链接已发送'
      });
      return;
    }
    
    // 生成重置令牌
    const codeConfig = VerificationCodeUtil.generateCodeConfig('password_reset');
    const resetToken = codeConfig.code;
    const resetKey = cache.generateKey(CACHE_KEYS.USER, 'password_reset', user.id);
    await cache.set(resetKey, resetToken, codeConfig.ttl);

    // 发送重置邮件
    try {
      await emailService.sendPasswordResetEmail(email, resetToken, user.username);
      logger.info(`已向 ${email} 发送密码重置邮件`, {
        userId: user.id,
        codeType: 'password_reset'
      });
    } catch (error) {
      logger.error('发送密码重置邮件失败:', {
        error: error.message,
        email,
        userId: user.id
      });
      // 不抛出异常，保持安全性
    }
    
    // 记录安全事件
    operationLogger.logSecurityEvent(req, 'password_reset_requested', 'medium', '密码重置请求', {
      email,
      userId: user.id
    });
    
    res.json({
      success: true,
      message: '如果邮箱存在，重置链接已发送'
    });
  });
  
  // 重置密码
  resetPassword = asyncHandler(async (req, res) => {
    const { token, newPassword } = req.body;
    
    if (!token || !newPassword) {
      throw new AppError('重置令牌和新密码不能为空', 400, 'MISSING_REQUIRED_FIELDS');
    }
    
    // 查找重置令牌
    let userId = null;
    const cacheKeys = await cache.redis.keys(`${CACHE_KEYS.USER}:password_reset:*`);
    
    for (const key of cacheKeys) {
      const storedToken = await cache.get(key);
      if (storedToken === token) {
        userId = key.split(':').pop();
        break;
      }
    }
    
    if (!userId) {
      throw new AppError('重置令牌无效或已过期', 400, 'INVALID_RESET_TOKEN');
    }
    
    // 更新密码
    await User.updatePassword(userId, newPassword);
    
    // 删除重置令牌
    const resetKey = cache.generateKey(CACHE_KEYS.USER, 'password_reset', userId);
    await cache.del(resetKey);
    
    // 使所有现有token失效（可选）
    // 这里可以实现token版本控制或全局失效
    
    // 记录安全事件
    operationLogger.logSecurityEvent(req, 'password_reset_completed', 'high', '密码重置完成', {
      userId
    });
    
    res.json({
      success: true,
      message: '密码重置成功，请使用新密码登录'
    });
  });
  
  // 修改密码
  changePassword = asyncHandler(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;
    
    // 获取用户信息
    const user = await User.findById(userId);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    // 验证当前密码
    const isValidPassword = await User.verifyPassword(currentPassword, user.password);
    if (!isValidPassword) {
      throw new AppError('当前密码错误', 400, 'INVALID_CURRENT_PASSWORD');
    }

    // 新密码验证已在中间件中完成

    // 更新密码
    await User.updatePassword(userId, newPassword);
    
    // 记录安全事件
    operationLogger.logSecurityEvent(req, 'password_changed', 'medium', '用户修改密码', {
      userId
    });
    
    res.json({
      success: true,
      message: '密码修改成功'
    });
  });
  
  // 获取当前用户信息
  getCurrentUser = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const user = await User.findById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    // 返回用户信息（不包含密码）
    const { password, ...userResponse } = user;
    
    res.json({
      success: true,
      data: {
        user: userResponse
      }
    });
  });

  // 获取密码要求配置（公开接口）
  getPasswordRequirements = asyncHandler(async (req, res) => {
    const minLength = await settingService.getSetting('security.passwordMinLength');
    const requireStrong = await settingService.getSetting('security.requireStrongPassword');

    res.json({
      success: true,
      data: {
        minLength: minLength ? parseInt(minLength) : 8,
        requireStrong: requireStrong === 'true' || requireStrong === true,
        requirements: {
          minLength: minLength ? parseInt(minLength) : 8,
          requireUppercase: requireStrong === 'true' || requireStrong === true,
          requireLowercase: requireStrong === 'true' || requireStrong === true,
          requireNumbers: requireStrong === 'true' || requireStrong === true,
          requireSpecialChars: requireStrong === 'true' || requireStrong === true
        }
      }
    });
  });

  // 获取公共系统设置
  getPublicSettings = asyncHandler(async (req, res) => {
    const allSettings = await settingService.getAllSettings();
    
    // 定义一个白名单，只返回安全的、公共的设置
    const publicSettingsWhitelist = [
      'site.name',
      'user.enableRegistration',
      'user.requireEmailVerification',
      'security.requireStrongPassword',
      'security.passwordMinLength'
    ];

    const publicSettings = {};
    for (const key of publicSettingsWhitelist) {
      if (allSettings.hasOwnProperty(key)) {
        publicSettings[key] = allSettings[key];
      }
    }

    res.status(200).json({
      success: true,
      data: publicSettings,
    });
  });
}

module.exports = new AuthController();
