const jwt = require('jsonwebtoken');
const { AppError } = require('./errorHandler');
const { redis } = require('../config/database');
const logger = require('../utils/logger');
const jwtConfig = require('../config/jwt'); // 导入新的JWT配置
const User = require('../database/models/User'); // 引入User模型
const asyncHandler = require('express-async-handler'); // 保留这个 asyncHandler

// 验证JWT令牌
const verifyToken = asyncHandler(async (req, res, next) => {
  let token;
  
  // 从请求头获取token
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  
  if (!token) {
    // 对于需要强制登录的接口，保持原样
    // 对于可选登录的接口，这个逻辑应该在optionalAuth中处理
    // 此处我们假设verifyToken用于需要强制登录的路由
    req.user = null;
    return next(); // 如果没有token，则作为游客继续
  }
  
  try {
    // 验证token
    const decoded = jwt.verify(token, jwtConfig.secret);
    
    const userId = decoded.userId || decoded.id;
    if (!userId) {
      throw new AppError('令牌无效(缺少用户信息)，请重新登录', 401, 'INVALID_TOKEN_PAYLOAD');
    }
    
    // --- 核心修复：验证用户是否存在于数据库 ---
    const user = await User.findById(userId);
    if (!user) {
      logger.warn(`认证失败：Token有效，但用户ID ${userId} 在数据库中不存在。`);
      throw new AppError('用户不存在或会话已失效', 401, 'INVALID_USER');
    }
    
    // 检查用户状态
    if (user.status !== 'active') {
      logger.warn(`用户 ${user.id} 尝试使用非活跃账户登录，状态: ${user.status}`);
      throw new AppError('您的账户已被禁用或尚未激活', 403, 'USER_INACTIVE');
    }
    
    // 检查token是否在黑名单中
    const isBlacklisted = await redis.get(`blacklist:${token}`);
    if (isBlacklisted) {
      throw new AppError('访问令牌已失效，请重新登录', 401, 'TOKEN_BLACKLISTED');
    }
    
    req.user = user; // 附加完整的用户信息到请求对象
    logger.debug(`用户认证成功: { id: ${user.id}, role: '${user.role}' }`);
    
    next();
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    if (error.name === 'TokenExpiredError') {
      throw new AppError('访问令牌已过期，请重新登录', 401, 'TOKEN_EXPIRED');
    }
    logger.error('Token验证过程中发生错误:', error.message);
    throw new AppError('无效的访问令牌', 401, 'INVALID_TOKEN');
  }
});

// 可选的token验证（不强制要求登录）
const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (token) {
    try {
      const decoded = jwt.verify(token, jwtConfig.secret);
      logger.info('Token解码成功:', { userId: decoded.userId || decoded.id, role: decoded.role });

      // 检查token是否在黑名单中
      try {
        const isBlacklisted = await redis.get(`blacklist:${token}`);
        if (isBlacklisted) {
           req.user = null;
           return next();
        }

        const userId = decoded.userId || decoded.id;
        const user = await User.findById(userId);

        // 只有当用户存在且状态正常时，才附加用户信息
        if (user && user.status === 'active') {
            req.user = user;
            logger.debug('可选认证成功:', { userId: req.user.id, role: req.user.role });
        } else {
            req.user = null; // 用户不存在或状态异常
            logger.warn(`可选认证：用户 ${userId} 不存在或状态异常。`);
        }
      } catch (redisError) {
        logger.error('Redis检查token黑名单失败:', redisError);
        // 🔧 可选认证中Redis失败时不设置用户信息，确保安全
        req.user = null;
      }
    } catch (error) {
      // 可选认证失败时不抛出错误，继续执行
      logger.warn('可选认证失败:', error.message);
    }
  }

  next();
});

// 检查用户角色权限
const requireRole = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      throw new AppError('需要登录才能访问', 401, 'AUTH_REQUIRED');
    }
    
    if (!roles.includes(req.user.role)) {
      throw new AppError('权限不足，无法访问', 403, 'INSUFFICIENT_PERMISSIONS');
    }
    
    next();
  };
};

// 检查管理员权限
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    throw new AppError('需要登录才能访问', 401, 'AUTH_REQUIRED');
  }
  
  if (req.user.role !== 'admin') {
    throw new AppError('需要管理员权限', 403, 'ADMIN_REQUIRED');
  }
  
  next();
};

// 检查会员权限
const requireMember = (req, res, next) => {
  if (!req.user) {
    throw new AppError('需要登录才能访问', 401, 'AUTH_REQUIRED');
  }
  
  if (!['member', 'vip', 'admin'].includes(req.user.role)) {
    throw new AppError('需要会员权限', 403, 'MEMBER_REQUIRED');
  }
  
  next();
};

// 检查VIP权限
const requireVIP = (req, res, next) => {
  if (!req.user) {
    throw new AppError('需要登录才能访问', 401, 'AUTH_REQUIRED');
  }
  
  if (!['vip', 'admin'].includes(req.user.role)) {
    throw new AppError('需要VIP权限', 403, 'VIP_REQUIRED');
  }
  
  next();
};

// 检查资源所有权
const requireOwnership = (resourceIdParam = 'id', resourceType = 'user') => {
  return asyncHandler(async (req, res, next) => {
    if (!req.user) {
      throw new AppError('需要登录才能访问', 401, 'AUTH_REQUIRED');
    }

    // 管理员可以访问所有资源
    if (req.user.role === 'admin') {
      return next();
    }

    const resourceId = req.params[resourceIdParam];
    const userId = req.user.id;

    // 🔧 改进的资源所有权检查
    try {
      const hasOwnership = await checkResourceOwnership(userId, resourceType, resourceId);
      if (!hasOwnership) {
        throw new AppError('只能访问自己的资源', 403, 'OWNERSHIP_REQUIRED');
      }
      next();
    } catch (error) {
      logger.error('检查资源所有权失败:', error);
      throw new AppError('权限验证失败', 500, 'PERMISSION_CHECK_FAILED');
    }
  });
};

// 🔧 新增：检查资源所有权的辅助函数
async function checkResourceOwnership(userId, resourceType, resourceId) {
  // 如果是用户资源，直接比较ID
  if (resourceType === 'user') {
    return resourceId.toString() === userId.toString();
  }

  // 对于其他资源类型，需要查询数据库
  // 这里可以根据实际需求扩展
  const { db } = require('../config/database');

  try {
    let query;
    switch (resourceType) {
      case 'video':
        query = 'SELECT user_id FROM videos WHERE id = ?';
        break;
      case 'comment':
        query = 'SELECT user_id FROM comments WHERE id = ?';
        break;
      case 'order':
        query = 'SELECT user_id FROM orders WHERE id = ?';
        break;
      default:
        return false;
    }

    const [rows] = await db.query(query, [resourceId]);
    return rows.length > 0 && rows[0].user_id.toString() === userId.toString();
  } catch (error) {
    logger.error(`检查${resourceType}资源所有权失败:`, error);
    return false;
  }
}

// 🔧 新增：一个通用的、强制要求登录的认证中间件
const auth = asyncHandler(async (req, res, next) => {
  let token;
  
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  
  if (!token) {
    throw new AppError('需要提供访问令牌才能访问', 401, 'TOKEN_REQUIRED');
  }
  
  try {
    const decoded = jwt.verify(token, jwtConfig.secret);
    
    const userId = decoded.userId || decoded.id;
    if (!userId) {
      throw new AppError('令牌无效(缺少用户信息)，请重新登录', 401, 'INVALID_TOKEN_PAYLOAD');
    }
    
    const user = await User.findById(userId);
    if (!user) {
      logger.warn(`认证失败：Token有效，但用户ID ${userId} 在数据库中不存在。`);
      throw new AppError('用户不存在或会话已失效', 401, 'INVALID_USER');
    }
    
    if (user.status !== 'active') {
      logger.warn(`用户 ${user.id} 尝试使用非活跃账户登录，状态: ${user.status}`);
      throw new AppError('您的账户已被禁用或尚未激活', 403, 'USER_INACTIVE');
    }
    
    const isBlacklisted = await redis.get(`blacklist:${token}`);
    if (isBlacklisted) {
      throw new AppError('访问令牌已失效，请重新登录', 401, 'TOKEN_BLACKLISTED');
    }
    
    req.user = user;
    next();
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    if (error.name === 'TokenExpiredError') {
      throw new AppError('访问令牌已过期，请重新登录', 401, 'TOKEN_EXPIRED');
    }
    logger.error('Token验证过程中发生错误:', error.message);
    throw new AppError('无效的访问令牌', 401, 'INVALID_TOKEN');
  }
});

// 生成JWT令牌
const generateToken = (payload, expiresIn = jwtConfig.expiresIn) => {
  return jwt.sign(payload, jwtConfig.secret, { expiresIn });
};

// 生成刷新令牌
const generateRefreshToken = (payload) => {
  return jwt.sign(payload, jwtConfig.refreshSecret, {
    expiresIn: jwtConfig.refreshExpiresIn
  });
};

// 将token加入黑名单
const blacklistToken = async (token) => {
  try {
    const decoded = jwt.decode(token);
    if (decoded && decoded.exp) {
      const ttl = decoded.exp - Math.floor(Date.now() / 1000);
      if (ttl > 0) {
        await redis.setEx(`blacklist:${token}`, ttl, 'true');
      }
    }
  } catch (error) {
    logger.error('将token加入黑名单失败:', error);
  }
};

module.exports = {
  auth,
  verifyToken,
  optionalAuth,
  requireRole,
  requireAdmin,
  requireMember,
  requireVIP,
  requireOwnership,
  generateToken,
  generateRefreshToken,
  blacklistToken
};
