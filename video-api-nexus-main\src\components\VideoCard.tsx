import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Play, Eye, Heart, MessageCircle, Clock, Star, Plus, Crown } from 'lucide-react';
import { useTheme } from '@/components/theme-provider';
import { toast } from '@/hooks/use-toast';
import { useLikeMutation, useFavoriteMutation } from '@/hooks/queries/useInteractions';
import { useTranslation } from 'react-i18next';

interface VideoCardProps {
  video: {
    id: string | number;
    title: string;
    thumbnail_url?: string;
    duration?: number;
    view_count?: number;
    like_count?: number;
    comment_count?: number;
    category?: { name: string };
    uploader?: { nickname: string };
    is_liked?: boolean;
    is_favorited?: boolean;
    media_type?: 'video' | 'audio';
    url?: string;
    visibility?: 'public' | 'member_only' | 'paid';
  };
  onInteractionChange?: (type: 'like' | 'favorite', newState: boolean) => void;
  onAddToPlaylist?: (video: any) => void;
  showLoginPrompt?: boolean;
}

const VideoCard: React.FC<VideoCardProps> = ({ video, onInteractionChange, onAddToPlaylist, showLoginPrompt = false }) => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // 使用React Query mutations
  const likeMutation = useLikeMutation();
  const favoriteMutation = useFavoriteMutation();

  // 本地状态管理
  const [isLiked, setIsLiked] = useState(video.is_liked || false);
  const [isFavorited, setIsFavorited] = useState(video.is_favorited || false);
  const [likeCount, setLikeCount] = useState(video.like_count || 0);

  const isLoading = likeMutation.isPending || favoriteMutation.isPending;

  const views = video.view_count || 0;
  const comments = video.comment_count || 0;
  const category = video.category?.name || t('videoInfo.uncategorized');

  // 优化上传者显示逻辑
  const uploaderNickname = video.uploader?.nickname;
  const isSystemAdmin = uploaderNickname === t('videoInfo.systemAdmin');
  const author = uploaderNickname || t('videoInfo.anonymousAuthor');

  // 根据主题获取特效类名
  const getThemeEffectClass = () => {
    switch (theme) {
      case 'neon': return 'video-card-glow';
      case 'ocean': return 'video-card-wave';
      case 'forest': return 'video-card-nature';
      case 'sunset': return 'video-card-warm';
      case 'rainbow': return 'video-card-rainbow';
      default: return '';
    }
  };

  // 格式化时长（从秒到 MM:SS）
  const formatDuration = (seconds) => {
    if (isNaN(seconds) || seconds < 0) {
      return '00:00';
    }
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 格式化数字
  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // 处理点赞
  const handleLike = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (showLoginPrompt) {
      toast({
        title: "需要登录",
        description: "请先登录后再进行点赞操作",
        variant: "default",
      });
      navigate('/login');
      return;
    }

    if (isLoading) return;

    // 使用React Query mutation
    likeMutation.mutate(
      { videoId: video.id, targetType: 'video' },
      {
        onSuccess: (data) => {
          const newLikedState = data?.data?.data?.liked;
          const newCount = newLikedState ? likeCount + 1 : Math.max(0, likeCount - 1);

          setIsLiked(newLikedState);
          setLikeCount(newCount);

          // 通知父组件状态变化
          if (onInteractionChange) {
            onInteractionChange('like', newLikedState);
          }
        },
      }
    );
  };

  // 处理收藏
  const handleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (showLoginPrompt) {
      toast({
        title: "需要登录",
        description: "请先登录后再进行收藏操作",
        variant: "default",
      });
      navigate('/login');
      return;
    }

    if (isLoading) return;

    // 使用React Query mutation
    favoriteMutation.mutate(video.id, {
      onSuccess: () => {
        const newFavoritedState = !isFavorited;
        setIsFavorited(newFavoritedState);

        // 通知父组件状态变化
        if (onInteractionChange) {
          onInteractionChange('favorite', newFavoritedState);
        }
      },
    });
  };

  // 处理添加到播放列表
  const handleAddToPlaylist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onAddToPlaylist) {
      onAddToPlaylist(video);
    }
  };

  return (
    <Link to={`/video/${video.id}`} className="block group">
      <Card className={`overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300 group-hover:scale-[1.02] bg-card/50 backdrop-blur-sm ${getThemeEffectClass()}`}>
        <div className="relative">
          {/* 缩略图 - 移动端高度调整 */}
          {video.thumbnail_url ? (
            <img
              src={video.thumbnail_url}
              alt={video.title}
              className="w-full h-36 sm:h-48 object-cover bg-muted"
            />
          ) : (
            <div className="w-full h-36 sm:h-48 bg-muted flex items-center justify-center">
              <Play className="w-12 h-12 sm:w-16 sm:h-16 text-muted-foreground/50" />
            </div>
          )}

          {/* 播放按钮覆盖层 - 移动端尺寸调整 */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/80 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 backdrop-blur-sm">
              <Play className="w-5 h-5 sm:w-6 sm:h-6 text-primary-foreground ml-0.5" fill="currentColor" />
            </div>
          </div>

          {/* 时长标签 - 移动端字体调整 */}
          <div className="absolute bottom-1 right-1 sm:bottom-2 sm:right-2 bg-black/75 text-white text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-md flex items-center gap-1 backdrop-blur-sm">
            <Clock className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
            <span className="text-xs">{formatDuration(video.duration)}</span>
          </div>



          {/* 会员专属标志 */}
          {video.visibility === 'member_only' && (
            <div className="absolute top-1 right-1 sm:top-2 sm:right-2">
              <Badge variant="default" className="text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 bg-yellow-500/90 text-white backdrop-blur-sm flex items-center gap-1">
                <Crown size={12} />
                <span>{t('videoInfo.member')}</span>
              </Badge>
            </div>
          )}

          {/* 付费视频标志 */}
          {video.visibility === 'paid' && (
            <div className="absolute top-1 right-1 sm:top-2 sm:right-2">
              <Badge variant="destructive" className="text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 bg-red-600/90 text-white backdrop-blur-sm flex items-center gap-1">
                <Star size={12} />
                <span>{t('videoInfo.paid')}</span>
              </Badge>
            </div>
          )}
        </div>

        <CardContent className="p-3 sm:p-4 space-y-2 sm:space-y-3">
          {/* 标题 - 移动端字体调整 */}
          <h3 className="font-semibold text-sm sm:text-base leading-tight line-clamp-2 group-hover:text-primary transition-colors" title={video.title}>
            {video.title}
          </h3>

          {/* 作者 - 移动端字体调整 */}
          {!isSystemAdmin && (
            <p className="text-xs sm:text-sm text-muted-foreground truncate">
              {author}
            </p>
          )}

          {/* 统计信息 - 移动端间距和字体调整 */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="flex items-center space-x-1">
                <Eye size={10} className="sm:w-3 sm:h-3" />
                <span className="text-xs">{formatNumber(views)}</span>
              </div>
              <div
                className="flex items-center space-x-1 cursor-pointer hover:text-red-500 transition-colors"
                onClick={handleLike}
                title={isLiked ? '取消点赞' : '点赞'}
              >
                <Heart
                  size={10}
                  className={`sm:w-3 sm:h-3 ${isLiked ? 'fill-red-500 text-red-500' : ''} ${isLoading ? 'animate-pulse' : ''}`}
                />
                <span className="text-xs">{formatNumber(likeCount)}</span>
              </div>
              <div className="flex items-center space-x-1">
                <MessageCircle size={10} className="sm:w-3 sm:h-3" />
                <span className="text-xs">{formatNumber(comments)}</span>
              </div>
            </div>

            <div className="flex items-center space-x-1 sm:space-x-2">
              {/* 收藏按钮 */}
              <div
                className="flex items-center space-x-1 cursor-pointer hover:text-yellow-500 transition-colors"
                onClick={handleFavorite}
                title={isFavorited ? '取消收藏' : '收藏'}
              >
                <Star
                  size={10}
                  className={`sm:w-3 sm:h-3 ${isFavorited ? 'fill-yellow-400 text-yellow-400' : ''} ${isLoading ? 'animate-pulse' : ''}`}
                />
              </div>

              {/* 添加到播放列表按钮 */}
              {onAddToPlaylist && (
                <div
                  className="flex items-center space-x-1 cursor-pointer hover:text-primary transition-colors"
                  onClick={handleAddToPlaylist}
                  title="添加到播放列表"
                >
                  <Plus
                    size={10}
                    className="sm:w-3 sm:h-3"
                  />
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default VideoCard; 