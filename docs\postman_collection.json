{"info": {"name": "视频平台API", "description": "视频平台API接口集合", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "认证模块", "item": [{"name": "POST /change-password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/change-password", "host": ["{{base_url}}"], "path": ["api", "auth", "change-password"]}}}, {"name": "POST /forgot-password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/forgot-password", "host": ["{{base_url}}"], "path": ["api", "auth", "forgot-password"]}}}, {"name": "POST /login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "POST /logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/logout", "host": ["{{base_url}}"], "path": ["api", "auth", "logout"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "GET /me", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/me", "host": ["{{base_url}}"], "path": ["api", "auth", "me"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "POST /refresh-token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "auth", "refresh-token"]}}}, {"name": "公开路由（无需认证）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "POST /resend-verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/resend-verification", "host": ["{{base_url}}"], "path": ["api", "auth", "resend-verification"]}}}, {"name": "POST /reset-password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/reset-password", "host": ["{{base_url}}"], "path": ["api", "auth", "reset-password"]}}}, {"name": "测试路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/test", "host": ["{{base_url}}"], "path": ["api", "auth", "test"]}}}, {"name": "POST /verify-email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/auth/verify-email", "host": ["{{base_url}}"], "path": ["api", "auth", "verify-email"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}]}, {"name": "用户模块", "item": [{"name": "GET /:id", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/:id", "host": ["{{base_url}}"], "path": ["api", "user", ":id"]}}}, {"name": "管理员操作路由", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/:id/ban", "host": ["{{base_url}}"], "path": ["api", "user", ":id", "ban"]}}}, {"name": "PUT /:id/role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/:id/role", "host": ["{{base_url}}"], "path": ["api", "user", ":id", "role"]}}}, {"name": "PUT /:id/unban", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/:id/unban", "host": ["{{base_url}}"], "path": ["api", "user", ":id", "unban"]}}}, {"name": "DELETE /account", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/account", "host": ["{{base_url}}"], "path": ["api", "user", "account"]}}}, {"name": "POST /avatar", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/avatar", "host": ["{{base_url}}"], "path": ["api", "user", "avatar"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "DELETE /avatar", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/avatar", "host": ["{{base_url}}"], "path": ["api", "user", "avatar"]}}}, {"name": "用户查询路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/list", "host": ["{{base_url}}"], "path": ["api", "user", "list"]}}}, {"name": "用户个人资料相关路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/profile", "host": ["{{base_url}}"], "path": ["api", "user", "profile"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "PUT /profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/profile", "host": ["{{base_url}}"], "path": ["api", "user", "profile"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "GET /stats", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/stats", "host": ["{{base_url}}"], "path": ["api", "user", "stats"]}}}, {"name": "测试路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/user/test", "host": ["{{base_url}}"], "path": ["api", "user", "test"]}}}]}, {"name": "视频模块", "item": [{"name": "GET /:id", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/:id", "host": ["{{base_url}}"], "path": ["api", "video", ":id"]}}}, {"name": "PUT /:id", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/:id", "host": ["{{base_url}}"], "path": ["api", "video", ":id"]}}}, {"name": "DELETE /:id", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/:id", "host": ["{{base_url}}"], "path": ["api", "video", ":id"]}}}, {"name": "GET /:id/processing-status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/:id/processing-status", "host": ["{{base_url}}"], "path": ["api", "video", ":id", "processing-status"]}}}, {"name": "POST /:id/reprocess", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/:id/reprocess", "host": ["{{base_url}}"], "path": ["api", "video", ":id", "reprocess"]}}}, {"name": "GET /:id/stats", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/:id/stats", "host": ["{{base_url}}"], "path": ["api", "video", ":id", "stats"]}}}, {"name": "公开路由（无需认证）", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/list", "host": ["{{base_url}}"], "path": ["api", "video", "list"]}}}, {"name": "GET /popular", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/popular", "host": ["{{base_url}}"], "path": ["api", "video", "popular"]}}}, {"name": "GET /recommended", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/recommended", "host": ["{{base_url}}"], "path": ["api", "video", "recommended"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "GET /search", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/search", "host": ["{{base_url}}"], "path": ["api", "video", "search"]}}}, {"name": "测试路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/test", "host": ["{{base_url}}"], "path": ["api", "video", "test"]}}}, {"name": "POST /upload", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/upload", "host": ["{{base_url}}"], "path": ["api", "video", "upload"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "GET /user/:id", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/video/user/:id", "host": ["{{base_url}}"], "path": ["api", "video", "user", ":id"]}}}]}, {"name": "互动模块", "item": [{"name": "批量检查互动状态", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/batch-check", "host": ["{{base_url}}"], "path": ["api", "interaction", "batch-check"]}}}, {"name": "评论相关路由", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/comments", "host": ["{{base_url}}"], "path": ["api", "interaction", "comments"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "GET /comments/:commentId/replies", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/comments/:commentId/replies", "host": ["{{base_url}}"], "path": ["api", "interaction", "comments", ":commentId", "replies"]}}}, {"name": "PUT /comments/:id", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/comments/:id", "host": ["{{base_url}}"], "path": ["api", "interaction", "comments", ":id"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "DELETE /comments/:id", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/comments/:id", "host": ["{{base_url}}"], "path": ["api", "interaction", "comments", ":id"]}}}, {"name": "GET /comments/search", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/comments/search", "host": ["{{base_url}}"], "path": ["api", "interaction", "comments", "search"]}}}, {"name": "收藏相关路由", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/favorites", "host": ["{{base_url}}"], "path": ["api", "interaction", "favorites"]}}}, {"name": "点赞相关路由", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/likes", "host": ["{{base_url}}"], "path": ["api", "interaction", "likes"]}}}, {"name": "测试路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/test", "host": ["{{base_url}}"], "path": ["api", "interaction", "test"]}}}, {"name": "GET /users/:id/comments", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/users/:id/comments", "host": ["{{base_url}}"], "path": ["api", "interaction", "users", ":id", "comments"]}}}, {"name": "用户互动记录路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/users/:id/favorites", "host": ["{{base_url}}"], "path": ["api", "interaction", "users", ":id", "favorites"]}}}, {"name": "GET /users/:id/likes", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/users/:id/likes", "host": ["{{base_url}}"], "path": ["api", "interaction", "users", ":id", "likes"]}}}, {"name": "公开路由（无需认证）", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/videos/:videoId/comments", "host": ["{{base_url}}"], "path": ["api", "interaction", "videos", ":videoId", "comments"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "GET /videos/:videoId/comments/popular", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/videos/:videoId/comments/popular", "host": ["{{base_url}}"], "path": ["api", "interaction", "videos", ":videoId", "comments", "popular"]}}}, {"name": "GET /videos/:videoId/stats", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/interaction/videos/:videoId/stats", "host": ["{{base_url}}"], "path": ["api", "interaction", "videos", ":videoId", "stats"]}}}]}, {"name": "会员模块", "item": [{"name": "GET /admin/plans", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/admin/plans", "host": ["{{base_url}}"], "path": ["api", "member", "admin", "plans"]}}}, {"name": "POST /admin/plans", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/admin/plans", "host": ["{{base_url}}"], "path": ["api", "member", "admin", "plans"]}}}, {"name": "PUT /admin/plans/:id", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/admin/plans/:id", "host": ["{{base_url}}"], "path": ["api", "member", "admin", "plans", ":id"]}}}, {"name": "DELETE /admin/plans/:id", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/admin/plans/:id", "host": ["{{base_url}}"], "path": ["api", "member", "admin", "plans", ":id"]}}}, {"name": "GET /admin/stats", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/admin/stats", "host": ["{{base_url}}"], "path": ["api", "member", "admin", "stats"]}}}, {"name": "POST /auto-renew", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/auto-renew", "host": ["{{base_url}}"], "path": ["api", "member", "auto-renew"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "会员专用功能", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/benefits", "host": ["{{base_url}}"], "path": ["api", "member", "benefits"]}}}, {"name": "POST /cancel", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/cancel", "host": ["{{base_url}}"], "path": ["api", "member", "cancel"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "GET /exclusive-content", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/exclusive-content", "host": ["{{base_url}}"], "path": ["api", "member", "exclusive-content"]}}}, {"name": "GET /my-history", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/my-history", "host": ["{{base_url}}"], "path": ["api", "member", "my-history"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "会员信息", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/my-membership", "host": ["{{base_url}}"], "path": ["api", "member", "my-membership"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "公开路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/plans", "host": ["{{base_url}}"], "path": ["api", "member", "plans"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "GET /plans/:id", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/plans/:id", "host": ["{{base_url}}"], "path": ["api", "member", "plans", ":id"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "POST /plans/compare", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/plans/compare", "host": ["{{base_url}}"], "path": ["api", "member", "plans", "compare"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "会员操作", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/subscribe", "host": ["{{base_url}}"], "path": ["api", "member", "subscribe"]}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}}}, {"name": "测试路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/member/test", "host": ["{{base_url}}"], "path": ["api", "member", "test"]}}}]}, {"name": "管理模块", "item": [{"name": "评论管理", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/comments", "host": ["{{base_url}}"], "path": ["api", "admin", "comments"]}}}, {"name": "仪表板和统计", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/dashboard/stats", "host": ["{{base_url}}"], "path": ["api", "admin", "dashboard", "stats"]}}}, {"name": "GET /statistics/access/detailed", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/statistics/access/detailed", "host": ["{{base_url}}"], "path": ["api", "admin", "statistics", "access", "detailed"]}}}, {"name": "统计分析", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/statistics/access/overview", "host": ["{{base_url}}"], "path": ["api", "admin", "statistics", "access", "overview"]}}}, {"name": "GET /statistics/content/popular", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/statistics/content/popular", "host": ["{{base_url}}"], "path": ["api", "admin", "statistics", "content", "popular"]}}}, {"name": "GET /statistics/revenue/detailed", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/statistics/revenue/detailed", "host": ["{{base_url}}"], "path": ["api", "admin", "statistics", "revenue", "detailed"]}}}, {"name": "GET /statistics/revenue/overview", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/statistics/revenue/overview", "host": ["{{base_url}}"], "path": ["api", "admin", "statistics", "revenue", "overview"]}}}, {"name": "GET /statistics/users/behavior", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/statistics/users/behavior", "host": ["{{base_url}}"], "path": ["api", "admin", "statistics", "users", "behavior"]}}}, {"name": "POST /system/cache/clear", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/system/cache/clear", "host": ["{{base_url}}"], "path": ["api", "admin", "system", "cache", "clear"]}}}, {"name": "GET /system/config", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/system/config", "host": ["{{base_url}}"], "path": ["api", "admin", "system", "config"]}}}, {"name": "PUT /system/config", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/system/config", "host": ["{{base_url}}"], "path": ["api", "admin", "system", "config"]}}}, {"name": "系统管理", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/system/logs", "host": ["{{base_url}}"], "path": ["api", "admin", "system", "logs"]}}}, {"name": "测试路由", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/test", "host": ["{{base_url}}"], "path": ["api", "admin", "test"]}}}, {"name": "用户管理", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/users", "host": ["{{base_url}}"], "path": ["api", "admin", "users"]}}}, {"name": "POST /users/batch", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/users/batch", "host": ["{{base_url}}"], "path": ["api", "admin", "users", "batch"]}}}, {"name": "视频管理", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/videos", "host": ["{{base_url}}"], "path": ["api", "admin", "videos"]}}}, {"name": "POST /videos/batch", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/admin/videos/batch", "host": ["{{base_url}}"], "path": ["api", "admin", "videos", "batch"]}}}]}]}