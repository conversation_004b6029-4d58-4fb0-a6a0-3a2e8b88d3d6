const express = require('express');
const router = express.Router();

// 导入控制器和中间件
const watchController = require('./controllers/watchController');
const { verifyToken, requireAdmin } = require('../../middleware/auth');
const { disabledRateLimiter } = require('../../middleware/rateLimiter');

// 所有观看记录路由都需要认证
router.use(verifyToken);

// 用户观看记录路由
router.post('/record',
  disabledRateLimiter,
  watchController.recordWatch
);

router.get('/history', 
  watchController.getUserWatchHistory
);

router.get('/stats', 
  watchController.getUserWatchStats
);

router.delete('/record/:videoId', 
  watchController.deleteWatchRecord
);

router.delete('/history/clear', 
  watchController.clearWatchHistory
);

// 管理员统计路由
router.get('/video/:videoId/stats',
  requireAdmin,
  watchController.getVideoWatchStats
);

router.get('/popular',
  requireAdmin,
  watchController.getPopularContent
);

// 测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '观看记录模块测试接口',
    module: 'watch',
    user: req.user || null
  });
});

module.exports = router;
