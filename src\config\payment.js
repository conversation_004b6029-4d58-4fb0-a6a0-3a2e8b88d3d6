/**
 * 支付配置
 * 支持易支付、微信支付、支付宝支付
 */

/**
 * 验证支付配置
 */
function validatePaymentConfig() {
  const config = getPaymentConfig();
  const errors = [];

  // 检查易支付配置
  if (config.epay.enabled) {
    if (!config.epay.partnerId) {
      errors.push('易支付商户ID未配置');
    }
    if (!config.epay.partnerKey) {
      errors.push('易支付商户密钥未配置');
    }
    if (!config.epay.apiUrl) {
      errors.push('易支付API地址未配置');
    }
  }

  // 检查微信支付配置
  if (config.wechat.enabled) {
    if (!config.wechat.appId) {
      errors.push('微信支付AppID未配置');
    }
    if (!config.wechat.mchId) {
      errors.push('微信支付商户号未配置');
    }
    if (!config.wechat.apiKey) {
      errors.push('微信支付API密钥未配置');
    }
  }

  // 检查支付宝配置
  if (config.alipay.enabled) {
    if (!config.alipay.appId) {
      errors.push('支付宝AppID未配置');
    }
    if (!config.alipay.privateKey) {
      errors.push('支付宝应用私钥未配置');
    }
    if (!config.alipay.alipayPublicKey) {
      errors.push('支付宝公钥未配置');
    }
  }

  // 检查Creem.io配置
  if (config.creem.enabled) {
    if (!config.creem.apiKey) {
      errors.push('Creem.io API Key未配置');
    }
    if (!config.creem.apiUrl) {
      errors.push('Creem.io API URL未配置');
    }
  }

  // 检查是否至少启用了一种支付方式
  const enabledMethods = [
    config.epay.enabled,
    config.wechat.enabled,
    config.alipay.enabled,
    config.creem.enabled,
  ].filter(Boolean);

  if (enabledMethods.length === 0) {
    errors.push('至少需要启用一种支付方式');
  }

  return errors;
}

/**
 * 获取启用的支付方式
 */
function getEnabledPaymentMethods() {
  const config = getPaymentConfig();
  const methods = [];

  if (config.epay.enabled) {
    methods.push('epay');
  }
  if (config.wechat.enabled) {
    methods.push('wechat');
  }
  if (config.alipay.enabled) {
    methods.push('alipay');
  }

  if (config.creem.enabled) {
    methods.push('creem');
  }

  return methods;
}

/**
 * 获取支付方式配置
 */
function getPaymentMethodConfig(method) {
  const config = getPaymentConfig();
  switch (method) {
    case 'epay':
      return config.epay;
    case 'wechat':
    case 'wxpay':
      return config.wechat;
    case 'alipay':
      return config.alipay;
    case 'creem':
      return config.creem;
    default:
      return null;
  }
}

/**
 * 检查支付金额是否有效
 */
function isValidPaymentAmount(amount) {
  const config = getPaymentConfig();
  const numAmount = parseFloat(amount);
  return numAmount >= config.common.minAmount && 
         numAmount <= config.common.maxAmount;
}

/**
 * 格式化支付金额
 */
function formatPaymentAmount(amount) {
  return parseFloat(amount).toFixed(2);
}

/**
 * 生成支付回调URL
 */
function generateNotifyUrl(method) {
  return `${process.env.API_BASE_URL}/api/payment/webhook/${method}`;
}

/**
 * 生成支付返回URL
 */
function generateReturnUrl(method) {
  return `${process.env.FRONTEND_URL}/payment/return?method=${method}`;
}


function getPaymentConfig() {
  return {
    epay: {
      enabled: process.env.EPAY_ENABLED === 'true',
      apiUrl: process.env.EPAY_API_URL || 'https://pay.example.com',
      partnerId: process.env.EPAY_PARTNER_ID,
      partnerKey: process.env.EPAY_PARTNER_KEY,
      notifyUrl: `${process.env.API_BASE_URL}/api/payment/webhook/epay`,
      returnUrl: `${process.env.FRONTEND_URL}/payment/return`
    },
    wechat: {
      enabled: process.env.WECHAT_PAY_ENABLED === 'true',
      appId: process.env.WECHAT_APP_ID,
      mchId: process.env.WECHAT_MCH_ID,
      apiKey: process.env.WECHAT_API_KEY,
      certPath: process.env.WECHAT_CERT_PATH,
      keyPath: process.env.WECHAT_KEY_PATH,
      notifyUrl: `${process.env.API_BASE_URL}/api/payment/webhook/wechat`
    },
    alipay: {
      enabled: process.env.ALIPAY_ENABLED === 'true',
      appId: process.env.ALIPAY_APP_ID,
      privateKey: process.env.ALIPAY_PRIVATE_KEY,
      alipayPublicKey: process.env.ALIPAY_PUBLIC_KEY,
      gatewayUrl: process.env.ALIPAY_GATEWAY_URL || 'https://openapi.alipay.com/gateway.do',
      notifyUrl: `${process.env.API_BASE_URL}/api/payment/webhook/alipay`,
      returnUrl: `${process.env.FRONTEND_URL}/payment/return`
    },
    common: {
      orderExpireMinutes: parseInt(process.env.ORDER_EXPIRE_MINUTES) || 30,
      paymentTimeoutSeconds: parseInt(process.env.PAYMENT_TIMEOUT_SECONDS) || 300,
      enablePaymentLog: process.env.ENABLE_PAYMENT_LOG !== 'false',
      successUrl: `${process.env.FRONTEND_URL}/payment/success`,
      failUrl: `${process.env.FRONTEND_URL}/payment/fail`,
      cancelUrl: `${process.env.FRONTEND_URL}/payment/cancel`,
      currency: process.env.PAYMENT_CURRENCY || 'CNY',
      minAmount: parseFloat(process.env.MIN_PAYMENT_AMOUNT) || 0.01,
      maxAmount: parseFloat(process.env.MAX_PAYMENT_AMOUNT) || 50000,
      supportedMethods: (process.env.SUPPORTED_PAYMENT_METHODS || 'epay,alipay,wechat').split(','),
      defaultMethod: process.env.DEFAULT_PAYMENT_METHOD || 'epay'
    },
    creem: {
      enabled: process.env.CREEM_ENABLED === 'true',
      apiKey: process.env.CREEM_MODE === 'live'
        ? process.env.CREEM_API_KEY_PROD
        : process.env.CREEM_API_KEY_DEV,
      apiUrl: process.env.CREEM_MODE === 'live'
        ? process.env.CREEM_BASE_URL_PROD || 'https://api.creem.io/v1'
        : process.env.CREEM_BASE_URL_DEV || 'https://api.creem.io/v1',
      webhookSecret: process.env.CREEM_WEBHOOK_SECRET,
    }
  };
}


module.exports = {
  getPaymentConfig,
  validatePaymentConfig,
  getEnabledPaymentMethods,
  getPaymentMethodConfig,
  isValidPaymentAmount,
  formatPaymentAmount,
  generateNotifyUrl,
  generateReturnUrl
};
