# 管理员账户管理指南

本指南详细说明如何创建、管理和维护视频平台的管理员账户。

## 📋 目录
- [默认管理员账户](#默认管理员账户)
- [创建管理员账户](#创建管理员账户)
- [管理员账户管理](#管理员账户管理)
- [密码重置](#密码重置)
- [安全最佳实践](#安全最佳实践)
- [故障排除](#故障排除)

## 🔑 默认管理员账户

### 自动创建
当您首次初始化数据库时，系统会自动创建一个默认管理员账户：

**默认账户信息**：
- **邮箱**: `<EMAIL>`
- **用户名**: `admin`
- **密码**: `Admin123456!`
- **角色**: `admin`
- **状态**: `active`

### 环境变量自定义
您可以通过环境变量自定义默认管理员信息：

```bash
# .env 文件
ADMIN_EMAIL=<EMAIL>
ADMIN_USERNAME=your_admin
ADMIN_PASSWORD=YourSecurePassword123!
```

### 初始化数据库
```bash
# 初始化数据库（会自动创建默认管理员）
node scripts/db-manager.js init

# 重置数据库（会重新创建默认管理员）
node scripts/db-manager.js reset
```

## 🔧 创建管理员账户

### 方法1：使用管理员管理工具（推荐）
```bash
# 交互式创建管理员账户
node scripts/admin-manager.js create
```

**功能特点**：
- 交互式输入界面
- 实时验证邮箱和用户名唯一性
- 密码强度检查
- 密码隐藏输入
- 双重密码确认

### 方法2：快速创建紧急管理员
```bash
# 创建紧急管理员账户
node scripts/quick-admin-reset.js emergency
```

**紧急管理员信息**：
- **邮箱**: `<EMAIL>`
- **用户名**: `emergency_admin`
- **密码**: `Emergency123456!`

## 📊 管理员账户管理

### 查看所有管理员
```bash
# 列出所有管理员账户
node scripts/admin-manager.js list
```

**显示信息**：
- 用户ID
- 邮箱地址
- 用户名
- 昵称
- 账户状态
- 邮箱验证状态
- 创建时间
- 最后登录时间

### 管理员权限
管理员账户具有以下权限：
- 用户管理（查看、禁用、删除用户）
- 视频管理（审核、删除、批量操作）
- 评论管理（审核、删除评论）
- 系统配置管理
- 数据统计查看
- 日志查看

## 🔄 密码重置

### 方法1：使用管理员管理工具
```bash
# 交互式重置管理员密码
node scripts/admin-manager.js reset
```

**功能**：
- 显示所有管理员列表
- 选择要重置的管理员
- 密码强度验证
- 双重密码确认

### 方法2：快速重置（紧急情况）
```bash
# 快速重置第一个管理员密码为默认密码
node scripts/quick-admin-reset.js reset
```

**注意**：此方法会将第一个管理员的密码重置为 `Admin123456!`

### 方法3：通过API重置（需要登录）
```bash
# 通过API修改密码
curl -X PUT http://localhost:3000/api/user/password \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "currentPassword": "current_password",
    "newPassword": "new_secure_password"
  }'
```

## 🔒 安全最佳实践

### 密码要求
- **最少8位字符**
- **包含大写字母** (A-Z)
- **包含小写字母** (a-z)
- **包含数字** (0-9)
- **包含特殊字符** (@$!%*?&)

### 安全建议
1. **立即修改默认密码**
   - 首次登录后立即修改密码
   - 不要在生产环境使用默认密码

2. **定期更换密码**
   - 建议每3-6个月更换一次
   - 避免重复使用旧密码

3. **启用双因子认证**（如果可用）
   - 增加账户安全性
   - 防止密码泄露风险

4. **限制管理员数量**
   - 只创建必要的管理员账户
   - 定期审查管理员列表

5. **监控登录活动**
   - 定期检查登录日志
   - 注意异常登录行为

### 账户安全检查
```bash
# 检查管理员账户状态
node scripts/admin-manager.js list

# 查看系统日志
tail -f logs/security.log
```

## 🚨 故障排除

### 常见问题

#### 1. 无法创建管理员账户
**可能原因**：
- 数据库连接失败
- 邮箱或用户名已存在
- 密码强度不足

**解决方案**：
```bash
# 检查数据库连接
node scripts/db-manager.js check

# 查看现有管理员
node scripts/admin-manager.js list

# 使用不同的邮箱和用户名
```

#### 2. 忘记管理员密码
**解决方案**：
```bash
# 快速重置密码
node scripts/quick-admin-reset.js reset

# 或使用管理员工具重置
node scripts/admin-manager.js reset
```

#### 3. 管理员账户被锁定
**解决方案**：
```bash
# 直接在数据库中解锁
mysql -u root -p
USE video_platform;
UPDATE users SET status = 'active' WHERE role = 'admin';
```

#### 4. 数据库中没有管理员
**解决方案**：
```bash
# 创建紧急管理员
node scripts/quick-admin-reset.js emergency

# 或重新初始化数据库
node scripts/db-manager.js reset
```

### 错误代码说明
- `EMAIL_EXISTS`: 邮箱已被注册
- `USERNAME_EXISTS`: 用户名已被使用
- `INVALID_PASSWORD`: 密码强度不足
- `USER_NOT_FOUND`: 用户不存在
- `ACCOUNT_DISABLED`: 账户已被禁用

### 日志文件位置
- **应用日志**: `logs/combined.log`
- **错误日志**: `logs/error.log`
- **安全日志**: `logs/security.log`
- **数据库日志**: `logs/database.log`

## 📞 技术支持

如果遇到无法解决的问题，请：

1. **检查日志文件**获取详细错误信息
2. **确认数据库连接**和配置正确
3. **验证环境变量**设置
4. **查看系统资源**使用情况

## 🔗 相关文档

- [部署指南](./DEPLOYMENT.md)
- [开发指南](./DEVELOPMENT.md)
- [API文档](./API.md)
- [安全配置](./SECURITY.md)

---

**重要提醒**：管理员账户拥有系统最高权限，请妥善保管账户信息，定期进行安全检查！
