require('dotenv').config();
const Favorite = require('./src/database/models/Favorite');

async function testFavorites() {
  try {
    console.log('开始测试收藏功能...');

    // 先测试简单的查询
    console.log('测试简单查询...');
    const simpleResult = await Favorite.query('SELECT COUNT(*) as count FROM favorites WHERE user_id = ?', [1]);
    console.log('简单查询结果:', simpleResult);

    // 检查表结构
    console.log('检查favorites表结构...');
    const favoritesStructure = await Favorite.query('DESCRIBE favorites');
    console.log('favorites表结构:', favoritesStructure);

    console.log('检查videos表结构...');
    const videosStructure = await Favorite.query('DESCRIBE videos');
    console.log('videos表结构:', videosStructure);

    // 检查实际数据
    console.log('检查favorites表数据...');
    const favoritesData = await Favorite.query('SELECT * FROM favorites LIMIT 1');
    console.log('favorites数据:', favoritesData);

    console.log('检查videos表数据...');
    const videosData = await Favorite.query('SELECT * FROM videos LIMIT 1');
    console.log('videos数据:', videosData);

    // 测试逐步添加JOIN
    console.log('测试单个JOIN...');
    const singleJoin = await Favorite.query(`
      SELECT f.*, v.title, v.status
      FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      WHERE f.user_id = ?
    `, [1]);
    console.log('单个JOIN结果:', singleJoin);

    console.log('测试双JOIN...');
    const doubleJoin = await Favorite.query(`
      SELECT f.*, v.title, v.status, u.username
      FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      LEFT JOIN users u ON v.user_id = u.id
      WHERE f.user_id = ?
    `, [1]);
    console.log('双JOIN结果:', doubleJoin);

    console.log('测试三JOIN...');
    const tripleJoin = await Favorite.query(`
      SELECT f.*, v.title, v.status, u.username, c.name as category_name
      FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      LEFT JOIN users u ON v.user_id = u.id
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE f.user_id = ?
    `, [1]);
    console.log('三JOIN结果:', tripleJoin);

    console.log('测试带参数的查询...');
    console.log('测试修复后的getUserFavorites方法...');
    try {
      const userFavorites = await Favorite.getUserFavorites(1, {
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'DESC'
      });
      console.log('用户收藏列表:', userFavorites);
    } catch (error) {
      console.log('getUserFavorites失败:', error.message);
    }

    console.log('测试LIMIT问题...');

    // 测试不带LIMIT的参数查询
    const noLimitQuery = await Favorite.query(`
      SELECT f.*, v.title, v.status
      FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      WHERE f.user_id = ? AND v.status = ?
    `, [1, 'published']);
    console.log('无LIMIT查询结果:', noLimitQuery);

    // 测试只有LIMIT的查询
    const onlyLimitQuery = await Favorite.query(`
      SELECT f.*, v.title, v.status
      FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      WHERE f.user_id = 1
      LIMIT ?
    `, [10]);
    console.log('只有LIMIT查询结果:', onlyLimitQuery);

  } catch (error) {
    console.error('测试失败:', error);
  }

  process.exit(0);
}

testFavorites();
