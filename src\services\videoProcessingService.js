const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs').promises;
const { promisify } = require('util');
const logger = require('../utils/logger');
const { cache, CACHE_KEYS } = require('../utils/cache');
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const ffprobePath = require('@ffprobe-installer/ffprobe').path;
const { toAbsolutePath } = require('../utils/pathResolver');

ffmpeg.setFfmpegPath(ffmpegPath);
ffmpeg.setFfprobePath(ffprobePath);

class VideoProcessingService {
  constructor() {
    this.processingQueue = new Map();
    this.outputFormats = ['mp4', 'webm'];
    this.qualities = {
      '360p': { width: 640, height: 360, bitrate: '800k' },
      '480p': { width: 854, height: 480, bitrate: '1200k' },
      '720p': { width: 1280, height: 720, bitrate: '2500k' },
      '1080p': { width: 1920, height: 1080, bitrate: '4000k' }
    };
    this.isFfmpegAvailable = false;
    this.checkFfmpeg();
  }

  // 检查FFmpeg是否可用
  async checkFfmpeg() {
    try {
      await promisify(ffmpeg.getAvailableCodecs)();
      this.isFfmpegAvailable = true;
      logger.info('FFmpeg检查通过，视频处理功能可用。');
    } catch (error) {
      this.isFfmpegAvailable = false;
      logger.error('FFmpeg检查失败！视频处理功能（如缩略图生成）将不可用。请确保已正确安装FFmpeg或修复@ffmpeg-installer/ffmpeg包的问题。');
      logger.error(error.message);
    }
  }

  // 抛出FFmpeg不可用错误
  throwFfmpegNotAvailableError() {
    if (!this.isFfmpegAvailable) {
      throw new Error('FFmpeg不可用，无法处理视频。请检查服务器配置和日志。');
    }
  }

  // 获取视频信息
  async getVideoInfo(inputPath) {
    this.throwFfmpegNotAvailableError();
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          logger.error('获取视频信息失败:', err);
          reject(err);
          return;
        }

        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');

        const info = {
          duration: metadata.format.duration,
          size: metadata.format.size,
          bitrate: metadata.format.bit_rate,
          format: metadata.format.format_name,
          video: videoStream ? {
            codec: videoStream.codec_name,
            width: videoStream.width,
            height: videoStream.height,
            fps: eval(videoStream.r_frame_rate),
            bitrate: videoStream.bit_rate
          } : null,
          audio: audioStream ? {
            codec: audioStream.codec_name,
            bitrate: audioStream.bit_rate,
            sampleRate: audioStream.sample_rate,
            channels: audioStream.channels
          } : null
        };

        resolve(info);
      });
    });
  }

  // 生成视频缩略图
  async generateThumbnail(inputPath, outputPath, options = {}) {
    this.throwFfmpegNotAvailableError();
    const {
      size = '640x360'
    } = options;

    return new Promise(async (resolve, reject) => {
      try {
        // 1. 获取视频信息以得到总时长
        const videoInfo = await this.getVideoInfo(inputPath);
        const duration = videoInfo.duration;
        if (!duration || duration <= 0) {
          throw new Error('无法获取有效的视频时长，无法生成缩略图。');
        }

        // 2. 计算25%位置的秒数
        const seekTimeInSeconds = duration * 0.25;

        // 3. 使用计算出的秒数来截图
        ffmpeg(inputPath)
          .seekInput(seekTimeInSeconds) // 定位到指定时间
          .frames(1) // 只取一帧
          .size(size)
          .format('image2') // 指定输出格式为图片
          .on('end', () => {
            logger.info(`缩略图生成完成: ${outputPath}`);
            resolve(outputPath);
          })
          .on('error', (err) => {
            logger.error(`缩略图生成失败: ${outputPath}`, err);
            reject(err);
          })
          .save(outputPath); // 严格按照指定的完整路径保存
      } catch (error) {
        reject(error);
      }
    });
  }

  // 转码视频到指定质量
  async transcodeVideo(inputPath, outputPath, quality = '720p') {
    const qualityConfig = this.qualities[quality];
    if (!qualityConfig) {
      throw new Error(`不支持的视频质量: ${quality}`);
    }

    return new Promise((resolve, reject) => {
      const command = ffmpeg(inputPath)
        .videoCodec('libx264')
        .audioCodec('aac')
        .size(`${qualityConfig.width}x${qualityConfig.height}`)
        .videoBitrate(qualityConfig.bitrate)
        .audioBitrate('128k')
        .format('mp4')
        .outputOptions([
          '-preset fast',
          '-crf 23',
          '-movflags +faststart'
        ]);

      command
        .on('start', (commandLine) => {
          logger.info(`开始转码: ${commandLine}`);
        })
        .on('progress', (progress) => {
          logger.debug(`转码进度: ${progress.percent}%`);
        })
        .on('end', () => {
          logger.info(`转码完成: ${outputPath}`);
          resolve(outputPath);
        })
        .on('error', (err) => {
          logger.error('转码失败:', err);
          reject(err);
        })
        .save(outputPath);
    });
  }

  // 生成HLS流媒体文件
  async generateHLS(inputPath, outputDir) {
    const playlistPath = path.join(outputDir, 'playlist.m3u8');
    
    return new Promise((resolve, reject) => {
      // 确保输出目录存在
      fs.mkdir(outputDir, { recursive: true }).then(() => {
        ffmpeg(inputPath)
          .outputOptions([
            '-c:v libx264',
            '-c:a aac',
            '-hls_time 10',
            '-hls_list_size 0',
            '-hls_segment_filename ' + path.join(outputDir, 'segment_%03d.ts'),
            '-f hls'
          ])
          .on('start', (commandLine) => {
            logger.info(`开始生成HLS: ${commandLine}`);
          })
          .on('end', () => {
            logger.info(`HLS生成完成: ${playlistPath}`);
            resolve(playlistPath);
          })
          .on('error', (err) => {
            logger.error('HLS生成失败:', err);
            reject(err);
          })
          .save(playlistPath);
      }).catch(reject);
    });
  }

  // 批量转码多种质量
  async transcodeMultipleQualities(inputPath, outputDir, qualities = ['360p', '720p']) {
    const results = {};
    
    // 确保输出目录存在
    await fs.mkdir(outputDir, { recursive: true });

    for (const quality of qualities) {
      try {
        const outputPath = path.join(outputDir, `video_${quality}.mp4`);
        await this.transcodeVideo(inputPath, outputPath, quality);
        results[quality] = outputPath;
      } catch (error) {
        logger.error(`转码${quality}失败:`, error);
        results[quality] = null;
      }
    }

    return results;
  }

  // 压缩视频
  async compressVideo(inputPath, outputPath, compressionLevel = 'medium') {
    const compressionSettings = {
      low: { crf: 28, preset: 'fast' },
      medium: { crf: 23, preset: 'medium' },
      high: { crf: 18, preset: 'slow' }
    };

    const settings = compressionSettings[compressionLevel] || compressionSettings.medium;

    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .videoCodec('libx264')
        .audioCodec('aac')
        .outputOptions([
          `-crf ${settings.crf}`,
          `-preset ${settings.preset}`,
          '-movflags +faststart'
        ])
        .on('start', (commandLine) => {
          logger.info(`开始压缩: ${commandLine}`);
        })
        .on('progress', (progress) => {
          logger.debug(`压缩进度: ${progress.percent}%`);
        })
        .on('end', () => {
          logger.info(`压缩完成: ${outputPath}`);
          resolve(outputPath);
        })
        .on('error', (err) => {
          logger.error('压缩失败:', err);
          reject(err);
        })
        .save(outputPath);
    });
  }

  // 提取音频
  async extractAudio(inputPath, outputPath, format = 'mp3') {
    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .noVideo()
        .audioCodec(format === 'mp3' ? 'libmp3lame' : 'aac')
        .audioBitrate('128k')
        .format(format)
        .on('start', (commandLine) => {
          logger.info(`开始提取音频: ${commandLine}`);
        })
        .on('end', () => {
          logger.info(`音频提取完成: ${outputPath}`);
          resolve(outputPath);
        })
        .on('error', (err) => {
          logger.error('音频提取失败:', err);
          reject(err);
        })
        .save(outputPath);
    });
  }

  // 处理音频文件
  async processAudio(inputPath, outputPath, options = {}) {
    const {
      bitrate = '128k',
      sampleRate = 44100,
      channels = 2,
      format = 'mp3',
      normalize = true
    } = options;

    return new Promise((resolve, reject) => {
      let command = ffmpeg(inputPath)
        .audioCodec(format === 'mp3' ? 'libmp3lame' : 'aac')
        .audioBitrate(bitrate)
        .audioFrequency(sampleRate)
        .audioChannels(channels)
        .format(format);

      // 音频标准化
      if (normalize) {
        command = command.audioFilters('loudnorm');
      }

      command
        .on('start', (commandLine) => {
          logger.info(`开始处理音频: ${commandLine}`);
        })
        .on('progress', (progress) => {
          logger.debug(`音频处理进度: ${progress.percent}%`);
        })
        .on('end', () => {
          logger.info(`音频处理完成: ${outputPath}`);
          resolve(outputPath);
        })
        .on('error', (err) => {
          logger.error('音频处理失败:', err);
          reject(err);
        })
        .save(outputPath);
    });
  }

  // 获取音频元数据
  async getAudioMetadata(inputPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          logger.error('获取音频元数据失败:', err);
          reject(err);
          return;
        }

        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
        if (!audioStream) {
          reject(new Error('未找到音频流'));
          return;
        }

        const audioInfo = {
          duration: parseFloat(metadata.format.duration) || 0,
          bitrate: parseInt(audioStream.bit_rate) || 0,
          sampleRate: parseInt(audioStream.sample_rate) || 0,
          channels: audioStream.channels || 0,
          format: metadata.format.format_name,
          codec: audioStream.codec_name,
          size: parseInt(metadata.format.size) || 0
        };

        logger.info(`音频元数据获取成功: ${JSON.stringify(audioInfo)}`);
        resolve(audioInfo);
      });
    });
  }

  // 生成音频波形图
  async generateWaveform(videoPath, videoId) {
    const waveformDir = toAbsolutePath(path.join('uploads', 'waveforms'));
    await fs.mkdir(waveformDir, { recursive: true });
    const waveformPath = path.join(waveformDir, `${videoId}-waveform.png`);

    return new Promise((resolve, reject) => {
      ffmpeg(videoPath)
        .complexFilter([
          `[0:a]showwavespic=s=${width}x${height}:colors=${colors}[v]`
        ])
        .map('[v]')
        .format('png')
        .on('start', (commandLine) => {
          logger.info(`开始生成波形图: ${commandLine}`);
        })
        .on('end', () => {
          logger.info(`波形图生成完成: ${waveformPath}`);
          resolve(waveformPath);
        })
        .on('error', (err) => {
          logger.error('波形图生成失败:', err);
          reject(err);
        })
        .save(waveformPath);
    });
  }

  // 完整的媒体处理流程（视频和音频）
  async processMedia(mediaId, inputPath, mediaType = 'video', options = {}) {
    if (mediaType === 'audio') {
      return await this.processAudioMedia(mediaId, inputPath, options);
    } else {
      return await this.processVideoMedia(mediaId, inputPath, options);
    }
  }

  // 完整的视频处理流程
  async processVideoMedia(videoId, inputPath, options = {}) {
    const {
      generateThumbnails = true,
      transcodeQualities = ['360p', '720p'],
      generateHLS = false,
      compress = true
    } = options;

    try {
      logger.info(`开始处理视频: ${videoId}`);
      
      // 设置处理状态
      this.processingQueue.set(videoId, { status: 'processing', progress: 0 });

      // 获取视频信息
      const videoInfo = await this.getVideoInfo(inputPath);
      logger.info(`视频信息:`, videoInfo);

      const outputDir = path.join(path.dirname(inputPath), `processed_${videoId}`);
      await fs.mkdir(outputDir, { recursive: true });

      const results = {
        videoInfo,
        thumbnails: null,
        transcoded: {},
        hls: null,
        compressed: null
      };

      // 更新进度
      this.processingQueue.set(videoId, { status: 'processing', progress: 20 });

      // 生成缩略图
      if (generateThumbnails) {
        try {
          const thumbnailDir = path.join(outputDir, 'thumbnails');
          await fs.mkdir(thumbnailDir, { recursive: true });
          results.thumbnails = await this.generateThumbnail(
            inputPath, 
            path.join(thumbnailDir, 'thumb.jpg')
          );
        } catch (error) {
          logger.error('缩略图生成失败:', error);
        }
      }

      // 更新进度
      this.processingQueue.set(videoId, { status: 'processing', progress: 40 });

      // 转码多种质量
      if (transcodeQualities.length > 0) {
        try {
          results.transcoded = await this.transcodeMultipleQualities(
            inputPath, 
            outputDir, 
            transcodeQualities
          );
        } catch (error) {
          logger.error('转码失败:', error);
        }
      }

      // 更新进度
      this.processingQueue.set(videoId, { status: 'processing', progress: 70 });

      // 生成HLS
      if (generateHLS) {
        try {
          const hlsDir = path.join(outputDir, 'hls');
          results.hls = await this.generateHLS(inputPath, hlsDir);
        } catch (error) {
          logger.error('HLS生成失败:', error);
        }
      }

      // 压缩原视频
      if (compress) {
        try {
          const compressedPath = path.join(outputDir, 'compressed.mp4');
          results.compressed = await this.compressVideo(inputPath, compressedPath);
        } catch (error) {
          logger.error('视频压缩失败:', error);
        }
      }

      // 完成处理
      this.processingQueue.set(videoId, { status: 'completed', progress: 100 });
      
      logger.info(`视频处理完成: ${videoId}`);
      return results;

    } catch (error) {
      this.processingQueue.set(videoId, { status: 'failed', progress: 0, error: error.message });
      logger.error(`视频处理失败: ${videoId}`, error);
      throw error;
    }
  }

  // 获取处理状态
  getProcessingStatus(videoId) {
    return this.processingQueue.get(videoId) || { status: 'not_found' };
  }

  // 清理处理状态
  clearProcessingStatus(videoId) {
    this.processingQueue.delete(videoId);
  }

  // 获取支持的格式
  getSupportedFormats() {
    return {
      input: ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv', '3gp'],
      output: ['mp4', 'webm', 'avi'],
      audio: ['mp3', 'aac', 'wav', 'ogg']
    };
  }

  // 验证视频格式
  isValidVideoFormat(filename) {
    const ext = path.extname(filename).toLowerCase().substring(1);
    return this.getSupportedFormats().input.includes(ext);
  }

  // 完整的音频处理流程
  async processAudioMedia(audioId, inputPath, options = {}) {
    const {
      generateWaveform = true,
      processQualities = ['128k', '192k', '320k'],
      normalize = true
    } = options;

    try {
      logger.info(`开始处理音频: ${audioId}`);
      const results = {};

      // 1. 获取音频元数据
      const metadata = await this.getAudioMetadata(inputPath);
      results.metadata = metadata;

      // 2. 生成处理后的音频文件
      const outputDir = path.dirname(inputPath).replace('original', 'processed');
      await this.ensureDirectory(outputDir);

      const processedFiles = {};
      for (const quality of processQualities) {
        const outputPath = path.join(outputDir, `${audioId}_${quality}.mp3`);
        try {
          await this.processAudio(inputPath, outputPath, {
            bitrate: quality,
            normalize,
            format: 'mp3'
          });
          processedFiles[quality] = outputPath;
        } catch (error) {
          logger.warn(`音频质量 ${quality} 处理失败:`, error);
        }
      }
      results.processedFiles = processedFiles;

      // 3. 生成波形图
      if (generateWaveform) {
        const waveformDir = path.join(process.cwd(), 'uploads', 'waveforms');
        await this.ensureDirectory(waveformDir);

        const waveformPath = path.join(waveformDir, `${audioId}_waveform.png`);
        try {
          await this.generateWaveform(inputPath, audioId);
          results.waveform = waveformPath;
        } catch (error) {
          logger.warn('波形图生成失败:', error);
        }
      }

      logger.info(`音频处理完成: ${audioId}`);
      return results;

    } catch (error) {
      logger.error(`音频处理失败: ${audioId}`, error);
      throw error;
    }
  }

  // 向后兼容的方法
  async processVideo(videoId, inputPath, options = {}) {
    return await this.processVideoMedia(videoId, inputPath, options);
  }

  // 清理临时文件
  async cleanupTempFiles(videoId) {
    try {
      const tempDir = path.join(process.cwd(), 'uploads', 'temp', videoId.toString());
      await fs.rmdir(tempDir, { recursive: true });
      logger.info(`清理临时文件: ${tempDir}`);
    } catch (error) {
      logger.warn(`清理临时文件失败: ${error.message}`);
    }
  }

  // 为HLS流创建临时转码目录
  async createTempDirForHLS(videoId) {
    const tempDir = toAbsolutePath(path.join('uploads', 'temp', videoId.toString()));
    await fs.mkdir(tempDir, { recursive: true });
    return tempDir;
  }
}

module.exports = new VideoProcessingService();
