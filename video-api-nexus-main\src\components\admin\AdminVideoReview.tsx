import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '@/services/adminApi';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import LoadingSpinner from '@/components/LoadingSpinner';
import { Badge } from '@/components/ui/badge';

export default function AdminVideoReview() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [page, setPage] = useState(1);
  const [rejectionInfo, setRejectionInfo] = useState<{ id: number; title: string } | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [previewVideo, setPreviewVideo] = useState<any>(null);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['pending-videos', page],
    queryFn: () => adminApi.getPendingVideos({ page, pageSize: 10 }),
    placeholderData: (previousData) => previousData,
  });

  const reviewMutation = useMutation({
    mutationFn: (variables: { id: number, data: { action: string, reason?: string } }) => adminApi.reviewVideo(variables.id, variables.data),
    onSuccess: (response) => {
      // 检查响应是否成功
      if (response.data.success) {
        queryClient.invalidateQueries({ queryKey: ['pending-videos'] });
        toast({ title: '操作成功', description: '审核状态已更新' });
        setRejectionInfo(null);
        setRejectionReason('');
      } else {
        toast({ title: '操作失败', description: response.data.message || '审核失败，请重试', variant: 'destructive' });
      }
    },
    onError: (err: any) => {
      toast({ title: '操作失败', description: err.response?.data?.message || err.message || '审核失败，请重试', variant: 'destructive' });
    },
  });

  const handleApprove = (id: number) => {
    reviewMutation.mutate({ id, data: { action: 'approve' } });
  };

  const handleOpenRejectDialog = (id: number, title: string) => {
    setRejectionInfo({ id, title });
  };

  const handleReject = () => {
    if (rejectionInfo && rejectionReason) {
      reviewMutation.mutate({ id: rejectionInfo.id, data: { action: 'reject', reason: rejectionReason } });
    }
  };

  if (isLoading) return <LoadingSpinner />;
  if (isError) return <p>错误: {(error as any).message}</p>;

  // 修复响应数据解析 - axios返回的数据在response.data中
  const videos = data?.data?.data?.videos || [];
  const pagination = data?.data?.data?.pagination || {};

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">视频审核</h1>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>标题</TableHead>
            <TableHead>上传者</TableHead>
            <TableHead>提交时间</TableHead>
            <TableHead>操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {videos.map((video: any) => (
            <TableRow key={video.id}>
              <TableCell>{video.id}</TableCell>
              <TableCell>{video.title}</TableCell>
              <TableCell>{video.uploader?.username || 'N/A'}</TableCell>
              <TableCell>{new Date(video.created_at).toLocaleString()}</TableCell>
              <TableCell>
                <Button size="sm" variant="outline" className="mr-2" onClick={() => setPreviewVideo(video)}>
                  预览
                </Button>
                <Button size="sm" onClick={() => handleApprove(video.id)}>批准</Button>
                <Button size="sm" variant="destructive" className="ml-2" onClick={() => handleOpenRejectDialog(video.id, video.title)}>
                  拒绝
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {/* Pagination Controls */}
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPage(old => Math.max(old - 1, 1))}
          disabled={!pagination.hasPrev}
        >
          上一页
        </Button>
        <span>第 {pagination.page || 1} / {pagination.totalPages || 1} 页</span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPage(old => old + 1)}
          disabled={!pagination.hasNext}
        >
          下一页
        </Button>
      </div>

      {/* Preview Dialog */}
      <Dialog open={!!previewVideo} onOpenChange={() => setPreviewVideo(null)}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>预览: {previewVideo?.title}</DialogTitle>
          </DialogHeader>
          {previewVideo?.url && (
            <video controls src={previewVideo.url} className="w-full rounded-lg" />
          )}
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog open={!!rejectionInfo} onOpenChange={() => setRejectionInfo(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>拒绝视频: "{rejectionInfo?.title}"</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Label htmlFor="reason">拒绝理由</Label>
            <Textarea
              id="reason"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="请填写拒绝该视频的理由..."
            />
          </div>
          <DialogFooter>
            <Button variant="ghost" onClick={() => setRejectionInfo(null)}>取消</Button>
            <Button onClick={handleReject} disabled={!rejectionReason.trim() || reviewMutation.isPending}>
              确认拒绝
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}