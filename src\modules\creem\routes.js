const express = require('express');
const router = express.Router();
const creemController = require('./controllers/creemController');
const { requireAdmin } = require('../../middleware/auth');

// 公开接口：获取所有creem计划（不需要认证）
router.get('/plans', creemController.getAllPlans);

// 管理员接口：需要认证
router.post('/', requireAdmin, creemController.createPlan);
router.put('/:id', requireAdmin, creemController.updatePlan);
router.delete('/:id', requireAdmin, creemController.deletePlan);

module.exports = router; 