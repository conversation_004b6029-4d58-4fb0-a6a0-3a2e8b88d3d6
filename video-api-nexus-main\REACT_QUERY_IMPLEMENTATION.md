# React Query 缓存实现文档

## 概述

本项目已成功集成 React Query (@tanstack/react-query) 作为前端数据缓存解决方案，替代了原有的手动状态管理方式。

## 实现的功能

### 1. 视频数据缓存
- **文件**: `src/hooks/queries/useVideos.ts`
- **功能**: 
  - `useVideos()` - 获取视频列表
  - `useVideoDetail()` - 获取视频详情
  - `usePopularVideos()` - 获取热门视频
  - `useRecommendedVideos()` - 获取推荐视频
  - `useSearchVideos()` - 搜索视频
  - `usePrefetchVideoDetail()` - 预取视频详情

### 2. 分类数据缓存
- **文件**: `src/hooks/queries/useCategories.ts`
- **功能**:
  - `useCategories()` - 获取分类列表
  - `useCategoryTree()` - 获取分类树
  - `useCategoryDetail()` - 获取分类详情
  - `useSearchCategories()` - 搜索分类

### 3. 交互状态缓存
- **文件**: `src/hooks/queries/useInteractions.ts`
- **功能**:
  - `useBatchCheckInteractions()` - 批量检查交互状态
  - `useUserFavorites()` - 获取用户收藏
  - `useLikeMutation()` - 点赞操作（支持乐观更新）
  - `useFavoriteMutation()` - 收藏操作（支持乐观更新）

## 缓存策略

### 缓存时间配置
- **视频列表**: 2分钟 staleTime, 5分钟 cacheTime
- **视频详情**: 5分钟 staleTime, 10分钟 cacheTime
- **分类数据**: 15分钟 staleTime, 30分钟 cacheTime
- **交互状态**: 2分钟 staleTime, 5分钟 cacheTime

### 查询键策略
使用工厂模式管理查询键，确保缓存的精确控制：
```typescript
export const videoKeys = {
  all: ['videos'] as const,
  lists: () => [...videoKeys.all, 'list'] as const,
  list: (filters: any) => [...videoKeys.lists(), filters] as const,
  details: () => [...videoKeys.all, 'detail'] as const,
  detail: (id: string | number) => [...videoKeys.details(), id] as const,
};
```

## 乐观更新

### 点赞操作
- 立即更新UI显示
- 失败时自动回滚
- 成功后同步服务器状态

### 收藏操作
- 立即更新UI显示
- 自动失效相关查询
- 显示操作结果提示

## 重构的组件

### 1. UserApp.tsx
**之前**: 使用 `useState` + `useEffect` + 手动API调用
```typescript
const [videos, setVideos] = useState([]);
const [loading, setLoading] = useState(true);
useEffect(() => {
  fetchVideos();
}, []);
```

**现在**: 使用 React Query hooks
```typescript
const { data: videos = [], isLoading: loading } = useVideos({});
```

### 2. VideoDetail.tsx
**之前**: 手动管理视频详情和交互状态
**现在**: 使用 `useVideoDetail` 和 `useBatchCheckInteractions`

### 3. VideoCard.tsx
**之前**: 直接调用API进行点赞/收藏操作
**现在**: 使用 `useLikeMutation` 和 `useFavoriteMutation`

### 4. useVideoInteractions.tsx
**之前**: 复杂的状态管理和API调用逻辑
**现在**: 简化为React Query hooks的封装

## 性能优化

### 1. 自动重试
- 查询失败时自动重试2次
- 变更操作失败时重试1次

### 2. 智能重新获取
- 窗口聚焦时不自动重新获取（避免不必要的请求）
- 网络重连时自动重新获取

### 3. 缓存失效策略
- 点赞/收藏操作后自动失效相关查询
- 使用查询键模式精确控制失效范围

## 测试

访问 `/react-query-test` 路由可以查看React Query的实现效果：
- 实时显示缓存状态
- 测试各种查询功能
- 验证错误处理机制

## 向后兼容

所有UI组件保持完全不变，只是底层数据获取方式从手动状态管理改为React Query缓存。用户体验完全一致，但性能得到显著提升。

## 优势

1. **自动缓存管理** - 无需手动管理loading、error状态
2. **智能重新验证** - 自动保持数据新鲜度
3. **乐观更新** - 操作响应更快
4. **错误处理** - 统一的错误处理和重试机制
5. **开发体验** - 更简洁的代码，更少的样板代码
6. **性能提升** - 减少不必要的API请求

## 下一步优化

1. 添加离线支持
2. 实现无限滚动查询
3. 添加更多预取策略
4. 优化缓存失效策略
