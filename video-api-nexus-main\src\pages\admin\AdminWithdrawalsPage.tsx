import React, { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '@/services/adminApi';
import { keepPreviousData } from '@tanstack/react-query';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDebounce } from '@/hooks/useDebounce';
import { format } from 'date-fns';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import ApproveWithdrawalDialog from '@/components/admin/ApproveWithdrawalDialog';
import RejectWithdrawalDialog from '@/components/admin/RejectWithdrawalDialog';


const WITHDRAWAL_STATUS_MAP = {
  pending: { label: '待处理', variant: 'secondary' },
  approved: { label: '已批准', variant: 'primary' },
  completed: { label: '已完成', variant: 'success' },
  rejected: { label: '已拒绝', variant: 'destructive' },
  failed: { label: '已失败', variant: 'destructive' },
};

const getStatusBadge = (status) => {
  const statusInfo = WITHDRAWAL_STATUS_MAP[status];
  if (!statusInfo) return <Badge variant="outline" className="text-xs px-2 py-1">未知</Badge>;
  
  // 使用更统一的按钮风格样式
  const baseClasses = "text-xs px-3 py-1 rounded-md font-medium";
  const variantClasses = {
    secondary: "bg-gray-100 text-gray-700 hover:bg-gray-200",
    primary: "bg-blue-100 text-blue-700 hover:bg-blue-200", 
    success: "bg-green-100 text-green-700 hover:bg-green-200",
    destructive: "bg-red-100 text-red-700 hover:bg-red-200"
  };
  
  return (
    <span className={`${baseClasses} ${variantClasses[statusInfo.variant] || variantClasses.secondary}`}>
      {statusInfo.label}
    </span>
  );
};

const AdminWithdrawalsPage = () => {
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState({ status: '', keyword: '' });
  const debouncedKeyword = useDebounce(filters.keyword, 500);

  const queryClient = useQueryClient();

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['adminWithdrawals', page, filters.status, debouncedKeyword],
    queryFn: async () => {
      try {
        const response = await adminApi.getWithdrawals({ 
          page, 
          limit: 10, 
          status: filters.status || undefined, 
          keyword: debouncedKeyword || undefined 
        });
        return response.data;
      } catch (err) {
        console.error('[Frontend] 提现管理API请求失败:', err);
        throw err;
      }
    },
    placeholderData: keepPreviousData,
  });

  // 简化的数据验证日志
  React.useEffect(() => {
    if (data?.data && Array.isArray(data.data.data)) {
      console.log(`[Frontend] 提现管理 - 成功加载 ${data.data.data.length} 条记录`);
    }
  }, [data]);

  const [selectedWithdrawal, setSelectedWithdrawal] = useState(null);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);

  const handleApprove = (withdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setIsApproveDialogOpen(true);
  };

  const handleReject = (withdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setIsRejectDialogOpen(true);
  };

  const handleSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['adminWithdrawals'] });
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">提现管理</h1>
      
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <Input
              placeholder="搜索用户或钱包地址..."
              value={filters.keyword}
              onChange={(e) => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
              className="max-w-sm"
            />
            <Select
              value={filters.status}
              onValueChange={(value) => setFilters(prev => ({ ...prev, status: value === 'all' ? '' : value }))}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="筛选状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                {Object.entries(WITHDRAWAL_STATUS_MAP).map(([key, { label }]) => (
                  <SelectItem key={key} value={key}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-48">用户</TableHead>
                <TableHead className="w-24">金额</TableHead>
                <TableHead className="w-24">状态</TableHead>
                <TableHead className="w-80">钱包地址</TableHead>
                <TableHead className="w-32">申请时间</TableHead>
                <TableHead className="w-32">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow><TableCell colSpan={6} className="text-center">加载中...</TableCell></TableRow>
              ) : isError ? (
                <TableRow><TableCell colSpan={6} className="text-center text-red-500">{error.message}</TableCell></TableRow>
              ) : !data?.data?.data || !Array.isArray(data.data.data) || data.data.data.length === 0 ? (
                <TableRow><TableCell colSpan={6} className="text-center">没有找到提现请求</TableCell></TableRow>
              ) : (
                data.data.data.map((w) => (
                  <TableRow key={w.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium text-sm">{w.username}</div>
                        <div className="text-xs text-muted-foreground truncate max-w-44">{w.email}</div>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium text-sm">¥{w.amount}</TableCell>
                    <TableCell>{getStatusBadge(w.status)}</TableCell>
                    <TableCell className="font-mono text-xs">
                      <div className="break-all leading-tight" title={w.wallet_address}>
                        {w.wallet_address}
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">{format(new Date(w.requested_at), 'MM-dd HH:mm')}</TableCell>
                    <TableCell className="w-32">
                      {w.status === 'pending' && (
                        <div className="flex gap-1">
                          <Button 
                            size="sm" 
                            variant="default"
                            className="h-8 px-3 text-xs bg-green-600 hover:bg-green-700 text-white"
                            onClick={() => handleApprove(w)}
                          >
                            批准
                          </Button>
                          <Button 
                            size="sm" 
                            variant="destructive" 
                            className="h-8 px-3 text-xs"
                            onClick={() => handleReject(w)}
                          >
                            拒绝
                          </Button>
                        </div>
                      )}
                      {w.status !== 'pending' && (
                        <span className="text-sm text-muted-foreground">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {data?.data && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" onClick={(e) => { e.preventDefault(); if (page > 1) setPage(page - 1); }} />
            </PaginationItem>
            {[...Array(data.data.totalPages).keys()].map(num => (
              <PaginationItem key={num + 1}>
                <PaginationLink href="#" isActive={page === num + 1} onClick={(e) => { e.preventDefault(); setPage(num + 1); }}>
                  {num + 1}
                </PaginationLink>
              </PaginationItem>
            ))}
            <PaginationItem>
              <PaginationNext href="#" onClick={(e) => { e.preventDefault(); if (page < data.data.totalPages) setPage(page + 1); }} />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}

      <ApproveWithdrawalDialog
        open={isApproveDialogOpen}
        onOpenChange={setIsApproveDialogOpen}
        withdrawal={selectedWithdrawal}
        onSuccess={handleSuccess}
      />

      <RejectWithdrawalDialog
        open={isRejectDialogOpen}
        onOpenChange={setIsRejectDialogOpen}
        withdrawal={selectedWithdrawal}
        onSuccess={handleSuccess}
      />
    </div>
  );
};

export default AdminWithdrawalsPage; 