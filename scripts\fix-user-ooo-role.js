const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'video_user',
  password: process.env.DB_PASSWORD || 'secure_password',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

async function fixUserOOORole() {
  let connection;
  
  try {
    console.log('🔧 修复用户OOO的角色...\n');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    
    // 开始事务
    await connection.beginTransaction();
    
    // 查找用户OOO
    const [users] = await connection.execute(`
      SELECT id, username, role FROM users WHERE username = 'OOO'
    `);
    
    if (users.length === 0) {
      console.log('❌ 未找到用户OOO');
      return;
    }
    
    const user = users[0];
    console.log(`👤 找到用户: ${user.username} (ID: ${user.id})`);
    console.log(`   当前角色: ${user.role}`);
    
    // 检查是否有有效的会员记录
    const [memberships] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM memberships 
      WHERE user_id = ? AND status = 'active' AND end_date > NOW()
    `, [user.id]);
    
    const hasActiveMembership = memberships[0].count > 0;
    console.log(`   有效会员记录: ${hasActiveMembership ? '是' : '否'}`);
    
    if (hasActiveMembership && user.role !== 'member') {
      // 更新用户角色为member
      await connection.execute(`
        UPDATE users SET role = 'member', updated_at = NOW() WHERE id = ?
      `, [user.id]);
      
      console.log('✅ 已将用户OOO的角色更新为 member');
      
      // 提交事务
      await connection.commit();
      
      // 验证更新结果
      const [updatedUsers] = await connection.execute(`
        SELECT username, role, updated_at FROM users WHERE id = ?
      `, [user.id]);
      
      if (updatedUsers.length > 0) {
        const updatedUser = updatedUsers[0];
        console.log(`\n🎉 更新成功:`);
        console.log(`   用户名: ${updatedUser.username}`);
        console.log(`   新角色: ${updatedUser.role}`);
        console.log(`   更新时间: ${updatedUser.updated_at}`);
      }
      
    } else if (!hasActiveMembership && user.role !== 'user') {
      // 更新用户角色为user
      await connection.execute(`
        UPDATE users SET role = 'user', updated_at = NOW() WHERE id = ?
      `, [user.id]);
      
      console.log('✅ 已将用户OOO的角色更新为 user');
      await connection.commit();
      
    } else {
      console.log('ℹ️  用户角色已经正确，无需更新');
      await connection.rollback();
    }
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error);
    if (connection) {
      await connection.rollback();
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixUserOOORole()
    .then(() => {
      console.log('\n🎉 修复完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { fixUserOOORole };
