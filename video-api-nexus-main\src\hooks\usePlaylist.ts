import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Playlist, 
  PlaylistItem, 
  PlaylistState, 
  PlaylistActions, 
  UsePlaylistOptions,
  STORAGE_KEYS,
  PlaylistSettings 
} from '@/types/playlist';

// 默认播放列表设置
const DEFAULT_SETTINGS: PlaylistSettings = {
  autoPlay: true,
  shuffleOnRepeat: false,
  saveHistory: true,
  maxHistoryItems: 100,
};

// 生成唯一ID
const generateId = () => `playlist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

export const usePlaylist = (options: UsePlaylistOptions = {}) => {
  const {
    onPlaylistEnd,
    onItemChange,
    onPlayModeChange,
    autoSave = true,
  } = options;

  const [state, setState] = useState<PlaylistState>({
    currentPlaylist: null,
    savedPlaylists: [],
    isLoading: true,
    error: null,
  });

  const [settings, setSettings] = useState<PlaylistSettings>(DEFAULT_SETTINGS);
  const previousItemRef = useRef<PlaylistItem | null>(null);

  // 从本地存储加载数据
  const loadFromStorage = useCallback(() => {
    try {
      // 加载当前播放列表
      const currentPlaylistData = localStorage.getItem(STORAGE_KEYS.CURRENT_PLAYLIST);
      const currentPlaylist = currentPlaylistData ? JSON.parse(currentPlaylistData) : null;

      // 加载保存的播放列表
      const savedPlaylistsData = localStorage.getItem(STORAGE_KEYS.SAVED_PLAYLISTS);
      const savedPlaylists = savedPlaylistsData ? JSON.parse(savedPlaylistsData) : [];

      // 加载设置
      const settingsData = localStorage.getItem(STORAGE_KEYS.PLAYLIST_SETTINGS);
      const loadedSettings = settingsData ? JSON.parse(settingsData) : DEFAULT_SETTINGS;

      setState({
        currentPlaylist,
        savedPlaylists,
        isLoading: false,
        error: null,
      });

      setSettings(loadedSettings);
    } catch (error) {
      console.error('加载播放列表数据失败:', error);
      setState({
        currentPlaylist: null,
        savedPlaylists: [],
        isLoading: false,
        error: '加载播放列表数据失败',
      });
    }
  }, []);

  // 保存到本地存储
  const saveToStorage = useCallback((data: Partial<Pick<PlaylistState, 'currentPlaylist' | 'savedPlaylists'>>) => {
    if (!autoSave) return;
    try {
      if (data.currentPlaylist !== undefined) {
        if (data.currentPlaylist) {
          localStorage.setItem(STORAGE_KEYS.CURRENT_PLAYLIST, JSON.stringify(data.currentPlaylist));
        } else {
          localStorage.removeItem(STORAGE_KEYS.CURRENT_PLAYLIST);
        }
      }
      if (data.savedPlaylists !== undefined) {
        localStorage.setItem(STORAGE_KEYS.SAVED_PLAYLISTS, JSON.stringify(data.savedPlaylists));
      }
    } catch (error) {
      console.error('保存播放列表数据失败:', error);
    }
  }, [autoSave]);

  // 保存设置
  const saveSettings = useCallback((newSettings: PlaylistSettings) => {
    try {
      localStorage.setItem(STORAGE_KEYS.PLAYLIST_SETTINGS, JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('保存播放列表设置失败:', error);
    }
  }, []);

  // 状态更新器 - 核心重构
  const updateState = useCallback((updater: (prevState: PlaylistState) => Partial<PlaylistState>) => {
    setState(prevState => {
      const changes = updater(prevState);
      const newState = { ...prevState, ...changes };
      
      // 分离保存逻辑
      const dataToSave: Partial<Pick<PlaylistState, 'currentPlaylist' | 'savedPlaylists'>> = {};
      if ('currentPlaylist' in changes) {
        dataToSave.currentPlaylist = newState.currentPlaylist;
      }
      if ('savedPlaylists' in changes) {
        dataToSave.savedPlaylists = newState.savedPlaylists;
      }
      
      if (Object.keys(dataToSave).length > 0) {
        saveToStorage(dataToSave);
      }
      
      return newState;
    });
  }, [saveToStorage]);

  // 创建播放列表
  const createPlaylist = useCallback((name: string, items: PlaylistItem[] = []) => {
    const newPlaylist: Playlist = {
      id: generateId(),
      name,
      items,
      currentIndex: 0,
      isPlaying: false,
      playMode: 'sequence',
      createdAt: new Date(),
      updatedAt: new Date(),
      isTemporary: false,
    };

    updateState(prev => ({
        savedPlaylists: [...prev.savedPlaylists, newPlaylist],
    }));

    return newPlaylist;
  }, [updateState]);

  // 创建临时播放列表
  const createTemporaryPlaylist = useCallback((items: PlaylistItem[]) => {
    const tempPlaylist: Playlist = {
      id: 'temp_' + generateId(),
      name: '临时播放列表',
      items,
      currentIndex: 0,
      isPlaying: false,
      playMode: 'sequence',
      createdAt: new Date(),
      updatedAt: new Date(),
      isTemporary: true,
    };

    updateState(() => ({ currentPlaylist: tempPlaylist }));

    return tempPlaylist;
  }, [updateState]);

  // 设置当前播放列表
  const setCurrentPlaylist = useCallback((playlist: Playlist | null) => {
    updateState(() => ({ currentPlaylist: playlist }));
  }, [updateState]);

  // 设置保存的播放列表（用于同步）
  const setSavedPlaylists = useCallback((playlists: Playlist[]) => {
    updateState(() => ({ savedPlaylists: playlists }));
  }, [updateState]);

  // 添加到当前播放列表
  const addToCurrentPlaylist = useCallback((item: PlaylistItem) => {
    updateState(prev => {
      let playlist = prev.currentPlaylist;

        // 如果没有当前播放列表，创建一个临时的
      if (!playlist) {
        playlist = {
          id: 'temp_' + generateId(),
          name: '临时播放列表',
          items: [],
          currentIndex: 0,
          isPlaying: false,
          playMode: 'sequence' as const,
          createdAt: new Date(),
          updatedAt: new Date(),
          isTemporary: true,
        };
      }
      
      // 确保 items 是一个数组，然后添加新项
      const newItems = [...(playlist.items || []), item];

      const updatedPlaylist: Playlist = {
        ...playlist,
        items: newItems,
        updatedAt: new Date(),
      };

      return { currentPlaylist: updatedPlaylist };
    });
  }, [updateState]);

  // 获取当前播放项
  const getCurrentItem = useCallback((): PlaylistItem | null => {
    if (!state.currentPlaylist || state.currentPlaylist.items.length === 0) {
      return null;
    }
    // 确保返回的不是 out of bound
    const item = state.currentPlaylist.items[state.currentPlaylist.currentIndex];
    return item || null;
  }, [state.currentPlaylist]);

  // 播放下一个
  const playNext = useCallback(() => {
    if (!state.currentPlaylist || state.currentPlaylist.items.length === 0) {
      onPlaylistEnd?.();
      return;
    }

    const { items, currentIndex, playMode } = state.currentPlaylist;
    let nextIndex: number;

    switch (playMode) {
      case 'random':
        nextIndex = Math.floor(Math.random() * items.length);
        break;
      case 'loop':
        nextIndex = (currentIndex + 1) % items.length;
        break;
      case 'sequence':
      default:
        nextIndex = currentIndex + 1;
        if (nextIndex >= items.length) {
          onPlaylistEnd?.();
          return;
        }
        break;
    }

    updateState(prev => {
      if (!prev.currentPlaylist) return {};
      return {
        currentPlaylist: {
        ...prev.currentPlaylist,
        currentIndex: nextIndex,
        updatedAt: new Date(),
        }
      };
    });
  }, [state.currentPlaylist, onPlaylistEnd, updateState]);

  // 播放上一个
  const playPrevious = useCallback(() => {
    if (!state.currentPlaylist || state.currentPlaylist.items.length === 0) {
      return;
    }

    const { items, currentIndex, playMode } = state.currentPlaylist;
    let prevIndex: number;

    switch (playMode) {
      case 'random':
        prevIndex = Math.floor(Math.random() * items.length);
        break;
      case 'loop':
        prevIndex = currentIndex - 1 < 0 ? items.length - 1 : currentIndex - 1;
        break;
      case 'sequence':
      default:
        prevIndex = Math.max(0, currentIndex - 1);
        break;
    }

    updateState(prev => {
      if (!prev.currentPlaylist) return {};
      return {
        currentPlaylist: {
        ...prev.currentPlaylist,
        currentIndex: prevIndex,
        updatedAt: new Date(),
        }
      };
    });
  }, [state.currentPlaylist, updateState]);

  // 监听当前播放项变化
  useEffect(() => {
    const currentItem = getCurrentItem();
    if (currentItem && currentItem.id !== previousItemRef.current?.id) {
      onItemChange?.(currentItem);
    }
    previousItemRef.current = currentItem;
  }, [state.currentPlaylist?.currentIndex, state.currentPlaylist?.items, onItemChange, getCurrentItem]);

  // 初始化时从本地存储加载
  useEffect(() => {
    loadFromStorage();
  }, [loadFromStorage]);

  // 删除播放列表
  const deletePlaylist = useCallback((playlistId: string) => {
    updateState(prev => {
      const newSavedPlaylists = prev.savedPlaylists.filter(p => p.id !== playlistId);
      const newState = { ...prev, savedPlaylists: newSavedPlaylists };

      // 如果删除的是当前播放列表，清空当前播放列表
      if (prev.currentPlaylist?.id === playlistId) {
        newState.currentPlaylist = null;
      }

      return newState;
    });
  }, [updateState]);

  // 播放指定项
  const playItem = useCallback((itemId: string) => {
    updateState(prev => {
      if (!prev.currentPlaylist) return {};
      const itemIndex = prev.currentPlaylist.items.findIndex(item => item.id === itemId);
      if (itemIndex === -1) return {};
      return {
        currentPlaylist: {
        ...prev.currentPlaylist,
          currentIndex: itemIndex,
        updatedAt: new Date(),
        }
      };
    });
  }, [updateState]);

  // 设置播放模式
  const setPlayMode = useCallback((mode: 'sequence' | 'loop' | 'random') => {
    updateState(prev => {
      if (!prev.currentPlaylist) return {};

      const updatedPlaylist = {
        ...prev.currentPlaylist,
        playMode: mode,
        updatedAt: new Date(),
      };

      if (onPlayModeChange) {
        onPlayModeChange(mode);
      }

      return { currentPlaylist: updatedPlaylist };
    });
  }, [updateState, onPlayModeChange]);

  // 设置播放状态
  const setPlaying = useCallback((isPlaying: boolean) => {
    updateState(prev => {
      if (!prev.currentPlaylist || prev.currentPlaylist.isPlaying === isPlaying) {
        return {};
      }
      return {
        currentPlaylist: {
        ...prev.currentPlaylist,
        isPlaying,
        }
      };
    });
  }, [updateState]);

  // 设置当前索引
  const setCurrentIndex = useCallback((index: number) => {
    updateState(prev => {
      if (!prev.currentPlaylist || index < 0 || index >= prev.currentPlaylist.items.length) {
        return {};
      }

      const updatedPlaylist = {
        ...prev.currentPlaylist,
        currentIndex: index,
        updatedAt: new Date(),
      };

      return { currentPlaylist: updatedPlaylist };
    });
  }, [updateState]);

  // 从播放列表移除项目
  const removeFromPlaylist = useCallback((itemId: string) => {
    updateState(prev => {
      if (!prev.currentPlaylist) return {};
      
      const newItems = prev.currentPlaylist.items.filter(item => item && item.id !== itemId);
      
      if (newItems.length === prev.currentPlaylist.items.length) {
        return {}; // 没有变化
      }

      let newCurrentIndex = prev.currentPlaylist.currentIndex;
      // 如果移除的是当前项或之前的项，需要调整索引
      const removedItemIndex = prev.currentPlaylist.items.findIndex(item => item.id === itemId);
      if (removedItemIndex !== -1 && newCurrentIndex >= removedItemIndex) {
          newCurrentIndex = Math.max(0, newCurrentIndex - 1);
      }
      
      // 如果列表为空，重置索引
      if (newItems.length === 0) {
        newCurrentIndex = 0;
      }

      return {
        currentPlaylist: {
          ...prev.currentPlaylist,
          items: newItems,
          currentIndex: newCurrentIndex,
          updatedAt: new Date(),
        }
      };
    });
  }, [updateState]);

  // 清空播放列表
  const clearPlaylist = useCallback(() => {
    updateState(prev => {
      if (!prev.currentPlaylist) return {};
      return {
        currentPlaylist: {
          ...prev.currentPlaylist,
          items: [],
          currentIndex: 0,
          updatedAt: new Date(),
        }
      };
    });
  }, [updateState]);

  // 切换播放模式
  const togglePlayMode = useCallback(() => {
    updateState(prev => {
      if (!prev.currentPlaylist) return {};
      const modes: Playlist['playMode'][] = ['sequence', 'loop', 'random'];
      const currentModeIndex = modes.indexOf(prev.currentPlaylist.playMode);
      const nextMode = modes[(currentModeIndex + 1) % modes.length];
      
      const updatedPlaylist = {
        ...prev.currentPlaylist,
        playMode: nextMode,
        updatedAt: new Date(),
      };

      if (onPlayModeChange) {
        onPlayModeChange(nextMode);
      }

      return { currentPlaylist: updatedPlaylist };
    });
  }, [updateState, onPlayModeChange]);

  const actions: Partial<PlaylistActions> = {
    createPlaylist,
    createTemporaryPlaylist,
    setCurrentPlaylist,
    setSavedPlaylists,
    addToCurrentPlaylist,
    removeFromPlaylist,
    clearPlaylist,
    playNext,
    playPrevious,
    playItem,
    togglePlayMode,
    setPlaying,
    saveSettings,
    setPlayMode,
    deletePlaylist,
    updatePlaylist: () => {}, // 待实现
    setCurrentIndex,
  };

  return {
    ...state,
    ...actions,
    getCurrentItem,
    settings,
    saveSettings,
    hasCurrentItem: !!getCurrentItem(),
  };
};
