# 📚 文档目录

本目录包含视频平台API的完整文档，提供详细的接口说明、部署指南和开发规范。

## 📋 文档列表

### 核心文档
- **[API接口文档](./API.md)** - 完整的API接口详细说明
- **[API快速参考](./API_QUICK_REFERENCE.md)** - API接口速查手册
- **[API测试指南](./API_TESTING_GUIDE.md)** - 完整的API测试方法
- **[部署指南](./DEPLOYMENT.md)** - 项目部署和配置指南
- **[开发指南](./DEVELOPMENT.md)** - 开发环境搭建和开发规范

### 辅助文档
- **[Postman集合](./postman_collection.json)** - 可导入Postman的接口测试集合
- **[API文档HTML版](./api.html)** - 美观的HTML格式API文档

## 🚀 快速开始

### 1. 部署项目
查看 [部署指南](./DEPLOYMENT.md) 了解如何部署项目：
- Docker部署
- 传统部署
- 云平台部署

### 2. 了解API
阅读 [API文档](./API.md) 或 [快速参考](./API_QUICK_REFERENCE.md) 了解接口使用：
- 认证机制
- 接口列表
- 请求格式
- 响应格式

### 3. 测试接口
使用 [API测试指南](./API_TESTING_GUIDE.md) 进行接口测试：
- 导入Postman集合
- 手动测试方法
- 自动化测试

### 4. 二次开发
参考 [开发指南](./DEVELOPMENT.md) 进行二次开发：
- 环境搭建
- 代码规范
- 开发流程

## 📊 API统计

- **总接口数**: 101个
- **模块数**: 7个核心模块
- **认证接口**: 11个
- **用户接口**: 12个
- **视频接口**: 22个 (含分类管理)
- **互动接口**: 15个
- **会员接口**: 16个
- **支付接口**: 8个
- **管理接口**: 17个

## 🔧 工具和资源

### 测试工具
- **Postman集合**: [postman_collection.json](./postman_collection.json)
- **自动化测试**: `npm test`
- **性能测试**: `npm run test:performance`

### 文档工具
- **HTML文档**: [api.html](./api.html)
- **Markdown文档**: [API.md](./API.md)
- **快速参考**: [API_QUICK_REFERENCE.md](./API_QUICK_REFERENCE.md)

### 开发工具
- **代码规范**: ESLint + Prettier
- **测试框架**: Mocha + Chai + Supertest
- **API文档**: 自动生成 + 手动维护

## 📈 文档版本

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.3 | 2025-01-07 | 新增API测试指南和快速参考 |
| v1.2 | 2025-01-07 | 完善API文档，新增HTML版本 |
| v1.1 | 2025-01-06 | 新增自动生成文档和Postman集合 |
| v1.0 | 2025-01-01 | 初始版本，基础API文档 |

## 🔄 文档更新

### 自动更新
- 运行 `npm run docs:generate` 更新HTML文档和Postman集合

### 手动更新
- 重要变更需要手动更新核心文档
- 新功能需要更新相应的使用指南

## 📞 技术支持

### 联系方式
- **API问题**: <EMAIL>
- **技术支持**: <EMAIL>
- **文档反馈**: <EMAIL>

### 问题反馈
- GitHub Issues: [项目地址]/issues
- 邮件支持: 上述联系邮箱
- 在线文档: [在线文档地址]

### 社区资源
- 开发者论坛: [论坛地址]
- 技术博客: [博客地址]
- 视频教程: [教程地址]

---

> 💡 **提示**: 建议先阅读 [API快速参考](./API_QUICK_REFERENCE.md) 快速了解接口，然后查看 [完整API文档](./API.md) 获取详细信息。
