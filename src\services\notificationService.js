const Notification = require('../database/models/Notification');
const logger = require('../utils/logger');

class NotificationService {
  async createNotification(data) {
    try {
      const notificationId = await Notification.createNotification(data);
      logger.info(`通知创建成功: userId=${data.userId}, type=${data.type}`);
      return { id: notificationId, ...data };
    } catch (error) {
      logger.error('创建通知失败:', error);
      throw error;
    }
  }

  async getUserNotifications(userId, options = {}) {
    return await Notification.findByUserId(userId, options);
  }

  async markAsRead(notificationId, userId) {
    const affectedRows = await Notification.markAsRead(notificationId, userId);
    return affectedRows > 0;
  }

  async markAllAsRead(userId) {
    return await Notification.markAllAsRead(userId);
  }
}

module.exports = new NotificationService(); 