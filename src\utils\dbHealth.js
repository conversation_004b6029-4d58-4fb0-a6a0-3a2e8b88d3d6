const { mysql, redis } = require('../config/database');
const logger = require('./logger');

class DatabaseHealthMonitor {
  constructor() {
    this.mysql = mysql;
    this.redis = redis;
    this.healthStatus = {
      mysql: { status: 'unknown', lastCheck: null, error: null },
      redis: { status: 'unknown', lastCheck: null, error: null }
    };
    
    // 定期健康检查间隔（毫秒）
    this.checkInterval = 30000; // 30秒
    this.intervalId = null;
  }

  // 检查MySQL连接健康状态
  async checkMySQLHealth() {
    try {
      const startTime = Date.now();
      const connection = await this.mysql.getConnection();
      
      // 执行简单查询测试连接
      await connection.execute('SELECT 1 as test');
      connection.release();
      
      const responseTime = Date.now() - startTime;
      
      this.healthStatus.mysql = {
        status: 'healthy',
        lastCheck: new Date(),
        responseTime: responseTime,
        error: null
      };
      
      logger.debug(`MySQL健康检查通过，响应时间: ${responseTime}ms`);
      return true;
      
    } catch (error) {
      this.healthStatus.mysql = {
        status: 'unhealthy',
        lastCheck: new Date(),
        error: error.message
      };
      
      logger.error('MySQL健康检查失败:', error);
      return false;
    }
  }

  // 检查Redis连接健康状态
  async checkRedisHealth() {
    try {
      if (!this.redis || !this.redis.isReady) {
        throw new Error('Redis连接未就绪');
      }
      
      const startTime = Date.now();
      await this.redis.ping();
      const responseTime = Date.now() - startTime;
      
      this.healthStatus.redis = {
        status: 'healthy',
        lastCheck: new Date(),
        responseTime: responseTime,
        error: null
      };
      
      logger.debug(`Redis健康检查通过，响应时间: ${responseTime}ms`);
      return true;
      
    } catch (error) {
      this.healthStatus.redis = {
        status: 'unhealthy',
        lastCheck: new Date(),
        error: error.message
      };
      
      logger.error('Redis健康检查失败:', error);
      return false;
    }
  }

  // 执行完整的健康检查
  async performHealthCheck() {
    logger.debug('开始数据库健康检查...');
    
    const results = await Promise.allSettled([
      this.checkMySQLHealth(),
      this.checkRedisHealth()
    ]);
    
    const mysqlHealthy = results[0].status === 'fulfilled' && results[0].value;
    const redisHealthy = results[1].status === 'fulfilled' && results[1].value;
    
    const overallHealth = {
      status: mysqlHealthy && redisHealthy ? 'healthy' : 'degraded',
      mysql: this.healthStatus.mysql,
      redis: this.healthStatus.redis,
      timestamp: new Date()
    };
    
    // 如果有服务不健康，记录警告
    if (!mysqlHealthy || !redisHealthy) {
      logger.warn('数据库服务健康检查发现问题:', {
        mysql: mysqlHealthy ? '正常' : '异常',
        redis: redisHealthy ? '正常' : '异常'
      });
    }
    
    return overallHealth;
  }

  // 获取MySQL连接池状态
  async getMySQLPoolStatus() {
    try {
      // 注意：mysql2连接池没有直接的状态API，这里模拟获取状态
      const connection = await this.mysql.getConnection();
      connection.release();
      
      return {
        status: 'active',
        // 这些值在实际的mysql2中可能不可用，仅作示例
        totalConnections: 'unknown',
        activeConnections: 'unknown',
        idleConnections: 'unknown'
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message
      };
    }
  }

  // 获取Redis状态信息
  async getRedisStatus() {
    try {
      if (!this.redis || !this.redis.isReady) {
        return {
          status: 'disconnected',
          connected: false
        };
      }
      
      const info = await this.redis.info('server');
      const memory = await this.redis.info('memory');
      
      return {
        status: 'connected',
        connected: true,
        info: {
          server: info,
          memory: memory
        }
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message
      };
    }
  }

  // 获取详细的健康状态报告
  async getDetailedHealthReport() {
    const healthCheck = await this.performHealthCheck();
    const mysqlPool = await this.getMySQLPoolStatus();
    const redisStatus = await getRedisStatus();
    
    return {
      overall: healthCheck,
      mysql: {
        health: healthCheck.mysql,
        pool: mysqlPool
      },
      redis: {
        health: healthCheck.redis,
        status: redisStatus
      },
      timestamp: new Date()
    };
  }

  // 启动定期健康检查
  startPeriodicHealthCheck() {
    if (this.intervalId) {
      logger.warn('定期健康检查已在运行');
      return;
    }
    
    logger.info(`启动定期数据库健康检查，间隔: ${this.checkInterval / 1000}秒`);
    
    this.intervalId = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        logger.error('定期健康检查执行失败:', error);
      }
    }, this.checkInterval);
  }

  // 停止定期健康检查
  stopPeriodicHealthCheck() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      logger.info('定期数据库健康检查已停止');
    }
  }

  // 获取当前健康状态
  getCurrentHealthStatus() {
    return this.healthStatus;
  }

  // 检查特定服务是否健康
  isMySQLHealthy() {
    return this.healthStatus.mysql.status === 'healthy';
  }

  isRedisHealthy() {
    return this.healthStatus.redis.status === 'healthy';
  }

  // 等待服务健康
  async waitForHealthy(service = 'all', timeout = 30000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      await this.performHealthCheck();
      
      if (service === 'mysql' && this.isMySQLHealthy()) {
        return true;
      } else if (service === 'redis' && this.isRedisHealthy()) {
        return true;
      } else if (service === 'all' && this.isMySQLHealthy() && this.isRedisHealthy()) {
        return true;
      }
      
      // 等待1秒后重试
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return false;
  }
}

// 创建健康监控实例
const dbHealthMonitor = new DatabaseHealthMonitor();

// 导出健康检查中间件
const healthCheckMiddleware = async (req, res, next) => {
  try {
    const health = await dbHealthMonitor.performHealthCheck();
    req.dbHealth = health;
    next();
  } catch (error) {
    logger.error('健康检查中间件执行失败:', error);
    req.dbHealth = {
      status: 'error',
      error: error.message,
      timestamp: new Date()
    };
    next();
  }
};

module.exports = {
  dbHealthMonitor,
  healthCheckMiddleware
};
