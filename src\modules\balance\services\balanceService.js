const connectionManager = require('../../../database/ConnectionManager');
const User = require('../../../database/models/User');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const { AppError } = require('../../../middleware/errorHandler');

class BalanceService {
  /**
   * 为用户增加余额。此操作应在一个事务中执行。
   * @param {number} userId - 用户ID
   * @param {number} amount - 要增加的金额
   * @param {object} connection - 数据库连接对象，用于事务处理
   */
  async addUserBalance(userId, amount, connection) {
    if (!connection) {
      throw new Error('addUserBalance 必须在事务中执行');
    }
    
    // 直接使用User实例，而不是尝试实例化它
    const userModel = User;
    
    // 获取当前余额并加锁
    const user = await userModel.findById(userId, 'FOR UPDATE', connection);
    if (!user) {
      throw new Error(`用户 ${userId} 不存在`);
    }

    const newBalance = parseFloat(user.balance) + parseFloat(amount);
    
    // 更新余额
    await userModel.update(userId, { balance: newBalance }, connection);
    
    // 清除用户资料缓存，以便下次获取用户资料时显示更新后的余额
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
    await cache.del(cacheKey);

    return {
      success: true,
      newBalance: newBalance,
    };
  }

  /**
   * 为用户扣除余额。此操作应在一个事务中执行。
   * @param {number} userId - 用户ID
   * @param {number} amount - 要扣除的金额
   * @param {object} connection - 数据库连接对象，用于事务处理
   */
  async deductUserBalance(userId, amount, connection) {
    if (!connection) {
      throw new Error('deductUserBalance 必须在事务中执行');
    }

    const userModel = User;
    const user = await userModel.findById(userId, 'FOR UPDATE', connection);
    if (!user) {
      throw new Error(`用户 ${userId} 不存在`);
    }

    const currentBalance = parseFloat(user.balance);
    const deductionAmount = parseFloat(amount);

    if (currentBalance < deductionAmount) {
      throw new AppError('用户余额不足', 400, 'INSUFFICIENT_FUNDS');
    }

    const newBalance = currentBalance - deductionAmount;
    
    await userModel.update(userId, { balance: newBalance }, connection);
    
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
    await cache.del(cacheKey);

    return {
      success: true,
      newBalance: newBalance,
    };
  }
}

module.exports = new BalanceService(); 