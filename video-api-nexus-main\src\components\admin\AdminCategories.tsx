import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, FolderTree, Search, Save, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import {
  getCategories,
  getCategoryTree,
  createCategory,
  updateCategory,
  deleteCategory,
  searchCategories
} from '@/lib/api';

interface Category {
  id: number;
  name: string;
  slug?: string;
  description?: string;
  parent_id?: number;
  sort_order: number;
  status: string;
  children?: Category[];
}

interface CategoryFormData {
  name: string;
  slug: string;
  description: string;
  parentId: number | null;
  sortOrder: number;
}

const AdminCategories = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    slug: '',
    description: '',
    parentId: null,
    sortOrder: 0
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      // 使用list API而不是tree API，避免缓存问题
      const response = await getCategories();
      console.log('分类管理页面API响应:', response); // 调试日志

      // 处理响应数据 - 修复数据解析路径
      const categoriesData = response.data?.data?.categories || response.data?.categories || response.data || [];
      console.log('提取的分类数据:', categoriesData); // 调试日志

      setCategories(Array.isArray(categoriesData) ? categoriesData : []);
    } catch (error) {
      console.error('获取分类失败:', error);

      let errorMessage = '获取分类列表失败';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: '错误',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchKeyword.trim()) {
      fetchCategories();
      return;
    }

    try {
      const response = await searchCategories(searchKeyword);
      setCategories(response.data?.data?.categories || response.data?.categories || []);
    } catch (error) {
      console.error('搜索分类失败:', error);

      let errorMessage = '搜索分类失败';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: '错误',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      parentId: null,
      sortOrder: 0
    });
    setEditingCategory(null);
    setShowCreateForm(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast({
        title: '错误',
        description: '分类名称不能为空',
        variant: 'destructive'
      });
      return;
    }

    try {
      if (editingCategory) {
        // 更新分类 - 使用数据库字段名称
        const updateData = {
          name: formData.name,
          slug: formData.slug.trim() || '', // 如果为空，保持原有slug或后端处理
          description: formData.description,
          parent_id: formData.parentId, // 转换为数据库字段名
          sort_order: formData.sortOrder  // 转换为数据库字段名
        };

        await updateCategory(editingCategory.id, updateData);
        toast({
          title: '成功',
          description: '分类更新成功'
        });
      } else {
        // 创建分类 - 使用控制器期望的驼峰命名
        const categoryData = {
          name: formData.name,
          slug: formData.slug.trim() || '', // 如果为空，后端会自动生成
          description: formData.description,
          parentId: formData.parentId,
          sortOrder: formData.sortOrder
        };

        await createCategory(categoryData);
        toast({
          title: '成功',
          description: '分类创建成功'
        });
      }

      resetForm();
      fetchCategories();
    } catch (error) {
      console.error('保存分类失败:', error);

      // 提取错误信息
      let errorMessage = editingCategory ? '更新分类失败' : '创建分类失败';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: '错误',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      slug: category.slug || '',
      description: category.description || '',
      parentId: category.parent_id || null,
      sortOrder: category.sort_order
    });
    setShowCreateForm(true);
  };

  const handleDelete = async (category: Category) => {
    if (!confirm(`确定要删除分类"${category.name}"吗？`)) {
      return;
    }

    try {
      await deleteCategory(category.id);
      toast({
        title: '成功',
        description: '分类删除成功'
      });
      fetchCategories();
    } catch (error) {
      console.error('删除分类失败:', error);

      // 提取错误信息
      let errorMessage = '删除分类失败';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      } else {
        errorMessage = '删除分类失败，可能存在关联的视频';
      }

      toast({
        title: '错误',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  const renderCategoryTree = (categoryList: Category[], level = 0) => {
    return categoryList.map((category) => (
      <div key={category.id} className="border rounded-lg mb-2">
        <div className={`p-4 flex items-center justify-between ${level > 0 ? 'ml-' + (level * 4) : ''}`}>
          <div className="flex items-center space-x-3">
            <FolderTree size={18} className="text-muted-foreground" />
            <div>
              <h3 className="font-medium">{category.name}</h3>
              {category.description && (
                <p className="text-sm text-muted-foreground">{category.description}</p>
              )}
              <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                <span>ID: {category.id}</span>
                <span>排序: {category.sort_order}</span>
                <span>状态: {category.status}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleEdit(category)}
            >
              <Edit size={14} />
              编辑
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleDelete(category)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 size={14} />
              删除
            </Button>
          </div>
        </div>
        {category.children && category.children.length > 0 && (
          <div className="pl-4 pb-2">
            {renderCategoryTree(category.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  const flattenCategories = (categoryList: Category[]): Category[] => {
    const result: Category[] = [];
    const flatten = (cats: Category[], level = 0) => {
      cats.forEach(cat => {
        result.push({ ...cat, level });
        if (cat.children && cat.children.length > 0) {
          flatten(cat.children, level + 1);
        }
      });
    };
    flatten(categoryList);
    return result;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">分类管理</h1>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus size={18} className="mr-2" />
          新建分类
        </Button>
      </div>

      {/* 搜索栏 */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 max-w-md">
          <Input
            placeholder="搜索分类..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
        </div>
        <Button onClick={handleSearch}>
          <Search size={18} className="mr-2" />
          搜索
        </Button>
        {searchKeyword && (
          <Button variant="outline" onClick={() => {
            setSearchKeyword('');
            fetchCategories();
          }}>
            清除
          </Button>
        )}
      </div>

      {/* 创建/编辑表单 */}
      {showCreateForm && (
        <div className="bg-card p-6 rounded-lg border">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">
              {editingCategory ? '编辑分类' : '创建分类'}
            </h2>
            <Button variant="ghost" size="sm" onClick={resetForm}>
              <X size={18} />
            </Button>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">分类名称 *</label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="输入分类名称"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">分类标识（可选）</label>
                <Input
                  value={formData.slug}
                  onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                  placeholder="留空将自动生成，或手动输入URL友好的标识"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  用于URL中的分类标识，支持中文、英文、数字、下划线和连字符
                </p>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">分类描述</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="输入分类描述"
                rows={3}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">父分类</label>
                <select
                  value={formData.parentId || ''}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    parentId: e.target.value ? parseInt(e.target.value) : null 
                  })}
                  className="w-full px-3 py-2 border border-input rounded-md bg-background"
                >
                  <option value="">无父分类（顶级分类）</option>
                  {flattenCategories(categories)
                    .filter(cat => !editingCategory || cat.id !== editingCategory.id)
                    .map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {'  '.repeat((cat as any).level || 0)}{cat.name}
                      </option>
                    ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">排序</label>
                <Input
                  type="number"
                  value={formData.sortOrder}
                  onChange={(e) => setFormData({ ...formData, sortOrder: parseInt(e.target.value) || 0 })}
                  placeholder="0"
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button type="submit">
                <Save size={18} className="mr-2" />
                {editingCategory ? '更新分类' : '创建分类'}
              </Button>
              <Button type="button" variant="outline" onClick={resetForm}>
                取消
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* 分类列表 */}
      <div className="space-y-4">
        {categories.length > 0 ? (
          renderCategoryTree(categories)
        ) : (
          <div className="text-center py-12">
            <FolderTree className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">暂无分类</h3>
            <p className="text-muted-foreground">开始创建第一个分类吧</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminCategories;
