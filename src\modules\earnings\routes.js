const express = require('express');
const router = express.Router();
const earningsController = require('./controllers/earningsController');
const { verifyToken } = require('../../middleware/auth');

// --- 收益中心 API ---

// 1. 获取收益摘要信息
// GET /api/earnings/summary
// 返回: { totalEarnings, monthEarnings, balance }
router.get('/summary', verifyToken, earningsController.getSummary);

// 2. 获取收益明细列表 (分页)
// GET /api/earnings/details?page=1&limit=10
router.get('/details', verifyToken, earningsController.getDetails);

// 3. 获取余额变动历史 (分页)
// GET /api/earnings/balance-history?page=1&limit=10
router.get('/balance-history', verifyToken, earningsController.getBalanceHistory);


module.exports = router; 