import { useState } from "react"
import { <PERSON>, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { useTheme } from "./theme-provider"
import { useTranslation } from 'react-i18next'

const getThemes = (t: any) => [
  {
    name: "light",
    label: t('theme.light'),
    description: t('theme.lightDesc'),
    colors: {
      background: "#ffffff",
      foreground: "#0f172a",
      primary: "#1e293b",
      secondary: "#f1f5f9",
      accent: "#f1f5f9",
      muted: "#f1f5f9"
    }
  },
  {
    name: "dark",
    label: t('theme.dark'),
    description: t('theme.darkDesc'),
    colors: {
      background: "#0f172a",
      foreground: "#f8fafc",
      primary: "#f8fafc",
      secondary: "#1e293b",
      accent: "#1e293b",
      muted: "#1e293b"
    }
  },
  {
    name: "neon",
    label: t('theme.neon'),
    description: t('theme.neonDesc'),
    colors: {
      background: "#0d0d0d",
      foreground: "#e879f9",
      primary: "#a855f7",
      secondary: "#1e1b4b",
      accent: "#06b6d4",
      muted: "#1e1b4b"
    }
  },
  {
    name: "ocean",
    label: t('theme.ocean'),
    description: t('theme.oceanDesc'),
    colors: {
      background: "#0c1e2e",
      foreground: "#a7f3d0",
      primary: "#0ea5e9",
      secondary: "#1e3a5f",
      accent: "#0891b2",
      muted: "#1e3a5f"
    }
  },
  {
    name: "forest",
    label: t('theme.forest'),
    description: t('theme.forestDesc'),
    colors: {
      background: "#0f1e0f",
      foreground: "#bbf7d0",
      primary: "#22c55e",
      secondary: "#1f3f1f",
      accent: "#eab308",
      muted: "#1f3f1f"
    }
  },
  {
    name: "sunset",
    label: t('theme.sunset'),
    description: t('theme.sunsetDesc'),
    colors: {
      background: "#1f1611",
      foreground: "#fed7aa",
      primary: "#f97316",
      secondary: "#3f2f1f",
      accent: "#dc2626",
      muted: "#3f2f1f"
    }
  },
  {
    name: "rainbow",
    label: t('theme.rainbow'),
    description: t('theme.rainbowDesc'),
    colors: {
      background: "#0f0f0f",
      foreground: "#f3f4f6",
      primary: "#8b5cf6",
      secondary: "#374151",
      accent: "#06b6d4",
      muted: "#374151"
    }
  }
]

export function ThemePreview() {
  const { theme, setTheme } = useTheme()
  const { t } = useTranslation()
  const [previewTheme, setPreviewTheme] = useState(theme)

  const themes = getThemes(t)

  const handleApplyTheme = () => {
    setTheme(previewTheme as any)
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Eye className="h-4 w-4" />
          {t('theme.preview')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('theme.previewTitle')}</DialogTitle>
          <DialogDescription>
            {t('theme.previewDescription')}
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
          {themes.map((themeOption) => (
            <Card 
              key={themeOption.name}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                previewTheme === themeOption.name ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setPreviewTheme(themeOption.name as any)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">{themeOption.label}</CardTitle>
                  {theme === themeOption.name && (
                    <Badge variant="default" className="text-xs">
                      {t('theme.current')}
                    </Badge>
                  )}
                  {previewTheme === themeOption.name && theme !== themeOption.name && (
                    <Badge variant="outline" className="text-xs">
                      {t('theme.previewing')}
                    </Badge>
                  )}
                </div>
                <CardDescription className="text-xs">
                  {themeOption.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                {/* 主题色彩预览 */}
                <div className="space-y-2">
                  <div className="flex gap-1">
                    <div 
                      className="w-6 h-6 rounded-sm border"
                      style={{ backgroundColor: themeOption.colors.background }}
                      title={t('theme.backgroundColor')}
                    />
                    <div 
                      className="w-6 h-6 rounded-sm border"
                      style={{ backgroundColor: themeOption.colors.primary }}
                      title={t('theme.primaryColor')}
                    />
                    <div 
                      className="w-6 h-6 rounded-sm border"
                      style={{ backgroundColor: themeOption.colors.accent }}
                      title={t('theme.accentColor')}
                    />
                  </div>
                  
                  {/* 模拟界面预览 */}
                  <div 
                    className="p-3 rounded-md border text-xs"
                    style={{ 
                      backgroundColor: themeOption.colors.background,
                      color: themeOption.colors.foreground,
                      borderColor: themeOption.colors.muted
                    }}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span style={{ color: themeOption.colors.foreground }}>
                        {t('theme.sampleInterface')}
                      </span>
                      <div
                        className="px-2 py-1 rounded text-xs"
                        style={{
                          backgroundColor: themeOption.colors.primary,
                          color: themeOption.colors.background
                        }}
                      >
                        {t('theme.button')}
                      </div>
                    </div>
                    <div 
                      className="p-2 rounded"
                      style={{ backgroundColor: themeOption.colors.muted }}
                    >
                      <div style={{ color: themeOption.colors.foreground }}>
                        {t('theme.cardContentSample')}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={() => setPreviewTheme(theme)}>
            {t('theme.reset')}
          </Button>
          <Button onClick={handleApplyTheme} disabled={previewTheme === theme}>
            <Check className="h-4 w-4 mr-2" />
            {t('theme.applyTheme')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
