const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'video_user',
  password: process.env.DB_PASSWORD || 'secure_password',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

async function testRoleSyncFix() {
  let connection;
  
  try {
    console.log('🧪 测试角色同步修复效果...\n');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    
    // 测试用户OOO和GGG的角色同步
    const testUsers = ['OOO', 'GGG'];
    
    for (const username of testUsers) {
      console.log(`📋 测试用户: ${username}`);
      
      // 获取用户信息
      const [users] = await connection.execute(`
        SELECT id, username, role, status FROM users WHERE username = ?
      `, [username]);
      
      if (users.length === 0) {
        console.log(`   ❌ 用户 ${username} 不存在\n`);
        continue;
      }
      
      const user = users[0];
      console.log(`   👤 用户ID: ${user.id}, 当前角色: ${user.role}`);
      
      // 检查会员状态
      const [memberships] = await connection.execute(`
        SELECT 
          COUNT(*) as total_count,
          SUM(CASE WHEN status = 'active' AND end_date > NOW() THEN 1 ELSE 0 END) as active_count,
          MAX(CASE WHEN status = 'active' AND end_date > NOW() THEN end_date ELSE NULL END) as latest_end_date
        FROM memberships 
        WHERE user_id = ?
      `, [user.id]);
      
      const membershipInfo = memberships[0];
      const hasActiveMembership = membershipInfo.active_count > 0;
      const expectedRole = hasActiveMembership ? 'member' : 'user';
      
      console.log(`   📊 会员记录: 总数 ${membershipInfo.total_count}, 有效 ${membershipInfo.active_count}`);
      if (hasActiveMembership) {
        console.log(`   ⏰ 最新到期时间: ${membershipInfo.latest_end_date}`);
      }
      console.log(`   🎯 期望角色: ${expectedRole}`);
      
      // 判断角色是否正确
      const isRoleCorrect = user.role === expectedRole;
      console.log(`   ✅ 角色是否正确: ${isRoleCorrect ? '是' : '否'}`);
      
      if (!isRoleCorrect) {
        console.log(`   ⚠️  角色不匹配！当前: ${user.role}, 期望: ${expectedRole}`);
        
        // 模拟角色同步（不实际执行，只是测试逻辑）
        console.log(`   🔧 建议修复: UPDATE users SET role = '${expectedRole}' WHERE id = ${user.id}`);
      }
      
      console.log('');
    }
    
    // 检查所有用户的角色一致性
    console.log('🔍 检查所有用户角色一致性...');
    
    const [inconsistentUsers] = await connection.execute(`
      SELECT 
        u.id,
        u.username,
        u.role as current_role,
        CASE 
          WHEN COUNT(m.id) > 0 AND SUM(CASE WHEN m.status = 'active' AND m.end_date > NOW() THEN 1 ELSE 0 END) > 0 
          THEN 'member' 
          ELSE 'user' 
        END as expected_role
      FROM users u
      LEFT JOIN memberships m ON u.id = m.user_id
      WHERE u.role != 'admin'
      GROUP BY u.id, u.username, u.role
      HAVING current_role != expected_role
    `);
    
    if (inconsistentUsers.length === 0) {
      console.log('✅ 所有用户角色都是一致的！');
    } else {
      console.log(`❌ 发现 ${inconsistentUsers.length} 个用户角色不一致:`);
      inconsistentUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.username} (ID: ${user.id}): ${user.current_role} -> ${user.expected_role}`);
      });
    }
    
    // 统计信息
    console.log('\n📈 统计信息:');
    
    const [userStats] = await connection.execute(`
      SELECT 
        role,
        COUNT(*) as count
      FROM users
      GROUP BY role
      ORDER BY role
    `);
    
    console.log('   用户角色分布:');
    userStats.forEach(stat => {
      console.log(`     ${stat.role}: ${stat.count} 人`);
    });
    
    const [membershipStats] = await connection.execute(`
      SELECT 
        status,
        COUNT(*) as count
      FROM memberships
      GROUP BY status
      ORDER BY status
    `);
    
    console.log('   会员记录状态分布:');
    membershipStats.forEach(stat => {
      console.log(`     ${stat.status}: ${stat.count} 条`);
    });
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testRoleSyncFix()
    .then(() => {
      console.log('\n🎉 测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testRoleSyncFix };
