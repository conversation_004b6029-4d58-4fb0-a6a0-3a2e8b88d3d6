
import React from 'react';
import CodeBlock from './CodeBlock';
import ResponseExample from './ResponseExample';

const ApiContent = ({ activeSection, searchQuery }) => {
  const renderContent = () => {
    switch (activeSection) {
      case 'overview':
      case 'intro':
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-4">API接口文档</h1>
              <p className="text-muted-foreground text-lg">
                本文档详细说明视频网站API框架的所有接口，共计 <strong>101个接口</strong>。
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-card p-4 rounded-lg border">
                <h3 className="font-semibold text-primary">认证模块</h3>
                <p className="text-2xl font-bold">11</p>
                <p className="text-sm text-muted-foreground">个接口</p>
              </div>
              <div className="bg-card p-4 rounded-lg border">
                <h3 className="font-semibold text-primary">用户模块</h3>
                <p className="text-2xl font-bold">12</p>
                <p className="text-sm text-muted-foreground">个接口</p>
              </div>
              <div className="bg-card p-4 rounded-lg border">
                <h3 className="font-semibold text-primary">视频模块</h3>
                <p className="text-2xl font-bold">22</p>
                <p className="text-sm text-muted-foreground">个接口</p>
              </div>
              <div className="bg-card p-4 rounded-lg border">
                <h3 className="font-semibold text-primary">管理模块</h3>
                <p className="text-2xl font-bold">17</p>
                <p className="text-sm text-muted-foreground">个接口</p>
              </div>
            </div>

            <div className="bg-muted p-4 rounded-lg">
              <h3 className="font-semibold mb-2">📋 基础信息</h3>
              <ul className="space-y-1 text-sm">
                <li><strong>Base URL:</strong> https://api.yourdomain.com</li>
                <li><strong>API版本:</strong> v1</li>
                <li><strong>认证方式:</strong> JWT Bearer Token</li>
                <li><strong>数据格式:</strong> JSON</li>
              </ul>
            </div>
          </div>
        );

      case 'auth-register':
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">用户注册</h1>
              <p className="text-muted-foreground">创建新的用户账户</p>
            </div>

            <div className="bg-card p-4 rounded-lg border">
              <CodeBlock 
                code="POST /api/auth/register" 
                language="http" 
                title="请求地址"
              />
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">请求参数</h3>
              <CodeBlock 
                code={`{
  "username": "string (3-20字符)",
  "email": "string (有效邮箱)",
  "password": "string (8-50字符)",
  "nickname": "string (可选，2-50字符)"
}`}
                language="json"
                title="请求体"
              />
            </div>

            <ResponseExample 
              response={{
                success: true,
                message: "注册成功",
                data: {
                  user: {
                    id: 1,
                    username: "testuser",
                    email: "<EMAIL>",
                    nickname: "测试用户",
                    role: "user",
                    status: "active",
                    created_at: "2025-01-07T12:00:00.000Z"
                  },
                  tokens: {
                    accessToken: "eyJhbGciOiJIUzI1NiIs...",
                    refreshToken: "eyJhbGciOiJIUzI1NiIs...",
                    expiresIn: 3600
                  }
                }
              }}
              title="响应示例"
            />
          </div>
        );

      case 'videos-list':
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">获取视频列表</h1>
              <p className="text-muted-foreground">获取视频列表，支持分页和筛选</p>
            </div>

            <div className="bg-card p-4 rounded-lg border">
              <CodeBlock 
                code="GET /api/videos/list" 
                language="http" 
                title="请求地址"
              />
            </div>

            <ResponseExample 
              response={{
                success: true,
                data: {
                  data: [
                    {
                      id: 1,
                      title: "示例视频",
                      description: "这是一个示例视频",
                      thumbnail: "/uploads/thumbnails/thumb_1.jpg",
                      duration: 120,
                      view_count: 1000,
                      like_count: 50,
                      comment_count: 10,
                      created_at: "2025-01-07T12:00:00.000Z",
                      username: "uploader",
                      category_name: "教育"
                    }
                  ],
                  pagination: {
                    page: 1,
                    pageSize: 20,
                    total: 100,
                    totalPages: 5,
                    hasNext: true,
                    hasPrev: false
                  }
                }
              }}
              title="响应示例"
            />
          </div>
        );

      case 'error-codes':
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">错误代码说明</h1>
              <p className="text-muted-foreground">API返回的错误代码及其含义</p>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-3">通用错误</h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-border">
                    <thead>
                      <tr className="bg-muted">
                        <th className="border border-border p-3 text-left">错误代码</th>
                        <th className="border border-border p-3 text-left">说明</th>
                        <th className="border border-border p-3 text-left">HTTP状态码</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-border p-3"><code>VALIDATION_ERROR</code></td>
                        <td className="border border-border p-3">数据验证失败</td>
                        <td className="border border-border p-3">400</td>
                      </tr>
                      <tr>
                        <td className="border border-border p-3"><code>UNAUTHORIZED</code></td>
                        <td className="border border-border p-3">未认证</td>
                        <td className="border border-border p-3">401</td>
                      </tr>
                      <tr>
                        <td className="border border-border p-3"><code>ACCESS_DENIED</code></td>
                        <td className="border border-border p-3">权限不足</td>
                        <td className="border border-border p-3">403</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        );

      case 'rate-limiting':
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">限流说明</h1>
              <p className="text-muted-foreground">API接口的访问频率限制</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">全局限流</h3>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-border">
                  <thead>
                    <tr className="bg-muted">
                      <th className="border border-border p-3 text-left">限制类型</th>
                      <th className="border border-border p-3 text-left">限制</th>
                      <th className="border border-border p-3 text-left">说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border border-border p-3">通用API</td>
                      <td className="border border-border p-3">100次/15分钟</td>
                      <td className="border border-border p-3">所有API接口的总体限制</td>
                    </tr>
                    <tr>
                      <td className="border border-border p-3">登录</td>
                      <td className="border border-border p-3">5次/分钟</td>
                      <td className="border border-border p-3">防止暴力破解</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">限流响应示例</h3>
              <CodeBlock 
                code={`{
  "success": false,
  "message": "请求过于频繁，请稍后再试",
  "code": "RATE_LIMIT_EXCEEDED",
  "details": {
    "limit": 100,
    "remaining": 0,
    "resetTime": "2025-01-07T13:00:00.000Z"
  }
}`}
                language="json"
                title="429状态码响应"
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">开发中</h1>
              <p className="text-muted-foreground">
                该部分内容正在开发中，敬请期待...
              </p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="max-w-4xl">
      {renderContent()}
    </div>
  );
};

export default ApiContent;
