const logger = require('../utils/logger');

// 自定义错误类
class AppError extends Error {
  constructor(message, statusCode, code = null, details = {}) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// 处理MySQL错误
const handleMySQLError = (error) => {
  let message = '数据库操作失败';
  let statusCode = 500;
  let code = 'DATABASE_ERROR';
  
  switch (error.code) {
    case 'ER_DUP_ENTRY':
      message = '数据已存在，请检查唯一性约束';
      statusCode = 409;
      code = 'DUPLICATE_ENTRY';
      break;
    case 'ER_NO_REFERENCED_ROW_2':
      message = '关联数据不存在';
      statusCode = 400;
      code = 'FOREIGN_KEY_CONSTRAINT';
      break;
    case 'ER_ROW_IS_REFERENCED_2':
      message = '数据被其他记录引用，无法删除';
      statusCode = 400;
      code = 'REFERENCED_ROW';
      break;
    case 'ER_BAD_FIELD_ERROR':
      message = '字段不存在';
      statusCode = 400;
      code = 'INVALID_FIELD';
      break;
    case 'ER_PARSE_ERROR':
      message = 'SQL语法错误';
      statusCode = 500;
      code = 'SQL_SYNTAX_ERROR';
      break;
    case 'ECONNREFUSED':
      message = '数据库连接被拒绝';
      statusCode = 503;
      code = 'DATABASE_CONNECTION_REFUSED';
      break;
    case 'ETIMEDOUT':
      message = '数据库连接超时';
      statusCode = 503;
      code = 'DATABASE_TIMEOUT';
      break;
  }
  
  return new AppError(message, statusCode, code);
};

// 处理JWT错误
const handleJWTError = (error) => {
  let message = '认证失败';
  let statusCode = 401;
  let code = 'AUTH_ERROR';
  
  if (error.name === 'JsonWebTokenError') {
    message = '无效的访问令牌';
    code = 'INVALID_TOKEN';
  } else if (error.name === 'TokenExpiredError') {
    message = '访问令牌已过期';
    code = 'TOKEN_EXPIRED';
  } else if (error.name === 'NotBeforeError') {
    message = '访问令牌尚未生效';
    code = 'TOKEN_NOT_ACTIVE';
  }
  
  return new AppError(message, statusCode, code);
};

// 处理验证错误
const handleValidationError = (error) => {
  const message = error.details ? error.details[0].message : '数据验证失败';
  return new AppError(message, 400, 'VALIDATION_ERROR');
};

// 处理文件上传错误
const handleMulterError = (error) => {
  let message = '文件上传失败';
  let statusCode = 400;
  let code = 'UPLOAD_ERROR';
  
  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      message = '文件大小超出限制';
      code = 'FILE_TOO_LARGE';
      break;
    case 'LIMIT_FILE_COUNT':
      message = '文件数量超出限制';
      code = 'TOO_MANY_FILES';
      break;
    case 'LIMIT_UNEXPECTED_FILE':
      message = '不支持的文件字段';
      code = 'UNEXPECTED_FILE';
      break;
    case 'MISSING_FILE':
      message = '缺少必需的文件';
      code = 'MISSING_FILE';
      break;
  }
  
  return new AppError(message, statusCode, code);
};

// 发送错误响应
const sendErrorResponse = (err, res) => {
  const response = {
    success: false,
    message: err.message,
    code: err.code || 'INTERNAL_ERROR',
    ...err.details
  };
  
  // 开发环境下返回错误堆栈
  if (process.env.NODE_ENV === 'development') {
    response.stack = err.stack;
  }
  
  res.status(err.statusCode || 500).json(response);
};

// 全局错误处理中间件
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;
  
  // 记录错误日志
  logger.error(`错误: ${err.message}`, {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    stack: err.stack
  });
  
  // 处理不同类型的错误
  if (err.code && err.code.startsWith('ER_')) {
    error = handleMySQLError(err);
  } else if (err.name && err.name.includes('JsonWebToken')) {
    error = handleJWTError(err);
  } else if (err.isJoi) {
    error = handleValidationError(err);
  } else if (err.code && err.code.startsWith('LIMIT_')) {
    error = handleMulterError(err);
  } else if (!err.isOperational) {
    // 未知错误，不暴露具体信息
    error = new AppError('服务器内部错误', 500, 'INTERNAL_ERROR');
  }
  
  sendErrorResponse(error, res);
};

// 异步错误包装器
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  AppError,
  errorHandler,
  asyncHandler
};
