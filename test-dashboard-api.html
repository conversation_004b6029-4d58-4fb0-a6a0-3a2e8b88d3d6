<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试仪表板API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .stat-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .stat-change {
            font-size: 12px;
            color: #28a745;
            margin-top: 5px;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .debug {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>仪表板API测试</h1>
        
        <div>
            <button onclick="testAPI()">测试API</button>
            <button onclick="clearDebug()">清除调试信息</button>
        </div>

        <div id="loading" class="loading" style="display: none;">正在加载...</div>
        <div id="error" class="error" style="display: none;"></div>
        
        <div id="stats" class="stats-grid" style="display: none;"></div>
        
        <div id="debug" class="debug"></div>
    </div>

    <script>
        function log(message) {
            const debugDiv = document.getElementById('debug');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.textContent += `[${timestamp}] ${message}\n`;
        }

        function clearDebug() {
            document.getElementById('debug').textContent = '';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('stats').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function displayStats(stats) {
            const statsDiv = document.getElementById('stats');
            
            const statItems = [
                {
                    title: '总用户数',
                    value: stats.totalUsers || 0,
                    change: stats.userChange || '无数据'
                },
                {
                    title: '总视频数',
                    value: stats.totalVideos || 0,
                    change: stats.videoChange || '无数据'
                },
                {
                    title: '总评论数',
                    value: stats.totalComments || 0,
                    change: stats.commentChange || '无数据'
                },
                {
                    title: '月收入',
                    value: `¥${stats.monthlyRevenue || '0.00'}`,
                    change: stats.revenueChange || '无数据'
                }
            ];

            statsDiv.innerHTML = statItems.map(stat => `
                <div class="stat-card">
                    <div class="stat-title">${stat.title}</div>
                    <div class="stat-value">${stat.value}</div>
                    <div class="stat-change">${stat.change}</div>
                </div>
            `).join('');

            statsDiv.style.display = 'grid';
        }

        async function testAPI() {
            log('开始测试仪表板API...');
            hideError();
            showLoading();

            try {
                // 获取token
                const token = localStorage.getItem('token');
                log(`Token: ${token ? '已找到' : '未找到'}`);

                if (!token) {
                    throw new Error('未找到认证token，请先登录');
                }

                // 调用API
                log('正在调用 /api/admin/dashboard/stats...');
                const response = await fetch('/api/admin/dashboard/stats', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`API响应状态: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    log(`API错误响应: ${errorText}`);
                    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                log(`API响应数据: ${JSON.stringify(data, null, 2)}`);

                // 检查响应格式
                if (data.success && data.data) {
                    log('使用标准API响应格式');
                    displayStats(data.data);
                } else if (data.totalUsers !== undefined) {
                    log('使用直接数据格式');
                    displayStats(data);
                } else {
                    throw new Error('无法识别的API响应格式');
                }

                log('API测试成功完成');

            } catch (error) {
                log(`API测试失败: ${error.message}`);
                showError(`API测试失败: ${error.message}`);
            } finally {
                hideLoading();
            }
        }

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            log('页面已加载，准备测试API');
            // 延迟1秒后自动测试
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
