const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'video_user',
  password: process.env.DB_PASSWORD || 'secure_password',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

async function checkUserOOO() {
  let connection;
  
  try {
    console.log('🔍 检查用户OOO的详细信息...\n');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    
    // 查找用户OOO的信息
    const [users] = await connection.execute(`
      SELECT id, username, email, role, status, created_at, updated_at
      FROM users 
      WHERE username = 'OOO' OR email LIKE '%OOO%'
    `);
    
    if (users.length === 0) {
      console.log('❌ 未找到用户OOO');
      return;
    }
    
    const user = users[0];
    console.log('👤 用户OOO的基本信息:');
    console.log(`   ID: ${user.id}`);
    console.log(`   用户名: ${user.username}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   角色: ${user.role}`);
    console.log(`   状态: ${user.status}`);
    console.log(`   注册时间: ${user.created_at}`);
    console.log(`   更新时间: ${user.updated_at}\n`);
    
    // 检查用户的会员记录
    const [memberships] = await connection.execute(`
      SELECT 
        m.id,
        m.start_date,
        m.end_date,
        m.status,
        mp.name as plan_name,
        mp.duration_days,
        m.created_at
      FROM memberships m
      JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.user_id = ?
      ORDER BY m.created_at DESC
    `, [user.id]);
    
    console.log('📋 用户OOO的会员记录:');
    if (memberships.length === 0) {
      console.log('   无会员记录');
    } else {
      memberships.forEach((membership, index) => {
        const isValid = new Date(membership.end_date) > new Date() && membership.status === 'active';
        console.log(`   ${index + 1}. 计划: ${membership.plan_name}`);
        console.log(`      状态: ${membership.status}`);
        console.log(`      开始时间: ${membership.start_date}`);
        console.log(`      结束时间: ${membership.end_date}`);
        console.log(`      创建时间: ${membership.created_at}`);
        console.log(`      是否有效: ${isValid ? '是' : '否'}`);
        console.log('');
      });
    }
    
    // 检查用户的订单记录
    const [orders] = await connection.execute(`
      SELECT 
        order_no,
        type,
        payment_status,
        final_amount,
        created_at,
        target_id
      FROM orders
      WHERE user_id = ? AND type IN ('MEMBERSHIP', 'membership')
      ORDER BY created_at DESC
      LIMIT 5
    `, [user.id]);
    
    console.log('💳 用户OOO的相关订单:');
    if (orders.length === 0) {
      console.log('   无会员订单记录');
    } else {
      orders.forEach((order, index) => {
        console.log(`   ${index + 1}. 订单号: ${order.order_no}`);
        console.log(`      类型: ${order.type}`);
        console.log(`      状态: ${order.payment_status}`);
        console.log(`      金额: ${order.final_amount}`);
        console.log(`      目标ID: ${order.target_id}`);
        console.log(`      创建时间: ${order.created_at}`);
        console.log('');
      });
    }
    
    // 判断用户应该具有的角色
    const activeMembership = memberships.find(m => 
      m.status === 'active' && new Date(m.end_date) > new Date()
    );
    
    const shouldBeRole = activeMembership ? 'member' : 'user';
    
    console.log(`🎯 角色分析:`);
    console.log(`   当前角色: ${user.role}`);
    console.log(`   应有角色: ${shouldBeRole}`);
    console.log(`   是否需要修复: ${user.role !== shouldBeRole ? '是' : '否'}`);
    
    // 如果需要修复，提供修复建议
    if (user.role !== shouldBeRole) {
      console.log(`\n🔧 修复建议:`);
      if (shouldBeRole === 'member') {
        console.log(`   用户OOO有有效的会员记录，但角色不是member`);
        console.log(`   建议执行: UPDATE users SET role = 'member' WHERE id = ${user.id}`);
      } else {
        console.log(`   用户OOO没有有效的会员记录，角色应该是user`);
        console.log(`   建议执行: UPDATE users SET role = 'user' WHERE id = ${user.id}`);
      }
    }
    
    // 同时检查用户GGG的情况作为对比
    console.log('\n📊 对比检查用户GGG:');
    const [gggUsers] = await connection.execute(`
      SELECT id, username, email, role, status
      FROM users 
      WHERE username = 'GGG'
    `);
    
    if (gggUsers.length > 0) {
      const gggUser = gggUsers[0];
      console.log(`   GGG - 角色: ${gggUser.role}, 状态: ${gggUser.status}`);
      
      // 检查GGG的会员记录
      const [gggMemberships] = await connection.execute(`
        SELECT COUNT(*) as count, 
               SUM(CASE WHEN status = 'active' AND end_date > NOW() THEN 1 ELSE 0 END) as active_count
        FROM memberships 
        WHERE user_id = ?
      `, [gggUser.id]);
      
      console.log(`   GGG - 会员记录总数: ${gggMemberships[0].count}, 有效记录: ${gggMemberships[0].active_count}`);
    }
    
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkUserOOO()
    .then(() => {
      console.log('\n🎉 检查完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { checkUserOOO };
