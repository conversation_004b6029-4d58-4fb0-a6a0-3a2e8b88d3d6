import React, { useState, useEffect, useMemo } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query'; // Import useMutation
import { getMyOrders } from '@/services/api';
import { paymentApi } from '@/services/paymentApi'; // Import paymentApi
import PurchaseDialog from '@/components/dialogs/PurchaseDialog'; // Import the dialog
import TronPaymentDialog from '@/components/dialogs/TronPaymentDialog'; // Import the new dialog
import { useToast } from "@/hooks/use-toast"; // Import useToast
import { useTranslation } from 'react-i18next';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ListOrdered } from 'lucide-react';

const MyOrdersPage: React.FC = () => {
  const { t } = useTranslation();

  const getOrderStatusMap = () => ({
    pending: { label: t('orders.statuses.pending'), variant: 'secondary' },
    paid: { label: t('orders.statuses.paid'), variant: 'success' },
    failed: { label: t('orders.statuses.failed'), variant: 'destructive' },
    expired: { label: t('orders.statuses.cancelled'), variant: 'outline' },
    refunded: { label: t('orders.statuses.refunded'), variant: 'default' },
    cancelled: { label: t('orders.statuses.cancelled'), variant: 'outline' },
  });

  const getOrderTypeMap = () => ({
    recharge: t('orders.types.recharge'),
    membership: t('orders.types.membership'),
    buy_membership: t('orders.types.membership'),
    video: t('orders.types.video'),
  });
  const [searchParams, setSearchParams] = useSearchParams();
  const page = parseInt(searchParams.get('page') || '1', 10);
  const type = searchParams.get('type') || 'all';
  const status = searchParams.get('status') || 'all';

  const queryClient = useQueryClient(); // Get the query client instance

  const { data, isLoading, isError, error, isPlaceholderData } = useQuery({
    queryKey: ['myOrders', page, type, status],
    queryFn: () => getMyOrders({ page, pageSize: 10, type: type === 'all' ? undefined : type, status: status === 'all' ? undefined : status }),
    placeholderData: (previousData) => previousData,
  });

  const [isPayDialogOpen, setIsPayDialogOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);

  const [isTronDialogOpen, setIsTronDialogOpen] = useState(false);
  const [selectedTronOrder, setSelectedTronOrder] = useState(null);
  const { toast } = useToast();

  const regeneratePaymentMutation = useMutation({
    mutationFn: (orderNo: string) => paymentApi.regenerateOrderPayment(orderNo),
    onSuccess: (response) => {
      const newPaymentInfo = response.data.data;
      // Manually map the camelCase response from the backend 
      // to the snake_case props expected by the TronPaymentDialog.
      setSelectedTronOrder({
        order_no: newPaymentInfo.orderNo,
        wallet_address: newPaymentInfo.walletAddress,
        final_amount: newPaymentInfo.finalAmount,
        final_amount_usdt: newPaymentInfo.finalAmountUsdt,
        qr_code_url: newPaymentInfo.qrCodeUrl,
      });
      setIsTronDialogOpen(true);
    },
    onError: (error: any) => {
      toast({
        title: "操作失败",
        description: error.response?.data?.message || "无法获取支付信息，请稍后再试。",
        variant: "destructive",
      });
    },
  });

  const handlePayClick = (order) => {
    if (order.type === 'membership') {
      regeneratePaymentMutation.mutate(order.order_no);
    } else {
      setSelectedOrder(order);
      setIsPayDialogOpen(true);
    }
  };

  const orders = data?.data?.data || [];
  const pagination = data?.data?.pagination || {};

  const handleFilterChange = (key: 'type' | 'status', value: string) => {
    setSearchParams(prev => {
      prev.set(key, value);
      prev.set('page', '1');
      return prev;
    });
  };

  const renderSkeletons = () => (
    Array.from({ length: 5 }).map((_, index) => (
      <TableRow key={index}>
        <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
        <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
      </TableRow>
    ))
  );

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ListOrdered className="w-6 h-6 mr-2" />
            {t('orders.title')}
          </CardTitle>
          <CardDescription>{t('orders.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <Select value={type} onValueChange={(value) => handleFilterChange('type', value)}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder={t('orders.orderType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('orders.allTypes')}</SelectItem>
                <SelectItem value="recharge">{t('orders.types.recharge')}</SelectItem>
                <SelectItem value="membership">{t('orders.types.membership')}</SelectItem>
                <SelectItem value="video">{t('orders.types.video')}</SelectItem>
              </SelectContent>
            </Select>
            <Select value={status} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder={t('orders.orderStatus')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('orders.allStatuses')}</SelectItem>
                {Object.entries(getOrderStatusMap()).map(([key, { label }]) => (
                  <SelectItem key={key} value={key}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('orders.table.orderNo')}</TableHead>
                  <TableHead>{t('orders.table.type')}</TableHead>
                  <TableHead>{t('orders.table.amount')}</TableHead>
                  <TableHead>{t('orders.table.status')}</TableHead>
                  <TableHead>{t('orders.table.paymentMethod')}</TableHead>
                  <TableHead>{t('orders.table.createTime')}</TableHead>
                  <TableHead>{t('orders.table.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  renderSkeletons()
                ) : isError ? (
                  <TableRow>
                    <TableCell colSpan={7}>
                      <Alert variant="destructive">
                        <AlertTitle>{t('orders.loadFailed')}</AlertTitle>
                        <AlertDescription>{(error as any)?.message || t('orders.unknownError')}</AlertDescription>
                      </Alert>
                    </TableCell>
                  </TableRow>
                ) : orders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center h-24">
                      {t('orders.noOrders')}
                    </TableCell>
                  </TableRow>
                ) : (
                  orders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-mono">{order.order_no}</TableCell>
                      <TableCell>{getOrderTypeMap()[order.type] || t('orders.types.other')}</TableCell>
                      <TableCell>
                        {/* Format the number to remove trailing zeros */}
                        {String(parseFloat(order.final_amount))} {order.payment_method === 'tron_usdt' ? 'TRX' : '元'}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getOrderStatusMap()[order.payment_status]?.variant || 'default'}>
                          {getOrderStatusMap()[order.payment_status]?.label || order.payment_status}
                        </Badge>
                      </TableCell>
                      <TableCell>{order.payment_method || '-'}</TableCell>
                      <TableCell>{new Date(order.created_at).toLocaleString('zh-CN')}</TableCell>
                      <TableCell>
                        {order.payment_status === 'pending' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePayClick(order)}
                            disabled={regeneratePaymentMutation.isPending && selectedTronOrder?.order_no === order.order_no}
                          >
                            {regeneratePaymentMutation.isPending && selectedTronOrder?.order_no === order.order_no ? '加载中...' : '去支付'}
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          {pagination.totalPages > 1 && (
            <div className="flex justify-center mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious 
                      href="#" 
                      onClick={(e) => {
                        e.preventDefault();
                        if (page > 1) setSearchParams(prev => { prev.set('page', String(page - 1)); return prev; });
                      }}
                      aria-disabled={page <= 1}
                      className={page <= 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                  {Array.from({ length: pagination.totalPages }).map((_, index) => (
                    <PaginationItem key={index}>
                      <PaginationLink 
                        href="#" 
                        onClick={(e) => {
                          e.preventDefault();
                          setSearchParams(prev => { prev.set('page', String(index + 1)); return prev; });
                        }}
                        isActive={page === index + 1}
                      >
                        {index + 1}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext 
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (page < pagination.totalPages) setSearchParams(prev => { prev.set('page', String(page + 1)); return prev; });
                      }}
                      aria-disabled={page >= pagination.totalPages}
                      className={page >= pagination.totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
      
      {selectedOrder && (
        <PurchaseDialog
          open={isPayDialogOpen}
          onOpenChange={setIsPayDialogOpen}
          order={selectedOrder}
          onPurchaseSuccess={() => {
            setIsPayDialogOpen(false);
            queryClient.invalidateQueries({ queryKey: ['myOrders'] });
          }}
        />
      )}

      {selectedTronOrder && (
        <TronPaymentDialog
          open={isTronDialogOpen}
          onOpenChange={setIsTronDialogOpen}
          order={selectedTronOrder}
        />
      )}
    </div>
  );
};

export default MyOrdersPage; 