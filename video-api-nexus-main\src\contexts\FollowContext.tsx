import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface FollowContextType {
  // 触发全局关注状态刷新
  refreshFollowState: () => void;
  // 更新特定用户的关注状态
  updateFollowStatus: (userId: number, isFollowing: boolean) => void;
  // 刷新计数器
  refreshCounter: number;
}

const FollowContext = createContext<FollowContextType | undefined>(undefined);

interface FollowProviderProps {
  children: ReactNode;
}

export const FollowProvider: React.FC<FollowProviderProps> = ({ children }) => {
  const [refreshCounter, setRefreshCounter] = useState(0);
  const [followStates, setFollowStates] = useState<Record<number, boolean>>({});

  const refreshFollowState = useCallback(() => {
    setRefreshCounter(prev => prev + 1);
  }, []);

  const updateFollowStatus = useCallback((userId: number, isFollowing: boolean) => {
    setFollowStates(prev => ({
      ...prev,
      [userId]: isFollowing
    }));
    // 同时触发全局刷新
    refreshFollowState();
  }, [refreshFollowState]);

  const value = {
    refreshFollowState,
    updateFollowStatus,
    refreshCounter,
  };

  return (
    <FollowContext.Provider value={value}>
      {children}
    </FollowContext.Provider>
  );
};

export const useFollowContext = () => {
  const context = useContext(FollowContext);
  if (context === undefined) {
    throw new Error('useFollowContext must be used within a FollowProvider');
  }
  return context;
};