const { AppError } = require('../../../middleware/errorHandler');
const CreemPlan = require('../../../database/models/CreemPlan');
const logger = require('../../../utils/logger');

/**
 * 创建Creem订单
 * @param {object} req - Express请求对象
 * @param {object} res - Express响应对象
 * @param {function} next - Express下一个中间件
 */
const createCreemOrder = async (req, res, next) => {
  try {
    const { planId } = req.body;
    const userId = req.user.id;
    const userEmail = req.user.email;

    if (!planId) {
      throw new AppError('计划ID是必需的', 400);
    }

    // 查找Creem计划
    const plan = await CreemPlan.findOne({ creem_product_id: planId });
    if (!plan) {
      throw new AppError('未找到指定的Creem计划', 404);
    }

    // 构建重定向URL
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:8081';
    const successUrl = `${baseUrl}/membership?status=success&plan=${encodeURIComponent(plan.name)}`;
    const cancelUrl = `${baseUrl}/membership?status=cancelled`;

    // 构建Creem支付URL - 使用正确的测试环境格式，包含重定向参数
    const paymentUrl = `https://www.creem.io/test/payment/${planId}` +
      `?success_url=${encodeURIComponent(successUrl)}` +
      `&cancel_url=${encodeURIComponent(cancelUrl)}` +
      `&customer_email=${encodeURIComponent(userEmail)}` +
      `&metadata[user_id]=${userId}` +
      `&metadata[plan_name]=${encodeURIComponent(plan.name)}`;

    logger.info('创建Creem订单:', {
      userId,
      userEmail,
      planId,
      planName: plan.name,
      paymentUrl
    });

    return res.json({
      success: true,
      data: {
        paymentUrl,
        planId,
        planName: plan.name,
        price: plan.price,
        currency: plan.currency
      }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createCreemOrder
}; 