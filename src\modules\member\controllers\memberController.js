const { AppError, asyncHandler } = require('../../../middleware/errorHandler');
const { operationLogger } = require('../../../middleware/requestLogger');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const Membership = require('../../../database/models/Membership');
const MembershipPlan = require('../../../database/models/MembershipPlan');
const Video = require('../../../database/models/Video');
const Order = require('../../../database/models/Order');
const { generateOrderNo } = require('../../../utils/orderHelper');
const { paymentServiceFactory } = require('../../payment');
const { paymentConfig } = require('../../../config/payment');
const User = require('../../../database/models/User');
const connectionManager = require('../../../database/ConnectionManager');
const PaymentServiceFactory = require('../../payment/services/PaymentServiceFactory');

class MemberController {
  // 获取会员计划列表
  getMembershipPlans = asyncHandler(async (req, res) => {
    logger.info('[MemberController] Entering getMembershipPlans');
    const cacheKey = cache.generateKey(CACHE_KEYS.MEMBERSHIP, 'plans');
    logger.debug(`[MemberController] Cache key: ${cacheKey}`);
    
    let plans;
    try {
      plans = await cache.get(cacheKey);
      logger.info(`[MemberController] Cache hit: ${!!plans}`);
    } catch (error) {
      logger.error('[MemberController] Error getting from cache:', error);
      // 如果缓存出错，继续从数据库获取
    }


    if (!plans) {
      logger.info('[MemberController] Cache miss, fetching from database.');
      try {
        plans = await MembershipPlan.getActivePlans();
        logger.info(`[MemberController] Fetched ${plans ? plans.length : 0} plans from DB.`);
        
        if (plans && plans.length > 0) {
          await cache.set(cacheKey, plans, 3600); // 1小时缓存
          logger.info('[MemberController] Plans cached successfully.');
        }
      } catch (dbError) {
        logger.error('[MemberController] Error fetching plans from DB:', dbError);
        // 即使数据库出错，也返回一个错误响应，而不是让它冒泡成一个未处理的异常
        throw new AppError('无法获取会员计划，请稍后重试', 500, 'DB_FETCH_ERROR');
      }
    }

    logger.info(`[MemberController] Sending response with ${plans ? plans.length : 0} plans.`);
    res.json({
      success: true,
      data: {
        plans: plans || [] // 确保即使出错也返回一个空数组
      }
    });
  });

  // 获取计划详情
  getPlanDetails = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const cacheKey = cache.generateKey(CACHE_KEYS.MEMBERSHIP, 'plan', id);
    let plan = await cache.get(cacheKey);

    if (!plan) {
      plan = await MembershipPlan.getPlanDetails(id);
      await cache.set(cacheKey, plan, 1800); // 30分钟缓存
    }

    res.json({
      success: true,
      data: {
        plan
      }
    });
  });

  // 比较计划
  comparePlans = asyncHandler(async (req, res) => {
    const { planIds } = req.body;

    if (!planIds || !Array.isArray(planIds) || planIds.length === 0) {
      throw new AppError('请提供要比较的计划ID', 400, 'INVALID_PLAN_IDS');
    }

    const plans = await MembershipPlan.comparePlans(planIds);

    res.json({
      success: true,
      data: {
        plans
      }
    });
  });

  // 获取我的会员信息
  getMyMembership = asyncHandler(async (req, res) => {
    const userId = req.user.id;

    const membership = await Membership.getUserMembership(userId);
    const membershipLevel = await Membership.getUserMembershipLevel(userId);

    res.json({
      success: true,
      data: {
        membership,
        level: membershipLevel,
        isMember: !!membership
      }
    });
  });

  // 获取会员历史
  getMembershipHistory = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { page = 1, pageSize = 10 } = req.query;

    const options = {
      page: parseInt(page, 10),
      pageSize: parseInt(pageSize, 10),
      type: 'membership', // 仅查询会员相关的订单
    };

    const orders = await Order.getUserOrders(userId, options);
    res.json({ success: true, data: orders });
  });

  // 订阅计划
  subscribePlan = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { planId, paymentMethod, paymentData } = req.body;

    // 1. 验证计划
    const plan = await MembershipPlan.findById(planId);
    if (!plan || !plan.is_active) {
      throw new AppError('会员计划不存在或已停用', 404, 'PLAN_NOT_FOUND');
    }

    // 2. 创建一个待处理的订单 (统一流程起点)
    const orderNo = generateOrderNo();
    
    // 增加折扣逻辑判断
    const hasDiscount = plan.original_price && new Date(plan.discount_until) > new Date();
    const finalAmount = hasDiscount ? parseFloat(plan.price) : (parseFloat(plan.original_price) || parseFloat(plan.price));
    const originalAmount = parseFloat(plan.original_price) || parseFloat(plan.price);
    const discountAmount = originalAmount - finalAmount;

    const orderData = {
      user_id: userId,
      type: 'membership',
      target_id: planId,
      amount: originalAmount,
      discount_amount: discountAmount > 0 ? discountAmount : 0,
      final_amount: finalAmount,
      payment_method: finalAmount > 0 ? paymentMethod : 'free',
      payment_status: 'pending',
      order_no: orderNo,
      expires_at: new Date(Date.now() + (paymentConfig.common.orderExpireMinutes || 30) * 60 * 1000),
      description: `订阅会员计划: ${plan.name}`,
    };
    await Order.createOrder(orderData);

    // 3. 根据价格处理支付和会员激活
    // 对于免费计划
    if (Number(plan.price) === 0) {
      const paymentResult = {
        method: 'free',
        transactionId: `free_${Date.now()}_${userId}`,
        amount: 0,
        status: 'success',
        orderNo,
      };
      
      // 直接激活会员并更新订单状态
      const membership = await Membership.renewMembership(userId, planId, paymentResult);
      await Order.updateOrderStatus(orderNo, 'paid', {
        transaction_id: paymentResult.transactionId,
        payment_time: new Date(),
      });

      // 清理缓存
      await this.clearMembershipCache(userId);
      await cache.del(cache.generateKey(CACHE_KEYS.ADMIN, 'dashboard_stats'));
      
      operationLogger.logUserOperation(req, 'membership_subscribe_free', membership.id, '订阅免费会员', { planId, orderNo });

      return res.status(201).json({
        success: true,
        message: '免费会员订阅成功',
        data: { membership, paymentInfo: null },
      });
    }

    // 对于付费计划
    const paymentInfo = await paymentServiceFactory.createPayment(paymentMethod, {
      orderNo,
      amount: finalAmount, // 支付时使用最终金额
      subject: `订阅: ${plan.name}`,
      paymentMethod: paymentMethod,
    });
    
    operationLogger.logUserOperation(req, 'membership_subscribe_request', null, '请求订阅付费会员', { planId, orderNo });

    res.status(200).json({
      success: true,
      message: '订单已创建，请完成支付',
      data: { membership: null, paymentInfo },
    });
  });

  // 取消会员
  cancelMembership = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { reason } = req.body;

    await Membership.cancelMembership(userId, reason);

    // 清除相关缓存
    await this.clearMembershipCache(userId);

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      'membership_cancel',
      userId,
      '取消会员',
      { reason }
    );

    res.json({
      success: true,
      message: '会员取消成功'
    });
  });

  // 设置自动续费
  setAutoRenew = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { autoRenew } = req.body;

    await Membership.setAutoRenew(userId, autoRenew);

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      'membership_auto_renew',
      userId,
      autoRenew ? '开启自动续费' : '关闭自动续费'
    );

    res.json({
      success: true,
      message: autoRenew ? '自动续费已开启' : '自动续费已关闭'
    });
  });

  // 获取会员权益
  getMemberBenefits = asyncHandler(async (req, res) => {
    const userId = req.user.id;

    const membership = await Membership.getUserMembership(userId);
    if (!membership) {
      throw new AppError('您不是会员', 403, 'NOT_A_MEMBER');
    }

    // 获取会员专属内容统计
    const memberVideoCount = await Video.query(`
      SELECT COUNT(*) as count 
      FROM videos 
      WHERE visibility IN ('member_only', 'vip_only') 
        AND status = 'published'
    `);

    const benefits = {
      plan: {
        name: membership.plan_name,
        features: membership.features || [],
        maxVideoUploads: membership.max_video_uploads,
        maxStorageGb: membership.max_storage_gb
      },
      stats: {
        memberVideoCount: memberVideoCount[0].count,
        remainingDays: Math.ceil((new Date(membership.end_date) - new Date()) / (1000 * 60 * 60 * 24))
      },
      permissions: {
        watchMemberVideos: true,
        watchVipVideos: true, // 所有会员都可以观看VIP内容
        downloadVideos: true, // 所有会员都可以下载
        adFree: true, // 所有会员都无广告
        prioritySupport: true // 所有会员都享受优先支持
      }
    };

    res.json({
      success: true,
      data: {
        benefits
      }
    });
  });

  // 获取会员专属内容
  getExclusiveContent = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { page = 1, pageSize = 20 } = req.query;

    const membershipLevel = await Membership.getUserMembershipLevel(userId);

    // 简化版：所有会员都可以访问所有会员内容
    if (membershipLevel === 'free') {
      throw new AppError('您不是会员', 403, 'NOT_A_MEMBER');
    }

    // 所有会员都可以访问member_only和vip_only内容
    const visibilityFilter = ['member_only', 'vip_only'];

    const filters = {
      visibility: visibilityFilter,
      status: 'published'
    };

    const options = {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      sortBy: 'created_at',
      sortOrder: 'DESC'
    };

    const result = await Video.getVideoList(filters, options);

    res.json({
      success: true,
      data: result
    });
  });

  // 获取会员统计（管理员）
  getMembershipStats = asyncHandler(async (req, res) => {
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'membership_stats');
    let stats = await cache.get(cacheKey);

    if (!stats) {
      const [membershipStats, planStats] = await Promise.all([
        Membership.getMembershipStats(),
        MembershipPlan.getAllPlansStats()
      ]);

      stats = {
        membership: membershipStats,
        plans: planStats
      };

      await cache.set(cacheKey, stats, 300); // 5分钟缓存
    }

    res.json({
      success: true,
      data: {
        stats
      }
    });
  });

  // 获取会员管理页面统计概览（管理员）
  getMembershipOverview = asyncHandler(async (req, res) => {
    try {
        const totalMembers = await Membership.query(
          "SELECT COUNT(DISTINCT user_id) as count FROM memberships WHERE status = 'active'"
        );
        const activeMembers = await Membership.query(
        "SELECT COUNT(DISTINCT user_id) as count FROM memberships WHERE status = 'active' AND end_date > NOW()"
        );
        
        const monthlyRevenueResult = await Order.query(`
          SELECT SUM(final_amount) as monthly_revenue
          FROM orders
          WHERE type = 'membership'
            AND payment_status = 'paid'
            AND payment_time >= DATE_FORMAT(NOW(), '%Y-%m-01')
        `);

        const totalPaidMembers = await Order.query(
          "SELECT COUNT(DISTINCT user_id) as count FROM orders WHERE type = 'membership' AND payment_status = 'paid'"
        );

      const overview = {
          totalMembers: totalMembers[0].count || 0,
          activeMembers: activeMembers[0].count || 0,
          monthlyRevenue: parseFloat(monthlyRevenueResult[0].monthly_revenue || 0).toFixed(2),
          paidMemberRate:
            totalMembers[0].count > 0
              ? ((totalPaidMembers[0].count / totalMembers[0].count) * 100).toFixed(2)
              : '0.00'
        };

      res.json({
        success: true,
        data: overview
      });
    } catch (error) {
      logger.error('获取会员管理概览失败:', error);
      throw new AppError('获取会员概览数据失败', 500, 'GET_MEMBERSHIP_OVERVIEW_FAILED');
    }
  });

  // 获取所有计划（管理员）
  getAllPlans = asyncHandler(async (req, res) => {
    // 获取计划列表和统计信息
    const [plans, planStats] = await Promise.all([
      MembershipPlan.getActivePlans(false),
      MembershipPlan.getAllPlansStats()
    ]);

    // 将统计信息合并到计划数据中
    const plansWithStats = plans.map(plan => {
      const stats = planStats.find(stat => stat.id === plan.id) || {
        total_subscriptions: 0,
        active_subscriptions: 0,
        total_revenue: 0
      };

      return {
        ...plan,
        total_subscriptions: parseInt(stats.total_subscriptions) || 0,
        active_subscriptions: parseInt(stats.active_subscriptions) || 0,
        total_revenue: parseFloat(stats.total_revenue) || 0
      };
    });

    res.json({
      success: true,
      data: {
        plans: plansWithStats
      }
    });
  });

  // 管理员: 创建新计划
  createPlan = asyncHandler(async (req, res) => {
    const {
      name,
      description,
      price,
      originalPrice,
      discountUntil,
      durationDays,
      features,
      maxVideoUploads,
      maxStorageGb,
      priority,
      isActive
    } = req.body;

    const planData = {
      name,
      description,
      price,
      original_price: originalPrice,
      discount_until: discountUntil,
      duration_days: durationDays,
      features,
      max_video_uploads: maxVideoUploads,
      max_storage_gb: maxStorageGb,
      priority,
      is_active: isActive
    };

    const newPlan = await MembershipPlan.createPlan(planData);

    // 清除计划列表缓存
    await this.clearPlanCache();
    await cache.del(cache.generateKey(CACHE_KEYS.ADMIN, 'dashboard_stats'));

    // 记录操作日志
    operationLogger.logAdminOperation(
      req,
      'plan_create',
      newPlan.id,
      '创建会员计划',
      { name }
    );

    res.status(201).json({
      success: true,
      message: '会员计划创建成功',
      data: {
        plan: newPlan
      }
    });
  });

  // 管理员: 更新计划
  updatePlan = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;

    const updatedPlan = await MembershipPlan.updatePlan(id, updateData);

    // 清除相关缓存
    await this.clearPlanCache(id);
    await cache.del(cache.generateKey(CACHE_KEYS.ADMIN, 'dashboard_stats'));

    // 记录操作日志
    operationLogger.logAdminOperation(
      req,
      'plan_update',
      id,
      '更新会员计划',
      { updatedFields: Object.keys(updateData) }
    );

    res.json({
      success: true,
      message: '会员计划更新成功',
      data: {
        plan: updatedPlan
      }
    });
  });

  // 管理员: 删除计划
  deletePlan = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const plan = await MembershipPlan.findById(id);
    if (!plan) {
      throw new AppError('会员计划不存在', 404, 'PLAN_NOT_FOUND');
    }

    await MembershipPlan.deletePlan(id);

    // 记录管理员操作
    operationLogger.logAdminOperation(
      req,
      'plan_delete',
      id,
      `删除会员计划: ${plan.name}`,
      { planId: id, planName: plan.name }
    );

    // 清除缓存
    await this.clearPlanCache();

    res.json({
      success: true,
      message: '会员计划删除成功'
    });
  });

  // 清除会员缓存
  async clearMembershipCache(userId) {
    const patterns = [
      `${CACHE_KEYS.MEMBERSHIP}:user:${userId}:*`,
      `${CACHE_KEYS.USER}:*:${userId}:*`
    ];

    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
  }

  // 清除计划缓存
  async clearPlanCache(id) {
    const key = cache.generateKey(CACHE_KEYS.MEMBERSHIP, 'plans');
    const patterns = [
      `${key}:*`, // 删除如 plan:1, plan:2 等详情缓存
      `${CACHE_KEYS.MEMBERSHIP}:plan:*` // 兼容旧的详情键模式
    ];

    // 1. 精确删除列表主键
    await cache.del(key);

    // 2. 模式删除关联键
    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
  }

  // 获取会员用户列表（管理员）
  getAdminMemberUsers = asyncHandler(async (req, res) => {
    const {
      page = 1,
      pageSize = 20,
      search = '',
      status = 'all'
    } = req.query;

    const offset = (page - 1) * pageSize;
    let whereClauses = [];
    const params = [];

    // 基础查询：只获取有会员记录的用户
    whereClauses.push('m.id IS NOT NULL');

    // 搜索条件
    if (search) {
      whereClauses.push('(u.username LIKE ? OR u.email LIKE ? OR u.nickname LIKE ?)');
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // 状态过滤
    if (status && status !== 'all') {
      whereClauses.push('m.status = ?');
      params.push(status);
    }

    const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';

    const sql = `
      SELECT
        u.id,
        u.username,
        u.email,
        u.nickname,
        u.role,
        u.status as user_status,
        m.id as membership_id,
        m.status as membership_status,
        m.start_date as membership_start_date,
        m.end_date as membership_end_date,
        m.auto_renew,
        m.created_at as membership_created_at,
        mp.id as plan_id,
        mp.name as plan_name,
        mp.price as plan_price,
        mp.duration_days as plan_duration
      FROM users u
      LEFT JOIN memberships m ON u.id = m.user_id
        AND m.status IN ('active', 'expired', 'cancelled')
        AND m.id = (
          SELECT id FROM memberships m2
          WHERE m2.user_id = u.id
          ORDER BY m2.created_at DESC
          LIMIT 1
        )
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      ${whereClause}
      ORDER BY m.created_at DESC
      LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
    `;

    const users = await Membership.query(sql, params);

    // 获取总数
    const countSql = `
      SELECT COUNT(DISTINCT u.id) as total
      FROM users u
      LEFT JOIN memberships m ON u.id = m.user_id
        AND m.status IN ('active', 'expired', 'cancelled')
        AND m.id = (
          SELECT id FROM memberships m2
          WHERE m2.user_id = u.id
          ORDER BY m2.created_at DESC
          LIMIT 1
        )
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      ${whereClause}
    `;

    const countResult = await Membership.query(countSql, params);
    const total = countResult[0].total;

    // 格式化数据
    const formattedUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      nickname: user.nickname,
      role: user.role,
      user_status: user.user_status,
      membership_id: user.membership_id,
      membership_status: user.membership_status,
      membership_start_date: user.membership_created_at, // 使用创建时间作为开始时间
      membership_end_date: user.membership_end_date,
      auto_renew: user.auto_renew,
      membership_plan: user.plan_id ? {
        id: user.plan_id,
        name: user.plan_name,
        price: user.plan_price,
        duration_days: user.plan_duration
      } : null
    }));

    res.json({
      success: true,
      data: {
        users: formattedUsers,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    });
  });

  createMembershipOrder = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { planId, paymentMethod } = req.body;

    const plan = await MembershipPlan.findById(planId);
    if (!plan || !plan.is_active) {
      throw new AppError('会员计划不存在或已失效', 404);
    }

    if (paymentMethod === 'balance') {
      // 执行余额支付逻辑
      const user = await User.findById(userId);
      if (!user) throw new AppError('用户不存在', 404);

      if (parseFloat(user.balance) < parseFloat(plan.price)) {
        throw new AppError('用户余额不足', 400, 'INSUFFICIENT_BALANCE');
      }
      
      const connection = await connectionManager.getMySQLConnection();
      await connection.beginTransaction();
      
      try {
        const userModel = User;
        const orderModel = Order;

        // 1. 扣除余额
        const newBalance = parseFloat(user.balance) - parseFloat(plan.price);
        await userModel.update(userId, { balance: newBalance }, connection);

        // 2. 创建已完成的订单
        const orderNo = generateOrderNo('M');
        const orderData = {
          user_id: userId,
          type: 'membership',
          target_id: planId,
          amount: plan.price,
          final_amount: plan.price,
          payment_method: 'balance',
          payment_status: 'paid',
          order_no: orderNo,
          description: `用户(ID: ${userId}) 使用余额购买会员: ${plan.name}`,
        };
        await orderModel.createOrder(orderData, connection);

        // 3. 开通会员
        await Membership.grantMembership(userId, plan, connection);

        await connection.commit();
        
        return res.json({
          success: true,
          message: '会员开通成功',
          data: { paymentMethod: 'balance', success: true }
        });

      } catch (error) {
        await connection.rollback();
        throw new AppError('使用余额购买失败，请重试', 500);
      } finally {
        connection.release();
      }

    } else {
      // 执行其他支付方式的逻辑 (支付宝、微信等)
      const orderNo = generateOrderNo('M');
      const orderModel = new Order();
      const orderData = {
        user_id: userId,
        type: 'membership',
        target_id: planId,
        amount: plan.price,
        final_amount: plan.price,
        payment_method: paymentMethod,
        payment_status: 'pending',
        order_no: orderNo,
        expires_at: new Date(Date.now() + (paymentConfig.common.orderExpireMinutes || 30) * 60 * 1000),
        description: `订阅会员计划: ${plan.name}`,
      };
      const paymentService = PaymentServiceFactory.getService(paymentMethod);
      const paymentResult = await paymentService.createOrder({
        orderNo,
        amount: plan.price,
        subject: `订阅: ${plan.name}`,
      });

      return res.json({
        success: true,
        message: '订单已创建，请完成支付',
        data: {
          orderId: orderData.id, // Assuming order creation returns id
          orderNo,
          ...paymentResult,
        },
      });
    }
  });
}

module.exports = new MemberController();
