const request = require('supertest');
const app = require('../app');
const { performance } = require('perf_hooks');

describe('性能测试套件', () => {
  let authToken;
  
  // 性能基准
  const PERFORMANCE_THRESHOLDS = {
    auth: 1000,      // 认证接口 < 1秒
    video_list: 500, // 视频列表 < 500ms
    video_detail: 300, // 视频详情 < 300ms
    search: 800,     // 搜索 < 800ms
    user_profile: 200 // 用户资料 < 200ms
  };

  before(async () => {
    console.log('开始性能测试...');
    
    // 创建测试用户并获取token
    try {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'perftest',
          email: '<EMAIL>',
          password: 'password123'
        });
      
      if (response.body.data && response.body.data.tokens) {
        authToken = response.body.data.tokens.accessToken;
      }
    } catch (error) {
      console.log('测试用户可能已存在，尝试登录...');
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        });
      
      if (loginResponse.body.data && loginResponse.body.data.tokens) {
        authToken = loginResponse.body.data.tokens.accessToken;
      }
    }
  });

  // 性能测试辅助函数
  async function measurePerformance(testName, testFunction, threshold) {
    const startTime = performance.now();
    
    try {
      await testFunction();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      console.log(`${testName}: ${duration.toFixed(2)}ms`);
      
      if (duration > threshold) {
        console.warn(`⚠️  ${testName} 超过性能阈值 ${threshold}ms`);
      } else {
        console.log(`✅ ${testName} 性能良好`);
      }
      
      return duration;
    } catch (error) {
      console.error(`❌ ${testName} 测试失败:`, error.message);
      throw error;
    }
  }

  describe('认证性能测试', () => {
    it('登录接口性能测试', async () => {
      await measurePerformance(
        '用户登录',
        async () => {
          const response = await request(app)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'password123'
            })
            .expect(200);
        },
        PERFORMANCE_THRESHOLDS.auth
      );
    });

    it('注册接口性能测试', async () => {
      const randomEmail = `test${Date.now()}@example.com`;
      
      await measurePerformance(
        '用户注册',
        async () => {
          const response = await request(app)
            .post('/api/auth/register')
            .send({
              username: `user${Date.now()}`,
              email: randomEmail,
              password: 'password123'
            })
            .expect(201);
        },
        PERFORMANCE_THRESHOLDS.auth
      );
    });
  });

  describe('视频接口性能测试', () => {
    it('视频列表性能测试', async () => {
      await measurePerformance(
        '视频列表查询',
        async () => {
          const response = await request(app)
            .get('/api/videos/list?page=1&pageSize=20')
            .expect(200);
        },
        PERFORMANCE_THRESHOLDS.video_list
      );
    });

    it('视频搜索性能测试', async () => {
      await measurePerformance(
        '视频搜索',
        async () => {
          const response = await request(app)
            .get('/api/videos/search?keyword=测试&page=1&pageSize=10')
            .expect(200);
        },
        PERFORMANCE_THRESHOLDS.search
      );
    });

    it('热门视频性能测试', async () => {
      await measurePerformance(
        '热门视频查询',
        async () => {
          const response = await request(app)
            .get('/api/videos/popular?limit=10')
            .expect(200);
        },
        PERFORMANCE_THRESHOLDS.video_list
      );
    });
  });

  describe('用户接口性能测试', () => {
    it('用户资料查询性能测试', async () => {
      if (!authToken) {
        console.log('跳过用户资料测试：无认证token');
        return;
      }

      await measurePerformance(
        '用户资料查询',
        async () => {
          const response = await request(app)
            .get('/api/users/profile')
            .set('Authorization', `Bearer ${authToken}`)
            .expect(200);
        },
        PERFORMANCE_THRESHOLDS.user_profile
      );
    });
  });

  describe('并发性能测试', () => {
    it('并发视频列表查询', async function() {
      this.timeout(10000);
      
      const concurrentRequests = 10;
      const requests = [];
      
      console.log(`开始 ${concurrentRequests} 个并发请求...`);
      
      const startTime = performance.now();
      
      for (let i = 0; i < concurrentRequests; i++) {
        requests.push(
          request(app)
            .get('/api/videos/list?page=1&pageSize=10')
            .expect(200)
        );
      }
      
      const responses = await Promise.all(requests);
      const endTime = performance.now();
      const totalDuration = endTime - startTime;
      const avgDuration = totalDuration / concurrentRequests;
      
      console.log(`并发测试完成:`);
      console.log(`- 总时间: ${totalDuration.toFixed(2)}ms`);
      console.log(`- 平均时间: ${avgDuration.toFixed(2)}ms`);
      console.log(`- 成功请求: ${responses.length}/${concurrentRequests}`);
      
      // 验证所有请求都成功
      responses.forEach((response, index) => {
        if (response.status !== 200) {
          console.error(`请求 ${index + 1} 失败: ${response.status}`);
        }
      });
    });

    it('并发认证请求', async function() {
      this.timeout(15000);
      
      const concurrentRequests = 5;
      const requests = [];
      
      console.log(`开始 ${concurrentRequests} 个并发登录请求...`);
      
      const startTime = performance.now();
      
      for (let i = 0; i < concurrentRequests; i++) {
        requests.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'password123'
            })
        );
      }
      
      const responses = await Promise.all(requests);
      const endTime = performance.now();
      const totalDuration = endTime - startTime;
      const avgDuration = totalDuration / concurrentRequests;
      
      console.log(`并发认证测试完成:`);
      console.log(`- 总时间: ${totalDuration.toFixed(2)}ms`);
      console.log(`- 平均时间: ${avgDuration.toFixed(2)}ms`);
      
      const successfulRequests = responses.filter(r => r.status === 200).length;
      console.log(`- 成功请求: ${successfulRequests}/${concurrentRequests}`);
    });
  });

  describe('内存和资源测试', () => {
    it('大量数据查询测试', async function() {
      this.timeout(5000);
      
      await measurePerformance(
        '大页面视频列表',
        async () => {
          const response = await request(app)
            .get('/api/videos/list?page=1&pageSize=50')
            .expect(200);
        },
        1000 // 1秒阈值
      );
    });

    it('连续请求测试', async function() {
      this.timeout(10000);
      
      const requestCount = 20;
      const durations = [];
      
      console.log(`开始 ${requestCount} 个连续请求...`);
      
      for (let i = 0; i < requestCount; i++) {
        const startTime = performance.now();
        
        await request(app)
          .get('/api/videos/list?page=1&pageSize=10')
          .expect(200);
        
        const endTime = performance.now();
        durations.push(endTime - startTime);
      }
      
      const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
      const maxDuration = Math.max(...durations);
      const minDuration = Math.min(...durations);
      
      console.log(`连续请求测试完成:`);
      console.log(`- 平均时间: ${avgDuration.toFixed(2)}ms`);
      console.log(`- 最大时间: ${maxDuration.toFixed(2)}ms`);
      console.log(`- 最小时间: ${minDuration.toFixed(2)}ms`);
      
      // 检查性能是否稳定（最大时间不应该超过平均时间的3倍）
      if (maxDuration > avgDuration * 3) {
        console.warn('⚠️  性能不稳定，存在异常慢的请求');
      }
    });
  });

  describe('缓存性能测试', () => {
    it('缓存命中测试', async function() {
      this.timeout(5000);
      
      // 第一次请求（缓存未命中）
      const firstDuration = await measurePerformance(
        '首次视频列表查询',
        async () => {
          await request(app)
            .get('/api/videos/list?page=1&pageSize=10')
            .expect(200);
        },
        PERFORMANCE_THRESHOLDS.video_list
      );
      
      // 等待一小段时间确保缓存生效
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 第二次请求（应该命中缓存）
      const secondDuration = await measurePerformance(
        '缓存命中视频列表查询',
        async () => {
          await request(app)
            .get('/api/videos/list?page=1&pageSize=10')
            .expect(200);
        },
        PERFORMANCE_THRESHOLDS.video_list
      );
      
      console.log(`缓存效果分析:`);
      console.log(`- 首次查询: ${firstDuration.toFixed(2)}ms`);
      console.log(`- 缓存查询: ${secondDuration.toFixed(2)}ms`);
      
      if (secondDuration < firstDuration * 0.8) {
        console.log('✅ 缓存有效，性能提升明显');
      } else {
        console.warn('⚠️  缓存效果不明显');
      }
    });
  });

  after(() => {
    console.log('性能测试完成');
    console.log('\n性能阈值参考:');
    Object.entries(PERFORMANCE_THRESHOLDS).forEach(([key, value]) => {
      console.log(`- ${key}: < ${value}ms`);
    });
  });
});
