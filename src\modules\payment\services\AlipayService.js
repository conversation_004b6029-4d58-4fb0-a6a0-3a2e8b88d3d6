const BasePaymentService = require('./BasePaymentService');

/**
 * 支付宝支付服务
 * 支持支付宝官方接口
 */
class AlipayService extends BasePaymentService {
  constructor(config) {
    super(config);
    this.name = 'alipay';
    this.appId = config.appId;
    this.privateKey = config.privateKey;
    this.alipayPublicKey = config.alipayPublicKey;
    this.notifyUrl = config.notifyUrl;
    this.returnUrl = config.returnUrl;
    this.gatewayUrl = config.gatewayUrl || 'https://openapi.alipay.com/gateway.do';
    this.signType = 'RSA2';
  }

  /**
   * 创建支付订单
   * @param {Object} orderData 订单数据
   * @returns {Promise<Object>} 支付结果
   */
  async createPayment(orderData) {
    try {
      this.validateRequiredFields(orderData, [
        'orderNo', 'amount', 'subject'
      ]);

      const bizContent = {
        out_trade_no: orderData.orderNo,
        total_amount: orderData.amount.toFixed(2),
        subject: orderData.subject,
        body: orderData.description || orderData.subject,
        product_code: orderData.productCode || 'FAST_INSTANT_TRADE_PAY'
      };

      const commonParams = {
        app_id: this.appId,
        method: 'alipay.trade.precreate', // 扫码支付
        charset: 'utf-8',
        sign_type: this.signType,
        timestamp: this.formatTimestamp(new Date()),
        version: '1.0',
        notify_url: this.notifyUrl,
        biz_content: JSON.stringify(bizContent)
      };

      // 生成签名
      commonParams.sign = this.generateSignature(commonParams);

      this.log('info', '创建支付宝订单', { 
        orderNo: orderData.orderNo, 
        amount: orderData.amount 
      });

      // 调用支付宝接口
      const response = await this.httpRequest(this.gatewayUrl, {
        method: 'POST',
        data: this.objectToUrlParams(commonParams),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const result = JSON.parse(response.alipay_trade_precreate_response);

      if (result.code !== '10000') {
        throw new Error(`支付宝接口调用失败: ${result.sub_msg || result.msg}`);
      }

      this.log('info', '支付宝订单创建成功', { 
        orderNo: orderData.orderNo,
        qrCode: result.qr_code 
      });

      return {
        success: true,
        qrCode: result.qr_code, // 二维码内容
        orderNo: orderData.orderNo,
        paymentMethod: 'alipay',
        data: result
      };

    } catch (error) {
      this.log('error', '创建支付宝订单失败', { 
        orderNo: orderData.orderNo, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 查询支付状态
   * @param {string} orderNo 订单号
   * @returns {Promise<Object>} 支付状态
   */
  async queryPayment(orderNo) {
    try {
      const bizContent = {
        out_trade_no: orderNo
      };

      const commonParams = {
        app_id: this.appId,
        method: 'alipay.trade.query',
        charset: 'utf-8',
        sign_type: this.signType,
        timestamp: this.formatTimestamp(new Date()),
        version: '1.0',
        biz_content: JSON.stringify(bizContent)
      };

      commonParams.sign = this.generateSignature(commonParams);

      const response = await this.httpRequest(this.gatewayUrl, {
        method: 'POST',
        data: this.objectToUrlParams(commonParams),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const result = JSON.parse(response.alipay_trade_query_response);

      this.log('info', '查询支付宝订单状态', { orderNo, result });

      return {
        success: result.code === '10000',
        status: this.mapTradeStatus(result.trade_status),
        orderNo,
        transactionId: result.trade_no,
        amount: parseFloat(result.total_amount || 0),
        payTime: result.gmt_payment,
        rawData: result
      };

    } catch (error) {
      this.log('error', '查询支付宝订单失败', { orderNo, error: error.message });
      throw error;
    }
  }

  /**
   * 处理支付回调
   * @param {Object} callbackData 回调数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleCallback(callbackData) {
    try {
      this.log('info', '处理支付宝回调', callbackData);

      // 验证签名
      if (!this.verifySignature(callbackData, callbackData.sign)) {
        throw new Error('签名验证失败');
      }

      const result = {
        success: callbackData.trade_status === 'TRADE_SUCCESS',
        orderNo: callbackData.out_trade_no,
        transactionId: callbackData.trade_no,
        amount: parseFloat(callbackData.total_amount),
        payTime: callbackData.gmt_payment,
        status: this.mapTradeStatus(callbackData.trade_status),
        rawData: callbackData
      };

      this.log('info', '支付宝回调处理完成', result);

      return result;

    } catch (error) {
      this.log('error', '处理支付宝回调失败', { 
        callbackData, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 申请退款
   * @param {Object} refundData 退款数据
   * @returns {Promise<Object>} 退款结果
   */
  async refund(refundData) {
    try {
      this.validateRequiredFields(refundData, [
        'orderNo', 'refundAmount'
      ]);

      const bizContent = {
        out_trade_no: refundData.orderNo,
        refund_amount: refundData.refundAmount.toFixed(2),
        refund_reason: refundData.reason || '用户申请退款',
        out_request_no: `${refundData.orderNo}_refund_${Date.now()}`
      };

      const commonParams = {
        app_id: this.appId,
        method: 'alipay.trade.refund',
        charset: 'utf-8',
        sign_type: this.signType,
        timestamp: this.formatTimestamp(new Date()),
        version: '1.0',
        biz_content: JSON.stringify(bizContent)
      };

      commonParams.sign = this.generateSignature(commonParams);

      const response = await this.httpRequest(this.gatewayUrl, {
        method: 'POST',
        data: this.objectToUrlParams(commonParams),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      const result = JSON.parse(response.alipay_trade_refund_response);

      this.log('info', '支付宝退款申请', { 
        orderNo: refundData.orderNo, 
        result 
      });

      return {
        success: result.code === '10000',
        refundId: result.out_request_no,
        message: result.sub_msg || result.msg,
        rawData: result
      };

    } catch (error) {
      this.log('error', '支付宝退款失败', { 
        orderNo: refundData.orderNo, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 验证回调签名
   * @param {Object} data 回调数据
   * @param {string} signature 签名
   * @returns {boolean} 验证结果
   */
  verifySignature(data, signature) {
    try {
      const crypto = require('crypto');
      
      const signData = { ...data };
      delete signData.sign;
      delete signData.sign_type;

      const signString = this.objectToUrlParams(signData);
      
      const verify = crypto.createVerify('RSA-SHA256');
      verify.update(signString, 'utf8');
      
      return verify.verify(this.alipayPublicKey, signature, 'base64');
    } catch (error) {
      this.log('error', '支付宝签名验证失败', { error: error.message });
      return false;
    }
  }

  /**
   * 生成签名
   * @param {Object} data 数据
   * @returns {string} 签名
   */
  generateSignature(data) {
    try {
      const crypto = require('crypto');
      
      const signData = { ...data };
      delete signData.sign;

      const signString = this.objectToUrlParams(signData);
      
      const sign = crypto.createSign('RSA-SHA256');
      sign.update(signString, 'utf8');
      
      return sign.sign(this.privateKey, 'base64');
    } catch (error) {
      this.log('error', '支付宝签名生成失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 格式化时间戳
   * @param {Date} date 日期
   * @returns {string} 格式化的时间戳
   */
  formatTimestamp(date) {
    return date.toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '');
  }

  /**
   * 映射交易状态
   * @param {string} tradeStatus 支付宝交易状态
   * @returns {string} 标准状态
   */
  mapTradeStatus(tradeStatus) {
    const statusMap = {
      'TRADE_SUCCESS': 'paid',
      'TRADE_FINISHED': 'paid',
      'TRADE_CLOSED': 'failed',
      'WAIT_BUYER_PAY': 'pending'
    };
    return statusMap[tradeStatus] || 'pending';
  }

  /**
   * 检查配置是否有效
   * @returns {boolean} 配置是否有效
   */
  isConfigValid() {
    return !!(this.appId && this.privateKey && this.alipayPublicKey);
  }
}

module.exports = AlipayService;
