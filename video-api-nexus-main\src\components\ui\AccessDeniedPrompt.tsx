import React from 'react';
import { Button } from './button';
import { ShieldAlert } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface AccessDeniedPromptProps {
  isLoggedIn: boolean;
  onNavigateToMembership?: () => void;
}

export const AccessDeniedPrompt: React.FC<AccessDeniedPromptProps> = ({ 
  isLoggedIn, 
  onNavigateToMembership 
}) => {
  const navigate = useNavigate();

  const handlePrimaryAction = () => {
    if (isLoggedIn) {
      if (onNavigateToMembership) {
        onNavigateToMembership();
      } else {
        navigate('/membership');
      }
    } else {
      navigate('/login');
    }
  };

  const title = isLoggedIn ? '需要会员权限' : '需要登录';
  const description = isLoggedIn 
    ? '此内容仅对会员开放。请升级您的账户以继续观看。' 
    : '请登录并开通会员以观看此内容。';
  const buttonText = isLoggedIn ? '立即开通会员' : '登录或注册';

  return (
    <div className="flex flex-col items-center justify-center h-64 bg-background rounded-lg border-2 border-dashed">
      <ShieldAlert className="w-12 h-12 text-destructive mb-4" />
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      <p className="text-muted-foreground mb-6 text-center px-4">{description}</p>
      <Button onClick={handlePrimaryAction}>{buttonText}</Button>
    </div>
  );
}; 