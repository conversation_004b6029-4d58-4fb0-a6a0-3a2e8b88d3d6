# 用户关注功能和粉丝功能实现方案

## 1. 项目现状分析

### 1.1 当前技术架构
- **后端**: Node.js + Express + MySQL
- **前端**: React + TypeScript + Tailwind CSS
- **文件结构**: 模块化设计，每个功能模块包含 controllers, services, routes
- **数据库**: MySQL，已有完整的用户系统和视频管理系统

### 1.2 现有相关表结构
- **users表**: 完整的用户信息管理 (id, username, nickname, avatar, bio等)
- **videos表**: 视频内容管理，包含创作者信息 (user_id)
- **likes表**: 点赞系统 (支持视频和评论点赞)
- **comments表**: 评论系统
- **notifications表**: 通知系统

## 2. 数据库设计

### 2.1 新增表结构

#### 关注关系表 (follows)
```sql
CREATE TABLE follows (
    id INT PRIMARY KEY AUTO_INCREMENT,
    follower_id INT NOT NULL COMMENT '关注者用户ID',
    followed_id INT NOT NULL COMMENT '被关注者用户ID',
    status ENUM('active', 'blocked') DEFAULT 'active' COMMENT '关注状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (followed_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 确保同一用户不能重复关注
    UNIQUE KEY uk_follower_followed (follower_id, followed_id),
    
    -- 索引优化
    INDEX idx_follower_id (follower_id),
    INDEX idx_followed_id (followed_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    -- 防止自己关注自己
    CHECK (follower_id != followed_id)
) ENGINE=InnoDB COMMENT='用户关注关系表';
```

#### 用户统计扩展表 (user_stats)
```sql
CREATE TABLE user_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    follower_count INT DEFAULT 0 COMMENT '粉丝数量',
    following_count INT DEFAULT 0 COMMENT '关注数量',
    video_count INT DEFAULT 0 COMMENT '视频数量',
    total_views INT DEFAULT 0 COMMENT '总播放量',
    total_likes INT DEFAULT 0 COMMENT '总获赞数',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_follower_count (follower_count),
    INDEX idx_following_count (following_count)
) ENGINE=InnoDB COMMENT='用户统计信息表';
```

### 2.2 现有表结构修改

#### 通知表扩展 (notifications)
在现有通知类型基础上增加关注相关类型：
```sql
-- 修改通知类型枚举
ALTER TABLE notifications 
MODIFY COLUMN type ENUM('system', 'video_review', 'comment', 'reply', 'follow', 'video_upload') 
DEFAULT 'system' COMMENT '通知类型';
```

## 3. 后端实现方案

### 3.1 模块结构
```
src/modules/follow/
├── controllers/
│   └── followController.js
├── services/
│   └── followService.js
├── models/
│   └── Follow.js
└── routes.js
```

### 3.2 数据模型 (Follow.js)
```javascript
const BaseModel = require('../../database/BaseModel');
const { AppError } = require('../../middleware/errorHandler');

class Follow extends BaseModel {
  constructor() {
    super('follows');
  }

  // 关注用户
  async followUser(followerId, followedId) {
    if (followerId === followedId) {
      throw new AppError('不能关注自己', 400, 'CANNOT_FOLLOW_SELF');
    }

    // 检查是否已经关注
    const existing = await this.findOne({
      follower_id: followerId,
      followed_id: followedId
    });

    if (existing) {
      throw new AppError('已经关注过该用户', 409, 'ALREADY_FOLLOWING');
    }

    const followId = await this.create({
      follower_id: followerId,
      followed_id: followedId,
      status: 'active'
    });

    // 更新统计数据
    await this.updateFollowStats(followerId, followedId, 'follow');
    
    return followId;
  }

  // 取消关注
  async unfollowUser(followerId, followedId) {
    const follow = await this.findOne({
      follower_id: followerId,
      followed_id: followedId
    });

    if (!follow) {
      throw new AppError('未关注该用户', 404, 'NOT_FOLLOWING');
    }

    await this.delete(follow.id);
    
    // 更新统计数据
    await this.updateFollowStats(followerId, followedId, 'unfollow');
    
    return true;
  }

  // 更新关注统计
  async updateFollowStats(followerId, followedId, action) {
    const increment = action === 'follow' ? 1 : -1;
    
    // 更新关注者的关注数
    await this.query(`
      INSERT INTO user_stats (user_id, following_count) 
      VALUES (?, 1) 
      ON DUPLICATE KEY UPDATE following_count = following_count + ?
    `, [followerId, increment]);

    // 更新被关注者的粉丝数
    await this.query(`
      INSERT INTO user_stats (user_id, follower_count) 
      VALUES (?, 1) 
      ON DUPLICATE KEY UPDATE follower_count = follower_count + ?
    `, [followedId, increment]);
  }

  // 获取粉丝列表
  async getFollowers(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const sql = `
      SELECT 
        f.id as follow_id,
        f.created_at as follow_time,
        u.id, u.username, u.nickname, u.avatar, u.bio,
        us.follower_count, us.following_count, us.video_count
      FROM follows f
      JOIN users u ON f.follower_id = u.id
      LEFT JOIN user_stats us ON u.id = us.user_id
      WHERE f.followed_id = ? AND f.status = 'active'
      ORDER BY f.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const followers = await this.query(sql, [userId, limit, offset]);

    const countSql = `
      SELECT COUNT(*) as total 
      FROM follows 
      WHERE followed_id = ? AND status = 'active'
    `;
    const countResult = await this.query(countSql, [userId]);

    return {
      data: followers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / limit)
      }
    };
  }

  // 获取关注列表
  async getFollowing(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const sql = `
      SELECT 
        f.id as follow_id,
        f.created_at as follow_time,
        u.id, u.username, u.nickname, u.avatar, u.bio,
        us.follower_count, us.following_count, us.video_count
      FROM follows f
      JOIN users u ON f.followed_id = u.id
      LEFT JOIN user_stats us ON u.id = us.user_id
      WHERE f.follower_id = ? AND f.status = 'active'
      ORDER BY f.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const following = await this.query(sql, [userId, limit, offset]);

    const countSql = `
      SELECT COUNT(*) as total 
      FROM follows 
      WHERE follower_id = ? AND status = 'active'
    `;
    const countResult = await this.query(countSql, [userId]);

    return {
      data: following,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / limit)
      }
    };
  }

  // 检查关注状态
  async checkFollowStatus(followerId, followedId) {
    const follow = await this.findOne({
      follower_id: followerId,
      followed_id: followedId,
      status: 'active'
    });
    
    return !!follow;
  }

  // 批量检查关注状态
  async batchCheckFollowStatus(followerId, userIds) {
    if (!userIds || userIds.length === 0) return {};

    const placeholders = userIds.map(() => '?').join(',');
    const sql = `
      SELECT followed_id
      FROM follows 
      WHERE follower_id = ? AND followed_id IN (${placeholders}) AND status = 'active'
    `;

    const results = await this.query(sql, [followerId, ...userIds]);
    const followedIds = new Set(results.map(r => r.followed_id));

    const statusMap = {};
    userIds.forEach(id => {
      statusMap[id] = followedIds.has(id);
    });

    return statusMap;
  }
}

module.exports = new Follow();
```

### 3.3 服务层 (followService.js)
```javascript
const Follow = require('../models/Follow');
const User = require('../../database/models/User');
const notificationService = require('../../services/notificationService');
const { AppError } = require('../../middleware/errorHandler');

class FollowService {
  // 关注用户
  async followUser(followerId, followedId) {
    // 验证被关注用户存在
    const targetUser = await User.findById(followedId);
    if (!targetUser) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }

    const followId = await Follow.followUser(followerId, followedId);

    // 发送关注通知
    await this.sendFollowNotification(followerId, followedId);

    return { success: true, followId };
  }

  // 取消关注
  async unfollowUser(followerId, followedId) {
    await Follow.unfollowUser(followerId, followedId);
    return { success: true };
  }

  // 获取粉丝列表
  async getFollowers(userId, page = 1, limit = 20, currentUserId = null) {
    const result = await Follow.getFollowers(userId, page, limit);

    // 如果有当前用户，检查关注状态
    if (currentUserId && result.data.length > 0) {
      const userIds = result.data.map(user => user.id);
      const followStatus = await Follow.batchCheckFollowStatus(currentUserId, userIds);
      
      result.data = result.data.map(user => ({
        ...user,
        is_following: followStatus[user.id] || false
      }));
    }

    return result;
  }

  // 获取关注列表
  async getFollowing(userId, page = 1, limit = 20, currentUserId = null) {
    const result = await Follow.getFollowing(userId, page, limit);

    // 如果有当前用户且不是本人，检查关注状态
    if (currentUserId && currentUserId !== userId && result.data.length > 0) {
      const userIds = result.data.map(user => user.id);
      const followStatus = await Follow.batchCheckFollowStatus(currentUserId, userIds);
      
      result.data = result.data.map(user => ({
        ...user,
        is_following: followStatus[user.id] || false
      }));
    }

    return result;
  }

  // 获取用户关注统计
  async getFollowStats(userId) {
    const sql = `
      SELECT 
        COALESCE(us.follower_count, 0) as follower_count,
        COALESCE(us.following_count, 0) as following_count,
        COALESCE(us.video_count, 0) as video_count,
        COALESCE(us.total_views, 0) as total_views,
        COALESCE(us.total_likes, 0) as total_likes
      FROM users u
      LEFT JOIN user_stats us ON u.id = us.user_id
      WHERE u.id = ?
    `;

    const result = await Follow.query(sql, [userId]);
    return result[0] || {
      follower_count: 0,
      following_count: 0,
      video_count: 0,
      total_views: 0,
      total_likes: 0
    };
  }

  // 发送关注通知
  async sendFollowNotification(followerId, followedId) {
    try {
      const follower = await User.findById(followerId);
      
      await notificationService.createNotification({
        user_id: followedId,
        type: 'follow',
        title: '新增粉丝',
        message: `${follower.nickname || follower.username} 关注了你`,
        reference_id: followerId,
        reference_type: 'user'
      });
    } catch (error) {
      // 通知发送失败不影响关注操作
      console.error('发送关注通知失败:', error);
    }
  }

  // 获取关注者的最新视频 (用于推荐)
  async getFollowingVideos(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const sql = `
      SELECT 
        v.id, v.title, v.description, v.thumbnail_url, v.url, 
        v.duration, v.view_count, v.like_count, v.comment_count,
        v.published_at, v.created_at,
        u.id as uploader_id, u.username, u.nickname, u.avatar
      FROM videos v
      JOIN users u ON v.user_id = u.id
      JOIN follows f ON u.id = f.followed_id
      WHERE f.follower_id = ? 
        AND f.status = 'active'
        AND v.status = 'published'
        AND v.visibility IN ('public', 'member_only')
      ORDER BY v.published_at DESC
      LIMIT ? OFFSET ?
    `;

    const videos = await Follow.query(sql, [userId, limit, offset]);

    const countSql = `
      SELECT COUNT(*) as total
      FROM videos v
      JOIN follows f ON v.user_id = f.followed_id
      WHERE f.follower_id = ? 
        AND f.status = 'active'
        AND v.status = 'published'
        AND v.visibility IN ('public', 'member_only')
    `;
    const countResult = await Follow.query(countSql, [userId]);

    return {
      data: videos,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: countResult[0].total,
        totalPages: Math.ceil(countResult[0].total / limit)
      }
    };
  }
}

module.exports = new FollowService();
```

### 3.4 控制器 (followController.js)
```javascript
const followService = require('../services/followService');
const { AppError } = require('../../middleware/errorHandler');

class FollowController {
  // 关注用户
  async followUser(req, res, next) {
    try {
      const followerId = req.user.id;
      const { userId: followedId } = req.params;

      const result = await followService.followUser(followerId, parseInt(followedId));

      res.json({
        success: true,
        message: '关注成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 取消关注
  async unfollowUser(req, res, next) {
    try {
      const followerId = req.user.id;
      const { userId: followedId } = req.params;

      const result = await followService.unfollowUser(followerId, parseInt(followedId));

      res.json({
        success: true,
        message: '取消关注成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取粉丝列表
  async getFollowers(req, res, next) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 20 } = req.query;
      const currentUserId = req.user?.id;

      const result = await followService.getFollowers(
        parseInt(userId), 
        parseInt(page), 
        parseInt(limit),
        currentUserId
      );

      res.json({
        success: true,
        message: '获取粉丝列表成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取关注列表
  async getFollowing(req, res, next) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 20 } = req.query;
      const currentUserId = req.user?.id;

      const result = await followService.getFollowing(
        parseInt(userId), 
        parseInt(page), 
        parseInt(limit),
        currentUserId
      );

      res.json({
        success: true,
        message: '获取关注列表成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 检查关注状态
  async checkFollowStatus(req, res, next) {
    try {
      const followerId = req.user.id;
      const { userId: followedId } = req.params;

      const Follow = require('../models/Follow');
      const isFollowing = await Follow.checkFollowStatus(followerId, parseInt(followedId));

      res.json({
        success: true,
        data: {
          is_following: isFollowing
        }
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取关注统计
  async getFollowStats(req, res, next) {
    try {
      const { userId } = req.params;

      const stats = await followService.getFollowStats(parseInt(userId));

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取关注者的视频动态
  async getFollowingVideos(req, res, next) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 20 } = req.query;

      const result = await followService.getFollowingVideos(
        userId, 
        parseInt(page), 
        parseInt(limit)
      );

      res.json({
        success: true,
        message: '获取关注动态成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new FollowController();
```

### 3.5 路由配置 (routes.js)
```javascript
const express = require('express');
const router = express.Router();
const followController = require('./controllers/followController');
const authMiddleware = require('../../middleware/auth');

// 需要登录的路由
router.use(authMiddleware);

// 关注相关路由
router.post('/follow/:userId', followController.followUser);        // 关注用户
router.delete('/follow/:userId', followController.unfollowUser);    // 取消关注
router.get('/follow/:userId/status', followController.checkFollowStatus); // 检查关注状态

// 粉丝和关注列表
router.get('/users/:userId/followers', followController.getFollowers);  // 获取粉丝列表
router.get('/users/:userId/following', followController.getFollowing);  // 获取关注列表
router.get('/users/:userId/stats', followController.getFollowStats);    // 获取关注统计

// 关注动态
router.get('/following/videos', followController.getFollowingVideos);   // 获取关注者视频动态

module.exports = router;
```

### 3.6 主应用集成 (app.js)
在现有的 app.js 中添加关注模块路由：
```javascript
// 导入关注模块路由
const followRoutes = require('./src/modules/follow/routes');

// 注册路由
app.use('/api', followRoutes);
```

## 4. 前端实现方案

### 4.1 API 服务 (src/services/followApi.ts)
```typescript
import { api } from './api';

export interface User {
  id: number;
  username: string;
  nickname: string;
  avatar: string;
  bio: string;
  follower_count: number;
  following_count: number;
  video_count: number;
  is_following?: boolean;
}

export interface FollowResponse {
  success: boolean;
  message: string;
  data: any;
}

export interface UserListResponse {
  data: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface FollowStats {
  follower_count: number;
  following_count: number;
  video_count: number;
  total_views: number;
  total_likes: number;
}

export const followApi = {
  // 关注用户
  followUser: (userId: number): Promise<FollowResponse> =>
    api.post(`/follow/${userId}`),

  // 取消关注
  unfollowUser: (userId: number): Promise<FollowResponse> =>
    api.delete(`/follow/${userId}`),

  // 检查关注状态
  checkFollowStatus: (userId: number): Promise<{ data: { is_following: boolean } }> =>
    api.get(`/follow/${userId}/status`),

  // 获取粉丝列表
  getFollowers: (userId: number, page = 1, limit = 20): Promise<{ data: UserListResponse }> =>
    api.get(`/users/${userId}/followers`, { params: { page, limit } }),

  // 获取关注列表  
  getFollowing: (userId: number, page = 1, limit = 20): Promise<{ data: UserListResponse }> =>
    api.get(`/users/${userId}/following`, { params: { page, limit } }),

  // 获取关注统计
  getFollowStats: (userId: number): Promise<{ data: FollowStats }> =>
    api.get(`/users/${userId}/stats`),

  // 获取关注动态
  getFollowingVideos: (page = 1, limit = 20) =>
    api.get('/following/videos', { params: { page, limit } })
};
```

### 4.2 关注按钮组件 (src/components/common/FollowButton.tsx)
```typescript
import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { followApi } from '../../services/followApi';
import { useAuth } from '../../hooks/useAuth';
import { toast } from 'sonner';

interface FollowButtonProps {
  userId: number;
  initialFollowStatus?: boolean;
  onFollowChange?: (isFollowing: boolean) => void;
  size?: 'sm' | 'md' | 'lg';
}

export const FollowButton: React.FC<FollowButtonProps> = ({
  userId,
  initialFollowStatus = false,
  onFollowChange,
  size = 'md'
}) => {
  const { user } = useAuth();
  const [isFollowing, setIsFollowing] = useState(initialFollowStatus);
  const [loading, setLoading] = useState(false);

  // 如果是自己，不显示关注按钮
  if (user?.id === userId) {
    return null;
  }

  // 检查关注状态
  useEffect(() => {
    const checkStatus = async () => {
      if (!user) return;
      
      try {
        const response = await followApi.checkFollowStatus(userId);
        setIsFollowing(response.data.is_following);
      } catch (error) {
        console.error('检查关注状态失败:', error);
      }
    };

    checkStatus();
  }, [userId, user]);

  const handleFollowToggle = async () => {
    if (!user) {
      toast.error('请先登录');
      return;
    }

    setLoading(true);
    try {
      if (isFollowing) {
        await followApi.unfollowUser(userId);
        setIsFollowing(false);
        toast.success('取消关注成功');
      } else {
        await followApi.followUser(userId);
        setIsFollowing(true);
        toast.success('关注成功');
      }

      onFollowChange?.(isFollowing);
    } catch (error: any) {
      toast.error(error.response?.data?.message || '操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={handleFollowToggle}
      loading={loading}
      size={size}
      variant={isFollowing ? 'outline' : 'default'}
      className={isFollowing ? 'text-gray-600' : ''}
    >
      {isFollowing ? '已关注' : '+ 关注'}
    </Button>
  );
};
```

### 4.3 用户统计组件 (src/components/user/UserStats.tsx)
```typescript
import React, { useEffect, useState } from 'react';
import { followApi, FollowStats } from '../../services/followApi';

interface UserStatsProps {
  userId: number;
}

export const UserStats: React.FC<UserStatsProps> = ({ userId }) => {
  const [stats, setStats] = useState<FollowStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await followApi.getFollowStats(userId);
        setStats(response.data);
      } catch (error) {
        console.error('获取用户统计失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [userId]);

  if (loading || !stats) {
    return <div className="animate-pulse h-16 bg-gray-200 rounded"></div>;
  }

  return (
    <div className="flex space-x-6 text-center">
      <div>
        <div className="text-2xl font-bold">{stats.video_count}</div>
        <div className="text-sm text-gray-600">作品</div>
      </div>
      <div>
        <div className="text-2xl font-bold">{stats.follower_count}</div>
        <div className="text-sm text-gray-600">粉丝</div>
      </div>
      <div>
        <div className="text-2xl font-bold">{stats.following_count}</div>
        <div className="text-sm text-gray-600">关注</div>
      </div>
      <div>
        <div className="text-2xl font-bold">{stats.total_likes}</div>
        <div className="text-sm text-gray-600">获赞</div>
      </div>
    </div>
  );
};
```

### 4.4 粉丝/关注列表页面 (src/pages/FollowListPage.tsx)
```typescript
import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { FollowButton } from '../components/common/FollowButton';
import { followApi, User } from '../services/followApi';

export const FollowListPage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const tab = searchParams.get('tab') || 'followers';

  const [followers, setFollowers] = useState<User[]>([]);
  const [following, setFollowing] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);

  const fetchFollowers = async (page = 1) => {
    if (!userId) return;
    
    setLoading(true);
    try {
      const response = await followApi.getFollowers(parseInt(userId), page);
      setFollowers(page === 1 ? response.data.data : [...followers, ...response.data.data]);
    } catch (error) {
      console.error('获取粉丝列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchFollowing = async (page = 1) => {
    if (!userId) return;
    
    setLoading(true);
    try {
      const response = await followApi.getFollowing(parseInt(userId), page);
      setFollowing(page === 1 ? response.data.data : [...following, ...response.data.data]);
    } catch (error) {
      console.error('获取关注列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (tab === 'followers') {
      fetchFollowers();
    } else {
      fetchFollowing();
    }
  }, [userId, tab]);

  const handleTabChange = (value: string) => {
    setSearchParams({ tab: value });
    setPage(1);
  };

  const UserListItem: React.FC<{ user: User }> = ({ user }) => (
    <div className="flex items-center justify-between p-4 border-b">
      <div className="flex items-center space-x-3">
        <Avatar>
          <AvatarImage src={user.avatar} />
          <AvatarFallback>{user.nickname?.[0] || user.username?.[0]}</AvatarFallback>
        </Avatar>
        <div>
          <div className="font-semibold">{user.nickname || user.username}</div>
          <div className="text-sm text-gray-600">
            {user.follower_count} 粉丝 · {user.video_count} 作品
          </div>
          {user.bio && (
            <div className="text-sm text-gray-500 mt-1">{user.bio}</div>
          )}
        </div>
      </div>
      <FollowButton 
        userId={user.id} 
        initialFollowStatus={user.is_following}
      />
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Tabs value={tab} onValueChange={handleTabChange}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="followers">粉丝</TabsTrigger>
          <TabsTrigger value="following">关注</TabsTrigger>
        </TabsList>
        
        <TabsContent value="followers" className="mt-4">
          <div className="space-y-0">
            {followers.map(user => (
              <UserListItem key={user.id} user={user} />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="following" className="mt-4">
          <div className="space-y-0">
            {following.map(user => (
              <UserListItem key={user.id} user={user} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
```

### 4.5 关注动态页面 (src/pages/FollowingFeedPage.tsx)
```typescript
import React, { useState, useEffect } from 'react';
import { VideoCard } from '../components/user/VideoCard';
import { followApi } from '../services/followApi';

export const FollowingFeedPage: React.FC = () => {
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchVideos = async (page = 1) => {
    setLoading(true);
    try {
      const response = await followApi.getFollowingVideos(page);
      const newVideos = response.data.data;
      
      if (page === 1) {
        setVideos(newVideos);
      } else {
        setVideos(prev => [...prev, ...newVideos]);
      }
      
      setHasMore(response.data.pagination.page < response.data.pagination.totalPages);
    } catch (error) {
      console.error('获取关注动态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVideos();
  }, []);

  const loadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchVideos(nextPage);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">关注动态</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {videos.map((video) => (
          <VideoCard key={video.id} video={video} />
        ))}
      </div>

      {hasMore && (
        <div className="text-center mt-6">
          <button
            onClick={loadMore}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? '加载中...' : '加载更多'}
          </button>
        </div>
      )}
    </div>
  );
};
```

## 5. 数据库迁移脚本

### 5.1 创建迁移文件
创建文件: `src/database/migrations/20250126_create_follow_system.sql`

```sql
-- 创建关注关系表
CREATE TABLE follows (
    id INT PRIMARY KEY AUTO_INCREMENT,
    follower_id INT NOT NULL COMMENT '关注者用户ID',
    followed_id INT NOT NULL COMMENT '被关注者用户ID', 
    status ENUM('active', 'blocked') DEFAULT 'active' COMMENT '关注状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (followed_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY uk_follower_followed (follower_id, followed_id),
    INDEX idx_follower_id (follower_id),
    INDEX idx_followed_id (followed_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    CHECK (follower_id != followed_id)
) ENGINE=InnoDB COMMENT='用户关注关系表';

-- 创建用户统计表
CREATE TABLE user_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    follower_count INT DEFAULT 0 COMMENT '粉丝数量',
    following_count INT DEFAULT 0 COMMENT '关注数量',
    video_count INT DEFAULT 0 COMMENT '视频数量',
    total_views INT DEFAULT 0 COMMENT '总播放量',
    total_likes INT DEFAULT 0 COMMENT '总获赞数',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_follower_count (follower_count),
    INDEX idx_following_count (following_count)
) ENGINE=InnoDB COMMENT='用户统计信息表';

-- 修改通知表支持关注通知
ALTER TABLE notifications 
MODIFY COLUMN type ENUM('system', 'video_review', 'comment', 'reply', 'follow', 'video_upload') 
DEFAULT 'system' COMMENT '通知类型';

-- 为现有用户初始化统计数据
INSERT INTO user_stats (user_id, video_count, total_views, total_likes)
SELECT 
    u.id,
    COALESCE(video_stats.video_count, 0),
    COALESCE(video_stats.total_views, 0),
    COALESCE(video_stats.total_likes, 0)
FROM users u
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) as video_count,
        SUM(view_count) as total_views,
        SUM(like_count) as total_likes
    FROM videos 
    WHERE status != 'deleted'
    GROUP BY user_id
) video_stats ON u.id = video_stats.user_id;
```

### 5.2 触发器优化统计数据
```sql
-- 当用户发布新视频时自动更新统计
DELIMITER //
CREATE TRIGGER update_user_stats_on_video_insert
AFTER INSERT ON videos
FOR EACH ROW
BEGIN
    INSERT INTO user_stats (user_id, video_count) 
    VALUES (NEW.user_id, 1)
    ON DUPLICATE KEY UPDATE 
        video_count = video_count + 1,
        updated_at = CURRENT_TIMESTAMP;
END //

-- 当视频被删除时更新统计
CREATE TRIGGER update_user_stats_on_video_delete  
AFTER UPDATE ON videos
FOR EACH ROW
BEGIN
    IF OLD.status != 'deleted' AND NEW.status = 'deleted' THEN
        UPDATE user_stats 
        SET video_count = GREATEST(video_count - 1, 0),
            total_views = GREATEST(total_views - OLD.view_count, 0),
            total_likes = GREATEST(total_likes - OLD.like_count, 0),
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = NEW.user_id;
    END IF;
END //

-- 当视频播放量或点赞数更新时更新统计
CREATE TRIGGER update_user_stats_on_video_update
AFTER UPDATE ON videos  
FOR EACH ROW
BEGIN
    IF NEW.status != 'deleted' THEN
        UPDATE user_stats 
        SET total_views = total_views + (NEW.view_count - OLD.view_count),
            total_likes = total_likes + (NEW.like_count - OLD.like_count),
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = NEW.user_id;
    END IF;
END //
DELIMITER ;
```

## 6. API 接口文档

### 6.1 关注操作接口

#### 关注用户
```
POST /api/follow/:userId
Authorization: Bearer <token>

Response:
{
  "success": true,
  "message": "关注成功",
  "data": {
    "success": true,
    "followId": 123
  }
}
```

#### 取消关注  
```
DELETE /api/follow/:userId
Authorization: Bearer <token>

Response:
{
  "success": true,
  "message": "取消关注成功",
  "data": {
    "success": true
  }
}
```

#### 检查关注状态
```
GET /api/follow/:userId/status
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "is_following": true
  }
}
```

### 6.2 用户列表接口

#### 获取粉丝列表
```
GET /api/users/:userId/followers?page=1&limit=20

Response:
{
  "success": true,
  "message": "获取粉丝列表成功",
  "data": {
    "data": [
      {
        "id": 1,
        "username": "user1",
        "nickname": "用户1",
        "avatar": "avatar_url",
        "bio": "个人简介",
        "follower_count": 100,
        "following_count": 50,
        "video_count": 10,
        "is_following": false,
        "follow_time": "2025-01-26T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

#### 获取关注列表
```
GET /api/users/:userId/following?page=1&limit=20

Response: (同粉丝列表格式)
```

### 6.3 统计信息接口

#### 获取用户统计
```
GET /api/users/:userId/stats

Response:
{
  "success": true,
  "data": {
    "follower_count": 1000,
    "following_count": 200,
    "video_count": 50,
    "total_views": 10000,
    "total_likes": 5000
  }
}
```

### 6.4 关注动态接口

#### 获取关注者视频动态
```
GET /api/following/videos?page=1&limit=20
Authorization: Bearer <token>

Response:
{
  "success": true,
  "message": "获取关注动态成功", 
  "data": {
    "data": [
      {
        "id": 1,
        "title": "视频标题",
        "description": "视频描述",
        "thumbnail_url": "缩略图URL",
        "url": "视频URL",
        "duration": 120,
        "view_count": 1000,
        "like_count": 100,
        "comment_count": 50,
        "published_at": "2025-01-26T10:00:00Z",
        "uploader_id": 2,
        "username": "creator1",
        "nickname": "创作者1",
        "avatar": "avatar_url"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

## 7. 部署指南

### 7.1 数据库迁移
```bash
# 1. 执行数据库迁移
mysql -u username -p database_name < src/database/migrations/20250126_create_follow_system.sql

# 2. 验证表创建
mysql -u username -p -e "SHOW TABLES LIKE 'follows'; SHOW TABLES LIKE 'user_stats';" database_name
```

### 7.2 后端部署
```bash
# 1. 安装依赖（如有新增）
npm install

# 2. 重启应用
pm2 restart all
# 或
npm run start

# 3. 验证路由
curl -X GET http://localhost:3000/api/users/1/stats
```

### 7.3 前端部署
```bash
# 1. 构建前端（如有修改）
cd video-api-nexus-main
npm run build

# 2. 部署构建文件
# 将 dist/ 目录内容部署到 Web 服务器
```

## 8. 测试计划

### 8.1 单元测试
- Follow 模型方法测试
- FollowService 业务逻辑测试
- API 接口响应测试

### 8.2 集成测试  
- 关注/取消关注流程测试
- 统计数据一致性测试
- 通知发送测试

### 8.3 性能测试
- 大量用户关注关系查询性能
- 粉丝列表分页加载性能
- 统计数据更新性能

### 8.4 用户体验测试
- 关注按钮交互响应
- 列表滚动加载体验
- 移动端适配测试

## 9. 功能特色

### 9.1 类抖音体验
- **一键关注**: 类似抖音的关注按钮设计
- **粉丝互动**: 完整的粉丝-创作者关系管理
- **动态推荐**: 基于关注关系的视频推荐
- **实时通知**: 关注时即时通知被关注者

### 9.2 数据统计
- **实时统计**: 粉丝数、关注数、作品数实时更新
- **数据一致性**: 通过触发器保证统计数据准确性
- **性能优化**: 专门的统计表避免重复计算

### 9.3 扩展性设计
- **模块化架构**: 独立的关注模块，易于维护
- **数据库优化**: 合理的索引设计支持大规模数据
- **API 标准化**: RESTful API 设计，易于集成

## 10. 总结

本方案提供了完整的用户关注功能实现，包括：

1. **完整的数据库设计** - 关注关系表、用户统计表、通知扩展
2. **后端 API 实现** - 模块化的 MVC 架构，包含完整的业务逻辑
3. **前端组件实现** - React + TypeScript 组件，提供良好的用户体验
4. **性能优化方案** - 索引优化、统计表设计、触发器自动更新
5. **完整的 API 文档** - 便于前后端对接和后续维护

该方案可以让用户像在抖音一样关注喜欢的创作者，查看粉丝列表，获取关注动态，提供完整的社交功能体验。