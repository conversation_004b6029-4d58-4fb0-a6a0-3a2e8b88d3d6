const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// 测试用户数据
const testUsers = [
  {
    email: '<EMAIL>',
    username: 'user1',
    password: 'test123456',
    nickname: '测试用户1'
  },
  {
    email: '<EMAIL>', 
    username: 'user2',
    password: 'test123456',
    nickname: '测试用户2'
  }
];

let user1Token = '';
let user2Token = '';
let user1Id = 0;
let user2Id = 0;

async function createTestUsers() {
  console.log('📝 创建测试用户...');
  
  try {
    // 创建用户1
    const user1Response = await axios.post(`${API_BASE}/auth/register`, testUsers[0]);
    console.log('✅ 用户1创建成功');
    
    // 创建用户2
    const user2Response = await axios.post(`${API_BASE}/auth/register`, testUsers[1]);
    console.log('✅ 用户2创建成功');
    
  } catch (error) {
    console.log('❌ 创建用户失败，可能用户已存在，继续测试...');
  }
}

async function loginUsers() {
  console.log('🔐 用户登录...');
  
  try {
    // 用户1登录
    const user1Login = await axios.post(`${API_BASE}/auth/login`, {
      email: testUsers[0].email,
      password: testUsers[0].password
    });
    console.log('用户1登录响应:', user1Login.data);
    user1Token = user1Login.data.data.tokens.accessToken;
    user1Id = user1Login.data.data.user.id;
    console.log(`✅ 用户1登录成功 (ID: ${user1Id}, Token: ${user1Token ? '有效' : '无效'})`);
    
    // 用户2登录
    const user2Login = await axios.post(`${API_BASE}/auth/login`, {
      email: testUsers[1].email,
      password: testUsers[1].password
    });
    console.log('用户2登录响应:', user2Login.data);
    user2Token = user2Login.data.data.tokens.accessToken;
    user2Id = user2Login.data.data.user.id;
    console.log(`✅ 用户2登录成功 (ID: ${user2Id}, Token: ${user2Token ? '有效' : '无效'})`);
    
  } catch (error) {
    console.error('❌ 用户登录失败:', error.response?.data || error.message);
    process.exit(1);
  }
}

async function testFollowFunctionality() {
  console.log('\\n🎯 开始测试关注功能...');
  
  // 检查token是否存在
  if (!user1Token || !user2Token) {
    console.error('❌ 缺少认证token');
    return;
  }
  
  try {
    // 验证token有效性
    console.log('🔍 验证用户1 token:', user1Token.substring(0, 20) + '...');
    
    // 先测试用户个人资料接口，确认token有效
    const profileResponse = await axios.get(
      `${API_BASE}/user/profile`,
      { 
        headers: { 
          'Authorization': `Bearer ${user1Token}`,
          'Content-Type': 'application/json'
        } 
      }
    );
    console.log('✅ Token验证成功，用户信息:', { id: profileResponse.data.data.id, username: profileResponse.data.data.username });
    
    // 1. 用户1关注用户2
    console.log('\\n1️⃣ 测试关注功能');
    console.log(`请求: POST ${API_BASE}/follow/${user2Id}`);
    
    const followResponse = await axios.post(
      `${API_BASE}/follow/${user2Id}`,
      {},
      { 
        headers: { 
          'Authorization': `Bearer ${user1Token}`,
          'Content-Type': 'application/json'
        } 
      }
    );
    console.log('✅ 用户1关注用户2成功:', followResponse.data.message);
    
    // 2. 检查关注状态
    console.log('\\n2️⃣ 检查关注状态');
    const statusResponse = await axios.get(
      `${API_BASE}/follow/${user2Id}/status`,
      { headers: { Authorization: `Bearer ${user1Token}` } }
    );
    console.log('✅ 关注状态检查成功:', statusResponse.data.data);
    
    // 3. 获取用户2的粉丝列表
    console.log('\\n3️⃣ 获取用户2的粉丝列表');
    const followersResponse = await axios.get(
      `${API_BASE}/users/${user2Id}/followers`,
      { headers: { Authorization: `Bearer ${user2Token}` } }
    );
    console.log('✅ 粉丝列表获取成功:', {
      total: followersResponse.data.data.pagination.total,
      followers: followersResponse.data.data.data.map(f => ({ id: f.id, nickname: f.nickname }))
    });
    
    // 4. 获取用户1的关注列表
    console.log('\\n4️⃣ 获取用户1的关注列表');
    const followingResponse = await axios.get(
      `${API_BASE}/users/${user1Id}/following`,
      { headers: { Authorization: `Bearer ${user1Token}` } }
    );
    console.log('✅ 关注列表获取成功:', {
      total: followingResponse.data.data.pagination.total,
      following: followingResponse.data.data.data.map(f => ({ id: f.id, nickname: f.nickname }))
    });
    
    // 5. 获取关注统计
    console.log('\\n5️⃣ 获取用户统计');
    const statsResponse = await axios.get(
      `${API_BASE}/users/${user2Id}/stats`,
      { headers: { Authorization: `Bearer ${user1Token}` } }
    );
    console.log('✅ 用户2统计信息:', statsResponse.data.data);
    
    // 6. 取消关注
    console.log('\\n6️⃣ 测试取消关注');
    const unfollowResponse = await axios.delete(
      `${API_BASE}/follow/${user2Id}`,
      { headers: { Authorization: `Bearer ${user1Token}` } }
    );
    console.log('✅ 取消关注成功:', unfollowResponse.data.message);
    
    // 7. 再次检查关注状态
    console.log('\\n7️⃣ 验证取消关注后的状态');
    const finalStatusResponse = await axios.get(
      `${API_BASE}/follow/${user2Id}/status`,
      { headers: { Authorization: `Bearer ${user1Token}` } }
    );
    console.log('✅ 最终关注状态:', finalStatusResponse.data.data);
    
    console.log('\\n🎉 所有关注功能测试通过！');
    
  } catch (error) {
    console.error('❌ 关注功能测试失败:', error.response?.data || error.message);
    if (error.response?.status) {
      console.error('状态码:', error.response.status);
    }
  }
}

async function runTests() {
  console.log('🚀 开始关注功能测试...');
  
  await createTestUsers();
  await loginUsers();
  await testFollowFunctionality();
  
  console.log('\\n✨ 测试完成');
  process.exit(0);
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 运行测试
runTests().catch(console.error);