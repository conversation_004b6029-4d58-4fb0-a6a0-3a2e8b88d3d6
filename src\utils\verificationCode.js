const crypto = require('crypto');
const logger = require('./logger');

/**
 * 验证码工具类
 * 提供统一的验证码生成、验证和管理功能
 */
class VerificationCodeUtil {
  
  /**
   * 生成6位纯数字验证码
   * @returns {string} 6位数字验证码 (100000-999999)
   */
  static generateNumericCode() {
    // 生成100000到999999之间的随机数
    const code = Math.floor(100000 + Math.random() * 900000);
    logger.debug(`生成数字验证码: ${code}`);
    return code.toString();
  }

  /**
   * 生成指定长度的数字验证码
   * @param {number} length - 验证码长度，默认6位
   * @returns {string} 指定长度的数字验证码
   */
  static generateNumericCodeWithLength(length = 6) {
    if (length < 4 || length > 10) {
      throw new Error('验证码长度必须在4-10位之间');
    }
    
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    const code = Math.floor(min + Math.random() * (max - min + 1));
    
    logger.debug(`生成${length}位数字验证码: ${code}`);
    return code.toString();
  }

  /**
   * 生成安全的十六进制令牌（用于邮箱验证链接等）
   * @param {number} bytes - 字节数，默认32字节（64位十六进制字符）
   * @returns {string} 十六进制令牌
   */
  static generateSecureToken(bytes = 32) {
    const token = crypto.randomBytes(bytes).toString('hex');
    logger.debug(`生成安全令牌，长度: ${token.length}位`);
    return token;
  }

  /**
   * 验证数字验证码格式
   * @param {string} code - 待验证的验证码
   * @param {number} expectedLength - 期望的长度，默认6位
   * @returns {boolean} 是否为有效格式
   */
  static isValidNumericCode(code, expectedLength = 6) {
    if (!code || typeof code !== 'string') {
      return false;
    }
    
    // 检查是否为纯数字且长度正确
    const numericRegex = new RegExp(`^\\d{${expectedLength}}$`);
    return numericRegex.test(code);
  }

  /**
   * 验证十六进制令牌格式
   * @param {string} token - 待验证的令牌
   * @param {number} expectedLength - 期望的长度，默认64位
   * @returns {boolean} 是否为有效格式
   */
  static isValidHexToken(token, expectedLength = 64) {
    if (!token || typeof token !== 'string') {
      return false;
    }
    
    // 检查是否为十六进制且长度正确
    const hexRegex = new RegExp(`^[a-fA-F0-9]{${expectedLength}}$`);
    return hexRegex.test(token);
  }

  /**
   * 生成验证码配置对象
   * @param {string} type - 验证码类型 ('email_change', 'email_verification', 'password_reset')
   * @returns {object} 包含验证码和配置的对象
   */
  static generateCodeConfig(type) {
    const configs = {
      email_change: {
        code: this.generateNumericCode(),
        ttl: 10 * 60, // 10分钟
        type: 'numeric',
        description: '邮箱更改验证码'
      },
      email_verification: {
        code: this.generateSecureToken(32),
        ttl: 24 * 60 * 60, // 24小时
        type: 'token',
        description: '邮箱验证令牌'
      },
      password_reset: {
        code: this.generateSecureToken(32),
        ttl: 60 * 60, // 1小时
        type: 'token',
        description: '密码重置令牌'
      },
      registration: {
        code: this.generateNumericCodeWithLength(6),
        ttl: 5 * 60, // 5分钟
        type: 'numeric',
        description: '注册验证码'
      }
    };

    const config = configs[type];
    if (!config) {
      throw new Error(`不支持的验证码类型: ${type}`);
    }

    logger.info(`生成${config.description}: ${config.type === 'numeric' ? config.code : '***'}`);
    return config;
  }

  /**
   * 格式化验证码用于显示（隐藏敏感信息）
   * @param {string} code - 验证码
   * @param {string} type - 类型 ('numeric' 或 'token')
   * @returns {string} 格式化后的显示字符串
   */
  static formatCodeForDisplay(code, type = 'numeric') {
    if (type === 'numeric') {
      return code; // 数字验证码可以完整显示
    } else {
      // 令牌只显示前4位和后4位
      if (code.length > 8) {
        return `${code.substring(0, 4)}...${code.substring(code.length - 4)}`;
      }
      return '***';
    }
  }

  /**
   * 验证码比较（支持大小写不敏感）
   * @param {string} inputCode - 用户输入的验证码
   * @param {string} storedCode - 存储的验证码
   * @param {boolean} caseSensitive - 是否区分大小写，默认false
   * @returns {boolean} 是否匹配
   */
  static compareCode(inputCode, storedCode, caseSensitive = false) {
    if (!inputCode || !storedCode) {
      return false;
    }

    if (caseSensitive) {
      return inputCode === storedCode;
    } else {
      return inputCode.toUpperCase() === storedCode.toUpperCase();
    }
  }
}

module.exports = VerificationCodeUtil;
