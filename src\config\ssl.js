/**
 * SSL证书配置和管理模块
 * 支持自动检测证书、加载证书、验证证书有效性
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const logger = require('../utils/logger');

class SSLManager {
  constructor() {
    this.sslOptions = null;
    this.certPath = process.env.SSL_CERT_PATH || './certs/certificate.crt';
    this.keyPath = process.env.SSL_KEY_PATH || './certs/private.key';
    this.enableHttps = process.env.ENABLE_HTTPS === 'true';
    this.autoDetect = process.env.AUTO_DETECT_SSL !== 'false';
  }

  /**
   * 检查证书文件是否存在
   */
  checkCertificateFiles() {
    try {
      const certExists = fs.existsSync(this.certPath);
      const keyExists = fs.existsSync(this.keyPath);
      
      logger.info('SSL证书检查:');
      logger.info(`- 证书文件 (${this.certPath}): ${certExists ? '✅ 存在' : '❌ 不存在'}`);
      logger.info(`- 私钥文件 (${this.keyPath}): ${keyExists ? '✅ 存在' : '❌ 不存在'}`);
      
      return certExists && keyExists;
    } catch (error) {
      logger.error('检查证书文件时出错:', error);
      return false;
    }
  }

  /**
   * 加载SSL证书
   */
  loadCertificates() {
    try {
      if (!this.checkCertificateFiles()) {
        return null;
      }

      const cert = fs.readFileSync(this.certPath, 'utf8');
      const key = fs.readFileSync(this.keyPath, 'utf8');

      // 验证证书格式
      if (!cert.includes('BEGIN CERTIFICATE') || !key.includes('BEGIN PRIVATE KEY')) {
        logger.error('SSL证书格式无效');
        return null;
      }

      this.sslOptions = {
        cert: cert,
        key: key,
        // 安全配置
        secureProtocol: 'TLSv1_2_method',
        ciphers: [
          'ECDHE-RSA-AES256-GCM-SHA512',
          'DHE-RSA-AES256-GCM-SHA512',
          'ECDHE-RSA-AES256-GCM-SHA384',
          'DHE-RSA-AES256-GCM-SHA384',
          'ECDHE-RSA-AES256-SHA384'
        ].join(':'),
        honorCipherOrder: true
      };

      logger.info('✅ SSL证书加载成功');
      return this.sslOptions;
    } catch (error) {
      logger.error('加载SSL证书失败:', error);
      return null;
    }
  }

  /**
   * 验证证书有效性
   */
  async validateCertificate() {
    if (!this.sslOptions) {
      return false;
    }

    try {
      // 创建临时HTTPS服务器来验证证书
      const server = https.createServer(this.sslOptions);
      
      return new Promise((resolve) => {
        server.listen(0, () => {
          const port = server.address().port;
          server.close(() => {
            logger.info('✅ SSL证书验证通过');
            resolve(true);
          });
        });

        server.on('error', (error) => {
          logger.error('SSL证书验证失败:', error);
          resolve(false);
        });
      });
    } catch (error) {
      logger.error('验证SSL证书时出错:', error);
      return false;
    }
  }

  /**
   * 获取证书信息
   */
  getCertificateInfo() {
    if (!this.sslOptions) {
      return null;
    }

    try {
      const crypto = require('crypto');
      const cert = this.sslOptions.cert;
      
      // 解析证书信息
      const certBuffer = Buffer.from(cert.replace(/-----BEGIN CERTIFICATE-----|\-----END CERTIFICATE-----|\n/g, ''), 'base64');
      
      return {
        loaded: true,
        certPath: this.certPath,
        keyPath: this.keyPath,
        size: certBuffer.length,
        // 注意：这里只是基本信息，实际生产环境可能需要更详细的证书解析
        status: 'valid'
      };
    } catch (error) {
      logger.error('获取证书信息失败:', error);
      return {
        loaded: false,
        error: error.message
      };
    }
  }

  /**
   * 自动检测并配置SSL
   */
  async autoConfigureSSL() {
    logger.info('🔍 开始自动SSL配置检测...');
    
    // 1. 检查环境变量配置
    if (!this.enableHttps && !this.autoDetect) {
      logger.info('HTTPS未启用且自动检测已禁用');
      return null;
    }

    // 2. 检查证书文件
    if (!this.checkCertificateFiles()) {
      if (this.enableHttps) {
        logger.warn('HTTPS已启用但证书文件不存在，将使用HTTP模式');
      }
      return null;
    }

    // 3. 加载证书
    const sslOptions = this.loadCertificates();
    if (!sslOptions) {
      logger.error('SSL证书加载失败');
      return null;
    }

    // 4. 验证证书
    const isValid = await this.validateCertificate();
    if (!isValid) {
      logger.error('SSL证书验证失败');
      return null;
    }

    logger.info('🎉 SSL自动配置成功');
    return sslOptions;
  }

  /**
   * 创建证书目录
   */
  createCertDirectory() {
    const certDir = path.dirname(this.certPath);
    const keyDir = path.dirname(this.keyPath);
    
    try {
      if (!fs.existsSync(certDir)) {
        fs.mkdirSync(certDir, { recursive: true });
        logger.info(`创建证书目录: ${certDir}`);
      }
      
      if (certDir !== keyDir && !fs.existsSync(keyDir)) {
        fs.mkdirSync(keyDir, { recursive: true });
        logger.info(`创建私钥目录: ${keyDir}`);
      }
      
      return true;
    } catch (error) {
      logger.error('创建证书目录失败:', error);
      return false;
    }
  }

  /**
   * 生成自签名证书（仅用于开发环境）
   */
  generateSelfSignedCert() {
    if (process.env.NODE_ENV === 'production') {
      logger.warn('生产环境不应使用自签名证书');
      return false;
    }

    try {
      const { execSync } = require('child_process');
      
      // 创建证书目录
      this.createCertDirectory();
      
      // 生成私钥和证书
      const commands = [
        `openssl genrsa -out ${this.keyPath} 2048`,
        `openssl req -new -x509 -key ${this.keyPath} -out ${this.certPath} -days 365 -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"`
      ];
      
      commands.forEach(cmd => {
        execSync(cmd, { stdio: 'inherit' });
      });
      
      logger.info('✅ 自签名证书生成成功');
      return true;
    } catch (error) {
      logger.error('生成自签名证书失败:', error);
      return false;
    }
  }

  /**
   * 获取SSL配置状态
   */
  getSSLStatus() {
    return {
      enabled: this.enableHttps,
      autoDetect: this.autoDetect,
      certPath: this.certPath,
      keyPath: this.keyPath,
      certificatesExist: this.checkCertificateFiles(),
      sslOptionsLoaded: !!this.sslOptions,
      certificateInfo: this.getCertificateInfo()
    };
  }
}

// 创建单例实例
const sslManager = new SSLManager();

module.exports = {
  SSLManager,
  sslManager,
  
  // 便捷方法
  autoConfigureSSL: () => sslManager.autoConfigureSSL(),
  getSSLStatus: () => sslManager.getSSLStatus(),
  checkCertificates: () => sslManager.checkCertificateFiles(),
  loadCertificates: () => sslManager.loadCertificates(),
  generateSelfSignedCert: () => sslManager.generateSelfSignedCert()
};
