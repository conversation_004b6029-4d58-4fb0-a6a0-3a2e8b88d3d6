const { asyncHandler, AppError } = require('../../../middleware/errorHandler');
const User = require('../../../database/models/User');
const Order = require('../../../database/models/Order');
const { generateOrderNo } = require('../../../utils/orderHelper');

class BalanceController {
  /**
   * @description 获取当前用户的余额
   */
  getUserBalance = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const user = await User.findById(userId);

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    res.json({
      success: true,
      data: {
        balance: user.balance,
      },
    });
  });

  /**
   * @description 创建一个待处理的充值订单
   */
  createRechargeOrder = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    // The amount is validated by `validateRecharge` middleware in routes.
    const { amount } = req.body;

    const orderNo = generateOrderNo(); // Corrected function name

    // Directly use the imported Order instance
    const orderData = {
      user_id: userId,
      type: 'BALANCE_RECHARGE', // Use a clear and specific type
      target_id: userId, // The target of the recharge is the user themselves
      amount: amount,
      final_amount: amount,
      payment_method: 'manual', // Indicates admin will handle it
      payment_status: 'pending', // Order is pending until admin action
      order_no: orderNo,
      expires_at: null, // Explicitly set to null for manual orders
      description: `用户(ID: ${userId}) 申请余额充值 ${amount}元`,
    };
    
    // Call the method on the shared instance
    const orderId = await Order.createOrder(orderData);

    res.status(201).json({
      success: true,
      message: '充值申请已提交，请等待管理员审核处理',
      data: {
        orderId,
        orderNo,
        amount,
        status: 'pending',
      },
    });
  });
}

module.exports = new BalanceController(); 