# 上下文
文件名：TRON_payment_integration_task.md
创建于：[日期时间]
创建者：AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol (Conditional Interactive Step Review Enhanced)

# 任务描述
现在请检查管理员后台“支付设置”。
优化给USDT和TRX配置收款监听。
让管理员只需要填写钱包收款地址和trongrid.io的API就可以使用

# 项目概述
本项目是一个包含前后端的视频平台。后端使用Node.js (Express)，前端为React (Vite)。数据库为MySQL。系统包含一个管理后台，其中有支付网关设置功能。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
- 系统拥有一个功能完善且灵活的支付网关管理界面 (`AdminPaymentSettings.tsx`)，可以动态渲染配置项。
- 支付网关配置存储在 `payment_gateways` 表中，`config` 字段为 JSON 字符串，包含如 `apiUrl`, `appId`, `privateKey` 等键值对。
- 后端API (`/api/admin/payment-gateways/:id`) 和服务 (`paymentGatewayService`) 已实现对支付网关的通用增删改查逻辑。
- 当前系统中不存在任何与TRON、TRX、USDT或 `trongrid.io` 相关的代码。需要从头开始构建支付监听和处理逻辑。
- 实现方案需要考虑如何在后台启动一个服务来监听链上交易。

# 提议的解决方案 (由 INNOVATE 模式填充)
在对几种可能的实现方案进行评估后（包括后端轮询、Webhook和前端轮询），最终选择**后端轮询服务**作为最佳方案。

**核心思路:**
1.  **扩展支付网关**: 在数据库中为TRON网络创建一个新的支付网关条目（例如，`key` 为 `tron_usdt`)。其 `config` 将包含 `walletAddress` 和 `apiKey` 两个字段。
2.  **创建后台监听服务**: 开发一个新的后台服务/模块，该服务会：
    a. 以固定的时间间隔（例如，每30-60秒）运行。
    b. 从数据库获取所有已启用的TRON支付网关配置。
    c. 使用配置中的钱包地址和API密钥，调用 `trongrid.io` 的API (`https://api.trongrid.io/v1/accounts/{address}/transactions/trc20`) 来获取最新的TRC20代币（特别是USDT）转账记录。
    d. 对比查询到的交易和系统中已记录的交易，识别出新的、未处理的入账交易。
    e. 对于每一笔新交易，验证其有效性（例如，金额、收款地址、代币类型）。
3.  **创建订单**: 验证通过后，在系统中自动创建一条新的订单记录，并将其状态直接标记为“已支付”。同时，记录相关的交易哈希（TxID），以防止重复处理。
4.  **服务管理**: 将这个后台监听服务集成到现有的Node.js应用中，确保它能随主应用一同启动和停止。可以考虑使用 `node-cron` 或简单的 `setInterval` 来实现定时任务。

**优势**:
- **可靠**: 即使服务短暂中断，下次启动时仍能同步到错过的交易。
- **安全**: 所有敏感信息（如API密钥）都存储在后端，不会暴露给客户端。
- **解耦**: 监听逻辑与核心支付流程分离，易于维护和扩展。

此方案虽然存在分钟级别的确认延迟，但这在加密货币支付场景中是完全可以接受的。

# 实施计划 (由 PLAN 模式生成)
[包含详细步骤、文件路径、函数签名、以及review:true/false标记的最终检查清单]
实施检查清单：
1. [在 `package.json` 中添加 `axios` 和 `node-cron` 依赖, review:true]
2. [运行 `npm install` 来安装新依赖, review:true]
3. [创建数据库迁移文件以向 `orders` 表添加 `transaction_id` 字段, review:true]
4. [运行数据库迁移, review:false]
5. [创建 `src/services/tronTransactionScannerService.js` 文件并实现其基本结构和定时任务逻辑, review:true]
6. [在 `tronTransactionScannerService.js` 中实现与 `trongrid.io` API的交互以及交易数据的处理逻辑, review:true]
7. [在 `tronTransactionScannerService.js` 中实现新订单的创建逻辑, review:true]
8. [在 `app.js` 中集成并启动 `tronTransactionScannerService`, review:true]
9. [创建一个 SQL 脚本在数据库中插入 TRON 支付网关的初始数据, review:true]
10. [更新任务文件 `TRON_payment_integration_task.md` 的 "实施计划" 部分, review:true]

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "[步骤10和更新任务进度]" (审查需求: review:true, 状态: [完成])

# 任务进度 (由 EXECUTE 模式在每步完成后，以及在交互式审查迭代中追加)
*   [2024-07-29]
    *   步骤：[1. 在 `package.json` 中添加 `axios` 和 `node-cron` 依赖, review:true]
    *   修改：无修改。依赖已存在。
    *   更改摘要：检查了`package.json`，确认所需依赖已存在。
    *   原因：执行计划步骤1。
    *   阻碍：无。
    *   用户确认状态：成功
*   [2024-07-29]
    *   步骤：[2. 运行 `npm install` 来安装新依赖, review:true]
    *   修改：无操作。无需安装。
    *   更改摘要：由于依赖已存在，跳过 `npm install`。
    *   原因：执行计划步骤2。
    *   阻碍：无。
    *   用户确认状态：成功
*   [2024-07-29]
    *   步骤：[3. 创建数据库迁移文件以向 `orders` 表添加 `transaction_id` 字段, review:true]
    *   修改：创建文件 `src/database/migrations/20250728_add_transaction_id_to_orders.sql`
    *   更改摘要：添加了用于给 `orders` 表增加 `transaction_id` 列的SQL迁移脚本。
    *   原因：执行计划步骤3。
    *   阻碍：无。
    *   用户确认状态：成功
*   [2024-07-29]
    *   步骤：[4. 运行数据库迁移, review:false]
    *   修改：无。
    *   更改摘要：尝试运行迁移脚本，但发现列已存在。结果符合预期，因此视为成功。
    *   原因：执行计划步骤4。
    *   阻碍：脚本报错，但原因是列已存在，不影响后续步骤。
    *   用户确认状态：成功
*   [2024-07-29]
    *   步骤：[5. 创建 `src/services/tronTransactionScannerService.js`, review:true]
    *   修改：创建文件 `src/services/tronTransactionScannerService.js`
    *   更改摘要：创建了服务文件，并实现了包含定时任务的基本类结构。
    *   原因：执行计划步骤5。
    *   阻碍：无。
    *   用户确认状态：成功
*   [2024-07-29]
    *   步骤：[6. 在 `tronTransactionScannerService.js` 中实现与 `trongrid.io` API的交互, review:true]
    *   修改：更新 `src/services/tronTransactionScannerService.js`
    *   更改摘要：在 `scanTransactions` 方法中添加了调用 `trongrid.io` API 并获取交易数据的逻辑。
    *   原因：执行计划步骤6。
    *   阻碍：无。
    *   用户确认状态：成功
*   [2024-07-29]
    *   步骤：[7. 在 `tronTransactionScannerService.js` 中实现新订单的创建逻辑, review:true]
    *   修改：更新 `src/services/tronTransactionScannerService.js`
    *   更改摘要：实现了 `processTransaction` 方法，用于检查重复交易并创建新订单。
    *   原因：执行计划步骤7。
    *   阻碍：无。
    *   用户确认状态：成功
*   [2024-07-29]
    *   步骤：[8. 在 `app.js` 中集成并启动 `tronTransactionScannerService`, review:true]
    *   修改：更新 `app.js`
    *   更改摘要：在应用启动时导入并启动了 `tronTransactionScannerService`。
    *   原因：执行计划步骤8。
    *   阻碍：无。
    *   用户确认状态：成功
*   [2024-07-29]
    *   步骤：[9. 创建一个 SQL 脚本在数据库中插入 TRON 支付网关的初始数据, review:true]
    *   修改：创建文件 `scripts/insert-tron-gateway.sql`
    *   更改摘要：创建了用于添加TRON支付方式的SQL脚本。
    *   原因：执行计划步骤9。
    *   阻碍：无。
    *   用户确认状态：成功

# 最终审查 (由 REVIEW 模式填充)
[对整个任务所有步骤综合结果的符合性评估总结，是否发现与最终确认计划不符的未报告偏差]
