#!/bin/bash

# 视频网站API框架 - 自动化安装脚本
# 支持Ubuntu/Debian和CentOS/RHEL系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 安装Node.js
install_nodejs() {
    log_info "安装Node.js..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "Node.js已安装: $NODE_VERSION"
        return
    fi
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
        sudo yum install -y nodejs
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    log_success "Node.js安装完成"
}

# 安装MySQL
install_mysql() {
    log_info "安装MySQL..."
    
    if command -v mysql &> /dev/null; then
        log_info "MySQL已安装"
        return
    fi
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt update
        sudo apt install -y mysql-server
        sudo systemctl start mysql
        sudo systemctl enable mysql
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        sudo yum install -y mysql-server
        sudo systemctl start mysqld
        sudo systemctl enable mysqld
    fi
    
    log_success "MySQL安装完成"
    log_warning "请运行 'sudo mysql_secure_installation' 来配置MySQL安全设置"
}

# 安装Redis
install_redis() {
    log_info "安装Redis..."
    
    if command -v redis-server &> /dev/null; then
        log_info "Redis已安装"
        return
    fi
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt install -y redis-server
        sudo systemctl start redis-server
        sudo systemctl enable redis-server
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        sudo yum install -y redis
        sudo systemctl start redis
        sudo systemctl enable redis
    fi
    
    log_success "Redis安装完成"
}

# 安装PM2
install_pm2() {
    log_info "安装PM2..."
    
    if command -v pm2 &> /dev/null; then
        log_info "PM2已安装"
        return
    fi
    
    sudo npm install -g pm2
    log_success "PM2安装完成"
}

# 创建项目目录和用户
setup_project() {
    log_info "设置项目环境..."
    
    # 创建项目目录
    PROJECT_DIR="/opt/video-platform-api"
    if [[ ! -d "$PROJECT_DIR" ]]; then
        sudo mkdir -p "$PROJECT_DIR"
        sudo chown $USER:$USER "$PROJECT_DIR"
    fi
    
    # 创建日志目录
    LOG_DIR="/var/log/video-platform"
    if [[ ! -d "$LOG_DIR" ]]; then
        sudo mkdir -p "$LOG_DIR"
        sudo chown $USER:$USER "$LOG_DIR"
    fi
    
    # 创建上传目录
    UPLOAD_DIR="$PROJECT_DIR/uploads"
    if [[ ! -d "$UPLOAD_DIR" ]]; then
        mkdir -p "$UPLOAD_DIR"/{videos,thumbnails,avatars}
        chmod 755 "$UPLOAD_DIR"
    fi
    
    log_success "项目环境设置完成"
}

# 安装项目依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [[ ! -f "package.json" ]]; then
        log_error "未找到package.json文件，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    npm install
    log_success "项目依赖安装完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.example" ]]; then
            cp .env.example .env
            log_info "已创建.env文件，请编辑配置"
        else
            log_warning "未找到.env.example文件"
        fi
    else
        log_info ".env文件已存在"
    fi
}

# 初始化数据库
setup_database() {
    log_info "初始化数据库..."
    
    read -p "请输入MySQL root密码: " -s MYSQL_ROOT_PASSWORD
    echo
    
    read -p "请输入数据库名称 [video_platform]: " DB_NAME
    DB_NAME=${DB_NAME:-video_platform}
    
    read -p "请输入数据库用户名 [video_user]: " DB_USER
    DB_USER=${DB_USER:-video_user}
    
    read -p "请输入数据库用户密码: " -s DB_PASSWORD
    echo
    
    # 创建数据库和用户
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
        CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
        GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
        FLUSH PRIVILEGES;
    "
    
    # 导入表结构
    if [[ -f "database/schema.sql" ]]; then
        mysql -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < database/schema.sql
        log_success "数据库表结构导入完成"
    else
        log_warning "未找到database/schema.sql文件"
    fi
    
    # 更新.env文件
    if [[ -f ".env" ]]; then
        sed -i "s/DB_NAME=.*/DB_NAME=$DB_NAME/" .env
        sed -i "s/DB_USER=.*/DB_USER=$DB_USER/" .env
        sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" .env
        log_success "数据库配置已更新到.env文件"
    fi
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        sudo ufw allow 22/tcp
        sudo ufw allow 80/tcp
        sudo ufw allow 443/tcp
        sudo ufw allow 3000/tcp
        log_success "UFW防火墙配置完成"
    elif command -v firewall-cmd &> /dev/null; then
        sudo firewall-cmd --permanent --add-port=22/tcp
        sudo firewall-cmd --permanent --add-port=80/tcp
        sudo firewall-cmd --permanent --add-port=443/tcp
        sudo firewall-cmd --permanent --add-port=3000/tcp
        sudo firewall-cmd --reload
        log_success "firewalld防火墙配置完成"
    else
        log_warning "未检测到防火墙，请手动配置"
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动应用
    if [[ -f "ecosystem.config.js" ]]; then
        pm2 start ecosystem.config.js
        pm2 save
        pm2 startup
        log_success "应用已启动"
    else
        log_warning "未找到PM2配置文件，请手动启动应用"
    fi
}

# 显示完成信息
show_completion() {
    log_success "安装完成！"
    echo
    echo "==================================="
    echo "  视频网站API框架安装完成"
    echo "==================================="
    echo
    echo "项目目录: $PROJECT_DIR"
    echo "日志目录: $LOG_DIR"
    echo "配置文件: .env"
    echo
    echo "下一步操作："
    echo "1. 编辑 .env 文件配置参数"
    echo "2. 运行 'npm run dev' 启动开发服务器"
    echo "3. 访问 http://localhost:3000/health 检查服务状态"
    echo "4. 查看 API 文档: http://localhost:3000/docs"
    echo
    echo "常用命令："
    echo "- 查看应用状态: pm2 status"
    echo "- 查看日志: pm2 logs"
    echo "- 重启应用: pm2 restart all"
    echo "- 停止应用: pm2 stop all"
    echo
}

# 主函数
main() {
    echo "==================================="
    echo "  视频网站API框架自动安装脚本"
    echo "==================================="
    echo
    
    check_root
    detect_os
    
    log_info "开始安装..."
    
    install_nodejs
    install_mysql
    install_redis
    install_pm2
    setup_project
    install_dependencies
    setup_environment
    
    read -p "是否要初始化数据库? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_database
    fi
    
    read -p "是否要配置防火墙? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_firewall
    fi
    
    read -p "是否要启动服务? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        start_services
    fi
    
    show_completion
}

# 运行主函数
main "$@"
