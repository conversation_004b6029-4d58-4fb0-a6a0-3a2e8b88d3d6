const BasePaymentService = require('./BasePaymentService');
const xml2js = require('xml2js');

/**
 * 微信支付服务
 * 支持微信支付官方接口
 */
class WechatPayService extends BasePaymentService {
  constructor(config) {
    super(config);
    this.name = 'wechat';
    this.appId = config.appId;
    this.mchId = config.mchId;
    this.apiKey = config.apiKey;
    this.notifyUrl = config.notifyUrl;
    this.apiUrl = 'https://api.mch.weixin.qq.com';
  }

  /**
   * 创建支付订单
   * @param {Object} orderData 订单数据
   * @returns {Promise<Object>} 支付结果
   */
  async createPayment(orderData) {
    try {
      this.validateRequiredFields(orderData, [
        'orderNo', 'amount', 'subject', 'clientIp'
      ]);

      const paymentData = {
        appid: this.appId,
        mch_id: this.mchId,
        nonce_str: this.generateNonce(),
        body: orderData.subject,
        out_trade_no: orderData.orderNo,
        total_fee: this.formatAmount(orderData.amount),
        spbill_create_ip: orderData.clientIp,
        notify_url: this.notifyUrl,
        trade_type: orderData.tradeType || 'NATIVE'
      };

      // 生成签名
      paymentData.sign = this.generateSignature(paymentData);

      this.log('info', '创建微信支付订单', { 
        orderNo: orderData.orderNo, 
        amount: orderData.amount 
      });

      // 转换为XML
      const xmlData = this.objectToXml(paymentData);

      // 调用微信统一下单接口
      const response = await this.httpRequest(`${this.apiUrl}/pay/unifiedorder`, {
        method: 'POST',
        data: xmlData,
        headers: {
          'Content-Type': 'application/xml'
        }
      });

      const result = await this.xmlToObject(response);

      if (result.return_code !== 'SUCCESS') {
        throw new Error(`微信支付接口调用失败: ${result.return_msg}`);
      }

      if (result.result_code !== 'SUCCESS') {
        throw new Error(`微信支付下单失败: ${result.err_code_des || result.err_code}`);
      }

      this.log('info', '微信支付订单创建成功', { 
        orderNo: orderData.orderNo,
        prepayId: result.prepay_id 
      });

      return {
        success: true,
        prepayId: result.prepay_id,
        codeUrl: result.code_url, // 二维码链接
        orderNo: orderData.orderNo,
        paymentMethod: 'wechat',
        data: result
      };

    } catch (error) {
      this.log('error', '创建微信支付订单失败', { 
        orderNo: orderData.orderNo, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 查询支付状态
   * @param {string} orderNo 订单号
   * @returns {Promise<Object>} 支付状态
   */
  async queryPayment(orderNo) {
    try {
      const queryData = {
        appid: this.appId,
        mch_id: this.mchId,
        out_trade_no: orderNo,
        nonce_str: this.generateNonce()
      };

      queryData.sign = this.generateSignature(queryData);

      const xmlData = this.objectToXml(queryData);

      const response = await this.httpRequest(`${this.apiUrl}/pay/orderquery`, {
        method: 'POST',
        data: xmlData,
        headers: {
          'Content-Type': 'application/xml'
        }
      });

      const result = await this.xmlToObject(response);

      this.log('info', '查询微信支付订单状态', { orderNo, result });

      if (result.return_code !== 'SUCCESS') {
        throw new Error(`微信支付查询失败: ${result.return_msg}`);
      }

      return {
        success: result.result_code === 'SUCCESS',
        status: this.mapTradeState(result.trade_state),
        orderNo,
        transactionId: result.transaction_id,
        amount: this.parseAmount(parseInt(result.total_fee || 0)),
        payTime: result.time_end,
        rawData: result
      };

    } catch (error) {
      this.log('error', '查询微信支付订单失败', { orderNo, error: error.message });
      throw error;
    }
  }

  /**
   * 处理支付回调
   * @param {string} xmlData 回调XML数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleCallback(xmlData) {
    try {
      this.log('info', '处理微信支付回调', { xmlData });

      const callbackData = await this.xmlToObject(xmlData);

      // 验证签名
      if (!this.verifySignature(callbackData, callbackData.sign)) {
        throw new Error('签名验证失败');
      }

      const result = {
        success: callbackData.result_code === 'SUCCESS',
        orderNo: callbackData.out_trade_no,
        transactionId: callbackData.transaction_id,
        amount: this.parseAmount(parseInt(callbackData.total_fee)),
        payTime: callbackData.time_end,
        status: this.mapTradeState(callbackData.result_code === 'SUCCESS' ? 'SUCCESS' : 'FAIL'),
        rawData: callbackData
      };

      this.log('info', '微信支付回调处理完成', result);

      return result;

    } catch (error) {
      this.log('error', '处理微信支付回调失败', { 
        xmlData, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 申请退款
   * @param {Object} refundData 退款数据
   * @returns {Promise<Object>} 退款结果
   */
  async refund(refundData) {
    try {
      this.validateRequiredFields(refundData, [
        'orderNo', 'refundAmount', 'totalAmount'
      ]);

      const refundParams = {
        appid: this.appId,
        mch_id: this.mchId,
        nonce_str: this.generateNonce(),
        out_trade_no: refundData.orderNo,
        out_refund_no: `${refundData.orderNo}_refund_${Date.now()}`,
        total_fee: this.formatAmount(refundData.totalAmount),
        refund_fee: this.formatAmount(refundData.refundAmount),
        refund_desc: refundData.reason || '用户申请退款'
      };

      refundParams.sign = this.generateSignature(refundParams);

      const xmlData = this.objectToXml(refundParams);

      // 微信退款需要证书，这里简化处理
      const response = await this.httpRequest(`${this.apiUrl}/secapi/pay/refund`, {
        method: 'POST',
        data: xmlData,
        headers: {
          'Content-Type': 'application/xml'
        }
      });

      const result = await this.xmlToObject(response);

      this.log('info', '微信支付退款申请', { 
        orderNo: refundData.orderNo, 
        result 
      });

      return {
        success: result.result_code === 'SUCCESS',
        refundId: result.refund_id,
        message: result.err_code_des || result.return_msg,
        rawData: result
      };

    } catch (error) {
      this.log('error', '微信支付退款失败', { 
        orderNo: refundData.orderNo, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 验证回调签名
   * @param {Object} data 回调数据
   * @param {string} signature 签名
   * @returns {boolean} 验证结果
   */
  verifySignature(data, signature) {
    const signData = { ...data };
    delete signData.sign;

    const expectedSign = this.generateSignature(signData);
    return expectedSign === signature;
  }

  /**
   * 生成签名
   * @param {Object} data 数据
   * @returns {string} 签名
   */
  generateSignature(data) {
    const signString = this.objectToUrlParams(data) + `&key=${this.apiKey}`;
    return this.md5(signString).toUpperCase();
  }

  /**
   * 对象转XML
   * @param {Object} obj 对象
   * @returns {string} XML字符串
   */
  objectToXml(obj) {
    let xml = '<xml>';
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        xml += `<${key}><![CDATA[${obj[key]}]]></${key}>`;
      }
    }
    xml += '</xml>';
    return xml;
  }

  /**
   * XML转对象
   * @param {string} xml XML字符串
   * @returns {Promise<Object>} 对象
   */
  async xmlToObject(xml) {
    const parser = new xml2js.Parser({ explicitArray: false });
    const result = await parser.parseStringPromise(xml);
    return result.xml;
  }

  /**
   * 映射交易状态
   * @param {string} tradeState 微信交易状态
   * @returns {string} 标准状态
   */
  mapTradeState(tradeState) {
    const stateMap = {
      'SUCCESS': 'paid',
      'REFUND': 'refunded',
      'NOTPAY': 'pending',
      'CLOSED': 'failed',
      'REVOKED': 'cancelled',
      'USERPAYING': 'pending',
      'PAYERROR': 'failed'
    };
    return stateMap[tradeState] || 'pending';
  }

  /**
   * 检查配置是否有效
   * @returns {boolean} 配置是否有效
   */
  isConfigValid() {
    return !!(this.appId && this.mchId && this.apiKey);
  }
}

module.exports = WechatPayService;
