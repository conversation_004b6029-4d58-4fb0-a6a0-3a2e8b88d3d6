import { useState, useEffect } from 'react';
import { Play, Search, Heart, MessageCircle, User, Settings, Crown, LogIn } from 'lucide-react';
import VideoCard from '@/components/user/VideoCard';
import UserHeader from '@/components/user/UserHeader';
import VideoPlayer from '@/components/user/VideoPlayer';
import { getUserProfile, updateUserProfile } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { Link } from 'react-router-dom';
import { useVideos } from '@/hooks/queries/useVideos';
import { useCategories } from '@/hooks/queries/useCategories';
import { useUserFavorites, useBatchCheckInteractions } from '@/hooks/queries/useInteractions';
import { useTranslation } from 'react-i18next';

const UserApp = () => {
  const { isAuthenticated, user, isLoading: authLoading } = useAuth();
  const { t } = useTranslation();
  const [currentView, setCurrentView] = useState('home');
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [userProfile, setUserProfile] = useState(null);

  // 使用React Query获取数据
  const { data: videos = [], isLoading: videosLoading, error: videosError } = useVideos({});
  const { data: categories = [], isLoading: categoriesLoading } = useCategories();

  // 获取视频交互状态
  const videoIds = videos.map(video => video.id);
  const { data: interactionStates = {}, isLoading: interactionsLoading } = useBatchCheckInteractions(videoIds);

  // 获取用户收藏（仅在收藏页面且用户已登录时）
  const { data: favorites = [], isLoading: favoritesLoading, refetch: refetchFavorites } = useUserFavorites(
    userProfile?.id || 0
  );

  // 计算总体加载状态
  const loading = videosLoading || categoriesLoading || authLoading;
  const error = videosError ? '加载数据失败，请稍后再试。' : null;

  // 检查用户认证状态
  useEffect(() => {
    const token = localStorage.getItem('token');
    console.log('当前用户token:', token ? '存在' : '不存在');
    if (token) {
      console.log('Token内容:', token);
    }
  }, []);

  useEffect(() => {
    const fetchProfile = async () => {
      // 只在用户已登录且还没有用户资料时才获取
      if (isAuthenticated && !userProfile && !authLoading) {
        try {
          console.log('用户已登录，开始获取用户资料...');
          const response = await getUserProfile();

          console.log('用户资料完整响应:', response);

          // 安全地访问用户数据
          const userData = response?.data?.data?.user ||
                          response?.data?.user ||
                          response?.data ||
                          null;

          console.log('解析后的用户资料数据:', userData);
          setUserProfile(userData);

          if (userData) {
            console.log('用户资料获取成功，用户ID:', userData.id);
          } else {
            console.warn('用户资料为空，可能数据格式错误');
          }
        } catch (err: any) {
          console.error('获取用户资料失败:', err);
          console.error('错误详情:', err?.response?.data);
          // 如果是认证错误，不设置错误状态，让用户可以继续浏览
        }
      }
    };

    fetchProfile();
  }, [isAuthenticated, authLoading, userProfile]);

  // 处理收藏状态变化
  const handleFavoriteChange = () => {
    // 触发收藏列表刷新
    refetchFavorites();
  };

  const handleProfileUpdate = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.target as HTMLFormElement);
    const data = Object.fromEntries(formData.entries());

    try {
      const response = await updateUserProfile(data);

      // 安全地访问更新后的用户数据
      const updatedUserData = response?.data?.data?.user ||
                             response?.data?.user ||
                             response?.data ||
                             null;

      if (updatedUserData) {
        setUserProfile(updatedUserData);
        alert('资料更新成功！');
      } else {
        throw new Error('更新响应数据格式错误');
      }
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || '更新资料失败';
      alert(errorMessage);
      console.error('更新资料失败:', err);
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>正在加载数据...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <div className="text-red-600 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-red-800 mb-2">加载失败</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              重新加载
            </button>
          </div>
        </div>
      );
    }

    if (selectedVideo) {
      const relatedVideos = videos.filter(v => v.id !== selectedVideo.id).slice(0, 10);
      return (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <VideoPlayer
              video={selectedVideo}
              onBack={() => setSelectedVideo(null)}
              onFavoriteChange={handleFavoriteChange}
            />
          </div>
          <div className="space-y-4">
            <h3 className="text-xl font-bold">{t('page.home.upNext')}</h3>
            {relatedVideos.map((video) => (
              <VideoCard 
                key={video.id} 
                video={video} 
                onPlay={() => setSelectedVideo(video)}
                variant="compact"
              />
            ))}
          </div>
        </div>
      );
    }

    switch (currentView) {
      case 'home':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">{t('page.home.title')}</h2>
              <div className="flex space-x-2">
                <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
                  {t('page.home.sort.latest')}
                </button>
                <button className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90">
                  {t('page.home.sort.popular')}
                </button>
                <button className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90">
                  {t('page.home.sort.recommended')}
                </button>
              </div>
            </div>

            {/* 分类筛选 */}
            <div className="flex space-x-4 pb-4 border-b">
              {categories.map((category) => (
                <button
                  key={category.id}
                  className="px-4 py-2 rounded-full border hover:bg-accent transition-colors"
                >
                  {category.name}
                </button>
              ))}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {videos.map((video) => {
                const videoId = String(video.id);
                const interactionState = interactionStates[videoId];

                // 为视频添加交互状态
                const enhancedVideo = {
                  ...video,
                  is_liked: interactionState?.isLiked || false,
                  is_favorited: interactionState?.isFavorited || false,
                };

                return (
                  <VideoCard
                    key={video.id}
                    video={enhancedVideo}
                    onPlay={() => setSelectedVideo(video)}
                    onFavoriteChange={(type, newState) => {
                      if (type === 'favorite') {
                        handleFavoriteChange();
                      }
                    }}
                  />
                );
              })}
            </div>
          </div>
        );

      case 'favorites':
        // 检查用户认证状态
        if (authLoading) {
          return <div className="text-center py-12">正在检查登录状态...</div>;
        }

        if (!isAuthenticated) {
          return (
            <div className="text-center py-12">
              <div className="bg-card p-8 rounded-lg border max-w-md mx-auto">
                <LogIn className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">需要登录</h3>
                <p className="text-muted-foreground mb-6">请先登录以查看您的收藏视频</p>
                <Link
                  to="/login"
                  className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  立即登录
                </Link>
              </div>
            </div>
          );
        }

        if (favoritesLoading) return <div className="text-center py-12">正在加载收藏...</div>;

        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">我的收藏</h2>
              <div className="text-sm text-muted-foreground">
                共 {favorites.length} 个视频
              </div>
            </div>

            {favorites.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {favorites.map((video) => {
                  const videoId = String(video.id);
                  const interactionState = interactionStates[videoId];

                  // 为收藏视频添加交互状态
                  const enhancedVideo = {
                    ...video,
                    is_liked: interactionState?.isLiked || false,
                    is_favorited: true, // 收藏页面的视频默认已收藏
                  };

                  return (
                    <VideoCard
                      key={video.id}
                      video={enhancedVideo}
                      onPlay={() => setSelectedVideo(video)}
                      onFavoriteChange={(type, newState) => {
                        if (type === 'favorite') {
                          handleFavoriteChange();
                        }
                      }}
                    />
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <Heart className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">暂无收藏</h3>
                <p className="text-muted-foreground">开始收藏你喜欢的视频吧</p>
              </div>
            )}
          </div>
        );

      case 'profile':
        if (loading) return <div className="text-center py-12">Loading Profile...</div>;
        if (!userProfile) return <div className="text-center py-12">Could not load profile.</div>;
        
        return (
          <form onSubmit={handleProfileUpdate} className="max-w-2xl mx-auto space-y-6">
            <h2 className="text-2xl font-bold">个人资料</h2>
            
            <div className="bg-card p-6 rounded-lg border space-y-6">
              <div className="flex items-center space-x-4">
                <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center">
                  <User className="h-10 w-10 text-primary-foreground" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold">{userProfile.username}</h3>
                  <p className="text-muted-foreground">{userProfile.email}</p>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                    <span>{t('stats.watchTime')}: {userProfile.watch_time || 0}</span>
                    <span>{t('stats.favoriteCount')}: {userProfile.favorites_count || 0}</span>
                    <span>{t('stats.likeCount')}: {userProfile.likes_count || 0}</span>
                  </div>
                </div>
                <button type="button" className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90">
                  编辑资料
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="username" className="block text-sm font-medium mb-2">昵称</label>
                  <input 
                    id="username"
                    name="username"
                    type="text" 
                    className="w-full px-3 py-2 border border-input rounded-md bg-background"
                    defaultValue={userProfile.username}
                  />
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium mb-2">手机号</label>
                  <input
                    id="phone"
                    name="phone"
                    type="tel" 
                    className="w-full px-3 py-2 border border-input rounded-md bg-background"
                    placeholder="输入手机号"
                    defaultValue={userProfile.phone}
                  />
                </div>
                <div>
                  <label htmlFor="birth_date" className="block text-sm font-medium mb-2">生日</label>
                  <input
                    id="birth_date"
                    name="birth_date"
                    type="date"
                    className="w-full px-3 py-2 border border-input rounded-md bg-background"
                    defaultValue={userProfile.birth_date ? new Date(userProfile.birth_date).toISOString().split('T')[0] : ''}
                  />
                </div>
                <div>
                  <label htmlFor="gender" className="block text-sm font-medium mb-2">性别</label>
                  <select id="gender" name="gender" defaultValue={userProfile.gender} className="w-full px-3 py-2 border border-input rounded-md bg-background">
                    <option value="">请选择</option>
                    <option value="male">男</option>
                    <option value="female">女</option>
                    <option value="other">其他</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label htmlFor="bio" className="block text-sm font-medium mb-2">个人简介</label>
                <textarea 
                  id="bio"
                  name="bio"
                  className="w-full px-3 py-2 border border-input rounded-md bg-background h-24"
                  placeholder="介绍一下自己..."
                  defaultValue={userProfile.bio}
                />
              </div>
              
              <div className="flex space-x-3">
                <button type="submit" className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
                  保存修改
                </button>
                <button type="button" className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90">
                  取消
                </button>
              </div>
            </div>

            {/* 会员信息 */}
            <div className="bg-card p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-4">会员信息</h3>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">
                    当前状态: {user?.role === 'vip' ? 'VIP会员' :
                              user?.role === 'member' ? '会员' : '普通用户'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {user?.role === 'member' || user?.role === 'vip' ?
                      '感谢您的支持，享受所有会员特权' :
                      '升级会员享受更多特权'}
                  </p>
                </div>
                {user?.role !== 'member' && user?.role !== 'vip' && user?.role !== 'admin' && (
                  <button className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-md hover:from-yellow-500 hover:to-orange-600">
                    <Crown size={18} />
                    <span>立即升级</span>
                  </button>
                )}
              </div>
            </div>

            {/* 观看历史 */}
            <div className="bg-card p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-4">最近观看</h3>
              <div className="space-y-3">
                {videos.slice(0, 3).map((video) => {
                  const isSystemAdmin = video.username === t('videoInfo.systemAdmin');
                  const displayUsername = video.username || t('videoInfo.anonymousAuthor');

                  return (
                    <div key={video.id} className="flex items-center space-x-3 p-2 hover:bg-accent rounded-md cursor-pointer">
                      <img src={video.thumbnail} alt={video.title} className="w-16 h-12 object-cover rounded" />
                      <div className="flex-1">
                        <p className="font-medium text-sm">{video.title}</p>
                        {!isSystemAdmin && (
                          <p className="text-xs text-muted-foreground">{displayUsername}</p>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground">2天前</span>
                    </div>
                  );
                })}
              </div>
            </div>
          </form>
        );

      default:
        return <div>页面不存在</div>;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <UserHeader currentView={currentView} setCurrentView={setCurrentView} />
      
      <main className="container mx-auto px-4 py-6">
        {renderContent()}
      </main>
    </div>
  );
};

export default UserApp;
