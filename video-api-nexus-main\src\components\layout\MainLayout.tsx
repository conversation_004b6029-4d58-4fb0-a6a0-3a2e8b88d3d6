import React, { useState } from 'react';
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Heart, Home, Search, User, Crown, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import ErrorBoundary from '@/components/ErrorBoundary';
import { cn } from "@/lib/utils";
import { UserNav } from './UserNav';
import { UpgradeDialog } from '../ui/UpgradeDialog';
import MembershipDialog from '../dialogs/MembershipDialog';
import { ThemeSwitcher } from '../theme-switcher';
import { LanguageSwitcher } from '../LanguageSwitcher';
import { useAuth } from '@/hooks/useAuth';
import { useMembership } from '@/hooks/useMembership';
import { usePlaylistSync } from '@/hooks/usePlaylistSync';
import PlaylistSyncIndicator from '@/components/PlaylistSyncIndicator';
import NotificationPanel from '../NotificationPanel';
import { useQuery } from '@tanstack/react-query';
import { getNotificationStatus } from '@/lib/api';
import { useTranslation } from 'react-i18next';

const MainLayout = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { pathname } = location;
  const { isAuthenticated, user } = useAuth();
  const { isMember, isVip, membershipLevel } = useMembership();
  const [isUpgradeDialogOpen, setIsUpgradeDialogOpen] = useState(false);
  const [isMembershipDialogOpen, setIsMembershipDialogOpen] = useState(false);
  const [isNotificationPanelOpen, setIsNotificationPanelOpen] = useState(false);

  // 获取通知状态
  const { data: notificationStatus } = useQuery({
    queryKey: ['notificationStatus'],
    queryFn: getNotificationStatus,
    enabled: isAuthenticated,
    refetchInterval: 15000, // 每15秒轮询一次
  });
  // 现在 getNotificationStatus 直接返回 { success: true, data: { unreadCount: ... } }
  const unreadCount = notificationStatus?.data?.unreadCount || 0;

  // 播放列表同步功能
  const { syncStatus, manualSync } = usePlaylistSync({
    autoSync: true,
  });

  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);

  // 判断是否显示升级会员按钮（已废弃，现在使用更精确的条件渲染）
  const shouldShowUpgradeButton = () => {
    if (!isAuthenticated) return false; // 未登录不显示
    if (user?.role === 'admin') return false; // 管理员不显示
    if (isVip) return false; // VIP用户不显示
    return true; // 普通用户和普通会员显示
  };

  return (
    <>
    <div className="min-h-screen bg-background">
      <header className="bg-card shadow-sm sticky top-0 z-50 border-b">
        <div className="container mx-auto px-3 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mobile-header-height">
            {/* 左侧：Logo */}
            <div className="flex items-center space-x-2">
              <Link
                to="/"
                className={cn(
                  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
                  "hover:bg-accent hover:text-accent-foreground",
                  "w-8 h-8 sm:w-auto sm:h-auto p-0 sm:px-3 sm:py-1",
                  {
                    "text-primary": pathname === '/',
                    "text-muted-foreground": pathname !== '/',
                  }
                )}
              >
                <Home size={18} className="sm:mr-1" />
                <span className="hidden sm:inline">{t('nav.home')}</span>
              </Link>
              <span className="text-lg sm:text-xl font-bold text-foreground hidden xs:block">{t('site.title')}</span>
            </div>

            {/* 右侧：导航按钮 */}
            <div className="flex items-center space-x-1 sm:space-x-2">
              {/* 会员标识 - 移动端优化 */}
              {isMember && (
                <button
                  className="flex items-center justify-center w-7 h-7 sm:w-auto sm:h-auto sm:px-3 sm:py-1 bg-black/5 dark:bg-white/10 border border-black/10 dark:border-white/20 rounded-full text-xs sm:text-sm font-medium transition-all duration-200 cursor-pointer sm:space-x-1"
                  onClick={() => setIsMembershipDialogOpen(true)}
                  title={t('membership.member')}
                >
                  <Crown size={14} className="text-yellow-500" />
                  <span className="hidden sm:inline">{t('membership.member')}</span>
                </button>
              )}

              {/* 升级会员按钮 - 移动端优化 */}
              {!isMember && isAuthenticated && user?.role !== 'admin' && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-xs sm:text-sm w-8 h-8 sm:w-auto sm:h-auto p-0 sm:px-3 sm:py-1 rounded-full sm:rounded-md"
                  onClick={() => setIsUpgradeDialogOpen(true)}
                  title={t('membership.upgrade')}
                >
                  <Crown size={16} className="text-foreground sm:mr-1" />
                  <span className="hidden sm:inline">{t('membership.upgrade')}</span>
                </Button>
              )}

              {/* 上传按钮 */}
              {isAuthenticated && (
                <Button asChild variant="outline" size="sm" className="w-8 h-8 sm:w-auto sm:h-auto p-0 sm:px-3 sm:py-1">
                  <Link to="/upload">
                    <Upload size={16} className="sm:mr-1" />
                    <span className="hidden sm:inline">{t('nav.upload')}</span>
                  </Link>
                </Button>
              )}

              {/* 通知中心 */}
              {isAuthenticated && (
                <NotificationPanel 
                  unreadCount={unreadCount}
                  isOpen={isNotificationPanelOpen}
                  onOpenChange={setIsNotificationPanelOpen}
                />
              )}

              {/* 语言切换器 */}
              <LanguageSwitcher />

              {/* 主题切换器 */}
              <ThemeSwitcher />

              {/* 播放列表同步指示器 */}
              <PlaylistSyncIndicator
                syncStatus={syncStatus}
                isAuthenticated={isAuthenticated}
                onManualSync={manualSync}
                className="hidden sm:flex"
              />

              {/* 用户导航 */}
              <UserNav />
            </div>
          </div>
        </div>
      </header>
      <main>
        <ErrorBoundary>
          <Outlet />
        </ErrorBoundary>
      </main>
    </div>
      <UpgradeDialog open={isUpgradeDialogOpen} onOpenChange={setIsUpgradeDialogOpen} />
      <MembershipDialog open={isMembershipDialogOpen} onOpenChange={setIsMembershipDialogOpen} />
    </>
  );
};

export default MainLayout; 