import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Title,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { Skeleton } from "./skeleton";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { memberApi } from "@/services/memberApi";
import { creemApi } from "@/services/creemApi";
import { paymentApi } from "@/services/paymentApi";
import { useMembership } from "@/hooks/useMembership";
import { useAuth } from "@/hooks/useAuth";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import CountdownTimer from "./CountdownTimer";
import { Badge } from "./badge";
import { Card, CardContent, CardHeader, CardTitle } from "./card";
import { Check, Crown, Qr<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { useTranslation } from 'react-i18next';

// 定义本地会员计划接口
interface Plan {
  id: number;
  name: string;
  price: string;
  original_price?: string;
  discount_until?: string;
  duration_days: number;
  features: string[];
}

// 定义Creem会员计划接口
interface CreemPlan {
  id: number;
  name: string;
  description: string;
  price: number;
  currency: string;
  creem_product_id: string;
  is_active: boolean;
  duration_days: number;
  tax_category: string;
  created_at: string;
  updated_at: string;
}

interface PaymentMethod {
  key: string;
  name: string;
}

interface UpgradeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isMember?: boolean;
}

export function UpgradeDialog({ open, onOpenChange, isMember = false }: UpgradeDialogProps) {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [creemPlans, setCreemPlans] = useState<CreemPlan[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlanId, setSelectedPlanId] = useState<number | null>(null);
  const [selectedCreemPlanId, setSelectedCreemPlanId] = useState<number | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('balance');
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [isQrCodeDialogOpen, setIsQrCodeDialogOpen] = useState(false);
  
  // 更新状态类型以反映新的API响应
  const [tronPaymentInfo, setTronPaymentInfo] = useState<{ 
    walletAddress: string; 
    amount: number; // 这是唯一的TRX金额
    finalAmountUsdt: number; // 新增：唯一的USDT金额
    orderNo: string; 
    qrCode: string; 
    baseAmount: number; // 原始USDT金额
  } | null>(null);

  const { toast } = useToast();
  const { updateMembershipAfterPayment } = useMembership();
  const { user } = useAuth();
  const { t } = useTranslation();

  // 判断当前显示的是Creem计划还是本地计划
  const isCreemPayment = selectedPaymentMethod === 'credit_card';
  
  // 根据支付方式获取当前选中的计划
  const selectedPlan = isCreemPayment 
    ? (Array.isArray(creemPlans) ? creemPlans.find(p => p.id === selectedCreemPlanId) : null)
    : (Array.isArray(plans) ? plans.find(p => p.id === selectedPlanId) : null);
    
  // 计算余额是否足够
  const isBalanceSufficient = !isCreemPayment && selectedPlan && user?.balance !== undefined && user.balance >= parseFloat(String(selectedPlan.price));

  useEffect(() => {
    if (open) {
      // 重置状态
      setTronPaymentInfo(null);
      setQrCode(null);

      const fetchData = async () => {
        try {
          setLoading(true);
          setError(null);
          
          // 并行请求本地会员计划、Creem会员计划和支付方式
          const [plansResponse, creemPlansResponse, methodsResponse] = await Promise.all([
            memberApi.getPlans(),
            creemApi.getCreemPlans(),
            memberApi.getPaymentMethods()
          ]);

          // 处理本地会员计划
          if (plansResponse.data.success) {
            const fetchedPlans = plansResponse.data.data.plans;
            setPlans(fetchedPlans);
            if (fetchedPlans.length > 0) {
              const defaultPlan = fetchedPlans.reduce((max: Plan, p: Plan) => parseFloat(p.price) > parseFloat(max.price) ? p : max, fetchedPlans[0]);
              setSelectedPlanId(defaultPlan.id);
            }
          } else {
            throw new Error(plansResponse.data.message || t('upgrade.fetchPlansError'));
          }

          // 处理Creem会员计划
          if (creemPlansResponse.data.success && Array.isArray(creemPlansResponse.data.data)) {
            const fetchedCreemPlans = creemPlansResponse.data.data;
            setCreemPlans(fetchedCreemPlans);
            if (fetchedCreemPlans.length > 0) {
              const defaultCreemPlan = fetchedCreemPlans.reduce((max: CreemPlan, p: CreemPlan) => p.price > max.price ? p : max, fetchedCreemPlans[0]);
              setSelectedCreemPlanId(defaultCreemPlan.id);
            }
          } else {
            console.error(t('upgrade.fetchCreemPlansError'));
            setCreemPlans([]); // 设置为空数组
          }

          // 处理支付方式
          if (methodsResponse.data.success) {
            // 添加信用卡支付方式
            const methods = methodsResponse.data.data;
            methods.push({
              key: 'credit_card',
              name: t('upgrade.creditCard')
            });
            setPaymentMethods(methods);
          } else {
            toast({ title: t('common.warning'), description: t('upgrade.loadPaymentMethodsError'), variant: "default" });
          }
        } catch (err: any) {
          setError(err.message || t('upgrade.networkError'));
        } finally {
          setLoading(false);
        }
      };
      fetchData();
    }
  }, [open]);

  useEffect(() => {
    // 当余额不足以支付当前选中套餐时，如果支付方式是余额支付，则自动切换到第一个可用的在线支付方式
    if (!isCreemPayment && selectedPlan && !isBalanceSufficient && selectedPaymentMethod === 'balance') {
      if (paymentMethods.length > 0) {
        const nonCreditCardMethods = paymentMethods.filter(m => m.key !== 'credit_card');
        if (nonCreditCardMethods.length > 0) {
          setSelectedPaymentMethod(nonCreditCardMethods[0].key);
        }
      }
    }
  }, [selectedPlan, isBalanceSufficient, paymentMethods, selectedPaymentMethod]);


  const handleUpgradeClick = async () => {
    if (isCreemPayment) {
      // 处理Creem支付
      if (!selectedCreemPlanId) {
        toast({ title: t('upgrade.selectPlanFirst'), variant: "destructive" });
        return;
      }
      
      setSubmitting(true);
      try {
        const response = await creemApi.createCreemOrder(Array.isArray(creemPlans) && creemPlans.find(p => p.id === selectedCreemPlanId)?.creem_product_id || '');
        
        if (response.data.success) {
          // 跳转到Creem支付页面
          window.location.href = response.data.data.paymentUrl;
        } else {
          throw new Error(response.data.message || t('upgrade.createOrderError'));
        }
      } catch (err: any) {
        toast({
          title: t('upgrade.operationFailed'),
          description: err.response?.data?.message || err.message || t('upgrade.unknownError'),
          variant: "destructive",
        });
      } finally {
        setSubmitting(false);
      }
    } else {
      // 处理本地支付
      if (!selectedPlanId) {
        toast({ title: t('upgrade.selectPlanFirst'), variant: "destructive" });
        return;
      }
      if (!selectedPaymentMethod) {
        toast({ title: t('upgrade.selectPaymentMethodFirst'), variant: "destructive" });
        return;
      }

      setSubmitting(true);
      try {
        const response = await paymentApi.createOrder({
          type: 'membership',
          targetId: selectedPlanId,
          paymentMethod: selectedPaymentMethod,
          description: `${t('upgrade.subscribePlan')}: ${selectedPlan?.name || ''}`
        });
        
        if (response.data.success) {
          const paymentInfo = response.data.data.order;
          if (paymentInfo.paymentUrl) {
            window.location.href = paymentInfo.paymentUrl;
          } else if (paymentInfo.paymentMethod === 'tron_usdt' && paymentInfo.walletAddress) {
            // 更新以匹配新的API响应结构
            setTronPaymentInfo({
              walletAddress: paymentInfo.walletAddress,
              amount: paymentInfo.finalAmount, // 这是唯一的TRX金额
              finalAmountUsdt: paymentInfo.finalAmountUsdt, // 新增：唯一的USDT金额
              orderNo: paymentInfo.orderNo,
              qrCode: paymentInfo.qrCode,
              baseAmount: paymentInfo.amount, // 这是原始的USDT金额
            });
          } else if (paymentInfo.qrCode) { // 将这个判断移到tron之后
            setQrCode(paymentInfo.qrCode);
            setIsQrCodeDialogOpen(true);
          } else {
            toast({ title: t('upgrade.paymentSuccess'), description: t('upgrade.subscriptionSuccess'), className: "bg-green-500 text-white" });
            await updateMembershipAfterPayment();
            onOpenChange(false);
          }
        } else {
          throw new Error(response.data.message || t('upgrade.createOrderError'));
        }
      } catch (err: any) {
        toast({
          title: t('upgrade.operationFailed'),
          description: err.response?.data?.message || err.message || t('upgrade.unknownError'),
          variant: "destructive",
        });
      } finally {
        setSubmitting(false);
      }
    }
  };

  const handleCopyAddress = (address: string) => {
    navigator.clipboard.writeText(address);
    toast({ title: t('upgrade.copied'), description: t('upgrade.addressCopied') });
  };

  const TronPaymentScreen = () => (
    <div className="py-4 text-center">
      <DialogHeader>
        <DialogTitle>TRON (TRX) 支付</DialogTitle>
        <DialogDescription>
          为确保订单被正确识别，请精确转账下方所示的TRX金额。
        </DialogDescription>
      </DialogHeader>
      <div className="my-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
        <div className="flex flex-col items-center">
          <img src={tronPaymentInfo?.qrCode} alt="TRON Wallet Address QR Code" className="w-40 h-40 mb-4 border rounded-md" />
          
          <Label htmlFor="walletAddress" className="text-base font-semibold">收款地址 (TRC20)</Label>
          <div className="mt-2 flex items-center justify-center p-3 bg-gray-100 dark:bg-gray-700 rounded-md w-full">
            <p id="walletAddress" className="text-sm md:text-base break-all font-mono mr-4 text-gray-700 dark:text-gray-300">{tronPaymentInfo?.walletAddress}</p>
            <Button variant="ghost" size="icon" onClick={() => handleCopyAddress(tronPaymentInfo!.walletAddress)}>
              <Copy className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
      <div className="my-6 space-y-4">
        <div>
          <Label className="text-lg font-semibold text-purple-600">支付金额 (TRX)</Label>
          <p className="text-2xl font-bold text-purple-600 mt-2 tracking-wider">{tronPaymentInfo?.amount} <span className="text-xl">TRX</span></p>
          <p className="text-xs text-muted-foreground mt-1">请确保转账金额完全一致，包括所有小数位。</p>
        </div>
        
        <div className="border-t pt-4">
          <Label className="text-base font-semibold">支付金额 (USDT)</Label>
          <p className="text-xl font-bold text-gray-700 dark:text-gray-300 mt-1">{tronPaymentInfo?.finalAmountUsdt} <span className="text-lg">USDT</span></p>
          <p className="text-xs text-muted-foreground mt-1">此笔交易等值于所选会员计划的价格。</p>
        </div>
      </div>
      <DialogFooter className="mt-8">
        <Button onClick={() => onOpenChange(false)} className="w-full" variant="outline">我已完成支付</Button>
      </DialogFooter>
    </div>
  );

  // 渲染会员计划卡片
  const renderPlans = () => {
    if (isCreemPayment) {
      // 渲染Creem会员计划
      return (
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {Array.isArray(creemPlans) && creemPlans.map((plan, index) => {
            const isRecommended = index === 1;
            const Icon = Crown;

            return (
              <Card
                key={plan.id}
                className={cn(
                  "cursor-pointer transition-all flex flex-col",
                  selectedCreemPlanId === plan.id ? "border-purple-500 ring-2 ring-purple-500" : "border-gray-200 hover:shadow-md",
                  isRecommended ? "bg-purple-50" : "bg-white"
                )}
                onClick={() => setSelectedCreemPlanId(plan.id)}
              >
                <CardHeader className="p-3 text-center">
                  <div className="flex justify-center items-center mb-1">
                    <Icon className="w-6 h-6 text-yellow-500" />
                  </div>
                  <CardTitle className="text-base">{plan.name}</CardTitle>
                </CardHeader>
                <CardContent className="flex-grow flex flex-col p-3 pt-0">
                  <div className="text-center">
                    <p className="font-bold text-2xl text-purple-600">{parseFloat(plan.price).toFixed(2)} {plan.currency}</p>
                    <p className="text-xs text-gray-500">{plan.duration_days}天</p>
                  </div>
                  <div className="flex-grow space-y-1 mt-2 text-xs">
                    {plan.description && (
                      <div className="flex items-center gap-1.5">
                        <Check className="h-3 w-3 text-green-500 flex-shrink-0" />
                        <span>{plan.description}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      );
    } else {
      // 渲染本地会员计划
      return (
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {Array.isArray(plans) && plans.map((plan, index) => {
            const isRecommended = index === 1;
            const hasDiscount = plan.original_price && parseFloat(plan.original_price) > parseFloat(plan.price);
            const Icon = Crown;

            return (
              <Card
                key={plan.id}
                className={cn(
                  "cursor-pointer transition-all flex flex-col",
                  selectedPlanId === plan.id ? "border-purple-500 ring-2 ring-purple-500" : "border-gray-200 hover:shadow-md",
                  isRecommended ? "bg-purple-50" : "bg-white"
                )}
                onClick={() => setSelectedPlanId(plan.id)}
              >
                <CardHeader className="p-3 text-center">
                  <div className="flex justify-center items-center mb-1">
                    <Icon className="w-6 h-6 text-yellow-500" />
                  </div>
                  <CardTitle className="text-base">{plan.name}</CardTitle>
                </CardHeader>
                <CardContent className="flex-grow flex flex-col p-3 pt-0">
                  <div className="text-center">
                    {hasDiscount && (<span className="text-xs text-gray-500 line-through">${parseFloat(plan.original_price!).toFixed(2)}</span>)}
                    <p className="font-bold text-2xl text-purple-600">${parseFloat(plan.price).toFixed(2)}</p>
                    <p className="text-xs text-gray-500">{plan.duration_days}天</p>
                  </div>
                  <div className="flex-grow space-y-1 mt-2 text-xs">
                    {Array.isArray(plan.features) && plan.features.map(feature => (
                      <div key={feature} className="flex items-center gap-1.5">
                        <Check className="h-3 w-3 text-green-500 flex-shrink-0" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      );
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={(isOpen) => { onOpenChange(isOpen); if (!isOpen) setTronPaymentInfo(null); }}>
        <DialogContent className="w-[95%] sm:max-w-md max-h-[90vh] flex flex-col p-0">
          {tronPaymentInfo ? (
            <div className="p-6"><TronPaymentScreen /></div>
          ) : (
            <>
              <DialogHeader className="p-6 pb-2 flex-shrink-0">
                <DialogTitle>{isMember ? t('upgrade.renewMembership') : t('upgrade.upgradeToVip')}</DialogTitle>
                <DialogDescription>
                  {isMember ? t('upgrade.renewDescription') : t('upgrade.upgradeDescription')}
                </DialogDescription>
              </DialogHeader>

              <div className="flex-grow overflow-y-auto px-6 py-2">
                {loading && (
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                    {[...Array(3)].map((_, i) => <Skeleton key={i} className="h-56 w-full" />)}
                  </div>
                )}
                {error && <p className="text-red-500 text-center py-10">{error}</p>}
                {!loading && !error && (
                  <>
                    {/* 先选择支付方式 */}
                    <div className="mb-4">
                      <h4 className="mb-2 text-sm font-medium">{t('upgrade.selectPaymentMethod')}</h4>
                      <RadioGroup value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod} className="grid grid-cols-2 gap-2">
                        <div>
                          <RadioGroupItem value="credit_card" id="credit-card" className="peer sr-only" />
                          <Label htmlFor="credit-card" className={cn("flex items-center justify-center rounded-md border-2 border-muted bg-popover p-2 text-xs hover:bg-accent hover:text-accent-foreground cursor-pointer", selectedPaymentMethod === 'credit_card' && "border-primary")}>
                            <CreditCard className="h-4 w-4 mr-2" />
                            {t('upgrade.creditCard')}
                          </Label>
                        </div>
                        <div>
                          <RadioGroupItem value="tron_usdt" id="tron-usdt" className="peer sr-only" />
                          <Label htmlFor="tron-usdt" className={cn("flex items-center justify-center rounded-md border-2 border-muted bg-popover p-2 text-xs hover:bg-accent hover:text-accent-foreground cursor-pointer", selectedPaymentMethod === 'tron_usdt' && "border-primary")}>
                            TRON (USDT-TRC20)
                          </Label>
                        </div>
                        {selectedPaymentMethod !== 'credit_card' && (
                          <div>
                            <RadioGroupItem value="balance" id="balance-final" className="peer sr-only" disabled={!isBalanceSufficient} />
                            <Label htmlFor="balance-final" className={cn("flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-2 text-xs hover:bg-accent hover:text-accent-foreground", !isBalanceSufficient ? "cursor-not-allowed opacity-50" : "cursor-pointer", selectedPaymentMethod === 'balance' && "border-primary")}>
                              <span className="font-semibold">{t('upgrade.balance')}</span>
                              <span className="text-xs text-muted-foreground">${user?.balance ? Number(user.balance).toFixed(2) : '0.00'}</span>
                            </Label>
                          </div>
                        )}
                        {selectedPaymentMethod !== 'credit_card' && paymentMethods
                          .filter(method => method.key !== 'credit_card' && method.key !== 'tron_usdt') // 过滤掉信用卡和已存在的TRON支付方式
                          .map(method => (
                            <div key={method.key}>
                              <RadioGroupItem value={method.key} id={`${method.key}-final`} className="peer sr-only" />
                              <Label htmlFor={`${method.key}-final`} className={cn("flex items-center justify-center rounded-md border-2 border-muted bg-popover p-2 text-xs hover:bg-accent hover:text-accent-foreground", selectedPaymentMethod === method.key && "border-primary")}>
                                {method.name}
                              </Label>
                            </div>
                          ))}
                      </RadioGroup>
                    </div>

                    {/* 根据支付方式显示不同的会员计划 */}
                    {renderPlans()}
                  </>
                )}
              </div>

              <DialogFooter className="p-6 pt-3 flex-shrink-0 border-t">
                <Button onClick={handleUpgradeClick} disabled={submitting || loading} className="w-full">
                  {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {t('upgrade.payNow')}
                  {isCreemPayment && selectedCreemPlanId && Array.isArray(creemPlans) && creemPlans.find(p => p.id === selectedCreemPlanId) ? 
                    ` (${parseFloat(creemPlans.find(p => p.id === selectedCreemPlanId)?.price || '0').toFixed(2)} ${creemPlans.find(p => p.id === selectedCreemPlanId)?.currency})` : 
                    selectedPlan ? ` ($${parseFloat(String(selectedPlan.price)).toFixed(2)})` : ''
                  }
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
      
      <Dialog open={isQrCodeDialogOpen} onOpenChange={setIsQrCodeDialogOpen}>
        <DialogContent className="sm:max-w-xs">
            <DialogHeader>
                <DialogTitle>扫码支付</DialogTitle>
                <DialogDescription>
                    请使用微信或支付宝扫描二维码完成支付。
                </DialogDescription>
            </DialogHeader>
            <div className="flex justify-center p-4">
                {qrCode && <img src={qrCode} alt="支付二维码" className="w-48 h-48" />}
            </div>
            <DialogFooter>
                <Button variant="outline" onClick={() => setIsQrCodeDialogOpen(false)}>关闭</Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
    </>
  );
} 