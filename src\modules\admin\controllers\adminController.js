const { AppError, asyncHandler } = require('../../../middleware/errorHandler');
const { operationLogger } = require('../../../middleware/requestLogger');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const User = require('../../../database/models/User');
const Order = require('../../../database/models/Order');
const MembershipPlan = require('../../../database/models/MembershipPlan');
const Video = require('../../../database/models/Video');
const Comment = require('../../../database/models/Comment');
const connectionManager = require('../../../database/ConnectionManager');
const statisticsService = require('../../../services/statisticsService');
const videoService = require('../../video/services/videoService');
const { toAbsolutePath } = require('../../../utils/pathResolver');
const fs = require('fs');
const path = require('path');
const adminService = require('../services/adminService');
const balanceService = require('../../balance/services/balanceService');
const settingService = require('../../../services/settingService');
const notificationService = require('../../../services/notificationService');
const videoModel = require('../../../database/models/Video');

class AdminController {
  // 获取系统概览统计
  getDashboardStats = asyncHandler(async (req, res) => {
    console.log('🎯 [Admin] 开始获取仪表板统计数据...');
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'dashboard_stats');
    let stats = await cache.get(cacheKey);

    if (!stats) {
      console.log('🔍 [Admin] 缓存中没有数据，开始查询数据库...');
      // 获取用户统计
      const userStatsResult = await User.query(`
        SELECT
          COUNT(*) as total_users,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
          COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_30d,
          COUNT(CASE WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_users_7d
        FROM users
      `);
      const userStats = userStatsResult[0];
      console.log('📊 [Admin] 用户统计:', userStats);

      // 获取视频统计
      const videoStatsResult = await Video.query(`
        SELECT
          COUNT(*) as total_videos,
          COUNT(CASE WHEN status = 'published' THEN 1 END) as published_videos,
          SUM(view_count) as total_views
        FROM videos
        WHERE status != 'deleted'
      `);
      const videoStats = videoStatsResult[0];

      // 获取评论统计
      const commentStatsResult = await Comment.query(`
        SELECT
          COUNT(*) as total_comments
        FROM comments
        WHERE status = 'active'
      `);
      const commentStats = commentStatsResult[0];

      // 获取收入统计
      const revenueStatsResult = await Order.query(`
        SELECT
          SUM(CASE WHEN payment_status = 'paid' THEN final_amount ELSE 0 END) as total_revenue,
          SUM(CASE WHEN payment_status = 'paid' AND created_at >= CURDATE() THEN final_amount ELSE 0 END) as today_revenue,
          SUM(CASE WHEN payment_status = 'paid' AND created_at >= DATE_FORMAT(NOW(), '%Y-%m-01') THEN final_amount ELSE 0 END) as monthly_revenue,
          SUM(CASE WHEN payment_status = 'paid' AND created_at >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m-01') AND created_at < DATE_FORMAT(NOW(), '%Y-%m-01') THEN final_amount ELSE 0 END) as last_month_revenue,
          COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as total_paid_orders
        FROM orders
      `);
      const revenueStats = revenueStatsResult[0];
      console.log('💰 [Admin] 收入统计:', revenueStats);

      // 计算收入变化百分比
      const currentMonthRevenue = parseFloat(revenueStats.monthly_revenue || 0);
      const lastMonthRevenue = parseFloat(revenueStats.last_month_revenue || 0);
      let revenueChangePercent = '+0%';

      if (lastMonthRevenue > 0) {
        const changePercent = ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100).toFixed(1);
        revenueChangePercent = changePercent >= 0 ? `+${changePercent}%` : `${changePercent}%`;
      } else if (currentMonthRevenue > 0) {
        revenueChangePercent = '+100%';
      }

      // 🔧 获取最近数据
      const recentVideos = await Video.query("SELECT id, title, status, 'system' as uploader_username, view_count as views FROM videos WHERE status != 'deleted' ORDER BY created_at DESC LIMIT 5");
      const recentUsers = await User.query("SELECT id, username, email, role, status FROM users ORDER BY created_at DESC LIMIT 5");

      // 🔧 重构响应结构以匹配前端期望
      stats = {
        totalUsers: userStats.total_users || 0,
        userChange: `+${userStats.new_users_30d || 0} in 30 days`,
        totalVideos: videoStats.total_videos || 0,
        videoChange: `+${videoStats.published_videos || 0} published`,
        totalComments: commentStats.total_comments || 0,
        commentChange: `+${commentStats.total_comments || 0} in total`,
        monthlyRevenue: currentMonthRevenue.toFixed(2),
        totalRevenue: parseFloat(revenueStats.total_revenue || 0),
        revenueChange: revenueChangePercent,
        todayRevenue: parseFloat(revenueStats.today_revenue || 0).toFixed(2),
        totalPaidOrders: revenueStats.total_paid_orders || 0,
        recentVideos: recentVideos.map(v => ({ ...v, uploader: { username: v.uploader_username }, status: v.status === 'published' ? '已发布' : '待处理' })),
        recentUsers: recentUsers.map(u => ({ ...u, status: u.status === 'active' ? '活跃' : '非活跃' })),
        lastUpdated: new Date()
      };

      await cache.set(cacheKey, stats, 300); // 5分钟缓存
      console.log('✅ [Admin] 统计数据已生成并缓存:', stats);
    } else {
      console.log('📋 [Admin] 使用缓存的统计数据:', stats);
    }

    console.log('🚀 [Admin] 返回统计数据给前端...');
    res.json({
      success: true,
      data: stats // 🔧 直接返回扁平化的stats对象
    });
  });

  // 获取用户管理列表
  getUserManagement = asyncHandler(async (req, res) => {
    const {
      page = 1,
      pageSize = 20,
      role,
      status,
      keyword,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    console.log('🔍 [Admin] getUserManagement 请求参数:', { page, pageSize, role, status, keyword, sortBy, sortOrder });

    const filters = {};
    if (role && role !== 'all') filters.role = role;
    if (status && status !== 'all') filters.status = status;

    let result;
    const searchTerm = req.query.search || keyword;

    if (searchTerm) {
      console.log('🔍 [Admin] 执行搜索用户:', searchTerm);
      result = await User.searchUsers(searchTerm, parseInt(page), parseInt(pageSize), filters);
    } else {
      console.log('🔍 [Admin] 执行获取用户列表');
      result = await User.getUserList(parseInt(page), parseInt(pageSize), filters, { sortBy, sortOrder });
    }

    // 调试：检查用户GGG的数据
    const gggUser = result.data.find(user => user.username === 'GGG');
    if (gggUser) {
      console.log('🎯 [Admin] 找到用户GGG，角色数据:', {
        id: gggUser.id,
        username: gggUser.username,
        role: gggUser.role,
        email: gggUser.email
      });
    }

    const responseData = {
      users: result.data || [],
      total: result.pagination ? result.pagination.total : 0,
      page: result.pagination ? result.pagination.page : parseInt(page),
      pageSize: result.pagination ? result.pagination.pageSize : parseInt(pageSize),
      totalPages: result.pagination ? result.pagination.totalPages : 0
    };

    res.json({
      success: true,
      data: responseData
    });
  });

  // 获取视频管理列表
  getVideoManagement = asyncHandler(async (req, res) => {
    const {
      page = 1,
      pageSize = 10,
      status,
      categoryId,
      userId,
      keyword,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(pageSize),
      sortBy,
      order: sortOrder,
      filters: {}
    };

    if (status && status !== 'all') options.filters.status = status;
    if (categoryId) options.filters.categoryId = parseInt(categoryId);
    if (userId) options.filters.userId = parseInt(userId);
    if (keyword) options.filters.keyword = keyword;

    const result = await Video.getAllVideos(options);

    res.json({
      success: true,
      data: {
        videos: result.videos,
        pagination: result
      }
    });
  });

  // 获取评论管理列表
  getCommentManagement = asyncHandler(async (req, res) => {
    const {
      page = 1,
      pageSize = 20,
      status,
      videoId,
      userId,
      keyword
    } = req.query;

    const searchParams = {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      videoId: videoId ? parseInt(videoId) : null,
      userId: userId ? parseInt(userId) : null,
      status: status === 'all' ? null : status,
      keyword
    };

    const [listResult, statsResult] = await Promise.all([
        adminService.getCommentList(searchParams),
        adminService.getCommentStats()
    ]);
    
    res.json({
      success: true,
      data: {
        comments: listResult.data,
        total: listResult.pagination.total,
        stats: statsResult
      }
    });
  });

  getPaymentManagement = asyncHandler(async (req, res) => {
    const { 
      page = 1, 
      pageSize = 20, 
      status, 
      type, 
      userId, 
      keyword, 
      sortBy = 'created_at', 
      sortOrder = 'DESC' 
    } = req.query;

    const options = {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      status: status === 'all' ? null : status,
      type: type === 'all' ? null : type,
      userId: userId ? parseInt(userId) : null,
      keyword,
      sortBy,
      sortOrder
    };

    const result = await Order.getAllOrders(options);
    
    // 获取统计数据
    const statsResult = await Order.query(`
        SELECT
            SUM(CASE WHEN payment_status = 'paid' AND created_at >= CURDATE() THEN final_amount ELSE 0 END) as today_revenue,
            SUM(CASE WHEN payment_status = 'paid' AND created_at >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH) THEN final_amount ELSE 0 END) as monthly_revenue,
            COUNT(*) as total_orders,
            COUNT(DISTINCT CASE WHEN payment_status = 'paid' THEN user_id END) as paid_users_count
        FROM orders
    `);
    const stats = statsResult[0] || {};

    res.json({
        success: true,
        data: {
            orders: result.data,
            pagination: result.pagination,
            stats: {
                todayRevenue: stats.today_revenue || 0,
                monthlyRevenue: stats.monthly_revenue || 0,
                totalOrders: stats.total_orders || 0,
                paidUsers: stats.paid_users_count || 0
            }
      }
    });
  });

  // 获取待审核视频列表
  getPendingVideos = asyncHandler(async (req, res) => {
    const { page = 1, pageSize = 10, sortBy = 'created_at', sortOrder = 'ASC' } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(pageSize),
      sortBy,
      order: sortOrder,
      filters: {
        status: 'pending_review'
      }
    };

    logger.info('[Admin] 获取待审核视频，查询条件:', options);
    
    const result = await Video.getAllVideos(options);
    
    logger.info(`[Admin] 待审核视频查询结果: 找到 ${result.videos?.length || 0} 个视频`);
    if (result.videos && result.videos.length > 0) {
      logger.info('[Admin] 待审核视频列表:', result.videos.map(v => ({ id: v.id, title: v.title, status: v.status })));
    }

    res.json({
      success: true,
      data: {
        videos: result.videos,
        pagination: result
      }
    });
  });

  // 审核视频
  reviewVideo = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { action, reason } = req.body; // action: 'approve' or 'reject'

    const video = await Video.findById(id);
    if (!video) {
      throw new AppError('视频不存在', 404);
    }

    if (video.status !== 'pending_review') {
      throw new AppError('视频状态不是待审核', 400);
    }

    if (action === 'approve') {
      await Video.updateVideo(id, { status: 'published', published_at: new Date() });

      // 可选：发送批准通知
      await notificationService.createNotification({
        userId: video.user_id,
        type: 'video_review',
        title: '您的视频已通过审核！',
        message: `您的视频《${video.title}》已经成功发布。`,
        referenceId: video.id,
        referenceType: 'video',
      });

      res.json({ success: true, message: '视频已批准并发布' });
    } else if (action === 'reject') {
      if (!reason) {
        throw new AppError('请提供拒绝理由', 400);
      }
      await Video.updateVideo(id, { status: 'rejected' });

      // 发送拒绝通知
      await notificationService.createNotification({
        userId: video.user_id,
        type: 'video_review',
        title: '您的视频未通过审核',
        message: `很遗憾，您的视频《${video.title}》未通过审核。原因：${reason}`,
        referenceId: video.id,
        referenceType: 'video',
      });

      res.json({ success: true, message: '视频已拒绝' });
    } else {
      throw new AppError('无效的操作', 400);
    }

    // 清除相关缓存
    await videoService.clearVideoCache(id);
  });

  // 更新订单状态
  updateOrderStatus = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { status, reason } = req.body;
    const adminId = req.user.id;
    
    logger.info(`[Admin] 接收到更新订单状态请求: ID=${id}, Body=${JSON.stringify(req.body)}`);

    // ID可能是数字ID或订单号，先尝试按订单号查找
    let order;

    if (!['paid', 'failed', 'cancelled'].includes(status)) {
      return res.status(400).json({ success: false, message: '无效的状态' });
    }

    // 检查id参数是否有效
    if (!id) {
      throw new AppError('订单ID或订单号不能为空', 400, 'INVALID_ORDER_PARAM');
    }

    const orderInstance = Order;
    // 直接使用connectionManager实例
    const connection = await connectionManager.getMySQLConnection();
    
    try {
      await connection.beginTransaction();

      // 首先尝试按订单号查找，确保id不是undefined
      if (typeof id === 'string' && id.trim().length > 0) {
        order = await orderInstance.getOrderByNo(id);
      }
      
      // 如果按订单号没找到，再尝试按ID查找
      if (!order) {
        const orderId = parseInt(id, 10);
        if (!isNaN(orderId)) {
          order = await orderInstance.getOrderById(orderId);
        }
      }

      // 如果还是没找到订单
      if (!order) {
        throw new AppError('订单不存在', 404, 'ORDER_NOT_FOUND');
      }

      // 检查订单是否已经是目标状态，防止重复处理
      if (order.payment_status === status) {
        await connection.commit();
        logger.info(`[Admin] 订单 ${order.order_no} 已经是 ${status} 状态，跳过处理`);

        const existingOrder = await (order.id ?
          orderInstance.getOrderById(order.id) :
          orderInstance.getOrderByNo(order.order_no));

        return res.json({
          success: true,
          message: '订单状态无需更新',
          data: existingOrder,
        });
      }

      const result = await orderInstance.adminUpdateOrderStatus(
        order.order_no,
        status,
        {
          admin_id: adminId,
          reason: reason || null
        },
        connection
      );

      if (result === 0) {
        throw new AppError('订单状态更新失败：订单不存在、不是待支付状态或状态无需更新', 409);
      }

      // 2. 处理订单关联的业务逻辑
      if (status === 'paid' && result > 0) {
        await this.processOrderBusiness(order, connection);
      }
      
      await connection.commit();

      logger.info(`[Admin] 事务提交成功，开始获取更新后的订单信息`);

      // 获取更新后的订单信息
      let updatedOrder;
      try {
        updatedOrder = await (order.id ? 
          orderInstance.getOrderById(order.id) : 
          orderInstance.getOrderByNo(order.order_no));
        
        logger.info(`[Admin] 获取更新后订单信息成功: ${order.order_no}`);
      } catch (fetchError) {
        logger.error(`[Admin] 获取更新后订单信息失败:`, fetchError);
        // 使用原订单信息作为fallback
        updatedOrder = order;
      }

      operationLogger.logAdminOperation(
        req, 
        'order_status_update', 
        order.order_no, 
        `管理员(ID:${adminId})将订单状态更新为[${status}]`
      );

      logger.info(`[Admin] 即将发送成功响应: ${order.order_no}`);
      
      res.json({
        success: true,
        message: '订单状态更新成功',
        data: updatedOrder,
      });

      logger.info(`[Admin] 成功响应已发送: ${order.order_no}`);
    } catch (error) {
      await connection.rollback();
      logger.error(`[Admin] 更新订单 ${id} 状态失败:`, error);
      throw new AppError(`更新订单状态时发生错误: ${error.message}`, 500);
    } finally {
      connection.release();
    }
  });

  // 获取订单详情
  getOrderDetail = asyncHandler(async (req, res) => {
    const { orderNo } = req.params;

    const order = await Order.getOrderDetailByNo(orderNo);
    if (!order) {
      throw new AppError('订单不存在', 404, 'ORDER_NOT_FOUND');
    }

    res.json({
      success: true,
      data: {
        order
      }
    });
  });

  // 处理订单业务逻辑
  async processOrderBusiness(order, connection) {
    logger.info(`[Admin] 开始处理订单业务逻辑，订单号: ${order.order_no}, 类型: ${order.type}`);

    // 再次检查订单状态，确保不重复处理
    const currentOrder = await Order.getOrderByNo(order.order_no);
    if (currentOrder && currentOrder.payment_status === 'paid') {
      logger.info(`[Admin] 订单 ${order.order_no} 已经处理过，跳过业务逻辑处理`);
      return true;
    }

    if (order.type === 'BALANCE_RECHARGE' || order.type === 'recharge') {
      logger.info(`[Admin] 检测到余额充值订单，为用户(ID:${order.user_id})增加余额: ${order.final_amount}`);
      // 调用 balanceService 来增加用户余额，传入数据库连接以确保事务性
      await balanceService.addUserBalance(order.user_id, order.final_amount, connection);
      logger.info(`[Admin] 用户(ID:${order.user_id})余额增加成功`);
    }

    console.log(`[DEBUG] 检查订单类型，当前类型: "${order.type}"`);
    console.log(`[DEBUG] 类型检查结果: MEMBERSHIP=${order.type === 'MEMBERSHIP'}, membership=${order.type === 'membership'}`);

    if (order.type === 'MEMBERSHIP' || order.type === 'membership') {
      console.log(`[DEBUG] 进入会员购买分支，订单类型: "${order.type}"`);
      // 处理购买会员的逻辑
      console.log(`[DEBUG] 第395行之后，用户ID: ${order.user_id}`);
      console.log(`[DEBUG] 第396行之前，用户ID: ${order.user_id}`);
      logger.info(`[Admin] 检测到会员购买订单，用户(ID:${order.user_id})`);
      console.log(`[DEBUG] 第397行之后，用户ID: ${order.user_id}`);
      logger.info(`[Admin] 即将进入会员处理逻辑的try块`);
      console.log(`[DEBUG] 即将进入会员处理逻辑的try块，用户ID: ${order.user_id}`);

      try {
        const Membership = require('../../../database/models/Membership');
        const MembershipPlan = require('../../../database/models/MembershipPlan');

        logger.info(`[Admin] 开始获取会员计划信息，计划ID: ${order.target_id}`);

        // 获取会员计划信息
        const plan = await MembershipPlan.findById(order.target_id, null, connection);
        if (!plan) {
          throw new Error(`会员计划不存在: ${order.target_id}`);
        }

        logger.info(`[Admin] 会员计划获取成功: ${plan.name}, 开始创建会员记录`);

        // 创建会员记录
        const membership = await Membership.grantMembership(order.user_id, plan, connection);

        logger.info(`[Admin] 用户(ID:${order.user_id})会员开通成功，计划: ${plan.name}, 会员记录ID: ${membership.id}`);
      } catch (error) {
        logger.error(`[Admin] 处理会员订单失败，错误详情:`, {
          message: error.message,
          stack: error.stack,
          orderId: order.id,
          userId: order.user_id,
          planId: order.target_id
        });

        // 如果是锁等待超时错误，提供更友好的错误信息
        if (error.message && error.message.includes('Lock wait timeout exceeded')) {
          throw new Error('系统繁忙，请稍后重试。如果问题持续存在，请联系技术支持。');
        }

        throw error;
      }
    } else {
      logger.warn(`[Admin] 订单号: ${order.order_no} 的类型 [${order.type}] 没有匹配的业务处理逻辑`);
    }

    console.log(`[DEBUG] processOrderBusiness方法即将结束，订单号: ${order.order_no}`);
    return true;
  }

  deleteComment = asyncHandler(async (req, res) => {
    const { id } = req.params;
    await adminService.deleteComment(id);
    res.json({ success: true, message: '评论删除成功' });
  });

  // 批量操作用户
  batchUserAction = asyncHandler(async (req, res) => {
    const { userIds, action, reason } = req.body;
    const adminId = req.user.id;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      throw new AppError('用户ID列表不能为空', 400, 'INVALID_USER_IDS');
    }

    const validActions = ['ban', 'unban', 'delete', 'restore'];
    if (!validActions.includes(action)) {
      throw new AppError('无效的操作类型', 400, 'INVALID_ACTION');
    }

    const results = [];
    for (const userId of userIds) {
      try {
        switch (action) {
          case 'ban':
            if (parseInt(userId, 10) === adminId) {
              throw new AppError('不能封禁自己的账户', 400, 'CANNOT_BAN_SELF');
            }
            await User.banUser(userId, reason);
            break;
          case 'unban':
            await User.unbanUser(userId);
            break;
          case 'restore':
            await User.update(userId, { status: 'active' });
            break;
          case 'delete':
            if (parseInt(userId, 10) === adminId) {
              throw new AppError('不能删除自己的账户', 400, 'CANNOT_DELETE_SELF');
            }
            
            // 增加保护，防止删除最后一个管理员
            const userToDelete = await User.findById(userId);
            if (userToDelete && userToDelete.role === 'admin') {
              const adminCount = await User.count({ role: 'admin', status: 'active' });
              if (adminCount <= 1) {
                throw new AppError('不能删除唯一的管理员账户', 400, 'CANNOT_DELETE_LAST_ADMIN');
              }
            }
            
            await User.update(userId, { status: 'deleted' });
            break;
        }
        results.push({ userId, success: true });
      } catch (error) {
        results.push({ userId, success: false, error: error.message });
      }
    }

    // 记录管理员操作
    operationLogger.logAdminOperation(
      req,
      `batch_user_${action}`,
      userIds.join(','),
      `批量${action}用户`,
      { userIds, reason, results }
    );

    res.json({
      success: true,
      message: `批量操作完成`,
      data: { results }
    });
  });

  // 批量操作视频
  batchVideoAction = asyncHandler(async (req, res) => {
    const { videoIds, action, reason } = req.body;

    if (!videoIds || !Array.isArray(videoIds) || videoIds.length === 0) {
      throw new AppError('视频ID列表不能为空', 400, 'INVALID_VIDEO_IDS');
    }

    const validActions = ['publish', 'unpublish', 'delete', 'hard_delete', 'cleanup_orphaned'];
    if (!validActions.includes(action)) {
      throw new AppError('无效的操作类型', 400, 'INVALID_ACTION');
    }

    const results = [];
    for (const videoId of videoIds) {
      try {
        switch (action) {
          case 'publish':
            await videoModel.updateVideo(videoId, { status: 'published', published_at: new Date() });
            break;
          case 'unpublish':
            await videoModel.updateVideo(videoId, { status: 'private' });
            break;
          case 'delete':
            await videoModel.softDeleteVideo(videoId);
            break;
          case 'hard_delete':
            // 硬删除：完全删除数据库记录和文件
            const video = await videoModel.findById(videoId);
            if (video) {
              try {
                // 先删除数据库记录（使用事务）
                await videoModel.hardDeleteVideo(videoId);

                // 然后删除文件（异步，不影响数据库操作）
                setImmediate(async () => {
                  try {
                    await videoService.deleteVideoFiles(video);
                  } catch (fileError) {
                    logger.error(`删除视频文件失败: ${videoId}`, fileError);
                  }
                });
              } catch (dbError) {
                logger.error(`硬删除视频失败: ${videoId}`, dbError);
                throw dbError; // 重新抛出数据库错误
              }
            }
            break;
          case 'cleanup_orphaned':
            // 清理孤立记录：删除没有对应文件的视频记录
            const orphanedVideo = await videoModel.findById(videoId);
            if (orphanedVideo) {
              try {
                // 直接删除数据库记录，不尝试删除文件
                await videoModel.hardDeleteVideo(videoId);
                logger.info(`清理孤立记录成功: ${videoId}`);
              } catch (dbError) {
                logger.error(`清理孤立记录失败: ${videoId}`, dbError);
                throw dbError;
              }
            }
            break;
        }
        results.push({ videoId, success: true });
      } catch (error) {
        results.push({ videoId, success: false, error: error.message });
      }
    }

    // 检查是否有任何失败的操作
    const isBatchSuccessful = results.every(r => r.success);

    // 记录管理员操作
    operationLogger.logAdminOperation(
      req,
      `batch_video_${action}`,
      videoIds.join(','),
      `批量${action}视频`,
      { videoIds, reason, results }
    );

    res.status(isBatchSuccessful ? 200 : 400).json({
      success: isBatchSuccessful,
      message: isBatchSuccessful ? `批量操作成功完成` : `部分操作失败，请查看详情`,
      data: { results }
    });
  });

  // 获取系统日志
  getSystemLogs = asyncHandler(async (req, res) => {
    const {
      page = 1,
      pageSize = 50,
      level = 'info',
      startDate,
      endDate
    } = req.query;

    // 这里应该从日志文件或日志数据库中获取日志
    // 暂时返回模拟数据
    const logs = [
      {
        timestamp: new Date(),
        level: 'info',
        message: '用户登录成功',
        userId: 1,
        ip: '***********'
      }
    ];

    res.json({
      success: true,
      data: {
        data: logs,
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: logs.length,
          totalPages: 1
        }
      }
    });
  });

  // 获取系统配置
  getSystemConfig = asyncHandler(async (req, res) => {
    const settings = await settingService.getAllSettings();
    res.json({ success: true, data: settings });
  });

  getPublicSettings = asyncHandler(async (req, res) => {
    const settings = await settingService.getPublicSettings();
    res.json({ success: true, data: settings });
  });

  // 更新系统配置
  updateSystemConfig = asyncHandler(async (req, res) => {
    const settings = req.body;
    logger.info('[Admin] 接收到更新系统配置的请求:', { settings });

    // 按分组更新配置
    // 例如，将 { "email.smtpHost": "...", "site.name": "..." }
    // 分成两个调用: bulkUpdate({ "smtpHost": "..." }, "email") 和 bulkUpdate({ "name": "..." }, "site")
    const settingsByGroup = {};

    for (const key in settings) {
      const parts = key.split('.');
      if (parts.length > 1) {
        const group = parts[0];
        const newKey = parts.slice(1).join('.');
        if (!settingsByGroup[group]) {
          settingsByGroup[group] = {};
        }
        settingsByGroup[group][newKey] = settings[key];
      } else {
        // 对于没有分组的键，可以分配一个默认分组，或单独处理
        if (!settingsByGroup['general']) {
          settingsByGroup['general'] = {};
        }
        settingsByGroup['general'][key] = settings[key];
      }
    }
    
    for (const group in settingsByGroup) {
      await settingService.updateSettingsByGroup(settingsByGroup[group], group);
    }
    
    // 记录管理员操作
    operationLogger.logAdminOperation(
      req,
      'system_config_update',
      'system',
      `更新系统配置`,
      settings
    );

    res.json({ success: true, message: '系统配置已更新' });
  });

  // 清理系统缓存
  clearSystemCache = asyncHandler(async (req, res) => {
    const { cacheType = 'all' } = req.body;

    let clearedCount = 0;

    try {
      switch (cacheType) {
        case 'user':
          clearedCount = await cache.delPattern(`${CACHE_KEYS.USER}:*`);
          break;
        case 'video':
          clearedCount = await cache.delPattern(`${CACHE_KEYS.VIDEO}:*`);
          break;
        case 'category':
          clearedCount = await cache.delPattern(`${CACHE_KEYS.CATEGORY}:*`);
          break;
        case 'all':
          await cache.redis.flushdb();
          clearedCount = 'all';
          break;
        default:
          throw new AppError('无效的缓存类型', 400, 'INVALID_CACHE_TYPE');
      }

      operationLogger.logAdminOperation(
        req,
        'cache_clear',
        cacheType,
        '清理系统缓存',
        { cacheType, clearedCount }
      );

      res.json({
        success: true,
        message: '缓存清理成功',
        data: { clearedCount }
      });

    } catch (error) {
      logger.error('清理缓存失败:', error);
      throw new AppError('清理缓存失败', 500, 'CACHE_CLEAR_FAILED');
    }
  });

  // 获取访问统计概览
  getAccessOverview = asyncHandler(async (req, res) => {
    const { timeRange = '7d' } = req.query;

    const overview = await statisticsService.getAccessOverview(timeRange);

    res.json({
      success: true,
      data: { overview }
    });
  });

  // 获取详细访问统计
  getDetailedAccessStats = asyncHandler(async (req, res) => {
    const {
      startDate,
      endDate,
      groupBy = 'day',
      metrics = 'users,videos,interactions'
    } = req.query;

    if (!startDate || !endDate) {
      throw new AppError('请提供开始和结束日期', 400, 'MISSING_DATE_RANGE');
    }

    const options = {
      startDate,
      endDate,
      groupBy,
      metrics: metrics.split(',')
    };

    const stats = await statisticsService.getDetailedAccessStats(options);

    res.json({
      success: true,
      data: { stats }
    });
  });

  // 获取收费统计概览
  getRevenueOverview = asyncHandler(async (req, res) => {
    const { timeRange = '30d' } = req.query;

    const overview = await statisticsService.getRevenueOverview(timeRange);

    res.json({
      success: true,
      data: { overview }
    });
  });

  // 获取详细收费统计
  getDetailedRevenueStats = asyncHandler(async (req, res) => {
    const {
      startDate,
      endDate,
      groupBy = 'day',
      planId
    } = req.query;

    if (!startDate || !endDate) {
      throw new AppError('请提供开始和结束日期', 400, 'MISSING_DATE_RANGE');
    }

    const options = {
      startDate,
      endDate,
      groupBy,
      planId: planId ? parseInt(planId) : null
    };

    const stats = await statisticsService.getDetailedRevenueStats(options);

    res.json({
      success: true,
      data: { stats }
    });
  });

  // 获取用户行为分析
  getUserBehaviorAnalysis = asyncHandler(async (req, res) => {
    const { timeRange = '30d' } = req.query;

    const analysis = await statisticsService.getUserBehaviorAnalysis(timeRange);

    res.json({
      success: true,
      data: { analysis }
    });
  });

  // 获取热门内容分析
  getPopularContentAnalysis = asyncHandler(async (req, res) => {
    const { timeRange = '7d' } = req.query;

    const analysis = await statisticsService.getPopularContentAnalysis(timeRange);

    res.json({
      success: true,
      data: { analysis }
    });
  });

  // 检查孤立的视频记录
  checkOrphanedVideos = asyncHandler(async (req, res) => {
    try {
      // 获取所有视频记录
      const videos = await videoModel.query('SELECT id, title, file_path, thumbnail_url FROM videos WHERE status != "deleted"');

      const orphanedVideos = [];
      const validVideos = [];

      for (const video of videos) {
        let isOrphaned = false;
        const missingFiles = [];

        // 检查主视频文件
        if (video.file_path) {
          try {
            await fs.promises.access(video.file_path);
          } catch (error) {
            isOrphaned = true;
            missingFiles.push('主视频文件');
          }
        } else {
          isOrphaned = true;
          missingFiles.push('主视频文件路径为空');
        }

        // 检查缩略图
        if (video.thumbnail_url) {
          const thumbnailPath = path.join(process.cwd(), 'uploads', video.thumbnail_url.replace('/uploads/', ''));
          try {
            await fs.promises.access(thumbnailPath);
          } catch (error) {
            missingFiles.push('缩略图');
          }
        }

        if (isOrphaned) {
          orphanedVideos.push({
            id: video.id,
            title: video.title,
            missingFiles
          });
        } else {
          validVideos.push({
            id: video.id,
            title: video.title
          });
        }
      }

      res.json({
        success: true,
        data: {
          total: videos.length,
          orphaned: orphanedVideos.length,
          valid: validVideos.length,
          orphanedVideos,
          summary: `发现 ${orphanedVideos.length} 个孤立记录，${validVideos.length} 个正常记录`
        }
      });

    } catch (error) {
      logger.error('检查孤立视频记录失败:', error);
      throw new AppError('检查孤立记录失败', 500, 'CHECK_ORPHANED_FAILED');
    }
  });

  // 批量清理孤立记录
  cleanupOrphanedVideos = asyncHandler(async (req, res) => {
    const { videoIds } = req.body;

    if (!Array.isArray(videoIds) || videoIds.length === 0) {
      throw new AppError('请提供要清理的视频ID列表', 400, 'INVALID_VIDEO_IDS');
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const videoId of videoIds) {
      try {
        await Video.hardDeleteVideo(videoId);
        results.push({ videoId, success: true });
        successCount++;
      } catch (error) {
        results.push({ videoId, success: false, error: error.message });
        errorCount++;
      }
    }

    // 记录管理员操作
    operationLogger.logAdminOperation(
      req,
      'cleanup_orphaned_videos',
      videoIds.join(','),
      '批量清理孤立视频记录',
      { videoIds, results, successCount, errorCount }
    );

    res.json({
      success: true,
      message: `清理完成：成功 ${successCount} 个，失败 ${errorCount} 个`,
      data: { results, successCount, errorCount }
    });
  });

  deleteVideo = asyncHandler(async (req, res, next) => {
    const { id } = req.params;
    const video = await Video.findById(id);

    if (!video) {
      return next(new AppError('未找到指定的视频', 404));
    }

    // 调用 videoService 的方法来删除文件
    await videoService.deleteVideoFiles(video);

    // 从数据库中删除记录
    const deleted = await Video.delete(id);

    if (!deleted) {
      return next(new AppError('删除视频记录失败', 500));
    }

    res.json({
      success: true,
      message: '视频删除成功'
    });
  });

  // 评论管理
  // This is a placeholder. Implementation is needed.
}

module.exports = new AdminController();
