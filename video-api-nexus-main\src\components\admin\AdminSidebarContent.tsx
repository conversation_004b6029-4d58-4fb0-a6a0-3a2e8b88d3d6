import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  BarChart3,
  Users,
  Play,
  MessageCircle,
  Settings,
  CreditCard,
  Crown,
  Shield,
  Upload,
  FolderTree,
  Banknote,
  Landmark,
} from 'lucide-react';

interface AdminSidebarContentProps {
  onNavigate?: () => void;
  className?: string;
}

const AdminSidebarContent: React.FC<AdminSidebarContentProps> = ({ 
  onNavigate,
  className = '' 
}) => {
  const location = useLocation();
  const activeSection = location.pathname.split('/')[2] || 'dashboard';

  const menuItems = [
    { id: 'dashboard', label: '仪表板', icon: BarChart3, path: '/admin/dashboard' },
    { id: 'users', label: '用户管理', icon: Users, path: '/admin/users' },
    { id: 'videos', label: '视频管理', icon: Play, path: '/admin/videos' },
    { id: 'review', label: '视频审核', icon: Shield, path: '/admin/videos/review' },
    { id: 'video-upload', label: '上传视频', icon: Upload, path: '/admin/video-upload' },
    { id: 'categories', label: '分类管理', icon: FolderTree, path: '/admin/categories' },
    { id: 'comments', label: '评论管理', icon: MessageCircle, path: '/admin/comments' },
    { id: 'payments', label: '订单管理', icon: CreditCard, path: '/admin/payments' },
    { id: 'withdrawals', label: '提现管理', icon: Landmark, path: '/admin/withdrawals' },
    { id: 'members', label: '会员管理', icon: Crown, path: '/admin/members' },
    { id: 'creem-plans', label: 'Creem 产品', icon: Banknote, path: '/admin/creem-plans' },
    { id: 'payment-settings', label: '支付设置', icon: Banknote, path: '/admin/payment-settings' },
    { id: 'settings', label: '系统设置', icon: Settings, path: '/admin/settings' },
  ];

  const handleNavigation = () => {
    if (onNavigate) {
      onNavigate();
    }
  };

  return (
    <nav className={`space-y-1 px-4 pb-4 ${className}`}>
      {menuItems.map((item) => {
        const IconComponent = item.icon;
        const isActive = item.id === 'videos' && location.pathname.includes('/review')
          ? activeSection === 'review'
          : activeSection === item.id;
        
        return (
          <Link
            key={item.id}
            to={item.path}
            onClick={handleNavigation}
            className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-colors text-sm ${
              isActive
                ? 'bg-primary text-primary-foreground shadow-sm'
                : 'hover:bg-accent hover:text-accent-foreground'
            }`}
          >
            <IconComponent size={18} />
            <span className="font-medium">{item.label}</span>
          </Link>
        );
      })}
    </nav>
  );
};

export default AdminSidebarContent;