import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>, Monitor, Sun, <PERSON>, Z<PERSON>, <PERSON>s, TreePine, Sunset, Rainbow } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "./theme-provider"
import { useTranslation } from 'react-i18next'

const getThemes = (t: any) => [
  {
    name: "system",
    label: t('theme.system'),
    icon: Monitor,
    description: t('theme.systemDesc')
  },
  {
    name: "light",
    label: t('theme.light'),
    icon: Sun,
    description: t('theme.lightDesc')
  },
  {
    name: "dark",
    label: t('theme.dark'),
    icon: Moon,
    description: t('theme.darkDesc')
  },
  {
    name: "neon",
    label: t('theme.neon'),
    icon: Zap,
    description: t('theme.neonDesc'),
    preview: "from-purple-500 to-pink-500"
  },
  {
    name: "ocean",
    label: t('theme.ocean'),
    icon: Waves,
    description: t('theme.oceanDesc'),
    preview: "from-blue-600 to-cyan-500"
  },
  {
    name: "forest",
    label: t('theme.forest'),
    icon: TreePine,
    description: t('theme.forestDesc'),
    preview: "from-green-600 to-emerald-500"
  },
  {
    name: "sunset",
    label: t('theme.sunset'),
    icon: Sunset,
    description: t('theme.sunsetDesc'),
    preview: "from-orange-500 to-red-500"
  },
  {
    name: "rainbow",
    label: t('theme.rainbow'),
    icon: Rainbow,
    description: t('theme.rainbowDesc'),
    preview: "from-purple-500 via-blue-500 to-green-500"
  }
]

export function ThemeSwitcher() {
  const { theme, setTheme } = useTheme()
  const { t } = useTranslation()
  const [isOpen, setIsOpen] = useState(false)

  const themes = getThemes(t)
  const currentTheme = themes.find(themeItem => themeItem.name === theme) || themes[0]

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <currentTheme.icon className="h-4 w-4" />
          <span className="hidden sm:inline">{currentTheme.label}</span>
          <Palette className="h-3 w-3 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Palette className="h-4 w-4" />
          选择主题
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* 基础主题 */}
        <div className="px-2 py-1">
          <div className="text-xs font-medium text-muted-foreground mb-2">{t('theme.basicThemes')}</div>
          {themes.slice(0, 3).map((themeOption) => {
            const Icon = themeOption.icon
            return (
              <DropdownMenuItem
                key={themeOption.name}
                onClick={() => setTheme(themeOption.name as any)}
                className="flex items-center gap-3 px-2 py-2 cursor-pointer"
              >
                <Icon className="h-4 w-4" />
                <div className="flex-1">
                  <div className="font-medium">{themeOption.label}</div>
                  <div className="text-xs text-muted-foreground">
                    {themeOption.description}
                  </div>
                </div>
                {theme === themeOption.name && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </DropdownMenuItem>
            )
          })}
        </div>

        <DropdownMenuSeparator />
        
        {/* 炫酷主题 */}
        <div className="px-2 py-1">
          <div className="text-xs font-medium text-muted-foreground mb-2">{t('theme.coolThemes')}</div>
          {themes.slice(3).map((themeOption) => {
            const Icon = themeOption.icon
            return (
              <DropdownMenuItem
                key={themeOption.name}
                onClick={() => setTheme(themeOption.name as any)}
                className="flex items-center gap-3 px-2 py-2 cursor-pointer"
              >
                <Icon className="h-4 w-4" />
                <div className="flex-1">
                  <div className="font-medium">{themeOption.label}</div>
                  <div className="text-xs text-muted-foreground">
                    {themeOption.description}
                  </div>
                </div>
                {themeOption.preview && (
                  <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${themeOption.preview}`} />
                )}
                {theme === themeOption.name && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </DropdownMenuItem>
            )
          })}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// 简化版主题切换器（用于移动端或空间受限的地方）
export function ThemeSwitcherCompact() {
  const { theme, setTheme } = useTheme()
  const { t } = useTranslation()

  const themes = getThemes(t)
  const currentTheme = themes.find(themeItem => themeItem.name === theme) || themes[0]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <currentTheme.icon className="h-4 w-4" />
          <span className="sr-only">{t('theme.switchTheme')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {themes.map((themeOption) => {
          const Icon = themeOption.icon
          return (
            <DropdownMenuItem
              key={themeOption.name}
              onClick={() => setTheme(themeOption.name as any)}
              className="flex items-center gap-2"
            >
              <Icon className="h-4 w-4" />
              {themeOption.label}
              {theme === themeOption.name && (
                <Check className="h-4 w-4 ml-auto" />
              )}
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
