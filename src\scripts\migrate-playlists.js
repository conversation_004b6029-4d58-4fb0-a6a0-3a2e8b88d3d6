const fs = require('fs').promises;
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

// MySQL连接配置
const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4',
  timezone: 'Z'
};

async function runMigration() {
  let connection;
  
  try {
    console.log('🚀 开始执行播放列表数据库迁移...');
    
    // 创建数据库连接
    connection = await mysql.createConnection(mysqlConfig);
    console.log('✅ 数据库连接成功');
    
    // 读取迁移文件
    const migrationPath = path.join(__dirname, '../database/migrations/20250122_create_playlists_tables.sql');
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');
    console.log('📄 迁移文件读取成功');
    
    // 执行迁移 - 分别执行每个CREATE TABLE语句
    console.log('⚡ 正在执行迁移...');

    // 手动定义SQL语句，确保正确执行
    const sqlStatements = [
      // 创建playlists表
      `CREATE TABLE IF NOT EXISTS playlists (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        play_mode ENUM('sequence', 'loop', 'random') DEFAULT 'sequence',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at),
        INDEX idx_updated_at (updated_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

      // 创建playlist_items表
      `CREATE TABLE IF NOT EXISTS playlist_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        playlist_id INT NOT NULL,
        video_id INT NOT NULL,
        position INT NOT NULL DEFAULT 0,
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE,
        FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,

        INDEX idx_playlist_id (playlist_id),
        INDEX idx_video_id (video_id),
        INDEX idx_position (position),
        INDEX idx_added_at (added_at),

        UNIQUE KEY unique_playlist_video (playlist_id, video_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

      // 创建play_history表
      `CREATE TABLE IF NOT EXISTS play_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        video_id INT NOT NULL,
        played_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        watch_duration INT DEFAULT 0,
        video_duration INT DEFAULT 0,
        completed BOOLEAN DEFAULT FALSE,

        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,

        INDEX idx_user_id (user_id),
        INDEX idx_video_id (video_id),
        INDEX idx_played_at (played_at),
        INDEX idx_completed (completed)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

      // 添加表注释
      `ALTER TABLE playlists COMMENT = '用户播放列表表'`,
      `ALTER TABLE playlist_items COMMENT = '播放列表项目表'`,
      `ALTER TABLE play_history COMMENT = '播放历史记录表'`
    ];

    console.log(`📝 准备执行 ${sqlStatements.length} 个SQL语句`);

    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      console.log(`⚡ 执行语句 ${i + 1}/${sqlStatements.length}...`);
      try {
        await connection.execute(statement);
        console.log(`✅ 语句 ${i + 1} 执行成功`);
      } catch (error) {
        console.error(`❌ 语句 ${i + 1} 执行失败:`, error.message);
        console.error('失败的SQL:', statement.substring(0, 100) + '...');
        throw error;
      }
    }

    console.log('✅ 播放列表表创建成功');
    
    // 验证表是否创建成功
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME IN ('playlists', 'playlist_items', 'play_history')
    `, [process.env.DB_NAME || 'video_platform']);
    
    console.log('📊 创建的表:');
    tables.forEach(table => {
      console.log(`  - ${table.TABLE_NAME}`);
    });
    
    // 检查表结构
    console.log('\n📋 表结构验证:');
    
    // 检查playlists表
    const [playlistsColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'playlists'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME || 'video_platform']);
    
    console.log('  playlists表字段:');
    playlistsColumns.forEach(col => {
      console.log(`    - ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
    });
    
    // 检查playlist_items表
    const [itemsColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'playlist_items'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME || 'video_platform']);
    
    console.log('  playlist_items表字段:');
    itemsColumns.forEach(col => {
      console.log(`    - ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
    });
    
    // 检查play_history表
    const [historyColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'play_history'
      ORDER BY ORDINAL_POSITION
    `, [process.env.DB_NAME || 'video_platform']);
    
    console.log('  play_history表字段:');
    historyColumns.forEach(col => {
      console.log(`    - ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
    });
    
    console.log('\n🎉 播放列表数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };
