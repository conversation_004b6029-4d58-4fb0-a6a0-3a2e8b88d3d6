const Joi = require('joi');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');
const settingService = require('../services/settingService');

// 验证中间件工厂函数
const validate = (schema, source = 'body') => {
  return (req, res, next) => {
    const data = req[source];
    
    const { error, value } = schema.validate(data, {
      abortEarly: false, // 返回所有错误
      allowUnknown: true, // 允许未知字段
      stripUnknown: false // 不移除未知字段
    });
    
    if (error) {
      const errorMessages = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      logger.warn('请求验证失败:', {
        url: req.url,
        method: req.method,
        requestData: data,
        errors: errorMessages
      });

      console.log('=== 验证失败详情 ===');
      console.log('URL:', req.url);
      console.log('Method:', req.method);
      console.log('请求数据:', data);
      console.log('验证错误:', errorMessages);

      return next(new AppError('请求数据验证失败', 400, 'VALIDATION_ERROR', errorMessages));
    }
    
    // 将验证后的数据替换原始数据
    req[source] = value;
    next();
  };
};

// 常用验证规则
const commonRules = {
  // ID验证
  id: Joi.number().integer().positive().required(),
  optionalId: Joi.number().integer().positive().optional(),
  
  // 字符串验证
  email: Joi.string().email().max(255).required(),
  optionalEmail: Joi.string().email().max(255).optional(),
  
  password: Joi.string().min(1).max(128).required(), // 基本验证，详细验证在动态验证中进行
  username: Joi.string().pattern(/^[a-zA-Z0-9\u4e00-\u9fa5_@.-]+$/).min(2).max(50).required().messages({
    'string.pattern.base': '用户名只能包含中文、字母、数字、下划线、@、点和短横线',
    'string.min': '用户名长度至少为 {#limit} 个字符',
    'string.max': '用户名长度不能超过 {#limit} 个字符',
  }),
  nickname: Joi.string().max(100).optional(),
  
  // 分页验证
  page: Joi.number().integer().min(1).default(1),
  pageSize: Joi.number().integer().min(1).max(100).default(20),
  
  // 排序验证
  sortBy: Joi.string().valid('id', 'created_at', 'updated_at', 'name', 'title').default('created_at'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  
  // 状态验证
  status: Joi.string().valid('active', 'inactive', 'deleted').optional(),
  
  // 日期验证
  dateRange: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
  }).optional(),
  
  // 搜索验证
  keyword: Joi.string().max(100).optional(),
  
  // 文件验证
  fileType: Joi.string().valid('image', 'video', 'document').optional()
};

// 用户相关验证规则
const userSchemas = {
  // 用户注册
  register: Joi.object({
    email: commonRules.email,
    password: commonRules.password,
    username: commonRules.username,
    nickname: commonRules.nickname,
    invitationCode: Joi.string().alphanum().length(8).optional()
  }),
  
  // 用户登录
  login: Joi.object({
    email: commonRules.email,
    password: commonRules.password,
    rememberMe: Joi.boolean().default(false)
  }),
  
  // 更新用户资料
  updateProfile: Joi.object({
    nickname: commonRules.nickname,
    avatar: Joi.string().uri().max(500).optional(),
    phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional(),
    gender: Joi.string().valid('male', 'female', 'other').optional(),
    birthday: Joi.date().max('now').optional(),
    bio: Joi.string().max(500).optional()
  }),
  
  // 修改密码
  changePassword: Joi.object({
    currentPassword: commonRules.password,
    newPassword: commonRules.password,
    confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required()
  }),
  
  // 用户列表查询
  userList: Joi.object({
    page: commonRules.page,
    pageSize: commonRules.pageSize,
    sortBy: Joi.string().valid('id', 'created_at', 'username', 'email').default('created_at'),
    sortOrder: commonRules.sortOrder,
    role: Joi.string().valid('user', 'member', 'vip', 'admin').optional(),
    status: commonRules.status,
    keyword: commonRules.keyword
  })
};

// 视频相关验证规则
const videoSchemas = {
  // 视频上传
  upload: Joi.object({
    title: Joi.string().max(255).required(),
    description: Joi.string().max(2000).optional(),
    categoryId: commonRules.optionalId,
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
    visibility: Joi.string().valid('public', 'private', 'member_only', 'vip_only', 'paid').default('public'),
    price: Joi.number().precision(2).min(0).max(9999.99).default(0)
  }),
  
  // 视频更新
  update: Joi.object({
    title: Joi.string().max(255).optional(),
    description: Joi.string().max(2000).optional(),
    categoryId: commonRules.optionalId,
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
    visibility: Joi.string().valid('public', 'private', 'member_only', 'vip_only', 'paid').optional(),
    price: Joi.number().precision(2).min(0).max(9999.99).optional()
  }),
  
  // 视频列表查询
  list: Joi.object({
    page: commonRules.page,
    pageSize: commonRules.pageSize,
    sortBy: Joi.string().valid('id', 'created_at', 'title', 'view_count', 'like_count').default('created_at'),
    sortOrder: commonRules.sortOrder,
    categoryId: commonRules.optionalId,
    visibility: Joi.string().valid('public', 'private', 'member_only', 'vip_only', 'paid').optional(),
    status: Joi.string().valid('uploading', 'processing', 'published', 'private', 'deleted').optional(),
    keyword: commonRules.keyword,
    userId: commonRules.optionalId
  })
};

// 评论相关验证规则
const commentSchemas = {
  // 创建评论
  create: Joi.object({
    videoId: commonRules.id,
    content: Joi.string().max(1000).required(),
    parentId: commonRules.optionalId
  }),

  // 评论列表
  list: Joi.object({
    videoId: commonRules.id,
    page: commonRules.page,
    pageSize: commonRules.pageSize,
    sortBy: Joi.string().valid('created_at', 'like_count').default('created_at'),
    sortOrder: commonRules.sortOrder
  })
};

// 分类相关验证规则
const categorySchemas = {
  // 创建分类
  create: Joi.object({
    name: Joi.string().max(100).required(),
    slug: Joi.string().max(100).pattern(/^[a-zA-Z0-9\u4e00-\u9fa5_-]*$/).optional().allow(''), // 支持中文、英文、数字、下划线、连字符，完全可选（*表示0个或多个）
    description: Joi.string().max(500).optional().allow(''),
    parentId: Joi.number().integer().positive().optional().allow(null),
    sortOrder: Joi.number().integer().min(0).default(0)
  }),

  // 更新分类
  update: Joi.object({
    name: Joi.string().max(100).optional(),
    slug: Joi.string().max(100).pattern(/^[a-zA-Z0-9\u4e00-\u9fa5_-]*$/).optional().allow(''), // 支持中文、英文、数字、下划线、连字符，完全可选（*表示0个或多个）
    description: Joi.string().max(500).optional().allow(''),
    parent_id: Joi.number().integer().positive().optional().allow(null), // 允许null值
    sort_order: Joi.number().integer().min(0).optional(),
    status: Joi.string().valid('active', 'inactive').optional()
  }),

  // 分类搜索
  search: Joi.object({
    keyword: Joi.string().max(100).required(),
    limit: Joi.number().integer().min(1).max(50).default(20)
  })
};

// 系统配置验证规则
const configSchemas = {
  // 更新配置
  update: Joi.object({
    configKey: Joi.string().max(100).required(),
    configValue: Joi.string().max(2000).required(),
    configType: Joi.string().valid('string', 'number', 'boolean', 'json').default('string'),
    description: Joi.string().max(500).optional(),
    isPublic: Joi.boolean().default(false)
  })
};

// 文件上传验证规则
const fileSchemas = {
  // 文件上传元数据
  uploadMeta: Joi.object({
    uploadType: Joi.string().valid('avatar', 'video', 'thumbnail', 'attachment').required(),
    originalName: Joi.string().max(255).required(),
    fileSize: Joi.number().integer().positive().max(500 * 1024 * 1024).required(), // 最大500MB
    mimeType: Joi.string().max(100).required()
  })
};

// 支付相关验证规则
const paymentSchemas = {
  // 创建支付订单
  createOrder: Joi.object({
    type: Joi.string().valid('membership', 'video', 'recharge').required(),
    targetId: Joi.number().integer().positive().when('type', {
      is: Joi.valid('membership', 'video'),
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    amount: Joi.number().positive().precision(2).when('type', {
      is: 'recharge',
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    paymentMethod: Joi.string().max(50).required(),
    description: Joi.string().max(500).optional(),
    tradeType: Joi.string().valid('NATIVE', 'JSAPI', 'H5').optional()
  }),
  
  // 我的订单列表查询
  myOrdersList: Joi.object({
    page: commonRules.page,
    pageSize: commonRules.pageSize,
    type: Joi.string().valid('membership', 'video', 'recharge', 'all').optional(),
    status: Joi.string().valid('pending', 'paid', 'failed', 'expired', 'refunded', 'cancelled', 'all').optional()
  }),

  // 申请退款
  refund: Joi.object({
    orderNo: Joi.string().max(32).required(),
    reason: Joi.string().max(200).required(),
    amount: Joi.number().positive().precision(2).optional()
  })
};

// 余额充值验证
const balanceSchemas = {
  recharge: Joi.object({
    amount: Joi.number().positive().precision(2).required().messages({
      'number.base': '充值金额必须是有效的数字',
      'number.positive': '充值金额必须大于零',
      'any.required': '必须提供充值金额'
    })
  })
};

// 验证中间件导出
const validationMiddleware = {
  // 用户验证
  validateUserRegister: validate(userSchemas.register),
  validateUserLogin: validate(userSchemas.login),
  validateUserUpdate: validate(userSchemas.updateProfile),
  validatePasswordChange: validate(userSchemas.changePassword),
  validateUserList: validate(userSchemas.userList, 'query'),

  // 视频验证
  validateVideoUpload: validate(videoSchemas.upload),
  validateVideoUpdate: validate(videoSchemas.update),
  validateVideoList: validate(videoSchemas.list, 'query'),

  // 评论验证
  validateCommentCreate: validate(commentSchemas.create),
  validateCommentList: validate(commentSchemas.list, 'query'),

  // 分类验证
  validateCategoryCreate: validate(categorySchemas.create),
  validateCategoryUpdate: validate(categorySchemas.update),
  validateCategorySearch: validate(categorySchemas.search, 'query'),

  // 支付验证
  validatePaymentCreate: validate(paymentSchemas.createOrder),
  validatePaymentRefund: validate(paymentSchemas.refund),
  validateMyOrdersList: validate(paymentSchemas.myOrdersList, 'query'),

  // 配置验证
  validateConfigUpdate: validate(configSchemas.update),

  // 文件验证
  validateFileUpload: validate(fileSchemas.uploadMeta),
  
  // 通用验证
  validateId: validate(Joi.object({ id: commonRules.id }), 'params'),
  validatePagination: validate(Joi.object({
    page: commonRules.page,
    pageSize: commonRules.pageSize
  }), 'query'),

  // 新增：充值验证
  validateRecharge: validate(Joi.object({
    amount: Joi.number().positive().required().messages({
      'number.base': '充值金额必须是数字',
      'number.positive': '充值金额必须为正数',
      'any.required': '充值金额不能为空',
    }),
    paymentMethod: Joi.string().valid('alipay', 'wechatpay', 'epay').required().messages({
      'string.base': '支付方式必须是字符串',
      'any.only': '不支持的支付方式',
      'any.required': '支付方式不能为空',
    }),
  })),
};

// 动态密码验证中间件
const validatePasswordRequirements = async (req, res, next) => {
  try {
    // 支持 password 和 newPassword 字段
    const password = req.body.password || req.body.newPassword;
    const fieldName = req.body.password ? 'password' : 'newPassword';

    if (!password) {
      return next(); // 如果没有密码字段，跳过验证（由基本验证处理）
    }

    // 获取密码要求配置
    const configMinLength = await settingService.getSetting('security.passwordMinLength');
    const configRequireStrong = await settingService.getSetting('security.requireStrongPassword');

    const minLength = configMinLength ? parseInt(configMinLength) : 8;
    const requireStrong = configRequireStrong === 'true' || configRequireStrong === true;

    // 检查密码长度
    if (password.length < minLength) {
      const error = new AppError(`密码长度至少需要${minLength}个字符`, 400, 'VALIDATION_ERROR');
      error.details = [{
        field: fieldName,
        message: `密码长度至少需要${minLength}个字符`,
        value: password
      }];
      return next(error);
    }

    // 检查强密码要求
    if (requireStrong) {
      const checks = [
        { regex: /[A-Z]/, message: "密码必须包含大写字母" },
        { regex: /[a-z]/, message: "密码必须包含小写字母" },
        { regex: /\d/, message: "密码必须包含数字" },
        { regex: /[!@#$%^&*(),.?":{}|<>]/, message: "密码必须包含特殊字符" }
      ];

      for (const check of checks) {
        if (!check.regex.test(password)) {
          const error = new AppError(check.message, 400, 'VALIDATION_ERROR');
          error.details = [{
            field: fieldName,
            message: check.message,
            value: password
          }];
          return next(error);
        }
      }
    }

    next();
  } catch (error) {
    logger.error('动态密码验证失败:', error);
    next(error);
  }
};

module.exports = {
  validate,
  commonRules,
  userSchemas,
  videoSchemas,
  commentSchemas,
  categorySchemas,
  configSchemas,
  fileSchemas,
  paymentSchemas,
  balanceSchemas,
  validatePasswordRequirements,
  ...validationMiddleware
};
