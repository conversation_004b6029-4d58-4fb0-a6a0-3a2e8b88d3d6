import React from 'react';
import { Shield } from 'lucide-react';
import AdminSidebarContent from './AdminSidebarContent';

const AdminSidebar = () => {
  return (
    <div className="hidden md:flex w-64 bg-card border-r flex-col">
      {/* 顶部标题 */}
      <div className="flex items-center space-x-3 p-6 pb-4 border-b">
        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
          <Shield className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-lg font-bold">管理后台</h2>
          <p className="text-xs text-muted-foreground">Admin Panel</p>
        </div>
      </div>

      {/* 导航内容 */}
      <div className="flex-1 py-4 overflow-y-auto">
        <AdminSidebarContent />
      </div>
    </div>
  );
};

export default AdminSidebar;
