version: '3.8'

services:
  # 应用服务
  app:
    build: .
    container_name: video-platform-api
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=video_user
      - DB_PASSWORD=secure_password
      - DB_NAME=video_platform
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your_jwt_secret_key_here
      - JWT_REFRESH_SECRET=your_refresh_secret_key_here
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./reports:/app/reports
    depends_on:
      - mysql
      - redis
    networks:
      - video-platform-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: video-platform-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=video_platform
      - MYSQL_USER=video_user
      - MYSQL_PASSWORD=secure_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    ports:
      - "3306:3306"
    networks:
      - video-platform-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: video-platform-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - video-platform-network
    command: redis-server --appendonly yes

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: video-platform-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./uploads:/var/www/uploads
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - video-platform-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  video-platform-network:
    driver: bridge
