const mysql = require('mysql2/promise');
const logger = require('../src/utils/logger');

// 加载环境变量
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'video_user',
  password: process.env.DB_PASSWORD || 'secure_password',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

async function checkAndFixUserRole() {
  let connection;
  
  try {
    console.log('🔍 开始检查用户角色同步问题...\n');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    
    // 查找用户GGG的信息
    const [users] = await connection.execute(`
      SELECT id, username, email, role, created_at 
      FROM users 
      WHERE username = 'GGG' OR email LIKE '%GGG%'
    `);
    
    if (users.length === 0) {
      console.log('❌ 未找到用户GGG');
      return;
    }
    
    const user = users[0];
    console.log('👤 找到用户信息:');
    console.log(`   ID: ${user.id}`);
    console.log(`   用户名: ${user.username}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   当前角色: ${user.role}`);
    console.log(`   注册时间: ${user.created_at}\n`);
    
    // 检查用户的会员记录
    const [memberships] = await connection.execute(`
      SELECT 
        m.id,
        m.start_date,
        m.end_date,
        m.status,
        mp.name as plan_name,
        mp.duration_days
      FROM memberships m
      JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.user_id = ?
      ORDER BY m.created_at DESC
    `, [user.id]);
    
    console.log('📋 用户会员记录:');
    if (memberships.length === 0) {
      console.log('   无会员记录');
    } else {
      memberships.forEach((membership, index) => {
        console.log(`   ${index + 1}. 计划: ${membership.plan_name}`);
        console.log(`      状态: ${membership.status}`);
        console.log(`      开始时间: ${membership.start_date}`);
        console.log(`      结束时间: ${membership.end_date}`);
        console.log(`      是否有效: ${new Date(membership.end_date) > new Date() && membership.status === 'active' ? '是' : '否'}`);
        console.log('');
      });
    }
    
    // 检查用户的订单记录
    const [orders] = await connection.execute(`
      SELECT 
        order_no,
        type,
        payment_status,
        final_amount,
        created_at
      FROM orders
      WHERE user_id = ? AND type IN ('MEMBERSHIP', 'membership')
      ORDER BY created_at DESC
      LIMIT 5
    `, [user.id]);
    
    console.log('💳 用户相关订单:');
    if (orders.length === 0) {
      console.log('   无会员订单记录');
    } else {
      orders.forEach((order, index) => {
        console.log(`   ${index + 1}. 订单号: ${order.order_no}`);
        console.log(`      类型: ${order.type}`);
        console.log(`      状态: ${order.payment_status}`);
        console.log(`      金额: ${order.final_amount}`);
        console.log(`      创建时间: ${order.created_at}`);
        console.log('');
      });
    }
    
    // 判断用户应该具有的角色
    const activeMembership = memberships.find(m => 
      m.status === 'active' && new Date(m.end_date) > new Date()
    );
    
    const shouldBeRole = activeMembership ? 'member' : 'user';
    
    console.log(`🎯 角色分析:`);
    console.log(`   当前角色: ${user.role}`);
    console.log(`   应有角色: ${shouldBeRole}`);
    console.log(`   是否需要修复: ${user.role !== shouldBeRole ? '是' : '否'}\n`);
    
    // 如果需要修复，执行修复
    if (user.role !== shouldBeRole) {
      console.log('🔧 开始修复用户角色...');
      
      await connection.execute(`
        UPDATE users SET role = ? WHERE id = ?
      `, [shouldBeRole, user.id]);
      
      console.log(`✅ 用户角色已从 "${user.role}" 更新为 "${shouldBeRole}"`);
      
      // 验证修复结果
      const [updatedUser] = await connection.execute(`
        SELECT role FROM users WHERE id = ?
      `, [user.id]);
      
      console.log(`✅ 验证结果: 用户当前角色为 "${updatedUser[0].role}"`);
    } else {
      console.log('✅ 用户角色正确，无需修复');
    }
    
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkAndFixUserRole()
    .then(() => {
      console.log('\n🎉 检查完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { checkAndFixUserRole };
