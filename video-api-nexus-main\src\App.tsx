import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import './i18n/index'; // 初始化 i18n
import { 
  createBrowserRouter, 
  RouterProvider,
  Navigate,
  Outlet
} from "react-router-dom";
import ErrorBoundary from "./components/ErrorBoundary";
import UserApp from "./pages/UserApp";
import AdminApp from "./pages/AdminApp";
import NotFound from "./pages/NotFound";
import LoginPage from "./pages/LoginPage";
import RegisterPage from "./pages/RegisterPage";
import AdminLoginPage from "./pages/AdminLoginPage";
import TestPage from "./pages/TestPage";

import Index from "./pages/Index";
import MainLayout from "./components/layout/MainLayout";
import FavoritesPage from "./pages/FavoritesPage";
import ProfilePage from "./pages/ProfilePage";
import UserDetailPage from "./pages/UserDetailPage";
import VideoDetail from './pages/VideoDetail';
import MyOrdersPage from './pages/MyOrdersPage';
import MyEarningsPage from './pages/MyEarningsPage'; // 1. Import the new page
import { MyWorksPage } from './pages/MyWorksPage';
import UserUploadPage from './pages/UserUploadPage';
import { FollowListPage } from './components/user/FollowListPage';

// 😬 新增: 引入所有管理员页面组件
import AdminDashboard from './components/admin/AdminDashboard';
import AdminUsers from './components/admin/AdminUsers';
import AdminVideos from './components/admin/AdminVideos';
import AdminUpload from './components/admin/AdminVideoUpload';
import AdminCategories from './components/admin/AdminCategories';
import AdminComments from './components/admin/AdminComments';
import AdminPayments from './components/admin/AdminPayments';
import AdminMembers from './components/admin/AdminMembers';
import AdminPaymentSettings from './components/admin/AdminPaymentSettings';
import AdminSettings from './components/admin/AdminSettings';
import AdminVideoReview from './components/admin/AdminVideoReview';
import AdminWithdrawalsPage from './pages/admin/AdminWithdrawalsPage';
import AdminCreemPlansPage from './pages/admin/AdminCreemPlansPage';


import { AuthProvider, useAuth } from './hooks/useAuth';
import { MembershipProvider } from './hooks/useMembership';
import { ThemeProvider } from './components/theme-provider';
import { FollowProvider } from './contexts/FollowContext';
import { adminApi } from './services/adminApi';
import React, { useEffect } from 'react';
import api from './services/api';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5分钟内数据被认为是新鲜的
      gcTime: 10 * 60 * 1000, 
      retry: 2, // 失败时重试2次
      refetchOnWindowFocus: false, // 窗口聚焦时不自动重新获取
      refetchOnReconnect: true, // 网络重连时重新获取
    },
    mutations: {
      retry: 1, // 变更操作失败时重试1次
    },
  },
});

const PrivateRoute = ({ children }: { children: React.ReactElement }) => {
  const { isAuthenticated, isLoading } = useAuth();
  if (isLoading) {
    return <div>Loading...</div>;
  }
  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

const AdminPrivateRoute = ({ children }: { children: React.ReactElement }) => {
  const { isAuthenticated, user, isLoading } = useAuth();
  if (isLoading) {
    return <div>Loading...</div>;
  }
  // Also check for admin role
  return isAuthenticated && user?.role === 'admin' ? children : <Navigate to="/admin/login" replace />;
};

const router = createBrowserRouter([
  {
    path: "/",
    element: <Root />,
    errorElement: <NotFound />,
    children: [
      { path: "login", element: <LoginPage /> },
      { path: "register", element: <RegisterPage /> },
      { path: "admin/login", element: <AdminLoginPage /> },
      { path: "test", element: <TestPage /> },
      {
        // 公开页面，使用 MainLayout，不需要登录
        element: <MainLayout />,
        children: [
          { index: true, element: <Index /> },
          { path: "video/:id", element: <VideoDetail /> },
          { path: "users/:userId", element: <UserDetailPage /> },
          { path: "users/:userId/followers", element: <FollowListPage /> },
          { path: "users/:userId/following", element: <FollowListPage /> },
        ]
      },
      {
        // 私有页面，同样使用 MainLayout，但需要登录
        element: (
          <PrivateRoute>
            <MainLayout />
          </PrivateRoute>
        ),
        children: [
          { path: "my-favorites", element: <FavoritesPage /> },
          { path: "profile", element: <ProfilePage /> },
          { path: "my-orders", element: <MyOrdersPage /> },
          { path: "my-earnings", element: <MyEarningsPage /> },
          { path: "my-works", element: <MyWorksPage /> },
          { path: "upload", element: <UserUploadPage /> },
        ]
      },
      {
        path: "admin",
        element: (
          <AdminPrivateRoute>
            <AdminApp />
          </AdminPrivateRoute>
        ),
        // 😬 重构: 明确定义所有管理员路由
        children: [
          { index: true, element: <Navigate to="dashboard" replace /> },
          { path: "dashboard", element: <AdminDashboard /> },
          { path: "users", element: <AdminUsers /> },
          { path: "videos", element: <AdminVideos /> },
          { path: "videos/review", element: <AdminVideoReview /> },
          { path: "video-upload", element: <AdminUpload /> },
          { path: "categories", element: <AdminCategories /> },
          { path: "comments", element: <AdminComments /> },
          { path: "payments", element: <AdminPayments /> },
          { path: "members", element: <AdminMembers /> },
          { path: "payment-settings", element: <AdminPaymentSettings /> },
          { path: "settings", element: <AdminSettings /> },
          { path: "withdrawals", element: <AdminWithdrawalsPage /> },
          { path: "creem-plans", element: <AdminCreemPlansPage /> },
        ]
      },
      // Keep legacy route if needed, though it's better to integrate it.
      {
        path: "original",
        element: (
          <PrivateRoute>
            <UserApp />
          </PrivateRoute>
        ),
      },
    ],
  },
]);

function Root() {
  return (
    <AuthProvider>
      <MembershipProvider>
        <FollowProvider>
          <Outlet />
        </FollowProvider>
      </MembershipProvider>
    </AuthProvider>
  )
}

function App() {
  useEffect(() => {
    const fetchConfigAndSetTitle = async () => {
      try {
        // 改为调用新的公共API端点
        const response = await api.get('/settings/public');
        const siteName = response.data?.data?.['site.site_name'];
        if (siteName && typeof siteName === 'string') {
          document.title = siteName;
        }
      } catch (error) {
        console.error('Failed to fetch public system config:', error);
      }
    };

    fetchConfigAndSetTitle();
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <RouterProvider 
              router={router}
              future={{
                v7_startTransition: true,
              }}
            />
        </TooltipProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
