const rateLimit = require('express-rate-limit');
const { cache, CACHE_KEYS } = require('../utils/cache');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');

// 基础限流配置
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 最大请求数
    message: {
      success: false,
      message: '请求过于频繁，请稍后再试',
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true, // 返回标准的 `RateLimit` 头部
    legacyHeaders: false, // 禁用 `X-RateLimit-*` 头部
    
    // 自定义键生成器
    keyGenerator: (req) => {
      return req.ip;
    },
    
    // 自定义跳过逻辑
    skip: (req) => {
      // 跳过健康检查
      return req.path === '/health';
    },
    
    // 自定义处理器
    handler: (req, res) => {
      logger.warn('请求限流触发:', {
        ip: req.ip,
        url: req.url,
        method: req.method,
        userAgent: req.get('User-Agent')
      });
      
      throw new AppError(
        options.message || '请求过于频繁，请稍后再试',
        429,
        'RATE_LIMIT_EXCEEDED'
      );
    }
  };
  
  return rateLimit({ ...defaultOptions, ...options });
};

// 基于Redis的高级限流器
class AdvancedRateLimiter {
  constructor() {
    this.cache = cache;
  }
  
  // 生成限流键
  generateKey(prefix, identifier) {
    return `${CACHE_KEYS.RATE_LIMIT}:${prefix}:${identifier}`;
  }
  
  // 检查限流
  async checkRateLimit(key, limit, windowMs) {
    try {
      const current = await this.cache.incr(key, Math.ceil(windowMs / 1000));
      
      if (current === 1) {
        // 第一次请求，设置过期时间
        await this.cache.expire(key, Math.ceil(windowMs / 1000));
      }
      
      return {
        current,
        limit,
        remaining: Math.max(0, limit - current),
        resetTime: Date.now() + windowMs,
        exceeded: current > limit
      };
    } catch (error) {
      logger.error('限流检查失败:', error);
      // Redis失败时允许请求通过
      return {
        current: 0,
        limit,
        remaining: limit,
        resetTime: Date.now() + windowMs,
        exceeded: false
      };
    }
  }
  
  // 创建限流中间件
  createMiddleware(options = {}) {
    const {
      keyPrefix = 'general',
      limit = 100,
      windowMs = 15 * 60 * 1000,
      keyGenerator = (req) => req.ip,
      skipSuccessfulRequests = false,
      skipFailedRequests = false,
      message = '请求过于频繁，请稍后再试'
    } = options;
    
    return async (req, res, next) => {
      try {
        const identifier = keyGenerator(req);
        const key = this.generateKey(keyPrefix, identifier);
        
        const result = await this.checkRateLimit(key, limit, windowMs);
        
        // 设置响应头
        res.set({
          'X-RateLimit-Limit': limit,
          'X-RateLimit-Remaining': result.remaining,
          'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
        });
        
        if (result.exceeded) {
          logger.warn('高级限流触发:', {
            key,
            current: result.current,
            limit,
            ip: req.ip,
            url: req.url
          });
          
          throw new AppError(message, 429, 'RATE_LIMIT_EXCEEDED');
        }
        
        // 在响应结束后处理计数
        if (skipSuccessfulRequests || skipFailedRequests) {
          const originalEnd = res.end;
          res.end = function(...args) {
            const shouldSkip = 
              (skipSuccessfulRequests && res.statusCode < 400) ||
              (skipFailedRequests && res.statusCode >= 400);
            
            if (shouldSkip) {
              // 减少计数
              cache.decr(key).catch(err => {
                logger.error('减少限流计数失败:', err);
              });
            }
            
            originalEnd.apply(this, args);
          };
        }
        
        next();
      } catch (error) {
        next(error);
      }
    };
  }
}

const advancedLimiter = new AdvancedRateLimiter();

// 预定义的限流器
const rateLimiters = {
  // 通用API限流
  general: createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100,
    message: '请求过于频繁，请稍后再试'
  }),
  
  // 严格限流（登录、注册等敏感操作）
  strict: createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 5,
    message: '操作过于频繁，请稍后再试'
  }),
  
  // 文件上传限流
  upload: createRateLimiter({
    windowMs: 60 * 60 * 1000, // 1小时
    max: 10,
    message: '上传过于频繁，请稍后再试'
  }),
  
  // 搜索限流
  search: advancedLimiter.createMiddleware({
    keyPrefix: 'search',
    limit: 30,
    windowMs: 60 * 1000, // 1分钟
    message: '搜索过于频繁，请稍后再试'
  }),
  
  // 评论限流
  comment: advancedLimiter.createMiddleware({
    keyPrefix: 'comment',
    limit: 10,
    windowMs: 60 * 1000, // 1分钟
    keyGenerator: (req) => req.user ? `user:${req.user.id}` : req.ip,
    message: '评论过于频繁，请稍后再试'
  }),
  
  // 点赞限流
  like: advancedLimiter.createMiddleware({
    keyPrefix: 'like',
    limit: 50,
    windowMs: 60 * 1000, // 1分钟
    keyGenerator: (req) => req.user ? `user:${req.user.id}` : req.ip,
    message: '点赞过于频繁，请稍后再试'
  }),
  
  // 视频观看限流（防刷播放量）
  videoView: advancedLimiter.createMiddleware({
    keyPrefix: 'video_view',
    limit: 100,
    windowMs: 60 * 60 * 1000, // 1小时
    keyGenerator: (req) => {
      const videoId = req.params.id || req.body.videoId;
      const identifier = req.user ? `user:${req.user.id}` : req.ip;
      return `${identifier}:video:${videoId}`;
    },
    message: '观看过于频繁，请稍后再试'
  }),
  
  // 密码重置限流
  passwordReset: advancedLimiter.createMiddleware({
    keyPrefix: 'password_reset',
    limit: 3,
    windowMs: 60 * 60 * 1000, // 1小时
    keyGenerator: (req) => req.body.email || req.ip,
    message: '密码重置请求过于频繁，请稍后再试'
  }),
  
  // 邮件发送限流
  emailSend: advancedLimiter.createMiddleware({
    keyPrefix: 'email_send',
    limit: 5,
    windowMs: 60 * 60 * 1000, // 1小时
    keyGenerator: (req) => req.body.email || req.ip,
    message: '邮件发送过于频繁，请稍后再试'
  }),

  // 支付限流
  payment: advancedLimiter.createMiddleware({
    keyPrefix: 'payment',
    limit: 10,
    windowMs: 5 * 60 * 1000, // 5分钟
    keyGenerator: (req) => req.user ? `user:${req.user.id}` : req.ip,
    message: '支付请求过于频繁，请稍后再试'
  })
};

// 一个什么都不做的中间件，用于禁用限流
const disabledRateLimiter = (req, res, next) => next();

// 基于用户角色的动态限流
const createRoleBasedLimiter = (limits) => {
  return async (req, res, next) => {
    const userRole = req.user?.role || 'guest';
    const limitConfig = limits[userRole] || limits.default || limits.guest;
    
    if (!limitConfig) {
      return next();
    }
    
    const limiter = advancedLimiter.createMiddleware({
      ...limitConfig,
      keyGenerator: (req) => {
        const identifier = req.user ? `user:${req.user.id}` : req.ip;
        return `${userRole}:${identifier}`;
      }
    });
    
    return limiter(req, res, next);
  };
};

// IP白名单限流跳过
const createWhitelistLimiter = (whitelist = []) => {
  return (req, res, next) => {
    const clientIP = req.ip;
    
    // 检查IP是否在白名单中
    const isWhitelisted = whitelist.some(ip => {
      if (ip.includes('/')) {
        // CIDR格式支持
        // 这里简化处理，实际应用中可以使用ip-range-check库
        return clientIP.startsWith(ip.split('/')[0]);
      }
      return clientIP === ip;
    });
    
    if (isWhitelisted) {
      logger.debug(`IP白名单跳过限流: ${clientIP}`);
      return next();
    }
    
    // 应用默认限流
    return rateLimiters.general(req, res, next);
  };
};

// 获取限流状态
const getRateLimitStatus = async (req, res) => {
  try {
    const ip = req.ip;
    const userId = req.user?.id;
    
    const status = {};
    
    // 检查各种限流状态
    const checks = [
      { key: 'general', prefix: 'general', identifier: ip },
      { key: 'search', prefix: 'search', identifier: ip },
      { key: 'comment', prefix: 'comment', identifier: userId ? `user:${userId}` : ip },
      { key: 'like', prefix: 'like', identifier: userId ? `user:${userId}` : ip }
    ];
    
    for (const check of checks) {
      const key = advancedLimiter.generateKey(check.prefix, check.identifier);
      const current = await cache.get(key) || 0;
      const ttl = await cache.ttl(key);
      
      status[check.key] = {
        current: parseInt(current),
        resetIn: ttl > 0 ? ttl : 0
      };
    }
    
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('获取限流状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取限流状态失败'
    });
  }
};

module.exports = {
  createRateLimiter,
  AdvancedRateLimiter,
  advancedLimiter,
  rateLimiters,
  createRoleBasedLimiter,
  createWhitelistLimiter,
  getRateLimitStatus,
  disabledRateLimiter,
};
