const commentService = require('./services');
const logger = require('../../utils/logger');
const settingService = require('../../services/settingService');

// 获取视频的评论列表
async function getComments(req, res, next) {
  try {
    const { videoId } = req.params;
    const comments = await commentService.getCommentsByVideoId(videoId);
    res.json({
      success: true,
      data: comments,
    });
  } catch (error) {
    next(error);
  }
}

// 发表新评论
async function postComment(req, res, next) {
  try {
    // 首先，检查系统设置是否允许评论
    const enableComments = await settingService.getSetting('user.enableComments');
    if (!enableComments) {
      return res.status(403).json({ success: false, message: '评论功能当前已关闭。' });
    }

    const { videoId, content, parentId } = req.body;
    const userId = req.user.id; // 从 authMiddleware 获取用户ID

    if (!videoId || !content) {
      return res.status(400).json({ success: false, message: '缺少必需的参数' });
    }

    const newComment = await commentService.createComment({
      videoId,
      userId,
      content,
      parentId,
    });
    
    res.status(201).json({
      message: '评论创建成功',
      data: newComment,
    });
  } catch (error) {
    logger.error('创建评论失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
}

module.exports = {
  getComments,
  postComment,
}; 