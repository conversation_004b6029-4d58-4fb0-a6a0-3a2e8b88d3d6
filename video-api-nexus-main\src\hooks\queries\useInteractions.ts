import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toggleLike, toggleFavorite, batchCheckInteractions, getUserFavorites } from '@/lib/api';
import { videoKeys } from './useVideos';
import { toast } from '@/hooks/use-toast';

// 查询键工厂
export const interactionKeys = {
  all: ['interactions'] as const,
  favorites: (userId: string | number) => [...interactionKeys.all, 'favorites', userId] as const,
  batchCheck: (videoIds: (string | number)[]) => [...interactionKeys.all, 'batch', videoIds] as const,
};

// 批量检查交互状态
export const useBatchCheckInteractions = (videoIds: (string | number)[]) => {
  return useQuery({
    queryKey: interactionKeys.batchCheck(videoIds),
    queryFn: () => batchCheckInteractions(videoIds),
    select: (data) => {
      const favorites = data?.data?.data?.favorites || {};
      const likes = data?.data?.data?.likes || {};
      
      // 构建交互状态对象
      const interactionStates: Record<string, { isLiked: boolean; isFavorited: boolean }> = {};
      videoIds.forEach(id => {
        const idStr = String(id);
        interactionStates[idStr] = {
          isLiked: likes[`video_${idStr}`] || false,
          isFavorited: favorites[idStr] || false,
        };
      });
      
      return interactionStates;
    },
    enabled: videoIds.length > 0,
    staleTime: 2 * 60 * 1000, // 2分钟
    cacheTime: 5 * 60 * 1000, // 5分钟
  });
};

// 获取用户收藏列表
export const useUserFavorites = (userId: string | number) => {
  return useQuery({
    queryKey: interactionKeys.favorites(userId),
    queryFn: () => getUserFavorites(userId),
    select: (data) => {
      const favoritesData = data?.data?.data?.data || [];
      
      // 格式化收藏数据，为VideoCard组件添加必要的字段
      return favoritesData.map((item: any) => ({
        id: item.video_id || item.id,
        title: item.title,
        description: item.description,
        thumbnail_url: item.thumbnail,
        duration: item.duration || 0,
        uploader: { nickname: item.nickname || item.username || '未知用户' },
        category: { name: item.category_name || '未分类' },
        view_count: item.view_count || 0,
        like_count: item.like_count || 0,
        comment_count: item.comment_count || 0,
        created_at: item.video_created_at || item.created_at,
        favorited_at: item.created_at,
        is_favorited: true, // 收藏页面的视频默认已收藏
      }));
    },
    enabled: !!userId,
    staleTime: 1 * 60 * 1000, // 1分钟
    cacheTime: 5 * 60 * 1000, // 5分钟
  });
};

// 点赞操作
export const useLikeMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ videoId, targetType = 'video' }: { videoId: string | number; targetType?: string }) => 
      toggleLike(videoId, targetType),
    onMutate: async ({ videoId }) => {
      // 乐观更新：立即更新UI
      const videoDetailKey = videoKeys.detail(videoId);
      
      // 取消正在进行的查询
      await queryClient.cancelQueries({ queryKey: videoDetailKey });
      
      // 获取当前数据
      const previousVideoDetail = queryClient.getQueryData(videoDetailKey);
      
      // 乐观更新视频详情
      if (previousVideoDetail) {
        queryClient.setQueryData(videoDetailKey, (old: any) => {
          if (!old) return old;
          const currentLiked = old.is_liked || false;
          const currentCount = old.like_count || 0;
          
          return {
            ...old,
            is_liked: !currentLiked,
            like_count: currentLiked ? Math.max(0, currentCount - 1) : currentCount + 1,
          };
        });
      }
      
      return { previousVideoDetail };
    },
    onError: (err, { videoId }, context) => {
      // 发生错误时回滚
      if (context?.previousVideoDetail) {
        queryClient.setQueryData(videoKeys.detail(videoId), context.previousVideoDetail);
      }
      
      toast({
        title: "操作失败",
        description: "点赞操作失败，请稍后重试",
        variant: "destructive",
      });
    },
    onSuccess: (data, { videoId }) => {
      // 成功后更新相关查询
      const newLikedState = data?.data?.data?.liked;
      
      // 更新视频详情
      queryClient.setQueryData(videoKeys.detail(videoId), (old: any) => {
        if (!old) return old;
        const currentCount = old.like_count || 0;
        
        return {
          ...old,
          is_liked: newLikedState,
          like_count: newLikedState ? currentCount : Math.max(0, currentCount - 1),
        };
      });
      
      // 使交互状态查询失效
      queryClient.invalidateQueries({ queryKey: interactionKeys.all });
    },
  });
};

// 收藏操作
export const useFavoriteMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (videoId: string | number) => toggleFavorite(videoId),
    onMutate: async (videoId) => {
      // 乐观更新
      const videoDetailKey = videoKeys.detail(videoId);
      
      await queryClient.cancelQueries({ queryKey: videoDetailKey });
      
      const previousVideoDetail = queryClient.getQueryData(videoDetailKey);
      
      // 乐观更新视频详情
      if (previousVideoDetail) {
        queryClient.setQueryData(videoDetailKey, (old: any) => {
          if (!old) return old;
          return {
            ...old,
            is_favorited: !old.is_favorited,
          };
        });
      }
      
      return { previousVideoDetail };
    },
    onError: (err, videoId, context) => {
      // 回滚
      if (context?.previousVideoDetail) {
        queryClient.setQueryData(videoKeys.detail(videoId), context.previousVideoDetail);
      }
      
      toast({
        title: "操作失败",
        description: "收藏操作失败，请稍后重试",
        variant: "destructive",
      });
    },
    onSuccess: (data, videoId) => {
      const newFavoritedState = !queryClient.getQueryData(videoKeys.detail(videoId))?.is_favorited;
      
      // 更新视频详情
      queryClient.setQueryData(videoKeys.detail(videoId), (old: any) => {
        if (!old) return old;
        return {
          ...old,
          is_favorited: newFavoritedState,
        };
      });
      
      // 使相关查询失效
      queryClient.invalidateQueries({ queryKey: interactionKeys.all });
      
      toast({
        title: newFavoritedState ? "已收藏" : "已取消收藏",
        description: newFavoritedState ? "视频已添加到收藏夹" : "视频已从收藏夹移除",
        variant: "default",
      });
    },
  });
};
