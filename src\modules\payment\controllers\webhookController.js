const crypto = require('crypto');
const { AppError, asyncHandler } = require('../../../middleware/errorHandler');
const CreemPlan = require('../../../database/models/CreemPlan');
const Membership = require('../../../database/models/Membership');
const User = require('../../../database/models/User');
const Order = require('../../../database/models/Order');  // 添加 Order 模型
const logger = require('../../../utils/logger');

/**
 * 验证 Creem webhook 签名
 */
const verifyWebhookSignature = (payload, signature, secret) => {
  if (!signature || !secret) {
    return false;
  }

  try {
    // Creem 使用 HMAC-SHA256 算法
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('hex');

    // 安全比较签名
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    logger.error('Webhook 签名验证失败:', error);
    return false;
  }
};

/**
 * 处理 Creem webhook 事件
 */
const handleCreemWebhook = asyncHandler(async (req, res) => {
  try {
    const signature = req.headers['creem-signature'];
    const webhookSecret = process.env.CREEM_WEBHOOK_SECRET;
    
    logger.info('收到 Creem webhook 请求:', {
      signature: signature ? '存在' : '不存在',
      headers: Object.keys(req.headers),
      bodyType: typeof req.body
    });

    if (!webhookSecret) {
      logger.warn('Webhook secret 未配置');
      // 在测试环境中，如果没有配置 secret，允许继续处理
      if (process.env.NODE_ENV !== 'production') {
        logger.info('测试环境：跳过签名验证');
      } else {
        throw new AppError('Webhook secret 未配置', 500);
      }
    }

    // 获取请求体
    let payload;
    if (typeof req.body === 'string') {
      payload = req.body;
    } else {
      payload = JSON.stringify(req.body);
    }
    
    // 在生产环境中验证签名
    if (webhookSecret && signature) {
      if (!verifyWebhookSignature(payload, signature, webhookSecret)) {
        logger.warn('Webhook 签名验证失败', {
          signature,
          payload: payload.substring(0, 100) + '...'
        });
        throw new AppError('签名验证失败', 401);
      }
    }

    // 解析事件数据
    let event;
    if (typeof req.body === 'string') {
      event = JSON.parse(req.body);
    } else {
      event = req.body;
    }

    logger.info('处理 Creem webhook 事件:', {
      type: event.type,
      id: event.id
    });

    // 处理不同类型的事件
    switch (event.eventType || event.type) {
      case 'checkout.completed':
        await handleCheckoutCompleted(event.object);
        break;
      
      case 'subscription.created':
        await handleSubscriptionCreated(event.object);
        break;
        
      case 'subscription.cancelled':
        await handleSubscriptionCancelled(event.object);
        break;
        
      default:
        logger.info(`未处理的 webhook 事件类型: ${event.eventType || event.type}`);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    logger.error('处理 Creem webhook 失败:', error);
    throw error;
  }
});

/**
 * 处理支付完成事件
 */
const handleCheckoutCompleted = async (checkoutData) => {
  try {
    // Creem 的实际数据结构
    const {
      order,
      product,
      customer,
      metadata = {}
    } = checkoutData;

    logger.info('处理支付完成事件:', {
      orderId: order?.id,
      productId: product?.id,
      customerEmail: customer?.email,
      amount: order?.amount,
      currency: order?.currency,
      metadata
    });

    // 查找对应的 Creem 计划
    const creemPlan = await CreemPlan.findOne({ 
      creem_product_id: product.id 
    });
    
    if (!creemPlan) {
      throw new Error(`未找到产品 ID ${product.id} 对应的会员计划`);
    }

    // 通过邮箱查找用户
    let user = await User.findOne({ email: customer.email });
    
    // 如果通过邮箱找不到用户，尝试通过 metadata 中的 user_id 查找
    if (!user && metadata.user_id) {
      user = await User.findById(metadata.user_id);
      if (!user) {
        throw new Error(`未找到用户 ID ${metadata.user_id} 对应的用户`);
      }
    }
    
    if (!user) {
      throw new Error(`未找到邮箱 ${customer.email} 对应的用户，且无用户ID`);
    }

    // 检查是否已经处理过这个支付
    const existingMembership = await Membership.findOne({
      user_id: user.id,
      payment_reference: order.id
    });

    if (existingMembership) {
      logger.info(`支付 ${order.id} 已经处理过，跳过`);
      return;
    }

    // 计算会员到期时间
    const startDate = new Date();
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + creemPlan.duration_days);

    // 创建或更新会员记录
    await createOrUpdateMembership(user.id, creemPlan, {
      checkoutId: order.id,
      amount: order.amount / 100, // Creem 以分为单位
      currency: order.currency,
      startDate,
      endDate
    });

    logger.info(`用户 ${user.email} 会员激活成功`, {
      plan: creemPlan.name,
      duration: creemPlan.duration_days,
      endDate: endDate.toISOString(),
      orderId: order.id
    });

  } catch (error) {
    logger.error('处理支付完成事件失败:', error);
    throw error;
  }
};

/**
 * 处理订阅创建事件
 */
const handleSubscriptionCreated = async (subscriptionData) => {
  logger.info('处理订阅创建事件:', subscriptionData);
  // 订阅类型的处理逻辑
  // 可以根据需要实现
};

/**
 * 处理订阅取消事件
 */
const handleSubscriptionCancelled = async (subscriptionData) => {
  try {
    const { customer_email: customerEmail } = subscriptionData;
    
    // 查找用户并停用会员
    const user = await User.findOne({ email: customerEmail });
    if (user) {
      await Membership.update(
        { status: 'cancelled' },
        { where: { user_id: user.id, status: 'active' } }
      );
      
      logger.info(`用户 ${customerEmail} 的会员订阅已取消`);
    }
  } catch (error) {
    logger.error('处理订阅取消事件失败:', error);
    throw error;
  }
};

/**
 * 创建或更新会员记录（同时创建订单记录）
 */
const createOrUpdateMembership = async (userId, creemPlan, paymentData) => {
  const { checkoutId, amount, currency, startDate, endDate } = paymentData;

  // 检查用户是否已有活跃会员
  const existingMembership = await Membership.findOne({
    user_id: userId,
    status: 'active'
  });

  if (existingMembership) {
    // 如果已有会员，延长到期时间
    const currentEndDate = new Date(existingMembership.end_date);
    const newEndDate = new Date(currentEndDate);
    newEndDate.setDate(newEndDate.getDate() + creemPlan.duration_days);

    await Membership.update({
      end_date: newEndDate,
      updated_at: new Date()
    }, {
      where: { id: existingMembership.id }
    });

    logger.info(`延长用户 ${userId} 的会员期限至 ${newEndDate.toISOString()}`);
  } else {
    // 创建新的会员记录
    await Membership.create({
      user_id: userId,
      plan_id: null, // Creem 计划不使用本地 plan_id
      plan_name: creemPlan.name,
      start_date: startDate,
      end_date: endDate,
      status: 'active',
      payment_method: 'creem',
      payment_reference: checkoutId,
      amount: parseFloat(amount),
      currency: currency,
      auto_renew: false, // Creem 订阅由其系统管理
      created_at: new Date(),
      updated_at: new Date()
    });

    logger.info(`为用户 ${userId} 创建新的会员记录，到期时间: ${endDate.toISOString()}`);
  }

  // 同时在 orders 表中创建订单记录，方便用户查看订单历史
  try {
    await Order.create({
      user_id: userId,
      order_no: checkoutId,
      type: 'membership',
      target_id: creemPlan.id,
      amount: parseFloat(amount),
      final_amount: parseFloat(amount),
      currency: currency,
      payment_method: 'creem',
      payment_status: 'paid',
      payment_time: new Date(),
      transaction_id: checkoutId,
      description: `Creem 会员计划: ${creemPlan.name}`,
      created_at: new Date(),
      updated_at: new Date()
    });

    logger.info(`为用户 ${userId} 创建 Creem 订单记录: ${checkoutId}`);
  } catch (orderError) {
    logger.error('创建订单记录失败:', orderError);
    // 不抛出错误，避免影响会员激活
  }
};

module.exports = {
  handleCreemWebhook
};