import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { FollowButton } from '@/components/common/FollowButton';
import { followApi } from '@/services/followApi';
import { getUserVideos } from '@/services/api';
import { Loader2, Users, UserPlus, Play, Eye, Heart } from 'lucide-react';
import { toast } from 'sonner';

interface Creator {
  id: number;
  username: string;
  nickname?: string;
  avatar?: string;
  bio?: string;
  follower_count?: number;
  following_count?: number;
  video_count?: number;
}

interface CreatorVideo {
  id: string;
  title: string;
  thumbnail_url?: string;
  view_count?: number;
  like_count?: number;
  duration?: number;
  created_at?: string;
}

interface CreatorProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  creatorId: number;
  creatorInfo?: {
    username?: string;
    nickname?: string;
    avatar?: string;
  };
}

export const CreatorProfileDialog: React.FC<CreatorProfileDialogProps> = ({
  open,
  onOpenChange,
  creatorId,
  creatorInfo
}) => {
  const navigate = useNavigate();
  const [creator, setCreator] = useState<Creator | null>(null);
  const [videos, setVideos] = useState<CreatorVideo[]>([]);
  const [loading, setLoading] = useState(false);
  const [videosLoading, setVideosLoading] = useState(false);

  // 获取创作者详细信息
  useEffect(() => {
    if (open && creatorId) {
      fetchCreatorInfo();
      fetchCreatorVideos();
    }
  }, [open, creatorId]);

  const fetchCreatorInfo = async () => {
    setLoading(true);
    try {
      const response = await followApi.getFollowStats(creatorId);
      const stats = response?.data?.data || response?.data || {};
      
      setCreator({
        id: creatorId,
        username: creatorInfo?.username || '未知用户',
        nickname: creatorInfo?.nickname,
        avatar: creatorInfo?.avatar,
        follower_count: stats.follower_count || 0,
        following_count: stats.following_count || 0,
        video_count: stats.video_count || 0,
      });
    } catch (error) {
      console.error('获取创作者信息失败:', error);
      toast.error('获取创作者信息失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchCreatorVideos = async () => {
    setVideosLoading(true);
    try {
      const response = await getUserVideos(creatorId, { page: 1, limit: 4 });
      console.log('Creator videos response:', response); // 调试日志
      
      // 安全地提取视频数据
      const videosData = response?.data?.data || response?.data?.videos || response?.data || [];
      
      // 确保数据是数组格式
      const processedVideos = Array.isArray(videosData) ? videosData.map(video => ({
        id: video.id,
        title: video.title,
        thumbnail_url: video.thumbnail_url || video.thumbnail,
        view_count: video.view_count || 0,
        like_count: video.like_count || 0,
        duration: video.duration || 0,
        created_at: video.created_at,
      })) : [];
      
      setVideos(processedVideos);
    } catch (error) {
      console.error('获取创作者视频失败:', error);
      setVideos([]);
    } finally {
      setVideosLoading(false);
    }
  };

  const handleViewFollowers = () => {
    onOpenChange(false);
    navigate(`/users/${creatorId}/followers?tab=followers`);
  };

  const handleViewFollowing = () => {
    onOpenChange(false);
    navigate(`/users/${creatorId}/following?tab=following`);
  };

  const handleVideoClick = (videoId: string) => {
    onOpenChange(false);
    navigate(`/video/${videoId}`);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>创作者信息</DialogTitle>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        ) : creator ? (
          <div className="space-y-6">
            {/* 创作者基本信息 */}
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={creator.avatar} alt={creator.nickname || creator.username} />
                <AvatarFallback>
                  {(creator.nickname || creator.username)?.[0]?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <h2 className="text-xl font-bold">
                  {creator.nickname || creator.username}
                </h2>
                {creator.nickname && (
                  <p className="text-sm text-muted-foreground">@{creator.username}</p>
                )}
                {creator.bio && (
                  <p className="text-sm text-muted-foreground mt-1">{creator.bio}</p>
                )}
              </div>

              <div className="flex flex-col space-y-2">
                <FollowButton userId={creator.id} size="sm" />
              </div>
            </div>

            {/* 统计数据 */}
            <div className="grid grid-cols-3 gap-4">
              <Card 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={handleViewFollowers}
              >
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <Users className="h-4 w-4 text-purple-500" />
                    <span className="text-lg font-bold">{creator.follower_count || 0}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">粉丝</p>
                </CardContent>
              </Card>

              <Card 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={handleViewFollowing}
              >
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <UserPlus className="h-4 w-4 text-green-500" />
                    <span className="text-lg font-bold">{creator.following_count || 0}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">关注</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <Play className="h-4 w-4 text-blue-500" />
                    <span className="text-lg font-bold">{creator.video_count || 0}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">作品</p>
                </CardContent>
              </Card>
            </div>

            {/* 创作者视频列表 */}
            <div>
              <h3 className="text-lg font-semibold mb-3">最新作品</h3>
              {videosLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">加载作品中...</span>
                </div>
              ) : videos.length > 0 ? (
                <div className="grid grid-cols-2 gap-3">
                  {videos.slice(0, 4).map((video) => (
                    <Card 
                      key={video.id}
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => handleVideoClick(video.id)}
                    >
                      <CardContent className="p-3">
                        <div className="aspect-video bg-gray-200 rounded mb-2 relative overflow-hidden">
                          {video.thumbnail_url ? (
                            <img 
                              src={video.thumbnail_url} 
                              alt={video.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Play className="h-8 w-8 text-gray-400" />
                            </div>
                          )}
                          
                          {/* 视频时长 */}
                          {video.duration && (
                            <Badge className="absolute bottom-1 right-1 text-xs">
                              {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}
                            </Badge>
                          )}
                        </div>
                        
                        <h4 className="text-sm font-medium line-clamp-2 mb-1">
                          {video.title}
                        </h4>
                        
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Eye className="h-3 w-3" />
                            <span>{video.view_count || 0}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Heart className="h-3 w-3" />
                            <span>{video.like_count || 0}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Play className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>暂无作品</p>
                </div>
              )}
              
              {videos.length > 4 && (
                <div className="mt-4 text-center">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      onOpenChange(false);
                      navigate(`/users/${creatorId}`);
                    }}
                  >
                    查看更多作品
                  </Button>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <p>获取创作者信息失败</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};