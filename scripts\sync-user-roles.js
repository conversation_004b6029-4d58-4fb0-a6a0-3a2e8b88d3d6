#!/usr/bin/env node

/**
 * 同步用户角色脚本
 * 根据用户的会员状态同步用户角色
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const Membership = require('../src/database/models/Membership');
const User = require('../src/database/models/User');
const logger = require('../src/utils/logger');

async function syncAllUserRoles() {
  try {
    logger.info('开始同步所有用户角色...');
    
    // 获取所有有会员记录的用户
    const usersWithMemberships = await Membership.query(`
      SELECT DISTINCT user_id 
      FROM memberships 
      WHERE status IN ('active', 'expired', 'cancelled')
    `);
    
    logger.info(`找到 ${usersWithMemberships.length} 个有会员记录的用户`);
    
    let syncCount = 0;
    let errorCount = 0;
    
    for (const userRecord of usersWithMemberships) {
      try {
        const userId = userRecord.user_id;
        const oldRole = await User.findById(userId).then(user => user?.role);
        const newRole = await Membership.syncUserRole(userId);
        
        if (oldRole !== newRole) {
          logger.info(`用户 ${userId} 角色已从 ${oldRole} 同步为 ${newRole}`);
          syncCount++;
        }
      } catch (error) {
        logger.error(`同步用户 ${userRecord.user_id} 角色失败:`, error);
        errorCount++;
      }
    }
    
    logger.info(`角色同步完成: ${syncCount} 个用户角色已更新, ${errorCount} 个错误`);
    
    // 显示统计信息
    const roleStats = await User.query(`
      SELECT role, COUNT(*) as count 
      FROM users 
      WHERE status != 'deleted'
      GROUP BY role
    `);
    
    logger.info('当前用户角色分布:');
    roleStats.forEach(stat => {
      logger.info(`  ${stat.role}: ${stat.count} 人`);
    });
    
  } catch (error) {
    logger.error('同步用户角色失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  syncAllUserRoles()
    .then(() => {
      logger.info('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { syncAllUserRoles };
