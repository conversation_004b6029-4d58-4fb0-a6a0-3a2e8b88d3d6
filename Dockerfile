# 使用官方Node.js运行时作为基础镜像
FROM node:18-alpine

# 安装FFmpeg和其他必要工具
RUN apk add --no-cache \
    ffmpeg \
    imagemagick \
    curl \
    bash

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p uploads/videos uploads/thumbnails uploads/avatars logs reports

# 设置权限
RUN chown -R node:node /app

# 切换到非root用户
USER node

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 启动应用
CMD ["node", "app.js"]
