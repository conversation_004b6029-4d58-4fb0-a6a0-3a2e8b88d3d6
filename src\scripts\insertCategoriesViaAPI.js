// 加载环境变量
require('dotenv').config();

const axios = require('axios');
const logger = require('../utils/logger');

// API配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'Admin123456!';

// 默认分类数据
const defaultCategories = [
  { name: '电影', slug: 'movies', description: '电影视频分类' },
  { name: '电视剧', slug: 'tv-series', description: '电视剧视频分类' },
  { name: '纪录片', slug: 'documentaries', description: '纪录片视频分类' },
  { name: '动漫', slug: 'anime', description: '动漫视频分类' },
  { name: '综艺', slug: 'variety', description: '综艺节目分类' },
  { name: '教育', slug: 'education', description: '教育视频分类' },
  { name: '音乐', slug: 'music', description: '音乐视频分类' },
  { name: '游戏', slug: 'gaming', description: '游戏视频分类' },
  { name: '科技', slug: 'technology', description: '科技视频分类' },
  { name: '生活', slug: 'lifestyle', description: '生活视频分类' }
];

// 管理员登录获取token
async function adminLogin() {
  try {
    logger.info('正在登录管理员账户...');
    
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });
    
    if (response.data.success && response.data.data.token) {
      logger.info('管理员登录成功');
      return response.data.data.token;
    } else {
      throw new Error('登录响应格式异常');
    }
  } catch (error) {
    logger.error('管理员登录失败:', error.response?.data?.message || error.message);
    throw error;
  }
}

// 获取现有分类
async function getExistingCategories(token) {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/video/categories/list`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    return response.data.data.categories || [];
  } catch (error) {
    logger.warn('获取现有分类失败:', error.response?.data?.message || error.message);
    return [];
  }
}

// 创建分类
async function createCategory(token, categoryData) {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/video/categories`, categoryData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data.data.category;
  } catch (error) {
    logger.error(`创建分类 "${categoryData.name}" 失败:`, error.response?.data?.message || error.message);
    throw error;
  }
}

// 主函数
async function insertCategoriesViaAPI() {
  try {
    logger.info('开始通过API插入默认分类...');
    
    // 登录获取token
    const token = await adminLogin();
    
    // 获取现有分类
    const existingCategories = await getExistingCategories(token);
    logger.info(`当前已有 ${existingCategories.length} 个分类`);
    
    if (existingCategories.length > 0) {
      logger.info('现有分类:');
      existingCategories.forEach(cat => {
        logger.info(`  - ${cat.name} (${cat.slug})`);
      });
    }
    
    // 插入新分类
    let insertedCount = 0;
    let skippedCount = 0;
    
    for (const category of defaultCategories) {
      try {
        // 检查分类是否已存在
        const exists = existingCategories.some(
          existing => existing.slug === category.slug || existing.name === category.name
        );
        
        if (exists) {
          logger.info(`分类 "${category.name}" 已存在，跳过`);
          skippedCount++;
          continue;
        }
        
        // 创建新分类
        const newCategory = await createCategory(token, {
          name: category.name,
          slug: category.slug,
          description: category.description,
          sortOrder: insertedCount
        });
        
        logger.info(`✅ 成功创建分类: ${newCategory.name} (ID: ${newCategory.id})`);
        insertedCount++;
        
      } catch (error) {
        logger.error(`处理分类 "${category.name}" 时出错:`, error.message);
      }
    }
    
    // 最终检查
    const finalCategories = await getExistingCategories(token);
    
    logger.info('\n=== 分类插入完成 ===');
    logger.info(`新插入: ${insertedCount} 个分类`);
    logger.info(`跳过: ${skippedCount} 个分类`);
    logger.info(`总计: ${finalCategories.length} 个分类`);
    
    logger.info('\n当前所有分类:');
    finalCategories.forEach((cat, index) => {
      logger.info(`  ${index + 1}. ${cat.name} (${cat.slug})`);
    });
    
    return true;
    
  } catch (error) {
    logger.error('通过API插入分类失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  insertCategoriesViaAPI()
    .then(() => {
      logger.info('分类插入脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('分类插入脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { insertCategoriesViaAPI };
