export interface SystemSettings {
  // 键是字符串，值可以是字符串、数字、布尔值或null
  [key: string]: string | number | boolean | null;

  // 为了更精确的类型提示，可以定义已知的键
  'site.site_name'?: string;
  'site.site_description'?: string;
  'site.site_contactEmail'?: string;
  
  'media.media_maxUploadSize'?: number;
  'media.media_allowedVideoFormats'?: string;
  
  'user.enableRegistration'?: boolean;
  'user.enableComments'?: boolean;
  'user.requireEmailVerification'?: boolean;

  'email.smtpHost'?: string;
  'email.smtpPort'?: number;
  'email.smtpUser'?: string;
  'email.smtpPassword'?: string;
  'email.fromEmail'?: string;
  'email.fromName'?: string;

  'security.enableTwoFactor'?: boolean;
  'security.sessionTimeout'?: number;
  'security.maxLoginAttempts'?: number;
  'security.passwordMinLength'?: number;
  'security.requireStrongPassword'?: boolean;
} 