const cron = require('node-cron');
const axios = require('axios');
const logger = require('../utils/logger');
const PaymentGateway = require('../database/models/PaymentGateway');
const Order = require('../database/models/Order');
const { Op } = require('sequelize'); // 直接从sequelize导入Op
const bs58 = require('bs58');

const NILE_API_URL = 'https://nile.trongrid.io'; // 使用Nile测试网地址
const USDT_TRC20_CONTRACT_ADDRESS = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'; // 更正为从日志中发现的正确地址
const TRX_DECIMALS = 6;

class TronTransactionScannerService {
  constructor() {
    this.task = null;
    this.isRunning = false;
    this.startTime = new Date(); // 记录服务启动时间
    this.walletAddressForCheck = null; // 初始化钱包地址
  }

  /**
   * 启动交易扫描服务
   */
  start() {
    // 每15秒运行一次
    this.task = cron.schedule('*/15 * * * * *', async () => {
      if (this.isRunning) {
        logger.info('[TronScanner] 上一轮扫描仍在进行中，跳过本次执行。');
        return;
      }

      logger.info('[TronScanner] 开始扫描TRON网络交易...');
      this.isRunning = true;
      try {
        await this.scanTransactions();
      } catch (error) {
        logger.error('[TronScanner] 扫描过程中发生错误:', error);
      } finally {
        this.isRunning = false;
        logger.info('[TronScanner] 本轮扫描结束。');
      }
    });

    logger.info('[TronScanner] TRON交易扫描服务已启动。');
  }

  /**
   * 停止交易扫描服务
   */
  stop() {
    if (this.task) {
      this.task.stop();
      logger.info('[TronScanner] TRON交易扫描服务已停止。');
    }
  }

  /**
   * 执行扫描的核心逻辑
   */
  async scanTransactions() {
    // 1. 前置检查：是否存在待处理的TRON订单
    const pendingOrder = await Order.findOne({ where: { payment_method: 'tron_usdt', payment_status: 'pending' } });
    if (!pendingOrder) {
      // logger.info('[TronScanner] 无待处理的TRON订单，跳过本轮扫描。');
      return;
    }

    const paymentGatewayModel = new PaymentGateway();
    const gateway = await paymentGatewayModel.findOne({ key: 'tron_usdt', enabled: true });

    if (!gateway) {
      logger.info('[TronScanner] TRON支付通道未启用或未配置，跳过扫描。');
      return;
    }

    const config = typeof gateway.config === 'string' ? JSON.parse(gateway.config) : gateway.config;
    const { walletAddress, apiKey } = config;

    if (!walletAddress || !apiKey) {
      logger.warn('[TronScanner] TRON支付通道缺少钱包地址或API Key，无法扫描。');
      return;
    }
    
    // 增加诊断日志，显示当前用于扫描的地址和API Key
    logger.info(`[TronScanner] 使用以下配置进行扫描: Wallet Address: ${walletAddress}, API Key: ${apiKey ? '******' + apiKey.slice(-4) : 'Not Set'}`);

    this.walletAddressForCheck = walletAddress; // 存储钱包地址以供后续使用

    try {
      // 并行获取USDT和TRX交易
      const [usdtResponse, trxResponse] = await Promise.all([
        // 获取USDT(TRC20)交易
        axios.get(`${NILE_API_URL}/v1/accounts/${walletAddress}/transactions/trc20`, {
          params: {
            limit: 50,
            only_to: true,
            order_by: 'block_timestamp,desc',
            min_block_timestamp: this.startTime.getTime()
          },
          headers: { 'TRON-PRO-API-KEY': apiKey }
        }),
        // 获取TRX原生交易
        axios.get(`${NILE_API_URL}/v1/accounts/${walletAddress}/transactions`, {
          params: {
            limit: 50,
            only_to: true,
            order_by: 'block_timestamp,desc',
            min_block_timestamp: this.startTime.getTime()
          },
          headers: { 'TRON-PRO-API-KEY': apiKey }
        })
      ]);

      if (!usdtResponse.data || !usdtResponse.data.success) {
        logger.error('[TronScanner] 从trongrid.io获取USDT数据失败:', usdtResponse.data);
      }
      if (!trxResponse.data || !trxResponse.data.success) {
        logger.error('[TronScanner] 从trongrid.io获取TRX数据失败:', trxResponse.data);
      }

      const usdtTransactions = usdtResponse.data.data || [];
      const trxTransactions = trxResponse.data.data || [];
      
      logger.info(`[TronScanner] 获取到 ${usdtTransactions.length} 条USDT交易, ${trxTransactions.length} 条TRX交易。`);

      // 增加诊断日志，打印获取到的每一条USDT交易的原始内容
      if (usdtTransactions.length > 0) {
        logger.info('[TronScanner] 打印获取到的USDT交易详情:');
        usdtTransactions.forEach((tx, index) => {
          logger.info(`  - 交易 #${index + 1}: ${JSON.stringify(tx, null, 2)}`);
        });
      }

      // 筛选出我们关心的USDT交易
      const filteredUsdtTransactions = usdtTransactions.filter(
        tx => tx.token_info && tx.token_info.address.toLowerCase() === USDT_TRC20_CONTRACT_ADDRESS.toLowerCase()
      );

      logger.info(`[TronScanner] 筛选后剩下 ${filteredUsdtTransactions.length} 条相关USDT交易。`);

      // 合并并处理所有交易
      const allTransactions = [
        ...filteredUsdtTransactions.map(tx => ({ ...tx, currency: 'USDT' })),
        ...trxTransactions.map(tx => ({ ...tx, currency: 'TRX' }))
      ];

      for (const tx of allTransactions) {
        await this.processTransaction(tx);
      }

    } catch (error) {
      if (error.response) {
        // 请求已发出，但服务器响应的状态码不在 2xx 范围内
        logger.error(`[TronScanner] 请求trongrid.io API出错: ${error.response.status} ${error.response.statusText}`, {
          url: error.config.url,
          params: error.config.params,
          response: error.response.data,
          error: error.stack
        });
      } else if (error.request) {
        // 请求已发出，但没有收到响应
        logger.error('[TronScanner] 请求trongrid.io API无响应:', error);
      } else {
        // 在设置请求时触发了一个错误
        logger.error('[TronScanner] 请求trongrid.io API时发生未知错误:', error);
      }
    }
  }

  /**
   * 处理单笔交易
   * @param {object} tx 
   */
  async processTransaction(tx) {
    // 1. 检查交易ID是否已经处理过
    const existingOrderForTx = await Order.findOne({ where: { transaction_id: tx.transaction_id } });
    if (existingOrderForTx) {
      return;
    }

    let amount;
    let currency;
    let amountInteger; // 新增：用于存储整数金额

    if (tx.currency === 'USDT' && tx.type === 'Transfer' && tx.token_info.symbol === 'USDT') {
      amountInteger = parseInt(tx.value, 10);
      amount = amountInteger / Math.pow(10, tx.token_info.decimals || 6);
      currency = 'USDT';
    } else if (tx.currency === 'TRX' && tx.raw_data.contract[0].type === 'TransferContract') {
      const transfer = tx.raw_data.contract[0].parameter.value;
      // A Tron address, when decoded from Base58, is 25 bytes:
      // 1-byte prefix (0x41) + 20-byte address + 4-byte checksum.
      // The API returns the 21-byte hex string (prefix + address).
      // We need to slice the checksum off our decoded address before comparing.
      const decoded = bs58.decode(this.walletAddressForCheck);
      const addressBytes = decoded.slice(0, -4); // Remove the last 4 bytes (checksum)
      const walletAddressHex = Buffer.from(addressBytes).toString('hex').toLowerCase();
      
      if (transfer.to_address.toLowerCase() === walletAddressHex) {
        amountInteger = parseInt(transfer.amount, 10);
        amount = amountInteger / Math.pow(10, TRX_DECIMALS);
        currency = 'TRX';
      }
    }

    if (!amount) {
      return; // 不是有效的目标交易，静默忽略
    }
    
    // 日志中同时显示浮点和整数金额
    logger.info(`[TronScanner] 发现新交易: ${tx.transaction_id}, From: ${tx.from || tx.raw_data.contract[0].parameter.value.owner_address}, To: ${tx.to || tx.raw_data.contract[0].parameter.value.to_address}, Amount: ${amount} ${currency} (Int: ${amountInteger})`);

    // 3. 根据唯一金额查找待支付订单
    try {
      // 核心逻辑：用收到的整数金额去匹配订单的整数金额字段
      const pendingOrder = await this.findMatchingOrder(amountInteger, currency);

      if (!pendingOrder) {
        logger.warn(`[TronScanner] 收到金额为 ${amount} ${currency} (Int: ${amountInteger}) 的新交易，但未找到匹配的待支付订单，忽略。`);
        return;
      }
      
      logger.info(`[TronScanner] 交易 ${tx.transaction_id} (金额: ${amount} ${currency}) 成功匹配到订单 ${pendingOrder.order_no}，准备更新状态...`);

      // 4. 找到订单，更新状态并处理业务逻辑
      const paymentController = require('../modules/payment/controllers/paymentController'); 

      const paymentResult = {
        transactionId: tx.transaction_id,
        payTime: new Date(tx.block_timestamp),
        amount: pendingOrder.final_amount, // 使用订单的原始金额
        status: 'paid'
      };
      
      await paymentController.handlePaymentSuccess(pendingOrder, paymentResult);

      logger.info(`[TronScanner] 订单 ${pendingOrder.order_no} 已成功更新为 'paid'.`);

    } catch (error) {
      logger.error(`[TronScanner] 处理交易 ${tx.transaction_id} 时发生错误:`, error);
    }
  }

  /**
   * 根据收到的金额和币种，查找匹配的待支付订单
   * @param {number} receivedAmount - 收到的金额
   * @param {string} currency - 币种 ('USDT' 或 'TRX')
   * @returns {Promise<Order|null>}
   */
  async findMatchingOrder(receivedAmountInteger, currency) {
    // 根据币种确定查询哪个字段
    const amountField = currency === 'TRX' ? 'final_amount_trx_integer' : 'final_amount_usdt_integer';

    // 直接进行精确的整数匹配
    const order = await Order.findOne({
      where: {
        payment_method: 'tron_usdt',
        payment_status: 'pending',
        [amountField]: receivedAmountInteger
      },
      order: [['created_at', 'ASC']] // 如果有多个匹配，优先处理最早的订单
    });

    return order;
  }
}

module.exports = new TronTransactionScannerService(); 