import React from 'react';
import { Clock, Pause, Play, Square } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';

interface TimerDisplayProps {
  isActive: boolean;
  isPaused: boolean;
  timeRemaining: number;
  progress: number;
  formatTime: (seconds: number) => string;
  onPause: () => void;
  onResume: () => void;
  onStop: () => void;
  className?: string;
}

const TimerDisplay: React.FC<TimerDisplayProps> = ({
  isActive,
  isPaused,
  timeRemaining,
  progress,
  formatTime,
  onPause,
  onResume,
  onStop,
  className = '',
}) => {
  if (!isActive) return null;

  return (
    <Card className={`p-3 bg-background/95 backdrop-blur-sm border-primary/20 ${className}`}>
      <div className="flex items-center justify-between">
        {/* 定时器信息 */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Clock className={`h-4 w-4 ${isPaused ? 'text-orange-500' : 'text-primary'}`} />
            <span className="text-sm font-medium text-muted-foreground">
              {isPaused ? '已暂停' : '定时中'}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <span className={`text-lg font-mono font-bold ${
              timeRemaining <= 60 ? 'text-red-500' : 
              timeRemaining <= 300 ? 'text-orange-500' : 
              'text-primary'
            }`}>
              {formatTime(timeRemaining)}
            </span>
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="flex items-center gap-1">
          {!isPaused ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={onPause}
              className="h-8 w-8 p-0"
              title="暂停定时器"
            >
              <Pause className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              onClick={onResume}
              className="h-8 w-8 p-0"
              title="继续定时器"
            >
              <Play className="h-4 w-4" />
            </Button>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onStop}
            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
            title="停止定时器"
          >
            <Square className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 进度条 */}
      <div className="mt-3">
        <Progress 
          value={progress} 
          className="h-2"
        />
        <div className="flex justify-between text-xs text-muted-foreground mt-1">
          <span>剩余时间</span>
          <span>{Math.round(progress)}%</span>
        </div>
      </div>

      {/* 状态提示 */}
      {timeRemaining <= 60 && (
        <div className="mt-2 text-xs text-red-500 text-center animate-pulse">
          ⚠️ 定时器即将结束，视频将自动暂停
        </div>
      )}
    </Card>
  );
};

export default TimerDisplay;
