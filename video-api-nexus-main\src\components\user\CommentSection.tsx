import React, { useState } from 'react';
import { ThumbsUp } from 'lucide-react';

const commentsData = [
  {
    id: 1,
    username: "学习者小李",
    content: "讲解得很清楚，感谢分享！",
    created_at: "2小时前",
    like_count: 5
  },
  {
    id: 2,
    username: "前端新手",
    content: "请问有配套的练习题吗？",
    created_at: "1小时前",
    like_count: 2
  }
];

interface CommentSectionProps {
  initialCommentCount: number;
}

const CommentSection: React.FC<CommentSectionProps> = ({ initialCommentCount }) => {
  const [comments, setComments] = useState(commentsData);
  const [newComment, setNewComment] = useState('');

  return (
    <div className="space-y-4 mt-8">
      <div className="flex items-center space-x-4">
        <h3 className="text-lg font-semibold">评论</h3>
        <span className="text-muted-foreground">({initialCommentCount})</span>
      </div>

      {/* Add Comment */}
      <div className="flex space-x-3">
        <div className="w-10 h-10 bg-secondary rounded-full flex-shrink-0 flex items-center justify-center">
          <span className="text-sm">我</span>
        </div>
        <div className="flex-1">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="添加评论..."
            className="w-full px-3 py-2 border border-input rounded-md text-sm resize-none"
            rows={3}
          />
          <div className="flex justify-end space-x-2 mt-2">
            <button 
              onClick={() => setNewComment('')}
              className="px-3 py-1 text-sm bg-secondary hover:bg-secondary/80 rounded-md"
            >
              取消
            </button>
            <button 
              disabled={!newComment.trim()}
              className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              评论
            </button>
          </div>
        </div>
      </div>

      {/* Comments List */}
      <div className="space-y-4">
        {comments.map((comment) => (
          <div key={comment.id} className="flex space-x-3">
            <div className="w-10 h-10 bg-secondary rounded-full flex-shrink-0 flex items-center justify-center">
              <span className="text-sm">{comment.username[0]}</span>
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <span className="font-medium text-sm">{comment.username}</span>
                <span className="text-xs text-muted-foreground">{comment.created_at}</span>
              </div>
              <p className="text-sm mb-2">{comment.content}</p>
              <div className="flex items-center space-x-4">
                <button className="flex items-center space-x-1 text-xs text-muted-foreground hover:text-foreground">
                  <ThumbsUp size={12} />
                  <span>{comment.like_count}</span>
                </button>
                <button className="text-xs text-muted-foreground hover:text-foreground">
                  回复
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CommentSection; 