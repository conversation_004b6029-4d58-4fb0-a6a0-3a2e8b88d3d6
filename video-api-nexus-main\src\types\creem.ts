export type TaxCategory = 'saas' | 'ebooks';

export interface CreemPlan {
  id: number;
  name: string;
  description: string | null;
  price: string;
  currency: string;
  creem_product_id: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  tax_category?: TaxCategory;
  duration_days?: number | null; // 会员天数，仅用于单次付款类型
}

export interface CreemPlanFormData {
  id?: number;
  name: string;
  description: string;
  price: string;
  currency: string;
  returnUrl?: string; // 返回URL
  paymentType: 'one_time' | 'subscription'; // 付款详情
  billing_period?: 'every-month' | 'every-year'; // 订阅间隔
  tax_category?: TaxCategory;
  duration_days?: string; // 会员天数，仅用于单次付款类型
  // 注意：'价格含税' 是一个UI状态，通常在提交前处理，不一定需要是表单数据的一部分
} 