const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const User = require('../../../database/models/User');
const Video = require('../../../database/models/Video');
const Comment = require('../../../database/models/Comment');
const Like = require('../../../database/models/Like');
const Favorite = require('../../../database/models/Favorite');

class AnalyticsService {
  // 获取用户增长统计
  async getUserGrowthStats(days = 30) {
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'user_growth', days);
    let stats = await cache.get(cacheKey);

    if (!stats) {
      const sql = `
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as new_users,
          SUM(COUNT(*)) OVER (ORDER BY DATE(created_at)) as total_users
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `;

      const dailyStats = await User.query(sql, [days]);

      // 计算增长率
      const growthStats = dailyStats.map((day, index) => {
        const prevDay = dailyStats[index - 1];
        const growthRate = prevDay ? 
          ((day.new_users - prevDay.new_users) / prevDay.new_users * 100) : 0;

        return {
          ...day,
          growthRate: Math.round(growthRate * 100) / 100
        };
      });

      stats = {
        daily: growthStats,
        summary: {
          totalNewUsers: dailyStats.reduce((sum, day) => sum + day.new_users, 0),
          avgDailyGrowth: dailyStats.length > 0 ? 
            Math.round((dailyStats.reduce((sum, day) => sum + day.new_users, 0) / dailyStats.length) * 100) / 100 : 0,
          currentTotal: dailyStats.length > 0 ? dailyStats[dailyStats.length - 1].total_users : 0
        }
      };

      await cache.set(cacheKey, stats, 3600); // 1小时缓存
    }

    return stats;
  }

  // 获取视频统计
  async getVideoStats(days = 30) {
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'video_stats', days);
    let stats = await cache.get(cacheKey);

    if (!stats) {
      // 每日视频上传统计
      const uploadStats = await Video.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as uploads,
          COUNT(CASE WHEN status = 'published' THEN 1 END) as published
        FROM videos 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
          AND status != 'deleted'
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `, [days]);

      // 观看量统计
      const viewStats = await Video.query(`
        SELECT 
          DATE(created_at) as date,
          SUM(view_count) as total_views,
          AVG(view_count) as avg_views
        FROM videos 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
          AND status = 'published'
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `, [days]);

      // 分类统计
      const categoryStats = await Video.query(`
        SELECT 
          c.name as category_name,
          COUNT(v.id) as video_count,
          SUM(v.view_count) as total_views
        FROM videos v
        LEFT JOIN categories c ON v.category_id = c.id
        WHERE v.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
          AND v.status = 'published'
        GROUP BY c.id, c.name
        ORDER BY video_count DESC
        LIMIT 10
      `, [days]);

      stats = {
        uploads: uploadStats,
        views: viewStats,
        categories: categoryStats,
        summary: {
          totalUploads: uploadStats.reduce((sum, day) => sum + day.uploads, 0),
          totalPublished: uploadStats.reduce((sum, day) => sum + day.published, 0),
          totalViews: viewStats.reduce((sum, day) => sum + day.total_views, 0)
        }
      };

      await cache.set(cacheKey, stats, 3600); // 1小时缓存
    }

    return stats;
  }

  // 获取互动统计
  async getInteractionStats(days = 30) {
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'interaction_stats', days);
    let stats = await cache.get(cacheKey);

    if (!stats) {
      // 评论统计
      const commentStats = await Comment.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as comments
        FROM comments 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
          AND status = 'active'
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `, [days]);

      // 点赞统计
      const likeStats = await Like.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as likes,
          COUNT(CASE WHEN target_type = 'video' THEN 1 END) as video_likes,
          COUNT(CASE WHEN target_type = 'comment' THEN 1 END) as comment_likes
        FROM likes 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `, [days]);

      // 收藏统计
      const favoriteStats = await Favorite.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as favorites
        FROM favorites 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
      `, [days]);

      stats = {
        comments: commentStats,
        likes: likeStats,
        favorites: favoriteStats,
        summary: {
          totalComments: commentStats.reduce((sum, day) => sum + day.comments, 0),
          totalLikes: likeStats.reduce((sum, day) => sum + day.likes, 0),
          totalFavorites: favoriteStats.reduce((sum, day) => sum + day.favorites, 0)
        }
      };

      await cache.set(cacheKey, stats, 3600); // 1小时缓存
    }

    return stats;
  }

  // 获取热门内容
  async getPopularContent(type = 'video', limit = 10, days = 7) {
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'popular_content', type, limit, days);
    let content = await cache.get(cacheKey);

    if (!content) {
      switch (type) {
        case 'video':
          content = await Video.query(`
            SELECT 
              v.id,
              v.title,
              v.view_count,
              v.like_count,
              v.comment_count,
              u.username as author,
              c.name as category
            FROM videos v
            LEFT JOIN users u ON v.user_id = u.id
            LEFT JOIN categories c ON v.category_id = c.id
            WHERE v.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
              AND v.status = 'published'
            ORDER BY (v.view_count * 0.5 + v.like_count * 0.3 + v.comment_count * 0.2) DESC
            LIMIT ?
          `, [days, limit]);
          break;

        case 'user':
          content = await User.query(`
            SELECT 
              u.id,
              u.username,
              u.nickname,
              COUNT(v.id) as video_count,
              SUM(v.view_count) as total_views,
              SUM(v.like_count) as total_likes
            FROM users u
            LEFT JOIN videos v ON u.id = v.user_id AND v.status = 'published'
            WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
              AND u.status = 'active'
            GROUP BY u.id
            ORDER BY total_views DESC
            LIMIT ?
          `, [days, limit]);
          break;

        default:
          content = [];
      }

      await cache.set(cacheKey, content, 1800); // 30分钟缓存
    }

    return content;
  }

  // 获取用户活跃度统计
  async getUserActivityStats(days = 30) {
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'user_activity', days);
    let stats = await cache.get(cacheKey);

    if (!stats) {
      // 日活跃用户
      const dailyActive = await User.query(`
        SELECT 
          DATE(last_login_at) as date,
          COUNT(DISTINCT id) as active_users
        FROM users 
        WHERE last_login_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(last_login_at)
        ORDER BY date ASC
      `, [days]);

      // 用户留存率（简化计算）
      const retentionStats = await User.query(`
        SELECT 
          DATE(created_at) as registration_date,
          COUNT(*) as registered_users,
          COUNT(CASE WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as retained_users
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY registration_date ASC
      `, [days]);

      stats = {
        dailyActive,
        retention: retentionStats.map(day => ({
          ...day,
          retentionRate: day.registered_users > 0 ? 
            Math.round((day.retained_users / day.registered_users * 100) * 100) / 100 : 0
        })),
        summary: {
          avgDailyActive: dailyActive.length > 0 ? 
            Math.round(dailyActive.reduce((sum, day) => sum + day.active_users, 0) / dailyActive.length) : 0,
          avgRetentionRate: retentionStats.length > 0 ? 
            Math.round(retentionStats.reduce((sum, day) => {
              const rate = day.registered_users > 0 ? (day.retained_users / day.registered_users * 100) : 0;
              return sum + rate;
            }, 0) / retentionStats.length * 100) / 100 : 0
        }
      };

      await cache.set(cacheKey, stats, 3600); // 1小时缓存
    }

    return stats;
  }

  // 获取收入统计（如果有付费功能）
  async getRevenueStats(days = 30) {
    // 这里应该从支付记录表获取数据
    // 暂时返回模拟数据
    return {
      daily: [],
      summary: {
        totalRevenue: 0,
        avgDailyRevenue: 0,
        paidUsers: 0
      }
    };
  }

  // 生成综合报告
  async generateReport(days = 30) {
    try {
      const [
        userGrowth,
        videoStats,
        interactionStats,
        userActivity,
        popularVideos,
        popularUsers,
        revenueStats
      ] = await Promise.all([
        this.getUserGrowthStats(days),
        this.getVideoStats(days),
        this.getInteractionStats(days),
        this.getUserActivityStats(days),
        this.getPopularContent('video', 10, days),
        this.getPopularContent('user', 10, days),
        this.getRevenueStats(days)
      ]);

      return {
        period: {
          days,
          startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
          endDate: new Date()
        },
        userGrowth,
        videoStats,
        interactionStats,
        userActivity,
        popularContent: {
          videos: popularVideos,
          users: popularUsers
        },
        revenueStats,
        generatedAt: new Date()
      };

    } catch (error) {
      logger.error('生成分析报告失败:', error);
      throw error;
    }
  }

  // 清理过期缓存
  async clearExpiredCache() {
    const patterns = [
      `${CACHE_KEYS.ADMIN}:user_growth:*`,
      `${CACHE_KEYS.ADMIN}:video_stats:*`,
      `${CACHE_KEYS.ADMIN}:interaction_stats:*`,
      `${CACHE_KEYS.ADMIN}:popular_content:*`,
      `${CACHE_KEYS.ADMIN}:user_activity:*`
    ];

    let totalCleared = 0;
    for (const pattern of patterns) {
      const cleared = await cache.delPattern(pattern);
      totalCleared += cleared;
    }

    logger.info(`清理分析缓存: ${totalCleared} 个缓存项`);
    return totalCleared;
  }
}

module.exports = new AnalyticsService();
