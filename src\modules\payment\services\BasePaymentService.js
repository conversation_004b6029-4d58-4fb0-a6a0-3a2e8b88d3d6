const crypto = require('crypto');
const { logger } = require('../../../utils/advancedLogger');
const Order = require('../../../database/models/Order');
const Membership = require('../../../database/models/Membership');
const MembershipPlan = require('../../../database/models/MembershipPlan');
const connectionManager = require('../../../database/ConnectionManager');
const BalanceService = require('../../balance/services/balanceService');

/**
 * 支付服务基类
 * 定义所有支付服务的通用接口和方法
 */
class BasePaymentService {
  constructor(config) {
    this.config = config;
    this.name = 'base';
  }

  /**
   * 创建支付订单
   * @param {Object} orderData 订单数据
   * @returns {Promise<Object>} 支付结果
   */
  async createPayment(orderData) {
    throw new Error('createPayment method must be implemented');
  }

  /**
   * 查询支付状态
   * @param {string} orderNo 订单号
   * @returns {Promise<Object>} 支付状态
   */
  async queryPayment(orderNo) {
    throw new Error('queryPayment method must be implemented');
  }

  /**
   * 处理支付回调
   * @param {Object} callbackData 回调数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleCallback(callbackData) {
    throw new Error('handleCallback method must be implemented');
  }

  /**
   * 申请退款
   * @param {Object} refundData 退款数据
   * @returns {Promise<Object>} 退款结果
   */
  async refund(refundData) {
    throw new Error('refund method must be implemented');
  }

  /**
   * 验证回调签名
   * @param {Object} data 回调数据
   * @param {string} signature 签名
   * @returns {boolean} 验证结果
   */
  verifySignature(data, signature) {
    throw new Error('verifySignature method must be implemented');
  }

  /**
   * 生成签名
   * @param {Object} data 数据
   * @returns {string} 签名
   */
  generateSignature(data) {
    throw new Error('generateSignature method must be implemented');
  }

  /**
   * 格式化金额（转换为分）
   * @param {number} amount 金额（元）
   * @returns {number} 金额（分）
   */
  formatAmount(amount) {
    return Math.round(amount * 100);
  }

  /**
   * 解析金额（转换为元）
   * @param {number} amount 金额（分）
   * @returns {number} 金额（元）
   */
  parseAmount(amount) {
    return amount / 100;
  }

  /**
   * 生成随机字符串
   * @param {number} length 长度
   * @returns {string} 随机字符串
   */
  generateNonce(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成时间戳
   * @returns {number} 时间戳
   */
  generateTimestamp() {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * 对象转URL参数
   * @param {Object} obj 对象
   * @returns {string} URL参数字符串
   */
  objectToUrlParams(obj) {
    return Object.keys(obj)
      .filter(key => obj[key] !== undefined && obj[key] !== null && obj[key] !== '')
      .sort()
      .map(key => `${key}=${encodeURIComponent(obj[key])}`)
      .join('&');
  }

  /**
   * MD5加密
   * @param {string} str 字符串
   * @returns {string} MD5值
   */
  md5(str) {
    return crypto.createHash('md5').update(str, 'utf8').digest('hex');
  }

  /**
   * SHA256加密
   * @param {string} str 字符串
   * @returns {string} SHA256值
   */
  sha256(str) {
    return crypto.createHash('sha256').update(str, 'utf8').digest('hex');
  }

  /**
   * HMAC-SHA256加密
   * @param {string} str 字符串
   * @param {string} key 密钥
   * @returns {string} HMAC-SHA256值
   */
  hmacSha256(str, key) {
    return crypto.createHmac('sha256', key).update(str, 'utf8').digest('hex');
  }

  /**
   * 记录支付日志
   * @param {string} level 日志级别
   * @param {string} message 消息
   * @param {Object} data 数据
   */
  log(level, message, data = {}) {
    logger[level](`[${this.name.toUpperCase()}] ${message}`, {
      service: this.name,
      ...data
    });
  }

  /**
   * 验证必需参数
   * @param {Object} data 数据
   * @param {Array} requiredFields 必需字段
   * @throws {Error} 参数错误
   */
  validateRequiredFields(data, requiredFields) {
    const missingFields = requiredFields.filter(field => 
      data[field] === undefined || data[field] === null || data[field] === ''
    );
    
    if (missingFields.length > 0) {
      throw new Error(`缺少必需参数: ${missingFields.join(', ')}`);
    }
  }

  /**
   * 处理HTTP请求
   * @param {string} url URL
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} 响应结果
   */
  async httpRequest(url, options = {}) {
    const axios = require('axios');
    
    try {
      this.log('debug', '发送HTTP请求', { url, options });
      
      const response = await axios({
        url,
        timeout: 30000,
        ...options
      });
      
      this.log('debug', 'HTTP请求成功', { 
        status: response.status,
        data: response.data 
      });
      
      return response.data;
    } catch (error) {
      this.log('error', 'HTTP请求失败', { 
        url,
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  /**
   * 获取支付方式名称
   * @returns {string} 支付方式名称
   */
  getPaymentMethodName() {
    return this.name;
  }

  /**
   * 获取支付配置
   * @returns {Object} 支付配置
   */
  getConfig() {
    return this.config;
  }

  /**
   * 检查配置是否有效
   * @returns {boolean} 配置是否有效
   */
  isConfigValid() {
    return this.config && Object.keys(this.config).length > 0;
  }

  async handleSuccess(orderNo, paymentData) {
    const connection = await connectionManager.getMySQLConnection();
    await connection.beginTransaction();

    try {
      const orderModel = new Order();
      const order = await orderModel.getOrderByNo(orderNo, connection);

      if (!order || order.payment_status === 'paid') {
        await connection.commit();
        return { success: true, message: '订单已处理' };
      }

      logger.info(`[BasePaymentService] 开始处理支付成功订单: ${orderNo}`, {
        orderId: order.id,
        userId: order.user_id,
        orderType: order.type,
        targetId: order.target_id
      });

      // 更新订单状态
      await orderModel.updateOrderStatus(orderNo, 'paid', paymentData, connection);

      // 根据订单类型执行不同操作
      if (order.type === 'membership') {
        logger.info(`[BasePaymentService] 处理会员订单，用户ID: ${order.user_id}, 计划ID: ${order.target_id}`);

        // 使用统一的 renewMembership 方法，而不是 grantMembership
        const Membership = require('../../database/models/Membership');
        await Membership.renewMembership(order.user_id, order.target_id, {
          method: order.payment_method,
          transactionId: order.transaction_id || paymentData.transactionId
        }, connection);

        logger.info(`[BasePaymentService] 会员开通成功，用户ID: ${order.user_id}`);

      } else if (order.type === 'recharge') {
        // 新增：处理充值订单
        logger.info(`[BasePaymentService] 处理充值订单，用户ID: ${order.user_id}, 金额: ${order.final_amount}`);
        await BalanceService.addUserBalance(order.user_id, order.final_amount, connection);

      } else if (order.type === 'video') {
        // 未来可以处理购买单个视频的逻辑
        logger.info(`[BasePaymentService] 处理视频订单，用户ID: ${order.user_id}, 视频ID: ${order.target_id}`);
      }

      await connection.commit();
      logger.info(`[BasePaymentService] 订单处理成功: ${orderNo}`);
      return { success: true, message: '支付成功' };

    } catch (error) {
      await connection.rollback();
      logger.error(`[BasePaymentService] 订单处理失败: ${orderNo}`, {
        error: error.message,
        stack: error.stack,
        orderType: order?.type,
        userId: order?.user_id
      });
      throw error;
    } finally {
      connection.release();
    }
  }
}

module.exports = BasePaymentService;
