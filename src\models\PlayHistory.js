const { mysql: db } = require('../config/database');

class PlayHistory {
  // 记录播放历史
  static async record(userId, videoId, watchDuration = 0, videoDuration = 0, completed = false) {
    const query = `
      INSERT INTO play_history (user_id, video_id, watch_duration, video_duration, completed)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE 
        watch_duration = GREATEST(watch_duration, VALUES(watch_duration)),
        video_duration = VALUES(video_duration),
        completed = VALUES(completed) OR completed,
        played_at = CURRENT_TIMESTAMP
    `;
    
    try {
      await db.execute(query, [userId, videoId, watchDuration, videoDuration, completed]);
      return true;
    } catch (error) {
      console.error('记录播放历史失败:', error);
      throw error;
    }
  }

  // 获取用户播放历史
  static async findByUserId(userId, limit = 50, offset = 0) {
    const query = `
      SELECT ph.*, v.title, v.thumbnail_url, v.duration, v.media_type
      FROM play_history ph
      LEFT JOIN videos v ON ph.video_id = v.id
      WHERE ph.user_id = ?
      ORDER BY ph.played_at DESC
      LIMIT ? OFFSET ?
    `;
    
    try {
      const [history] = await db.execute(query, [userId, limit, offset]);
      return history;
    } catch (error) {
      console.error('获取播放历史失败:', error);
      throw error;
    }
  }

  // 获取用户最近播放的视频
  static async getRecentlyPlayed(userId, limit = 10) {
    const query = `
      SELECT DISTINCT ph.video_id, v.title, v.thumbnail_url, v.duration, v.media_type, ph.played_at
      FROM play_history ph
      LEFT JOIN videos v ON ph.video_id = v.id
      WHERE ph.user_id = ?
      ORDER BY ph.played_at DESC
      LIMIT ?
    `;
    
    try {
      const [videos] = await db.execute(query, [userId, limit]);
      return videos;
    } catch (error) {
      console.error('获取最近播放失败:', error);
      throw error;
    }
  }

  // 清除播放历史
  static async clearHistory(userId, videoId = null) {
    let query = `DELETE FROM play_history WHERE user_id = ?`;
    const params = [userId];
    
    if (videoId) {
      query += ` AND video_id = ?`;
      params.push(videoId);
    }
    
    try {
      const [result] = await db.execute(query, params);
      return result.affectedRows;
    } catch (error) {
      console.error('清除播放历史失败:', error);
      throw error;
    }
  }

  // 获取播放统计
  static async getPlayStats(userId) {
    const query = `
      SELECT 
        COUNT(*) as total_plays,
        COUNT(DISTINCT video_id) as unique_videos,
        SUM(watch_duration) as total_watch_time,
        COUNT(CASE WHEN completed = TRUE THEN 1 END) as completed_videos,
        AVG(watch_duration) as avg_watch_time
      FROM play_history 
      WHERE user_id = ?
    `;
    
    try {
      const [stats] = await db.execute(query, [userId]);
      return stats[0];
    } catch (error) {
      console.error('获取播放统计失败:', error);
      throw error;
    }
  }
}

module.exports = PlayHistory;
