# API接口文档

本文档详细说明视频网站API框架的所有接口，共计 **104个接口**。

## 📋 基础信息

- **Base URL**: `https://api.yourdomain.com`
- **API版本**: v1
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8
- **总接口数**: 104个
- **模块数**: 7个核心模块

## 🔐 认证说明

### JWT Token
大部分API需要在请求头中包含JWT Token：

```http
Authorization: Bearer <your_jwt_token>
```

### 获取Token
通过登录接口获取Token，Token有效期为1小时，可通过刷新Token接口延长。

## 📝 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": {
    // 错误详情（可选）
  }
}
```

### HTTP状态码
- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求过于频繁
- `500` - 服务器内部错误

## 🔑 认证模块 (/api/auth) - 11个接口

### 用户注册
```http
POST /api/auth/register
```

**请求参数**:
```json
{
  "username": "string (3-20字符)",
  "email": "string (有效邮箱)",
  "password": "string (8-50字符)",
  "nickname": "string (可选，2-50字符)"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "nickname": "测试用户",
      "role": "user",
      "status": "active",
      "created_at": "2025-01-07T12:00:00.000Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 3600
    }
  }
}
```

### 用户登录
```http
POST /api/auth/login
```

**请求参数**:
```json
{
  "email": "string",
  "password": "string"
}
```

### 用户登出
```http
POST /api/auth/logout
```

**请求头**: `Authorization: Bearer <token>`

### 刷新Token
```http
POST /api/auth/refresh-token
```

**请求参数**:
```json
{
  "refreshToken": "string"
}
```

### 获取当前用户信息
```http
GET /api/auth/me
```

**请求头**: `Authorization: Bearer <token>`

### 修改密码
```http
POST /api/auth/change-password
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "oldPassword": "string",
  "newPassword": "string"
}
```

### 忘记密码
```http
POST /api/auth/forgot-password
```

**请求参数**:
```json
{
  "email": "string"
}
```

### 重置密码
```http
POST /api/auth/reset-password
```

**请求参数**:
```json
{
  "token": "string",
  "newPassword": "string"
}
```

### 邮箱验证
```http
POST /api/auth/verify-email
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "token": "string"
}
```

### 重发验证邮件
```http
POST /api/auth/resend-verification
```

**请求参数**:
```json
{
  "email": "string"
}
```

### 认证测试
```http
GET /api/auth/test
```

## 👤 用户模块 (/api/users) - 12个接口

### 获取个人资料
```http
GET /api/users/profile
```

**请求头**: `Authorization: Bearer <token>`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "nickname": "测试用户",
      "avatar": "/uploads/avatars/avatar_1.jpg",
      "bio": "这是我的个人简介",
      "location": "北京",
      "role": "user",
      "status": "active",
      "created_at": "2025-01-07T12:00:00.000Z",
      "stats": {
        "videoCount": 5,
        "favoriteCount": 12,
        "commentCount": 8
      }
    }
  }
}
```

### 更新个人资料
```http
PUT /api/users/profile
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "nickname": "string (可选)",
  "bio": "string (可选)",
  "location": "string (可选)",
  "phone": "string (可选)",
  "gender": "male|female|other (可选)",
  "birthday": "YYYY-MM-DD (可选)"
}
```

### 上传头像
```http
POST /api/users/avatar
```

**请求头**: `Authorization: Bearer <token>`
**请求类型**: `multipart/form-data`
**请求参数**: `avatar` (文件，支持jpg/png，最大5MB)

### 删除头像
```http
DELETE /api/users/avatar
```

**请求头**: `Authorization: Bearer <token>`

### 获取用户列表
```http
GET /api/users/list
```

**查询参数**:
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)
- `keyword` - 搜索关键词 (可选)
- `role` - 角色筛选 (可选)
- `status` - 状态筛选 (可选)

### 获取用户详情
```http
GET /api/users/:id
```

**路径参数**: `id` - 用户ID

### 封禁用户
```http
PUT /api/users/:id/ban
```

**请求头**: `Authorization: Bearer <admin_token>`
**路径参数**: `id` - 用户ID

**请求参数**:
```json
{
  "reason": "string (封禁原因)",
  "duration": "number (封禁天数，0为永久)"
}
```

### 解封用户
```http
PUT /api/users/:id/unban
```

**请求头**: `Authorization: Bearer <admin_token>`
**路径参数**: `id` - 用户ID

### 修改用户角色
```http
PUT /api/users/:id/role
```

**请求头**: `Authorization: Bearer <admin_token>`
**路径参数**: `id` - 用户ID

**请求参数**:
```json
{
  "role": "user|member|vip|admin"
}
```

### 删除账户
```http
DELETE /api/users/account
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "password": "string (确认密码)",
  "reason": "string (可选，删除原因)"
}
```

### 用户统计
```http
GET /api/users/stats
```

**请求头**: `Authorization: Bearer <admin_token>`

### 用户测试
```http
GET /api/users/test
```

**请求头**: `Authorization: Bearer <admin_token>`

## 🎬 媒体模块 (/api/videos) - 25个接口

**注意**: 此模块现已支持视频和音频文件的统一管理。所有接口都兼容音频文件处理。

### 获取媒体列表
```http
GET /api/videos/list
```

**查询参数**:
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20, 最大: 50)
- `categoryId` - 分类ID (可选)
- `mediaType` - 媒体类型 (video|audio, 可选)
- `keyword` - 搜索关键词 (可选)
- `sortBy` - 排序字段 (created_at|view_count|like_count)
- `sortOrder` - 排序方向 (ASC|DESC)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "media_type": "video",
        "title": "示例视频",
        "description": "这是一个示例视频",
        "thumbnail": "/uploads/thumbnails/thumb_1.jpg",
        "duration": 120,
        "view_count": 1000,
        "like_count": 50,
        "comment_count": 10,
        "format": "mp4",
        "created_at": "2025-01-07T12:00:00.000Z",
        "username": "uploader",
        "category_name": "教育"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 上传媒体文件（视频或音频）
```http
POST /api/videos/upload
```

**请求头**: `Authorization: Bearer <admin_token>`
**请求类型**: `multipart/form-data`

**请求参数**:
- `video` 或 `audio` - 媒体文件 (必需)
  - 支持视频格式: mp4, avi, mov, wmv, flv, webm, mkv
  - 支持音频格式: mp3, wav, flac, aac, ogg, m4a, wma
- `title` - 媒体标题 (必需)
- `description` - 媒体描述 (可选)
- `categoryId` - 分类ID (可选)
- `tags` - 标签JSON数组 (可选)
- `visibility` - 可见性 (public|private|member_only|vip_only)

**注意**: 系统会自动检测文件类型并进行相应处理。

### 获取媒体详情
```http
GET /api/videos/:id
```

**路径参数**: `id` - 媒体ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "media": {
      "id": 1,
      "media_type": "video",
      "title": "示例视频",
      "description": "详细描述...",
      "thumbnail": "/uploads/thumbnails/thumb_1.jpg",
      "file_path": "/uploads/videos/video_1.mp4",
      "duration": 120,
      "resolution": "1920x1080",
      "format": "mp4",
      "view_count": 1000,
      "like_count": 50,
      "comment_count": 10,
      "tags": ["教育", "技术"],
      "visibility": "public",
      "status": "published",
      "created_at": "2025-01-07T12:00:00.000Z",
      "username": "uploader",
      "nickname": "上传者",
      "category_name": "教育",
      "is_liked": false,
      "is_favorited": false,
      // 音频文件特有字段（仅当media_type为audio时）
      "bitrate": 320,
      "sample_rate": 44100,
      "channels": 2
    }
  }
}
```

### 更新视频信息
```http
PUT /api/videos/:id
```

**请求头**: `Authorization: Bearer <token>`
**路径参数**: `id` - 视频ID

**请求参数**:
```json
{
  "title": "string (可选)",
  "description": "string (可选)",
  "categoryId": "number (可选)",
  "tags": "array (可选)",
  "visibility": "string (可选)"
}
```

### 删除视频
```http
DELETE /api/videos/:id
```

**请求头**: `Authorization: Bearer <token>`
**路径参数**: `id` - 视频ID

### 搜索视频
```http
GET /api/videos/search
```

**查询参数**:
- `keyword` - 搜索关键词 (必需)
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)
- `categoryId` - 分类ID (可选)
- `sortBy` - 排序方式 (relevance|newest|popular)

### 热门视频
```http
GET /api/videos/popular
```

**查询参数**:
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)
- `timeRange` - 时间范围 (24h|7d|30d)

### 推荐视频
```http
GET /api/videos/recommended
```

**请求头**: `Authorization: Bearer <token>`

### 专用音频上传接口
```http
POST /api/videos/upload-audio
```

**请求头**: `Authorization: Bearer <admin_token>`
**请求类型**: `multipart/form-data`

**请求参数**:
- `audio` - 音频文件 (必需)
- `title` - 音频标题 (必需)
- `description` - 音频描述 (可选)
- `categoryId` - 分类ID (可选)
- `tags` - 标签JSON数组 (可选)
- `visibility` - 可见性 (public|private|member_only|vip_only)

**响应示例**:
```json
{
  "success": true,
  "message": "音频上传成功，正在处理中",
  "data": {
    "media": {
      "id": 2,
      "title": "示例音频",
      "mediaType": "audio",
      "status": "uploading",
      "created_at": "2025-01-07T12:00:00.000Z"
    }
  }
}
```

### 获取音频列表
```http
GET /api/videos/audio-list
```

**查询参数**: 与媒体列表相同，但自动筛选音频类型

### 获取纯视频列表
```http
GET /api/videos/video-list
```

**查询参数**: 与媒体列表相同，但自动筛选视频类型

### 获取用户媒体
```http
GET /api/videos/user/:id
```

**路径参数**: `id` - 用户ID

### 获取处理状态
```http
GET /api/videos/:id/processing-status
```

**路径参数**: `id` - 视频ID

### 重新处理视频
```http
POST /api/videos/:id/reprocess
```

**请求头**: `Authorization: Bearer <admin_token>`
**路径参数**: `id` - 视频ID

### 视频统计
```http
GET /api/videos/:id/stats
```

**路径参数**: `id` - 视频ID

### 视频测试
```http
GET /api/videos/test
```

**请求头**: `Authorization: Bearer <admin_token>`

## 📁 视频分类管理 (/api/videos/categories)

### 获取分类树
```http
GET /api/videos/categories/tree
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "教育",
        "description": "教育类视频",
        "parent_id": null,
        "children": [
          {
            "id": 2,
            "name": "编程",
            "parent_id": 1,
            "children": []
          }
        ]
      }
    ]
  }
}
```

### 获取分类列表
```http
GET /api/videos/categories/list
```

**查询参数**:
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)
- `parentId` - 父分类ID (可选)

### 热门分类
```http
GET /api/videos/categories/popular
```

### 搜索分类
```http
GET /api/videos/categories/search
```

**查询参数**:
- `keyword` - 搜索关键词 (必需)

### 获取分类详情
```http
GET /api/videos/categories/:id
```

**路径参数**: `id` - 分类ID

### 获取分类下的视频
```http
GET /api/videos/categories/:id/videos
```

**路径参数**: `id` - 分类ID

**查询参数**:
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)
- `sortBy` - 排序字段 (created_at|view_count|like_count)

### 创建分类 (仅管理员)
```http
POST /api/videos/categories
```

**请求头**: `Authorization: Bearer <admin_token>`

**请求参数**:
```json
{
  "name": "string (必需)",
  "description": "string (可选)",
  "parentId": "number (可选)"
}
```

### 更新分类 (仅管理员)
```http
PUT /api/videos/categories/:id
```

**请求头**: `Authorization: Bearer <admin_token>`
**路径参数**: `id` - 分类ID

### 删除分类 (仅管理员)
```http
DELETE /api/videos/categories/:id
```

**请求头**: `Authorization: Bearer <admin_token>`
**路径参数**: `id` - 分类ID

## 💬 互动模块 (/api/interactions) - 15个接口

### 发表评论
```http
POST /api/interactions/comments
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "videoId": "number (必需)",
  "content": "string (必需，1-500字符)",
  "parentId": "number (可选，回复评论ID)"
}
```

### 编辑评论
```http
PUT /api/interactions/comments/:id
```

**请求头**: `Authorization: Bearer <token>`
**路径参数**: `id` - 评论ID

**请求参数**:
```json
{
  "content": "string (必需，1-500字符)"
}
```

### 删除评论
```http
DELETE /api/interactions/comments/:id
```

**请求头**: `Authorization: Bearer <token>`
**路径参数**: `id` - 评论ID

### 获取视频评论
```http
GET /api/interactions/videos/:videoId/comments
```

**路径参数**: `videoId` - 视频ID

**查询参数**:
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)
- `sortBy` - 排序字段 (created_at|like_count)
- `sortOrder` - 排序方向 (ASC|DESC)

### 获取热门评论
```http
GET /api/interactions/videos/:videoId/comments/popular
```

**路径参数**: `videoId` - 视频ID

### 获取评论回复
```http
GET /api/interactions/comments/:commentId/replies
```

**路径参数**: `commentId` - 评论ID

### 搜索评论
```http
GET /api/interactions/comments/search
```

**查询参数**:
- `keyword` - 搜索关键词 (必需)
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)

### 获取互动统计
```http
GET /api/interactions/videos/:videoId/stats
```

**路径参数**: `videoId` - 视频ID

### 点赞/取消点赞
```http
POST /api/interactions/likes
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "targetId": "number (必需)",
  "targetType": "video|comment (必需)"
}
```

### 收藏/取消收藏
```http
POST /api/interactions/favorites
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "videoId": "number (必需)"
}
```

### 获取用户收藏
```http
GET /api/interactions/users/:id/favorites
```

**路径参数**: `id` - 用户ID
**请求头**: `Authorization: Bearer <token>`

### 获取用户点赞
```http
GET /api/interactions/users/:id/likes
```

**路径参数**: `id` - 用户ID
**请求头**: `Authorization: Bearer <token>`

### 获取用户评论
```http
GET /api/interactions/users/:id/comments
```

**路径参数**: `id` - 用户ID

### 批量检查互动状态
```http
POST /api/interactions/batch-check
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "videoIds": "array (视频ID数组)"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "interactions": [
      {
        "videoId": 1,
        "isLiked": true,
        "isFavorited": false
      }
    ]
  }
}
```

### 互动测试
```http
GET /api/interactions/test
```

## 💎 会员模块 (/api/members) - 16个接口

### 获取会员计划列表
```http
GET /api/members/plans
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": 1,
        "name": "基础会员",
        "price": 19.9,
        "duration": 30,
        "features": ["无广告观看", "高清画质"],
        "isPopular": false
      },
      {
        "id": 2,
        "name": "VIP会员",
        "price": 39.9,
        "duration": 30,
        "features": ["无广告观看", "超清画质", "独家内容"],
        "isPopular": true
      }
    ]
  }
}
```

### 获取会员计划详情
```http
GET /api/members/plans/:id
```

**路径参数**: `id` - 计划ID

### 比较会员计划
```http
POST /api/members/plans/compare
```

**请求参数**:
```json
{
  "planIds": "array (计划ID数组)"
}
```

### 获取我的会员信息
```http
GET /api/members/my-membership
```

**请求头**: `Authorization: Bearer <token>`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "membership": {
      "id": 1,
      "planName": "VIP会员",
      "status": "active",
      "startDate": "2025-01-01T00:00:00.000Z",
      "endDate": "2025-01-31T23:59:59.000Z",
      "autoRenew": true,
      "features": ["无广告观看", "超清画质", "独家内容"]
    }
  }
}
```

### 获取会员历史
```http
GET /api/members/my-history
```

**请求头**: `Authorization: Bearer <token>`

### 订阅会员计划
```http
POST /api/members/subscribe
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "planId": "number (必需)",
  "paymentMethod": "string (支付方式)"
}
```

### 取消会员订阅
```http
POST /api/members/cancel
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "reason": "string (取消原因，可选)"
}
```

### 设置自动续费
```http
POST /api/members/auto-renew
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "autoRenew": "boolean (是否自动续费)"
}
```

### 获取会员权益
```http
GET /api/members/benefits
```

**请求头**: `Authorization: Bearer <member_token>`

### 获取独家内容
```http
GET /api/members/exclusive-content
```

**请求头**: `Authorization: Bearer <member_token>`

### 获取会员统计 (管理员)
```http
GET /api/members/admin/stats
```

**请求头**: `Authorization: Bearer <admin_token>`

### 获取所有计划 (管理员)
```http
GET /api/members/admin/plans
```

**请求头**: `Authorization: Bearer <admin_token>`

### 创建会员计划 (管理员)
```http
POST /api/members/admin/plans
```

**请求头**: `Authorization: Bearer <admin_token>`

**请求参数**:
```json
{
  "name": "string (必需)",
  "price": "number (必需)",
  "duration": "number (天数)",
  "features": "array (功能列表)",
  "description": "string (可选)"
}
```

### 更新会员计划 (管理员)
```http
PUT /api/members/admin/plans/:id
```

**请求头**: `Authorization: Bearer <admin_token>`
**路径参数**: `id` - 计划ID

### 删除会员计划 (管理员)
```http
DELETE /api/members/admin/plans/:id
```

**请求头**: `Authorization: Bearer <admin_token>`
**路径参数**: `id` - 计划ID

### 会员测试
```http
GET /api/members/test
```

## 💳 支付模块 (/api/payment) - 8个接口

### 获取支付方式列表
```http
GET /api/payment/methods
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "methods": [
      {
        "id": "epay",
        "name": "易支付",
        "enabled": true,
        "supportedTypes": ["alipay", "wechat", "qq"]
      }
    ]
  }
}
```

### 支付回调 (Webhook)
```http
POST /api/payment/webhook/:provider
```

**路径参数**: `provider` - 支付提供商 (epay|alipay|wechat)
**说明**: 第三方支付平台的回调接口，用于处理支付结果通知

### 创建支付订单
```http
POST /api/payment/create-order
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "type": "membership|video|recharge",
  "itemId": "number (商品ID)",
  "amount": "number (金额)",
  "paymentMethod": "string (支付方式)",
  "returnUrl": "string (可选，支付成功跳转URL)",
  "notifyUrl": "string (可选，异步通知URL)"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "order": {
      "orderNo": "ORD20250107001",
      "amount": 19.9,
      "status": "pending",
      "paymentUrl": "https://pay.example.com/pay?order=xxx",
      "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
      "expiresAt": "2025-01-07T13:00:00.000Z"
    }
  }
}
```

### 查询订单状态
```http
GET /api/payment/orders/:orderNo
```

**请求头**: `Authorization: Bearer <token>`
**路径参数**: `orderNo` - 订单号

**响应示例**:
```json
{
  "success": true,
  "data": {
    "order": {
      "orderNo": "ORD20250107001",
      "type": "membership",
      "amount": 19.9,
      "status": "paid",
      "paymentMethod": "alipay",
      "createdAt": "2025-01-07T12:00:00.000Z",
      "paidAt": "2025-01-07T12:05:00.000Z"
    }
  }
}
```

### 获取我的订单列表
```http
GET /api/payment/my-orders
```

**请求头**: `Authorization: Bearer <token>`

**查询参数**:
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)
- `status` - 订单状态筛选 (pending|paid|failed|refunded)
- `type` - 订单类型筛选 (membership|video|recharge)

### 申请退款
```http
POST /api/payment/refund
```

**请求头**: `Authorization: Bearer <token>`

**请求参数**:
```json
{
  "orderNo": "string (订单号)",
  "reason": "string (退款原因)",
  "amount": "number (可选，部分退款金额)"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "refund": {
      "refundNo": "REF20250107001",
      "orderNo": "ORD20250107001",
      "amount": 19.9,
      "status": "processing",
      "reason": "用户主动申请",
      "createdAt": "2025-01-07T14:00:00.000Z"
    }
  }
}
```

### 支付统计 (仅管理员)
```http
GET /api/payment/stats
```

**请求头**: `Authorization: Bearer <admin_token>`

### 支付测试 (仅管理员)
```http
GET /api/payment/test
```

**请求头**: `Authorization: Bearer <admin_token>`

## 🔧 管理模块 (/api/admin) - 17个接口

**注意**: 所有管理接口都需要管理员权限

### 获取仪表板统计
```http
GET /api/admin/dashboard/stats
```

**请求头**: `Authorization: Bearer <admin_token>`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "stats": {
      "users": {
        "total": 1000,
        "active": 800,
        "newToday": 50
      },
      "videos": {
        "total": 500,
        "published": 450,
        "processing": 10
      },
      "revenue": {
        "today": 1000.0,
        "thisMonth": 25000.0,
        "total": 100000.0
      },
      "interactions": {
        "comments": 2000,
        "likes": 5000,
        "views": 50000
      }
    }
  }
}
```

### 用户管理
```http
GET /api/admin/users
```

**请求头**: `Authorization: Bearer <admin_token>`

**查询参数**:
- `page` - 页码 (默认: 1)
- `pageSize` - 每页数量 (默认: 20)
- `keyword` - 搜索关键词 (可选)
- `role` - 角色筛选 (可选)
- `status` - 状态筛选 (可选)
- `sortBy` - 排序字段 (created_at|last_login)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "role": "user",
        "status": "active",
        "created_at": "2025-01-01T00:00:00.000Z",
        "last_login": "2025-01-07T12:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### 批量用户操作
```http
POST /api/admin/users/batch
```

**请求头**: `Authorization: Bearer <admin_token>`

**请求参数**:
```json
{
  "userIds": "array (用户ID数组)",
  "action": "ban|unban|delete|changeRole",
  "params": {
    "role": "string (当action为changeRole时)",
    "reason": "string (当action为ban时)"
  }
}
```

### 视频管理
```http
GET /api/admin/videos
```

**请求头**: `Authorization: Bearer <admin_token>`

**查询参数**:
- `page` - 页码
- `pageSize` - 每页数量
- `status` - 状态筛选
- `visibility` - 可见性筛选
- `keyword` - 搜索关键词

### 批量视频操作
```http
POST /api/admin/videos/batch
```

**请求头**: `Authorization: Bearer <admin_token>`

**请求参数**:
```json
{
  "videoIds": "array (视频ID数组)",
  "action": "publish|unpublish|delete|changeCategory",
  "params": {
    "categoryId": "number (当action为changeCategory时)"
  }
}
```

### 评论管理
```http
GET /api/admin/comments
```

**请求头**: `Authorization: Bearer <admin_token>`

**查询参数**:
- `page` - 页码
- `pageSize` - 每页数量
- `status` - 状态筛选
- `videoId` - 视频ID筛选
- `keyword` - 搜索关键词

### 系统日志
```http
GET /api/admin/system/logs
```

**请求头**: `Authorization: Bearer <admin_token>`

**查询参数**:
- `level` - 日志级别 (error|warn|info|debug)
- `startDate` - 开始日期
- `endDate` - 结束日期
- `page` - 页码
- `pageSize` - 每页数量

### 获取系统配置
```http
GET /api/admin/system/config
```

**请求头**: `Authorization: Bearer <admin_token>`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "config": {
      "site_name": "视频平台",
      "max_upload_size": 500,
      "allowed_video_formats": ["mp4", "avi", "mov"],
      "member_price_monthly": 19.9,
      "vip_price_monthly": 39.9
    }
  }
}
```

### 更新系统配置
```http
PUT /api/admin/system/config
```

**请求头**: `Authorization: Bearer <admin_token>`

**请求参数**:
```json
{
  "site_name": "string (可选)",
  "max_upload_size": "number (可选)",
  "member_price_monthly": "number (可选)"
}
```

### 清理系统缓存
```http
POST /api/admin/system/cache/clear
```

**请求头**: `Authorization: Bearer <admin_token>`

### 获取访问统计概览
```http
GET /api/admin/statistics/access/overview
```

**请求头**: `Authorization: Bearer <admin_token>`

**查询参数**:
- `timeRange` - 时间范围 (7d|30d|90d)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalViews": 100000,
      "uniqueVisitors": 5000,
      "avgSessionDuration": 300,
      "bounceRate": 0.3,
      "topPages": [
        {"path": "/videos/1", "views": 1000},
        {"path": "/videos/2", "views": 800}
      ]
    }
  }
}
```

### 获取访问统计详情
```http
GET /api/admin/statistics/access/detailed
```

**请求头**: `Authorization: Bearer <admin_token>`

### 获取收费统计概览
```http
GET /api/admin/statistics/revenue/overview
```

**请求头**: `Authorization: Bearer <admin_token>`

**查询参数**:
- `timeRange` - 时间范围 (30d|90d|1y)

### 获取收费统计详情
```http
GET /api/admin/statistics/revenue/detailed
```

**请求头**: `Authorization: Bearer <admin_token>`

### 获取用户行为统计
```http
GET /api/admin/statistics/users/behavior
```

**请求头**: `Authorization: Bearer <admin_token>`

### 获取热门内容统计
```http
GET /api/admin/statistics/content/popular
```

**请求头**: `Authorization: Bearer <admin_token>`

### 管理测试
```http
GET /api/admin/test
```

**请求头**: `Authorization: Bearer <admin_token>`

## 📊 错误代码说明

### 通用错误
| 错误代码 | 说明 | HTTP状态码 |
|---------|------|-----------|
| `VALIDATION_ERROR` | 数据验证失败 | 400 |
| `UNAUTHORIZED` | 未认证 | 401 |
| `ACCESS_DENIED` | 权限不足 | 403 |
| `NOT_FOUND` | 资源不存在 | 404 |
| `RATE_LIMIT_EXCEEDED` | 请求过于频繁 | 429 |
| `INTERNAL_ERROR` | 服务器内部错误 | 500 |

### 认证相关错误
| 错误代码 | 说明 | HTTP状态码 |
|---------|------|-----------|
| `INVALID_CREDENTIALS` | 用户名或密码错误 | 401 |
| `TOKEN_EXPIRED` | Token已过期 | 401 |
| `TOKEN_INVALID` | Token无效 | 401 |
| `EMAIL_NOT_VERIFIED` | 邮箱未验证 | 403 |
| `ACCOUNT_BANNED` | 账户已被封禁 | 403 |

### 业务相关错误
| 错误代码 | 说明 | HTTP状态码 |
|---------|------|-----------|
| `VIDEO_NOT_FOUND` | 视频不存在 | 404 |
| `INSUFFICIENT_PERMISSIONS` | 权限不足 | 403 |
| `PAYMENT_FAILED` | 支付失败 | 400 |
| `MEMBERSHIP_EXPIRED` | 会员已过期 | 403 |
| `UPLOAD_SIZE_EXCEEDED` | 文件大小超限 | 413 |

## 🔄 限流说明

### 全局限流
| 限制类型 | 限制 | 说明 |
|---------|------|------|
| 通用API | 100次/15分钟 | 所有API接口的总体限制 |
| 登录 | 5次/分钟 | 防止暴力破解 |
| 注册 | 3次/分钟 | 防止恶意注册 |
| 上传视频 | 5次/小时 | 防止滥用存储 |
| 发表评论 | 10次/分钟 | 防止垃圾评论 |
| 搜索 | 30次/分钟 | 防止搜索滥用 |

### 限流响应
当触发限流时，API将返回429状态码：
```json
{
  "success": false,
  "message": "请求过于频繁，请稍后再试",
  "code": "RATE_LIMIT_EXCEEDED",
  "details": {
    "limit": 100,
    "remaining": 0,
    "resetTime": "2025-01-07T13:00:00.000Z"
  }
}
```

---

## 📝 重要说明

### 时间格式
- 所有时间格式均为ISO 8601标准 (YYYY-MM-DDTHH:mm:ss.sssZ)
- 时区统一使用UTC时间
- 客户端需要根据本地时区进行转换

### 文件上传
- 支持的视频格式：mp4, avi, mov, wmv, flv, webm, mkv
- 支持的音频格式：mp3, wav, flac, aac, ogg, m4a, wma
- 支持的图片格式：jpg, jpeg, png, gif, webp
- 最大文件大小：视频500MB，音频100MB
- 上传使用multipart/form-data格式
- 系统自动检测媒体类型并进行相应处理

### 分页说明
- 默认页码从1开始
- 默认每页20条记录
- 最大每页50条记录
- 响应中包含完整的分页信息

### 认证说明
- JWT Token有效期为1小时
- Refresh Token有效期为30天
- Token需要在请求头中携带：`Authorization: Bearer <token>`
- 管理员接口需要admin角色权限
