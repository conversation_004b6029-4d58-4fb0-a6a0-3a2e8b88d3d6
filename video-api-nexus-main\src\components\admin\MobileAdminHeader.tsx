import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Menu, Shield } from 'lucide-react';
import AdminSidebarContent from './AdminSidebarContent';

interface MobileAdminHeaderProps {
  className?: string;
}

const MobileAdminHeader: React.FC<MobileAdminHeaderProps> = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={`flex items-center justify-between p-4 bg-card border-b ${className}`}>
      {/* 左侧：菜单按钮和标题 */}
      <div className="flex items-center space-x-3">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="md:hidden">
              <Menu className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-64 p-0">
            <SheetHeader className="p-6 pb-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <SheetTitle className="text-lg font-bold">管理后台</SheetTitle>
                  <p className="text-xs text-muted-foreground">Admin Panel</p>
                </div>
              </div>
            </SheetHeader>
            <AdminSidebarContent onNavigate={() => setIsOpen(false)} />
          </SheetContent>
        </Sheet>

        {/* 标题（桌面端可见） */}
        <div className="hidden md:flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            <Shield className="h-4 w-4 text-white" />
          </div>
          <div>
            <h2 className="text-base font-bold">管理后台</h2>
          </div>
        </div>
      </div>

      {/* 右侧：可以添加用户信息、通知等 */}
      <div className="flex items-center space-x-2">
        {/* 这里可以添加其他功能按钮 */}
      </div>
    </div>
  );
};

export default MobileAdminHeader;