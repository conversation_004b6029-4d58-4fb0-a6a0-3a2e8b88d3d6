// 播放列表相关类型定义

export interface PlaylistItem {
  id: string;
  videoId: string | number;
  title: string;
  duration: number;
  thumbnail?: string;
  mediaType: 'video' | 'audio';
  addedAt: Date;
  url?: string;
}

export interface Playlist {
  id: string;
  name: string;
  items: PlaylistItem[];
  currentIndex: number;
  isPlaying: boolean;
  playMode: 'sequence' | 'loop' | 'random';
  createdAt: Date;
  updatedAt: Date;
  isTemporary: boolean; // 是否为临时播放列表
}

export interface PlaylistState {
  currentPlaylist: Playlist | null;
  savedPlaylists: Playlist[]; // 用户保存的播放列表
  isLoading: boolean;
  error: string | null;
}

export interface PlaylistActions {
  createPlaylist: (name: string, items?: PlaylistItem[]) => Playlist;
  createTemporaryPlaylist: (items: PlaylistItem[]) => Playlist;
  setCurrentPlaylist: (playlist: Playlist | null) => void;
  setSavedPlaylists: (playlists: Playlist[]) => void;
  addToCurrentPlaylist: (item: PlaylistItem) => void;
  removeFromPlaylist: (itemId: string) => void;
  clearPlaylist: () => void;
  playNext: () => void;
  playPrevious: () => void;
  playItem: (itemId: string) => void;
  togglePlayMode: () => void;
  setPlaying: (isPlaying: boolean) => void;
  saveSettings: (settings: PlaylistSettings) => void;
  setPlayMode: (mode: 'sequence' | 'loop' | 'random') => void;
  deletePlaylist: (playlistId: string) => void;
  updatePlaylist: (playlistId: string, updates: Partial<Playlist>) => void;
  setCurrentIndex: (index: number) => void;
}

export interface UsePlaylistOptions {
  onPlaylistEnd?: () => void;
  onItemChange?: (item: PlaylistItem | null) => void;
  onPlayModeChange?: (mode: 'sequence' | 'loop' | 'random') => void;
  autoSave?: boolean; // 是否自动保存到本地存储
}

// 播放历史相关类型
export interface PlayHistory {
  id: string;
  videoId: string | number;
  title: string;
  playedAt: Date;
  duration: number;
  watchTime: number; // 观看时长（秒）
  mediaType: 'video' | 'audio';
}

// API相关类型
export interface CreatePlaylistRequest {
  name: string;
  description?: string;
  items?: PlaylistItem[];
}

export interface UpdatePlaylistRequest {
  name?: string;
  description?: string;
  items?: PlaylistItem[];
}

export interface PlaylistResponse {
  id: string;
  name: string;
  description?: string;
  items: PlaylistItem[];
  userId: string;
  createdAt: string;
  updatedAt: string;
}

// 本地存储键名
export const STORAGE_KEYS = {
  CURRENT_PLAYLIST: 'currentPlaylist',
  SAVED_PLAYLISTS: 'savedPlaylists',
  PLAY_HISTORY: 'playHistory',
  PLAYLIST_SETTINGS: 'playlistSettings',
  USER_PLAYLISTS: 'userPlaylists', // 按用户ID分组的播放列表
  SYNC_STATUS: 'playlistSyncStatus', // 同步状态
} as const;

// 按用户分组的播放列表存储结构
export interface UserPlaylistStorage {
  [userId: string]: {
    savedPlaylists: Playlist[];
    currentPlaylist: Playlist | null;
    lastSyncTime: string | null;
    syncVersion: number;
  };
}

// 同步状态
export interface SyncStatus {
  isLoading: boolean;
  lastSyncTime: Date | null;
  error: string | null;
  pendingOperations: number;
}

// 播放列表设置
export interface PlaylistSettings {
  autoPlay: boolean;
  shuffleOnRepeat: boolean;
  saveHistory: boolean;
  maxHistoryItems: number;
}
