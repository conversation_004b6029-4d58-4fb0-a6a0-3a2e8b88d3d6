const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testNewMemberFlow() {
  try {
    console.log('=== 测试新用户开通会员完整流程 ===\n');

    // 1. 管理员登录
    console.log('1. 管理员登录...');
    const adminLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123456!',
      isAdminLogin: true
    });

    const adminToken = adminLoginResponse.data.data.tokens.accessToken;
    console.log('✓ 管理员登录成功\n');

    // 2. 注册新用户
    console.log('2. 注册新用户...');
    const testEmail = `test_${Date.now()}@example.com`;
    const testUsername = `test_${Date.now()}`;
    
    const registerResponse = await axios.post(`${API_BASE}/auth/register`, {
      email: testEmail,
      username: testUsername,
      password: 'Test123456!',
      nickname: '测试用户'
    });

    console.log(`✓ 新用户注册成功: ${testUsername} (${testEmail})\n`);

    // 3. 新用户登录
    console.log('3. 新用户登录...');
    const userLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: testEmail,
      password: 'Test123456!'
    });

    const userToken = userLoginResponse.data.data.tokens.accessToken;
    const userId = userLoginResponse.data.data.user.id;
    console.log(`✓ 新用户登录成功，用户ID: ${userId}\n`);

    // 4. 获取会员计划
    console.log('4. 获取会员计划...');
    const plansResponse = await axios.get(`${API_BASE}/member/plans`);
    const plans = plansResponse.data.data.plans;
    const basicPlan = plans.find(p => p.name === '基础会员');
    
    console.log(`✓ 找到基础会员计划: ID ${basicPlan.id}, 价格 ¥${basicPlan.price}\n`);

    // 5. 创建会员订单
    console.log('5. 创建会员订单...');
    const subscribeResponse = await axios.post(`${API_BASE}/member/subscribe`, {
      planId: basicPlan.id,
      paymentMethod: 'epay'
    }, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    const orderNo = subscribeResponse.data.data.paymentInfo.orderNo;
    console.log(`✓ 会员订单创建成功: ${orderNo}\n`);

    // 6. 管理员手动完成订单（模拟支付成功）
    console.log('6. 管理员手动完成订单...');
    
    // 先获取订单详情
    const orderDetailResponse = await axios.get(`${API_BASE}/admin/payments/${orderNo}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    console.log('订单详情响应:', JSON.stringify(orderDetailResponse.data, null, 2));
    const orderId = orderDetailResponse.data.data.order.id;
    console.log(`✓ 获取订单详情: ID ${orderId}`);

    // 更新订单状态为已支付
    const updateOrderResponse = await axios.put(`${API_BASE}/admin/payments/${orderId}/status`, {
      status: 'paid',
      reason: '测试：手动完成订单'
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    console.log('✓ 订单状态更新为已支付\n');

    // 7. 验证会员记录是否创建
    console.log('7. 验证会员记录...');
    
    // 等待一下确保处理完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 检查用户会员状态
    const membershipResponse = await axios.get(`${API_BASE}/member/my-membership`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    if (membershipResponse.data.success && membershipResponse.data.data.membership) {
      const membership = membershipResponse.data.data.membership;
      console.log('✅ 会员记录创建成功:');
      console.log(`   计划: ${membership.plan_name}`);
      console.log(`   状态: ${membership.status}`);
      console.log(`   结束时间: ${membership.end_date}`);
    } else {
      console.log('❌ 会员记录未创建');
    }

    // 8. 检查管理员界面是否显示新会员
    console.log('\n8. 检查管理员界面...');
    const adminMembersResponse = await axios.get(`${API_BASE}/member/admin/users`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { role: 'member' }
    });

    const members = adminMembersResponse.data.data.users;
    const newMember = members.find(m => m.id === userId);

    if (newMember) {
      console.log('✅ 新会员在管理界面中显示:');
      console.log(`   用户: ${newMember.username}(${newMember.id})`);
      console.log(`   角色: ${newMember.role}`);
      console.log(`   会员状态: ${newMember.membership_status}`);
      console.log(`   会员计划: ${newMember.membership_plan?.name || '无'}`);
    } else {
      console.log('❌ 新会员未在管理界面中显示');
    }

    // 9. 检查会员统计
    console.log('\n9. 检查会员统计...');
    const overviewResponse = await axios.get(`${API_BASE}/member/admin/overview`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    const overview = overviewResponse.data.data;
    console.log('✅ 会员统计更新:');
    console.log(`   总会员数: ${overview.totalMembers}`);
    console.log(`   活跃会员数: ${overview.activeMembers}`);

    console.log('\n=== 测试完成：新用户开通会员流程正常 ===');

  } catch (error) {
    console.error('测试过程中发生错误:', error.response?.data || error.message);
    
    if (error.response?.data?.stack) {
      console.error('错误堆栈:', error.response.data.stack);
    }
  }
}

// 运行测试
testNewMemberFlow().then(() => {
  console.log('\n=== 测试脚本执行完成 ===');
  process.exit(0);
}).catch(error => {
  console.error('测试脚本执行失败:', error);
  process.exit(1);
});
