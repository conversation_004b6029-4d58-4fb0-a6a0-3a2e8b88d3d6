const CreemPlan = require('../../../database/models/CreemPlan');
const CreemIoService = require('../../payment/services/CreemIoService');
const logger = require('../../../utils/logger');
const { AppError } = require('../../../middleware/errorHandler');

class CreemService {

  /**
   * 创建一个新的Creem计划，包括在Creem.io上创建产品并在本地数据库中记录。
   * @param {object} planData - 来自请求的计划数据
   * @returns {Promise<object>} 在本地数据库中创建的完整计划对象
   */
  async createPlan(planData) {
    logger.info('开始创建Creem计划流程...', { planData });

    // 从planData中提取duration_days，这个字段不会发送到Creem.io API
    const { duration_days, ...creemApiData } = planData;
    logger.info('提取会员天数', { duration_days });

    // 步骤 1: 在Creem.io上创建产品
    let creemProduct;
    try {
      creemProduct = await CreemIoService.createProduct(creemApiData);
      if (!creemProduct || !creemProduct.id) {
        throw new AppError('从Creem.io返回的产品数据无效', 500, 'INVALID_CREEM_RESPONSE');
      }
      logger.info('在Creem.io上成功创建产品', { creemProductId: creemProduct.id });
    } catch (error) {
      logger.error('在Creem.io上创建产品失败', { error: error.message, details: error.details });
      // 直接重新抛出AppError，让控制器处理它
      throw error;
    }

    // 步骤 2: 将计划信息（包括Creem产品ID和会员天数）保存到本地数据库
    try {
      const localPlanData = {
        ...planData, // 包含duration_days
        creem_product_id: creemProduct.id,
      };

      const newPlan = await CreemPlan.createPlan(localPlanData);
      logger.info('成功在本地数据库中创建Creem计划', { planId: newPlan.id, duration_days });
      return newPlan;
    } catch (dbError) {
      logger.error('在本地数据库中创建Creem计划失败，尝试回滚...', { dbError: dbError.message });
      
      // 关键步骤：如果本地数据库保存失败，我们应该尝试删除刚刚在Creem.io上创建的产品，以避免数据不一致。
      try {
        await CreemIoService.deleteProduct(creemProduct.id);
        logger.warn('成功回滚：已删除在Creem.io上创建的孤立产品', { creemProductId: creemProduct.id });
      } catch (rollbackError) {
        logger.error('CRITICAL: 回滚失败！无法删除在Creem.io上创建的孤立产品。请手动处理。', {
          creemProductId: creemProduct.id,
          rollbackError: rollbackError.message
        });
        // 抛出一个特殊的错误，告知需要手动干预
        throw new AppError(
          `数据库错误，且无法自动清理在Creem.io上创建的资源(ID: ${creemProduct.id})。请手动检查。`, 
          500, 
          'DB_ERROR_WITH_ROLLBACK_FAILURE'
        );
      }
      
      // 抛出原始的数据库错误
      throw dbError;
    }
  }

  /**
   * 获取所有本地存储的Creem计划
   * @returns {Promise<Array<object>>}
   */
  async getAllPlans() {
    return CreemPlan.findAll();
  }

  /**
   * 更新一个Creem计划
   * @param {number} planId - 本地计划ID
   * @param {object} updateData - 要更新的数据
   * @returns {Promise<object>} 更新后的计划
   */
  async updatePlan(planId, updateData) {
    logger.info(`开始更新Creem计划: ${planId}`, { updateData });
    
    // 获取当前计划以得到creem_product_id
    const plan = await CreemPlan.findById(planId);
    if (!plan) {
      throw new AppError('计划未找到', 404, 'PLAN_NOT_FOUND');
    }

    // 从updateData中提取duration_days，这个字段不会发送到Creem.io API
    const { duration_days, ...creemApiData } = updateData;
    logger.info('提取会员天数', { duration_days });

    // 步骤 1: 如果有需要，在Creem.io上更新产品
    if (plan.creem_product_id && (creemApiData.name || creemApiData.description || creemApiData.price || creemApiData.currency)) {
      try {
        await CreemIoService.updateProduct(plan.creem_product_id, creemApiData);
        logger.info(`在Creem.io上成功更新产品: ${plan.creem_product_id}`);
      } catch (error) {
        logger.error(`在Creem.io上更新产品失败: ${plan.creem_product_id}`, { error: error.message });
        throw error;
      }
    }

    // 步骤 2: 在本地数据库中更新计划（包括会员天数）
    const updatedPlan = await CreemPlan.updatePlan(planId, updateData);
    logger.info(`在本地数据库中成功更新计划: ${planId}`, { duration_days });

    return updatedPlan;
  }

  /**
   * 删除一个Creem计划
   * @param {number} planId - 本地计划ID
   * @returns {Promise<boolean>}
   */
  async deletePlan(planId) {
    logger.info(`开始删除Creem计划: ${planId}`);

    const plan = await CreemPlan.findById(planId);
    if (!plan) {
      throw new AppError('计划未找到', 404, 'PLAN_NOT_FOUND');
    }

    // 步骤 1: 如果存在，从Creem.io删除产品
    if (plan.creem_product_id) {
      try {
        await CreemIoService.deleteProduct(plan.creem_product_id);
        logger.info(`在Creem.io上成功删除产品: ${plan.creem_product_id}`);
      } catch (error) {
        // 如果Creem.io上删除失败，我们只记录错误但继续执行，
        // 因为主要目标是删除本地记录。这可能需要手动清理。
        logger.error(`在Creem.io上删除产品失败，但将继续删除本地记录: ${plan.creem_product_id}`, { error: error.message });
      }
    }

    // 步骤 2: 从本地数据库删除计划
    const result = await CreemPlan.deletePlan(planId);
    logger.info(`在本地数据库中成功删除计划: ${planId}`);
    
    return result;
  }
}

module.exports = new CreemService(); 