const WatchHistory = require('../../../database/models/WatchHistory');
const Video = require('../../../database/models/Video');
const { AppError, asyncHandler } = require('../../../middleware/errorHandler');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const { operationLogger } = require('../../../middleware/requestLogger');

class WatchController {
  // 记录观看记录
  recordWatch = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { videoId, watchDuration, progress, lastPosition } = req.body;

    // 验证视频是否存在
    const video = await Video.findById(videoId);
    if (!video) {
      throw new AppError('视频不存在', 404, 'VIDEO_NOT_FOUND');
    }

    if (video.status !== 'published') {
      throw new AppError('视频不可观看', 400, 'VIDEO_NOT_AVAILABLE');
    }

    // 获取设备和IP信息
    const deviceType = req.headers['user-agent'] || 'unknown';
    const ipAddress = req.ip || req.connection.remoteAddress;

    // 记录观看历史
    await WatchHistory.recordWatch(userId, videoId, {
      watch_duration: watchDuration,
      progress: progress,
      last_position: lastPosition,
      device_type: deviceType,
      ip_address: ipAddress
    });

    // 更新视频观看次数
    await Video.incrementViewCount(videoId);

    // 清除用户统计缓存
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
    await cache.del(cacheKey);

    res.json({
      success: true,
      message: '观看记录已保存'
    });
  });

  // 获取用户观看历史
  getUserWatchHistory = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { page = 1, pageSize = 20 } = req.query;

    const result = await WatchHistory.getUserWatchHistory(userId, {
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

    res.json({
      success: true,
      data: result
    });
  });

  // 获取用户观看统计
  getUserWatchStats = asyncHandler(async (req, res) => {
    const userId = req.user.id;

    const stats = await WatchHistory.getUserWatchStats(userId);

    res.json({
      success: true,
      data: {
        stats
      }
    });
  });

  // 删除观看记录
  deleteWatchRecord = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { videoId } = req.params;

    await WatchHistory.deleteWatchRecord(userId, videoId);

    // 清除用户统计缓存
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
    await cache.del(cacheKey);

    res.json({
      success: true,
      message: '观看记录已删除'
    });
  });

  // 清空观看历史
  clearWatchHistory = asyncHandler(async (req, res) => {
    const userId = req.user.id;

    await WatchHistory.clearUserWatchHistory(userId);

    // 清除用户统计缓存
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
    await cache.del(cacheKey);

    // 记录操作日志
    operationLogger.logUserOperation(
      req, 
      'watch_history_clear', 
      userId, 
      '清空观看历史'
    );

    res.json({
      success: true,
      message: '观看历史已清空'
    });
  });

  // 获取视频观看统计（管理员）
  getVideoWatchStats = asyncHandler(async (req, res) => {
    const { videoId } = req.params;

    const stats = await WatchHistory.getVideoWatchStats(videoId);

    res.json({
      success: true,
      data: {
        stats
      }
    });
  });

  // 获取热门观看内容（管理员）
  getPopularContent = asyncHandler(async (req, res) => {
    const { timeRange = '7d', limit = 10 } = req.query;

    const content = await WatchHistory.getPopularContent(timeRange, parseInt(limit));

    res.json({
      success: true,
      data: {
        content
      }
    });
  });
}

module.exports = new WatchController();
