const mysql = require('mysql2/promise');
const logger = require('../src/utils/logger');

// 加载环境变量
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'video_user',
  password: process.env.DB_PASSWORD || 'secure_password',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

async function fixPaymentMembershipSync() {
  let connection;
  
  try {
    console.log('🔧 开始修复支付订单与会员状态同步问题...\n');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功\n');
    
    // 查找需要修复的订单
    console.log('🔍 查找需要修复的订单...');
    const [problematicOrders] = await connection.execute(`
      SELECT 
        o.id,
        o.user_id,
        o.order_no,
        o.type,
        o.target_id,
        o.payment_status,
        o.payment_method,
        o.payment_time,
        o.transaction_id,
        o.final_amount,
        o.created_at,
        u.username,
        u.email,
        u.role as user_role,
        mp.name as plan_name,
        mp.duration_days,
        mp.price
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN membership_plans mp ON o.target_id = mp.id
      WHERE o.type = 'membership' 
        AND o.payment_status = 'paid'
        AND NOT EXISTS (
          SELECT 1 FROM memberships m 
          WHERE m.user_id = o.user_id 
            AND m.plan_id = o.target_id 
            AND (m.transaction_id = o.transaction_id OR m.transaction_id IS NULL)
        )
      ORDER BY o.created_at DESC
    `);
    
    console.log(`找到 ${problematicOrders.length} 个需要修复的订单\n`);
    
    if (problematicOrders.length === 0) {
      console.log('✅ 没有发现需要修复的订单');
      return;
    }
    
    // 开始修复
    let fixedCount = 0;
    let failedCount = 0;
    
    for (const order of problematicOrders) {
      console.log(`修复订单: ${order.order_no} (用户: ${order.username || order.email})`);
      
      try {
        // 开始事务
        await connection.beginTransaction();
        
        // 1. 创建会员记录
        const startDate = new Date();
        const endDate = new Date(startDate.getTime() + order.duration_days * 24 * 60 * 60 * 1000);
        
        // 检查用户是否已有有效会员
        const [existingMemberships] = await connection.execute(`
          SELECT * FROM memberships 
          WHERE user_id = ? AND status = 'active' AND end_date > NOW()
          ORDER BY end_date DESC LIMIT 1
        `, [order.user_id]);
        
        let actualStartDate = startDate;
        let actualEndDate = endDate;
        
        if (existingMemberships.length > 0) {
          // 如果有有效会员，从过期日期开始计算
          const existingMembership = existingMemberships[0];
          actualStartDate = new Date(existingMembership.end_date);
          actualEndDate = new Date(actualStartDate.getTime() + order.duration_days * 24 * 60 * 60 * 1000);
          
          console.log(`  用户已有有效会员，从 ${actualStartDate.toISOString().split('T')[0]} 开始续费`);
        }
        
        // 创建会员记录
        const [membershipResult] = await connection.execute(`
          INSERT INTO memberships (
            user_id, plan_id, start_date, end_date, status, 
            payment_method, transaction_id, auto_renew, created_at, updated_at
          ) VALUES (?, ?, ?, ?, 'active', ?, ?, false, NOW(), NOW())
        `, [
          order.user_id,
          order.target_id,
          actualStartDate,
          actualEndDate,
          order.payment_method,
          order.transaction_id
        ]);
        
        console.log(`  ✅ 创建会员记录 ID: ${membershipResult.insertId}`);
        
        // 2. 更新用户角色（如果需要）
        if (order.user_role !== 'admin' && order.user_role !== 'member') {
          await connection.execute(`
            UPDATE users SET role = 'member' WHERE id = ?
          `, [order.user_id]);
          
          console.log(`  ✅ 更新用户角色: ${order.user_role} -> member`);
        }
        
        // 3. 如果支付时间为空，设置支付时间
        if (!order.payment_time) {
          await connection.execute(`
            UPDATE orders SET payment_time = created_at WHERE id = ?
          `, [order.id]);
          
          console.log(`  ✅ 设置支付时间为订单创建时间`);
        }
        
        // 提交事务
        await connection.commit();
        fixedCount++;
        
        console.log(`  ✅ 订单 ${order.order_no} 修复完成\n`);
        
      } catch (error) {
        // 回滚事务
        await connection.rollback();
        failedCount++;
        
        console.log(`  ❌ 订单 ${order.order_no} 修复失败: ${error.message}\n`);
        logger.error('修复订单失败:', {
          orderNo: order.order_no,
          error: error.message,
          stack: error.stack
        });
      }
    }
    
    // 输出修复结果
    console.log('📊 修复结果汇总:');
    console.log(`总订单数: ${problematicOrders.length}`);
    console.log(`修复成功: ${fixedCount}`);
    console.log(`修复失败: ${failedCount}`);
    
    if (fixedCount > 0) {
      console.log('\n✅ 修复完成！建议重新运行检查脚本验证结果。');
    }
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error);
    logger.error('修复支付会员同步失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixPaymentMembershipSync()
    .then(() => {
      console.log('\n✅ 修复脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 修复脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { fixPaymentMembershipSync };
