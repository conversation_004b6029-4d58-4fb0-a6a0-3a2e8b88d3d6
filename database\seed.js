require('dotenv').config({ path: require('path').resolve(process.cwd(), '.env') });

const { faker } = require('@faker-js/faker');
const bcrypt = require('bcryptjs');
const { mysql } = require('../src/config/database'); 
const logger = require('../src/utils/logger');

const BCRYPT_ROUNDS = 12;
const ADMIN_PASSWORD = 'admin_password'; // 您可以用这个密码登录

const NUM_USERS = 50;
const NUM_CATEGORIES = 8;
const NUM_VIDEOS_PER_USER = 5;
const NUM_COMMENTS_PER_VIDEO = 10;

async function seed() {
  let connection;
  try {
    connection = await mysql.getConnection();
    logger.info('数据库连接成功，开始填充数据...');

    // 清空现有数据 (注意：这会删除所有数据!)
    logger.info('正在清空旧数据...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    const [tables] = await connection.execute("SHOW TABLES LIKE '%s'");
    const tableNames = Object.values(tables).map(t => Object.values(t)[0]);
    for (const tableName of tableNames) {
        if (tableName !== 'migrations') { // 假设您有迁移表
             await connection.execute(`TRUNCATE TABLE ${tableName}`);
        }
    }
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    logger.info('旧数据已清空。');

    // 1. 创建管理员用户
    logger.info('正在创建管理员账户...');
    const adminPasswordHash = await bcrypt.hash(ADMIN_PASSWORD, BCRYPT_ROUNDS);
    const [adminResult] = await connection.execute(
      `INSERT INTO users (email, password, username, nickname, role, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      ['<EMAIL>', adminPasswordHash, 'admin', '超级管理员', 'admin', 'active', true]
    );
    const adminId = adminResult.insertId;
    logger.info(`管理员账户已创建，ID: ${adminId}, 密码: ${ADMIN_PASSWORD}`);

    // 2. 创建普通用户
    logger.info(`正在创建 ${NUM_USERS} 个普通用户...`);
    const users = [];
    const userPasswordHash = await bcrypt.hash('password123', BCRYPT_ROUNDS);
    for (let i = 0; i < NUM_USERS; i++) {
      const user = {
        email: faker.internet.email(),
        password: userPasswordHash,
        username: faker.internet.userName(),
        nickname: faker.person.fullName(),
        role: faker.helpers.arrayElement(['user', 'member', 'vip']),
        status: 'active',
        email_verified: true
      };
      const [result] = await connection.execute(
        `INSERT INTO users (email, password, username, nickname, role, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [user.email, user.password, user.username, user.nickname, user.role, user.status, user.email_verified]
      );
      users.push({ ...user, id: result.insertId });
    }
    logger.info('普通用户创建完毕。');

    // 3. 创建分类
    logger.info(`正在创建 ${NUM_CATEGORIES} 个分类...`);
    const categories = [];
    for (let i = 0; i < NUM_CATEGORIES; i++) {
      const name = faker.commerce.department();
      const category = {
        name,
        slug: `${faker.helpers.slugify(name).toLowerCase()}-${faker.string.alphanumeric(4)}`,
      };
      const [result] = await connection.execute(
        `INSERT INTO categories (name, slug) VALUES (?, ?)`,
        [category.name, category.slug]
      );
      categories.push({ ...category, id: result.insertId });
    }
    logger.info('分类创建完毕。');
    
    // 3.5. 创建会员计划
    logger.info('正在创建会员计划...');
    const plans = [
      { name: '基础会员', price: 19.9, duration_days: 30, features: '["无广告观看","高清画质"]', priority: 10 },
      { name: 'VIP会员', price: 39.9, duration_days: 30, features: '["无广告观看","超清画质","独家内容"]', priority: 20 },
      { name: '年度会员', price: 199, duration_days: 365, features: '["所有VIP特权","年度优惠","专属徽章"]', priority: 30 }
    ];
    for (const plan of plans) {
        await connection.execute(
            `INSERT INTO membership_plans (name, price, duration_days, features, status, priority) VALUES (?, ?, ?, ?, 'active', ?)`,
            [plan.name, plan.price, plan.duration_days, plan.features, plan.priority]
        );
    }
    logger.info('会员计划创建完毕。');

    // 4. 为每个用户创建视频
    logger.info('正在创建视频...');
    const allUsers = [{ id: adminId }, ...users];
    const videos = [];
    for (const user of allUsers) {
      for (let i = 0; i < NUM_VIDEOS_PER_USER; i++) {
        const video = {
          user_id: user.id,
          category_id: faker.helpers.arrayElement(categories).id,
          title: faker.lorem.sentence(5),
          description: faker.lorem.paragraphs(3),
          tags: JSON.stringify(faker.lorem.words(5).split(' ')),
          duration: faker.number.int({ min: 60, max: 1200 }),
          visibility: 'public',
          status: 'published',
          view_count: faker.number.int({ min: 100, max: 100000 }),
          like_count: faker.number.int({ min: 10, max: 5000 }),
          comment_count: 0
        };
        const [result] = await connection.execute(
          `INSERT INTO videos (user_id, category_id, title, description, tags, duration, visibility, status, view_count, like_count, comment_count) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [video.user_id, video.category_id, video.title, video.description, video.tags, video.duration, video.visibility, video.status, video.view_count, video.like_count, video.comment_count]
        );
        videos.push({ ...video, id: result.insertId });
      }
    }
    logger.info('视频创建完毕。');

    // 5. 为每个视频创建评论
    logger.info('正在创建评论...');
    for (const video of videos) {
        let commentCount = 0;
        for (let i = 0; i < NUM_COMMENTS_PER_VIDEO; i++) {
            const user = faker.helpers.arrayElement(allUsers);
            const comment = {
                video_id: video.id,
                user_id: user.id,
                content: faker.lorem.sentence()
            };
            await connection.execute(
                `INSERT INTO comments (video_id, user_id, content) VALUES (?, ?, ?)`,
                [comment.video_id, comment.user_id, comment.content]
            );
            commentCount++;
        }
        // 更新视频的评论数
        await connection.execute(`UPDATE videos SET comment_count = ? WHERE id = ?`, [commentCount, video.id]);
    }
    logger.info('评论创建完毕。');

    logger.info('🎉 数据填充成功！');

  } catch (error) {
    logger.error('数据填充失败:', error);
  } finally {
    if (connection) {
      await connection.release();
      logger.info('数据库连接已释放。');
    }
    // 强制退出进程
    process.exit();
  }
}

seed(); 