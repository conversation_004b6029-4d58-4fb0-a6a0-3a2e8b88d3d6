{"site": {"title": "Video Platform", "description": "A modern video platform"}, "nav": {"home": "Home", "upload": "Upload", "search": "Search", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "user": {"profile": "Profile", "myWorks": "My Works", "favorites": "Favorites", "orders": "Orders", "earnings": "Earnings", "balance": "Balance", "followers": "Followers", "following": "Following", "settings": "Settings"}, "membership": {"info": "Membership Information", "currentStatus": "Current Status", "upgradePrompt": "Upgrade membership for more privileges", "upgradeNow": "Upgrade Now", "memberUser": "Member User", "vipUser": "VIP User", "regularUser": "Regular User", "joinedAt": "Joined", "thankYouSupport": "Thank you for your support, enjoy all member privileges", "expiryTime": "Expiry Time", "remainingDays": "Remaining Days", "days": "days", "currentPlan": "Current Plan", "benefits": "Member Benefits", "adFreeViewing": "Ad-Free Viewing", "hdVideo": "HD Video", "videoDownload": "Video Download", "exclusiveContent": "Exclusive Content", "prioritySupport": "Priority Support", "renewMembership": "Renew Membership", "clickToViewDetails": "Click to view membership details"}, "video": {"title": "Title", "description": "Description", "category": "Category", "duration": "Duration", "views": "Views", "likes": "<PERSON>s", "comments": "Comments", "upload": "Upload Video", "processing": "Processing", "published": "Published", "draft": "Draft"}, "common": {"save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "loading": "Loading...", "error": "Error", "success": "Success", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "all": "All", "noMoreContent": "No more content", "latest": "Latest", "popular": "Popular", "recommended": "Recommended", "searchPlaceholder": "Search videos...", "unknown": "Unknown", "unknownUser": "Unknown user", "noReason": "No reason provided", "noData": "No data available", "loadFailed": "Load Failed", "cannotLoadProfile": "Unable to load user profile, please refresh and try again", "submitSuccess": "Submit Success", "operationFailed": "Operation Failed", "rechargeSubmitted": "Your {{amount}} yuan recharge application has been submitted, please wait for administrator review.", "cannotSubmitRecharge": "Unable to submit recharge application, please try again later.", "cannotCreatePaymentOrder": "Unable to create payment order, please try again later.", "completePayment": "Complete Payment", "paymentInstructions": "Please use payment app to scan the QR code below, or click the link to go to the payment page. Balance will be credited automatically after successful payment.", "paymentAmount": "Payment Amount", "clickToPayment": "Click here to go to payment page", "orCopyLink": "Or copy link", "locale": "en-US", "warning": "Warning"}, "categories": {"电影": "Movies", "电视剧": "TV Shows", "纪录片": "Documentaries", "动漫": "Anime", "综艺": "Variety Shows", "教育": "Education", "音乐": "Music", "体育": "Sports", "游戏": "Gaming", "科技": "Technology", "生活": "Lifestyle", "美食": "Food"}, "form": {"required": "This field is required", "email": "Please enter a valid email address", "password": "Password", "confirmPassword": "Confirm Password", "passwordMismatch": "Passwords do not match", "username": "Username", "nickname": "Nickname", "titleRequired": "Title *", "categoryLabel": "Category", "titlePlaceholder": "Enter title", "categoryPlaceholder": "Select category (optional)", "categoryLoading": "Loading...", "submitting": "Submitting...", "submitForReview": "Submit for Review", "uploadSuccess": "Success", "uploadSuccessMessage": "Video submitted for review!", "uploadFailed": "Upload Failed", "uploadFailedMessage": "Video upload failed, please try again"}, "avatar": {"uploading": "Uploading...", "changeAvatar": "Change Avatar", "deleteAvatar": "Delete", "supportedFormats": "Supports JPG, PNG, GIF, WebP formats, file size up to 2MB"}, "role": {"admin": "Administrator", "vip": "VIP Member", "member": "Member", "user": "User"}, "status": {"active": "Active", "inactive": "Inactive", "banned": "Banned", "unknown": "Unknown"}, "stats": {"watchTime": "Watch Time (Hours)", "favoriteCount": "Favorites", "likeCount": "<PERSON>s", "commentCount": "Comments", "followerCount": "Followers", "followingCount": "Following"}, "profile": {"personalInfo": "Personal Information", "editProfile": "Edit Profile", "editPersonalInfo": "Edit Personal Information", "username": "Username", "nickname": "Nickname"}, "works": {"title": "My Works", "noWorks": "You haven't uploaded any works yet.", "loadFailed": "Load Failed", "loadFailedDesc": "Unable to load your works list, please try again later."}, "videoStatus": {"published": "Published", "pending_review": "Under Review", "rejected": "Rejected", "processing": "Processing", "private": "Private"}, "videoInfo": {"uncategorized": "Uncategorized", "anonymousAuthor": "Anonymous Author", "views": "views", "member": "Member", "paid": "Paid", "systemAdmin": "System Admin"}, "favorites": {"title": "My Favorites", "videoCount": "{count} videos in total", "noFavorites": "No Favorites", "noFavoritesDesc": "Start favoriting videos you like", "reload": "Reload"}, "orders": {"title": "My Orders", "description": "Here are all your transaction records.", "orderType": "Order Type", "orderStatus": "Order Status", "allTypes": "All Types", "allStatuses": "All Statuses", "noOrders": "No related orders found.", "loadFailed": "Load Failed", "unknownError": "Unknown Error", "table": {"orderNo": "Order No.", "type": "Type", "amount": "Amount", "status": "Status", "paymentMethod": "Payment Method", "createTime": "Create Time", "actions": "Actions"}, "types": {"recharge": "Account Recharge", "membership": "Membership Purchase", "video": "Video Purchase", "other": "Other"}, "statuses": {"pending": "Pending Payment", "paid": "Paid", "failed": "Payment Failed", "refunded": "Refunded", "cancelled": "Cancelled"}}, "earnings": {"title": "My Earnings", "withdraw": "Apply for Withdrawal", "totalEarnings": "Total Earnings", "monthEarnings": "Monthly Earnings", "availableBalance": "Available Balance", "totalEarningsDesc": "Historical cumulative total earnings", "monthEarningsDesc": "Total earnings for current natural month", "availableBalanceDesc": "Withdrawable balance", "tabs": {"earnings": "Earnings Details", "balance": "Balance History", "withdrawals": "Withdrawal Records"}, "earningsTable": {"videoTitle": "Video Title", "orderAmount": "Order Amount", "myEarnings": "My Earnings", "earningTime": "Earning Time", "videoDeleted": "Video Deleted"}, "balanceTable": {"type": "Type", "amount": "Amount", "balance": "Balance", "time": "Time"}, "withdrawalTable": {"applyTime": "Apply Time", "amount": "Amount", "status": "Status", "wallet": "Wallet Address", "processTime": "Process Time", "noRecords": "No withdrawal records"}}, "notification": {"title": "Notification Center", "noNotifications": "No notifications", "markAllRead": "Mark all as read", "markAllReadAction": "Mark all as read", "types": {"video_approved": "Your video has been approved!", "video_rejected": "Your video was not approved", "video_uploaded": "Video uploaded successfully", "follow": "New follower", "like": "Someone liked your video", "comment": "Someone commented on your video", "system": "System notification", "membership": "Membership notification", "payment": "Payment notification"}, "messages": {"videoApproved": "Your video \"{{title}}\" has been successfully published.", "videoRejected": "Your video \"{{title}}\" was not approved. Reason: {{reason}}", "videoUploaded": "Your video \"{{title}}\" has been uploaded successfully and is under review.", "newFollower": "{{username}} followed you", "videoLiked": "{{username}} liked your video \"{{title}}\"", "videoCommented": "{{username}} commented on your video \"{{title}}\": {{comment}}", "systemMessage": "{{message}}", "membershipExpiring": "Your membership will expire in {{days}} days", "paymentSuccess": "Payment successful, amount: ${{amount}}"}}, "playlist": {"sync": "Sync Playlist", "syncing": "Syncing...", "syncSuccess": "Sync successful", "syncError": "Sync failed"}, "theme": {"light": "Light Mode", "dark": "Dark Mode", "system": "Follow System", "lightDesc": "Classic white theme", "darkDesc": "Eye-friendly dark theme", "systemDesc": "Auto switch light/dark mode", "neon": "Neon Theme", "neonDesc": "Cool neon effects", "ocean": "Ocean Theme", "oceanDesc": "Peaceful ocean style", "forest": "Forest Theme", "forestDesc": "Natural green tones", "sunset": "Sunset Theme", "sunsetDesc": "Warm orange tones", "rainbow": "Rainbow Theme", "rainbowDesc": "Colorful gradient effects", "basicThemes": "Basic Themes", "coolThemes": "Cool Themes", "switchTheme": "Switch Theme", "preview": "Preview Theme", "previewTitle": "Theme Preview", "previewDescription": "Select your favorite theme style, click to preview the effect", "current": "Current", "previewing": "Previewing", "backgroundColor": "Background Color", "primaryColor": "Primary Color", "accentColor": "Accent Color", "sampleInterface": "Sample Interface", "button": "<PERSON><PERSON>", "cardContentSample": "Card Content Sample", "reset": "Reset", "applyTheme": "Apply Theme"}, "language": {"title": "Language", "select": "Select Language"}, "page": {"home": {"title": "Recommended Videos", "categories": {"all": "All", "movie": "Movies", "tv": "TV Shows", "documentary": "Documentary", "variety": "Variety", "education": "Education"}, "sort": {"latest": "Latest", "popular": "Popular", "recommended": "Recommended"}, "loading": "Loading...", "loadMore": "Load More", "fetchCategoriesError": "Failed to fetch categories", "upNext": "Up Next"}, "profile": {"title": "Personal Center", "subtitle": "Manage your personal information and account settings", "personalInfo": "Personal Information", "myWallet": "My Wallet", "accountSecurity": "Account Security", "themeSettings": "Theme Settings", "currentBalance": "Current Balance", "rechargeAmount": "Recharge Amount", "customAmount": "Enter custom amount", "autoRecharge": "Auto Recharge", "manualSubmit": "Manual Submit Application", "submitting": "Submitting...", "creatingOrder": "Creating Order...", "rechargeNote": "Auto recharge is instant. Manual applications will be reviewed by administrators who will complete your order manually.", "emailVerification": "Email Verification", "emailVerified": "Verified", "twoFactorAuth": "Two-Factor Authentication", "twoFactorNotEnabled": "Not Enabled", "passwordStrength": "Password Strength", "passwordMedium": "Medium", "modify": "Modify", "interfaceTheme": "Interface Theme", "selectThemeStyle": "Choose your preferred interface style", "passwordChangeDialog": "Change Password Dialog"}, "upload": {"title": "Upload Your Work", "workInfo": "Work Information", "fileUpload": "File Upload", "selectFile": "Select video or audio file", "maxSize": "Max 500MB", "chooseFile": "Choose <PERSON>", "visibility": "Visibility", "public": "Public", "memberOnly": "Member Only", "paid": "Paid Content", "workDescription": "Briefly introduce your work (optional)"}, "recharge": {"title": "Recharge", "subtitle": "Top up your account balance", "autoRechargeNote": "Auto recharge is processed immediately. After manual application submission, administrators will review your recharge request and manually complete the order for you.", "manualApplication": "Manual Application", "autoRecharge": "Auto Recharge"}, "personalInfo": {"title": "Personal Information", "username": "Username", "email": "Email", "editProfile": "Edit Profile"}, "myMoney": {"title": "My Wallet", "viewBalance": "View your account balance and recharge."}, "accountSafety": {"title": "Account Security", "emailVerification": "Email Verification", "ensureAccountSecurity": "Ensure account security", "verified": "Verified", "twoFactorAuth": "Two-Factor Authentication", "addExtraProtection": "Add extra protection to your account", "notEnabled": "Not Enabled", "passwordStrength": "Password Strength", "updatePasswordRegularly": "Update password regularly", "medium": "Medium"}, "themeConfig": {"title": "Theme Settings", "interfaceTheme": "Interface Theme", "choosePreferredStyle": "Choose your preferred interface style"}}, "upgrade": {"upgradeToVip": "Upgrade to Premium Member", "renewMembership": "Renew Membership", "upgradeDescription": "Unlock all exclusive content, enjoy ad-free and HD viewing experience.", "renewDescription": "Extend your membership validity and continue enjoying exclusive benefits.", "selectPaymentMethod": "Select Payment Method", "creditCard": "Credit Card", "balance": "Balance", "payNow": "Pay Now", "selectPlanFirst": "Please select a membership plan first", "selectPaymentMethodFirst": "Please select a payment method", "subscribePlan": "Subscribe to membership plan", "paymentSuccess": "Payment Successful!", "subscriptionSuccess": "You have successfully subscribed to the membership plan.", "operationFailed": "Operation Failed", "unknownError": "An unknown error occurred.", "createOrderError": "Failed to create order", "copied": "<PERSON>pied", "addressCopied": "Payment address copied to clipboard.", "fetchPlansError": "Failed to fetch membership plans", "fetchCreemPlansError": "Failed to fetch Creem membership plans or data format error", "loadPaymentMethodsError": "Unable to load online payment methods, please try again later.", "networkError": "Network request failed, please try again later"}}