const BaseModel = require('../BaseModel');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');

class MembershipPlan extends BaseModel {
  constructor() {
    super('membership_plans');
  }

  // 创建会员计划
  async createPlan(planData) {
    const {
      name,
      description = null,
      price,
      original_price = null,
      discount_until = null,
      duration_days,
      features = [],
      maxVideoUploads = null,
      maxStorageGb = null,
      priority,
      isActive = true
    } = planData;

    // 如果未提供priority，则根据价格自动计算
    const calculatedPriority = priority === undefined ? Math.floor(parseFloat(price)) : priority;

    // 检查计划名称是否已存在
    const existingPlan = await this.findOne({ name });
    if (existingPlan) {
      throw new AppError('计划名称已存在', 409, 'PLAN_NAME_EXISTS');
    }

    // 格式化 discount_until
    let formattedDiscountUntil = null;
    if (discount_until) {
      try {
        const date = new Date(discount_until);
        // 检查日期是否有效
        if (!isNaN(date.getTime())) {
          // 格式化为 YYYY-MM-DD
          formattedDiscountUntil = date.toISOString().split('T')[0];
        }
      } catch (e) {
        logger.warn(`无效的日期格式: ${discount_until}`, e);
        // 如果格式无效，则保持为null，避免数据库错误
      }
    }

    const plan = await this.create({
      name,
      description,
      price,
      original_price: original_price || price,
      discount_until: formattedDiscountUntil,
      duration_days: duration_days,
      features: JSON.stringify(features),
      max_video_uploads: maxVideoUploads,
      max_storage_gb: maxStorageGb,
      priority: calculatedPriority,
      is_active: true
    });

    logger.info(`会员计划创建成功: ${plan.id}`, { name, price });
    return plan;
  }

  // 获取所有活跃的会员计划
  async getActivePlans(onlyActive = true) {
    let sql = `
      SELECT 
        mp.id,
        mp.name,
        mp.description,
        mp.price,
        mp.original_price,
        mp.discount_until,
        mp.duration_days,
        mp.features,
        mp.is_active,
        COUNT(m.id) as subscriber_count
      FROM 
        membership_plans mp
      LEFT JOIN 
        memberships m ON mp.id = m.plan_id AND m.status = 'active'
    `;

    const params = [];
    if (onlyActive) {
      sql += ' WHERE mp.is_active = ?';
      params.push(true);
    }

    sql += ' GROUP BY mp.id ORDER BY mp.priority ASC';

    const plans = await this.query(sql, params);

    return plans.map(plan => {
      // 解析特性，增加健壮性
      if (plan.features) {
        if (typeof plan.features === 'string') {
        try {
          plan.features = JSON.parse(plan.features);
        } catch (error) {
            // 如果解析失败，且内容不是一个看似数组的字符串，则将其视为一个单元素的数组
            if (!plan.features.startsWith('[')) {
              plan.features = [plan.features];
            } else {
          plan.features = [];
            }
          }
        }
        // 如果它已经是数组(或对象)，则保持原样
      } else {
        plan.features = [];
      }
      return plan;
    });
  }

  // 获取计划详情
  async getPlanDetails(planId) {
    const plan = await this.findById(planId);
    if (!plan) {
      throw new AppError('会员计划不存在', 404, 'PLAN_NOT_FOUND');
    }

    // 解析特性，增加健壮性
    if (plan.features) {
      if (typeof plan.features === 'string') {
      try {
        plan.features = JSON.parse(plan.features);
      } catch (error) {
          // 如果解析失败，且内容不是一个看似数组的字符串，则将其视为一个单元素的数组
          if (!plan.features.startsWith('[')) {
            plan.features = [plan.features];
          } else {
        plan.features = [];
          }
        }
      }
      // 如果它已经是数组(或对象)，则保持原样
    } else {
      plan.features = [];
    }

    // 获取计划统计
    const stats = await this.getPlanStats(planId);
    plan.stats = stats;

    return plan;
  }

  // 更新会员计划
  async updatePlan(planId, updateData) {
    const allowedFields = [
      'name', 'description', 'price', 'original_price', 'discount_until', 'duration_days', 
      'features', 'max_video_uploads', 'max_storage_gb', 
      'priority', 'is_active'
    ];
    
    // 手动将驼峰命名映射到下划线命名
    const mappedData = {
      ...updateData,
      original_price: updateData.originalPrice !== undefined ? updateData.originalPrice : updateData.original_price,
      discount_until: updateData.discountUntil !== undefined ? updateData.discountUntil : updateData.discount_until,
      duration_days: updateData.durationDays !== undefined ? updateData.durationDays : updateData.duration_days,
      max_video_uploads: updateData.maxVideoUploads !== undefined ? updateData.maxVideoUploads : updateData.max_video_uploads,
      max_storage_gb: updateData.maxStorageGb !== undefined ? updateData.maxStorageGb : updateData.max_storage_gb,
      is_active: updateData.isActive !== undefined ? updateData.isActive : updateData.is_active,
    };

    // 移除驼峰式命名的键，避免它们被错误地处理
    delete mappedData.originalPrice;
    delete mappedData.discountUntil;
    delete mappedData.durationDays;
    delete mappedData.maxVideoUploads;
    delete mappedData.maxStorageGb;

    // 格式化 discount_until
    if (mappedData.discount_until) {
        try {
            const date = new Date(mappedData.discount_until);
            if (!isNaN(date.getTime())) {
                mappedData.discount_until = date.toISOString().split('T')[0];
            } else {
                // 如果格式无效，则设置为null或根据业务逻辑处理
                mappedData.discount_until = null; 
            }
        } catch (e) {
            logger.warn(`更新时无效的日期格式: ${mappedData.discount_until}`, e);
            mappedData.discount_until = null;
        }
    }

    // 如果价格被更新，并且没有手动提供priority，则自动更新priority
    if (mappedData.price !== undefined && mappedData.priority === undefined) {
      mappedData.priority = Math.floor(parseFloat(mappedData.price));
    }

    const filteredData = {};
    for (const field of allowedFields) {
      if (mappedData[field] !== undefined) {
        if (field === 'features') {
          let featuresArray = [];
          if (Array.isArray(mappedData[field])) {
            featuresArray = mappedData[field];
          } else if (typeof mappedData[field] === 'string') {
            featuresArray = mappedData[field].split('\\n').map(s => s.trim()).filter(Boolean);
          }
          filteredData[field] = JSON.stringify(featuresArray);
        } else {
          filteredData[field] = mappedData[field];
        }
      }
    }

    if (Object.keys(filteredData).length === 0) {
      throw new AppError('没有可更新的字段', 400, 'NO_UPDATE_FIELDS');
    }

    // 检查名称重复
    if (filteredData.name) {
      const existingPlan = await this.findOne({ name: filteredData.name });
      if (existingPlan && existingPlan.id !== parseInt(planId, 10)) {
        throw new AppError('计划名称已存在', 409, 'PLAN_NAME_EXISTS');
      }
    }

    const result = await this.update(planId, filteredData);
    if (!result) {
      throw new AppError('会员计划不存在或更新失败', 404, 'PLAN_NOT_FOUND');
    }

    logger.info(`会员计划更新成功: ${planId}`);
    
    // 返回经过正确解析的完整计划详情
    return await this.getPlanDetails(planId);
  }

  // 删除会员计划
  async deletePlan(planId) {
    // 检查是否有用户正在使用此计划
    const activeUsers = await this.query(`
      SELECT COUNT(*) as count 
      FROM memberships 
      WHERE plan_id = ? AND status = 'active' AND end_date > NOW()
    `, [planId]);

    if (activeUsers[0].count > 0) {
      throw new AppError('该计划仍有活跃用户，无法删除', 400, 'PLAN_IN_USE');
    }

    // 硬删除：直接从数据库删除记录
    const result = await this.delete(planId);
    if (!result) {
      throw new AppError('会员计划不存在或删除失败', 404, 'PLAN_NOT_FOUND');
    }

    logger.info(`会员计划已被永久删除: ${planId}`);
    return true;
  }

  // 获取计划统计信息
  async getPlanStats(planId) {
    const sql = `
      SELECT 
        COUNT(*) as total_subscriptions,
        COUNT(CASE WHEN status = 'active' AND end_date > NOW() THEN 1 END) as active_subscriptions,
        COUNT(CASE WHEN m.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_subscriptions_30d,
        SUM(CASE WHEN status != 'cancelled' THEN mp.price ELSE 0 END) as total_revenue
      FROM memberships m
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.plan_id = ?
    `;

    const result = await this.query(sql, [planId]);
    return result[0];
  }

  // 获取所有计划的统计
  async getAllPlansStats() {
    const sql = `
      SELECT 
        mp.id,
        mp.name,
        mp.price,
        COUNT(m.id) as total_subscriptions,
        COUNT(CASE WHEN m.status = 'active' AND m.end_date > NOW() THEN 1 END) as active_subscriptions,
        SUM(CASE WHEN m.status != 'cancelled' THEN mp.price ELSE 0 END) as total_revenue
      FROM membership_plans mp
      LEFT JOIN memberships m ON mp.id = m.plan_id
      WHERE mp.is_active = true
      GROUP BY mp.id
      ORDER BY mp.priority ASC
    `;

    return await this.query(sql);
  }

  // 比较计划功能
  async comparePlans(planIds) {
    if (!planIds || planIds.length === 0) {
      throw new AppError('请提供要比较的计划ID', 400, 'INVALID_PLAN_IDS');
    }

    const placeholders = planIds.map(() => '?').join(',');
    const sql = `
      SELECT * FROM membership_plans 
      WHERE id IN (${placeholders}) AND is_active = true
      ORDER BY priority ASC
    `;

    const plans = await this.query(sql, planIds);
    
    return plans.map(plan => {
      // 解析特性
      if (plan.features) {
        try {
          plan.features = JSON.parse(plan.features);
        } catch (error) {
          plan.features = [];
        }
      }
      return plan;
    });
  }

  // 推荐计划
  async getRecommendedPlan(userProfile = {}) {
    const {
      currentPlan = null,
      usage = {},
      budget = null
    } = userProfile;

    // 获取所有活跃计划
    const plans = await this.getActivePlans();
    
    if (plans.length === 0) {
      return null;
    }

    // 简单的推荐逻辑
    let recommendedPlan = plans[0]; // 默认推荐第一个计划

    // 如果用户有预算限制
    if (budget) {
      const affordablePlans = plans.filter(plan => plan.price <= budget);
      if (affordablePlans.length > 0) {
        recommendedPlan = affordablePlans[affordablePlans.length - 1]; // 预算内最贵的
      }
    }

    // 如果用户有当前计划，推荐升级
    if (currentPlan) {
      const currentPlanIndex = plans.findIndex(plan => plan.id === currentPlan);
      if (currentPlanIndex >= 0 && currentPlanIndex < plans.length - 1) {
        recommendedPlan = plans[currentPlanIndex + 1];
      }
    }

    return recommendedPlan;
  }

  // 计算升级/降级价格差
  async calculatePriceChange(fromPlanId, toPlanId, remainingDays = 0) {
    const [fromPlan, toPlan] = await Promise.all([
      this.findById(fromPlanId),
      this.findById(toPlanId)
    ]);

    if (!fromPlan || !toPlan) {
      throw new AppError('计划不存在', 404, 'PLAN_NOT_FOUND');
    }

    // 计算剩余价值
    const dailyRateFrom = fromPlan.price / fromPlan.duration_days;
    const remainingValue = dailyRateFrom * remainingDays;

    // 计算价格差
    const priceDifference = toPlan.price - remainingValue;

    return {
      fromPlan: {
        id: fromPlan.id,
        name: fromPlan.name,
        price: fromPlan.price
      },
      toPlan: {
        id: toPlan.id,
        name: toPlan.name,
        price: toPlan.price
      },
      remainingValue: Math.round(remainingValue * 100) / 100,
      priceDifference: Math.round(priceDifference * 100) / 100,
      isUpgrade: priceDifference > 0,
      remainingDays
    };
  }

  // 获取热门计划
  async getPopularPlans(limit = 3) {
    const sql = `
      SELECT 
        mp.*,
        COUNT(m.id) as subscription_count
      FROM membership_plans mp
      LEFT JOIN memberships m ON mp.id = m.plan_id 
        AND m.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      WHERE mp.is_active = true
      GROUP BY mp.id
      ORDER BY subscription_count DESC, mp.priority ASC
      LIMIT ?
    `;

    const plans = await this.query(sql, [limit]);
    
    return plans.map(plan => {
      // 解析特性
      if (plan.features) {
        try {
          plan.features = JSON.parse(plan.features);
        } catch (error) {
          plan.features = [];
        }
      }
      return plan;
    });
  }

  // 验证计划配置
  validatePlanConfig(planData) {
    const errors = [];

    if (!planData.name || planData.name.trim().length === 0) {
      errors.push('计划名称不能为空');
    }

    if (!planData.price || planData.price < 0) {
      errors.push('价格必须大于等于0');
    }

    if (!planData.duration_days || planData.duration_days <= 0) {
      errors.push('持续天数必须大于0');
    }

    if (planData.maxVideoUploads && planData.maxVideoUploads < 0) {
      errors.push('最大视频上传数不能为负数');
    }

    if (planData.maxStorageGb && planData.maxStorageGb < 0) {
      errors.push('最大存储空间不能为负数');
    }

    return errors;
  }

  // 创建默认计划（简化版：统一权益，不同价格）
  async createDefaultPlans() {
    const defaultPlans = [
      {
        name: '月度会员',
        description: '享受所有会员特权',
        price: 19.99,
        duration_days: 30,
        features: ['watch_member_videos', 'watch_vip_videos', 'ad_free', 'download_videos', 'priority_support', 'high_quality'],
        maxVideoUploads: 100,
        maxStorageGb: 50,
        priority: 1
      },
      {
        name: '年度会员',
        description: '享受所有会员特权（年度优惠）',
        price: 199.99,
        duration_days: 365,
        features: ['watch_member_videos', 'watch_vip_videos', 'ad_free', 'download_videos', 'priority_support', 'high_quality'],
        maxVideoUploads: 100,
        maxStorageGb: 50,
        priority: 2
      }
    ];

    const createdPlans = [];
    for (const planData of defaultPlans) {
      try {
        const existingPlan = await this.findOne({ name: planData.name });
        if (!existingPlan) {
          const plan = await this.createPlan(planData);
          createdPlans.push(plan);
        }
      } catch (error) {
        logger.error(`创建默认计划失败: ${planData.name}`, error);
      }
    }

    return createdPlans;
  }
}

module.exports = new MembershipPlan();
