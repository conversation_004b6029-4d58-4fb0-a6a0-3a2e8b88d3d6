const NodeCache = require('node-cache');
const logger = require('./logger');
const pm2 = require('pm2');

class CacheManager {
  constructor(options = {}) {
    this.cache = new NodeCache({
      stdTTL: options.stdTTL || 600, // 默认10分钟
      checkperiod: options.checkperiod || 120, // 2分钟检查一次
      useClones: false,
      ...options
    });

    // 连接到PM2的消息总线
    pm2.launchBus((err, pm2_bus) => {
      if (err) {
        return logger.error('[Cache IPC] Error launching PM2 bus:', err);
      }
      logger.info('[Cache IPC] PM2 message bus connected.');
      
      // 监听通用的'process:msg'事件
      pm2_bus.on('process:msg', (packet) => {
        // 检查消息格式和我们自定义的topic
        if (!packet || !packet.data || packet.data.topic !== 'cache:clear') {
          return;
  }

        // 确保消息不是自己发给自己的 (虽然PM2通常会处理，但双重保险)
        if (packet.process.pm_id === process.env.pm_id) {
          return;
    }

        const { key, pattern } = packet.data.data;

        if (key) {
          logger.info(`[Cache IPC] Process ${process.pid} received clear command for key: ${key}`);
          this.localDel(key);
    }
        if (pattern) {
          logger.info(`[Cache IPC] Process ${process.pid} received clear command for pattern: ${pattern}`);
          this.localDelPattern(pattern);
        }
      });
    });
  }

  // 生成缓存键
  generateKey(...args) {
    return args.join(':');
  }

  // --- 原始删除方法，仅操作本地缓存 ---
  localDel(key) {
    return this.cache.del(key);
  }

  localDelPattern(pattern) {
    const keys = this.cache.keys();
    const regex = new RegExp(pattern.replace(/:\*/g, '.*'));
    const keysToDelete = keys.filter(k => regex.test(k));
    if (keysToDelete.length > 0) {
      this.cache.del(keysToDelete);
    }
  }

  // 广播消息到总线
  broadcast(topic, data) {
    // 检查是否在PM2的worker进程中
    if (typeof process.send === 'function') {
      process.send({
        type: 'process:msg',
        data: {
          topic,
          data // 实际的数据负载
        }
      });
    }
  }

  // --- 公共的、跨进程安全的删除方法 ---
  del(key) {
    this.localDel(key);
    this.broadcast('cache:clear', { key });
  }

  async delPattern(pattern) {
    this.localDelPattern(pattern);
    this.broadcast('cache:clear', { pattern });
  }

  // --- 其他方法保持不变 ---
  get(key) {
    return this.cache.get(key);
    }

  set(key, value, ttl) {
    return this.cache.set(key, value, ttl);
    }

  getTtl(key) {
    return this.cache.getTtl(key);
    }

  keys() {
    return this.cache.keys();
  }

  flushAll() {
    this.localDelPattern('.*');
    this.broadcast('cache:clear', { pattern: '.*' });
    }
  }

const CACHE_KEYS = {
  USER: 'user',
  VIDEO: 'video',
  COMMENT: 'comment',
  CATEGORY: 'category',
  SEARCH: 'search',
  MEMBERSHIP: 'membership',
  ADMIN: 'admin',
  PAYMENT: 'payment'
};

const cache = new CacheManager({ stdTTL: 600, checkperiod: 120 });

module.exports = {
  cache,
  CACHE_KEYS
};
