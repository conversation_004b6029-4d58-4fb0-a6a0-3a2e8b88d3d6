const PlaylistService = require('../services/playlistService');
const PlaylistUtils = require('../utils/playlistUtils');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

class PlaylistController {
  // 获取用户的所有播放列表
  static async getUserPlaylists(req, res, next) {
    try {
      const userId = req.user.id;
      const { includeItems } = req.query;

      const playlists = await PlaylistService.getUserPlaylists(userId, includeItems);
      const formattedPlaylists = playlists.map(PlaylistUtils.formatPlaylistData);

      res.json({
        success: true,
        message: '获取播放列表成功',
        data: {
          playlists: formattedPlaylists,
          total: formattedPlaylists.length
        }
      });
    } catch (error) {
      logger.error('获取用户播放列表失败:', error);
      next(error);
    }
  }

  // 获取播放列表详情
  static async getPlaylistById(req, res, next) {
    try {
      const { playlistId } = req.params;
      const userId = req.user?.id; // 可能是游客访问公开播放列表

      const playlist = await PlaylistService.getPlaylistById(playlistId, userId);
      const formattedPlaylist = PlaylistUtils.formatPlaylistData(playlist);

      // 检查权限
      const permissions = PlaylistUtils.checkPlaylistPermissions(playlist, userId);
      if (!permissions.canView) {
        throw new AppError('无权限访问此播放列表', 403, 'ACCESS_DENIED');
      }

      res.json({
        success: true,
        message: '获取播放列表详情成功',
        data: {
          playlist: formattedPlaylist,
          permissions
        }
      });
    } catch (error) {
      logger.error('获取播放列表详情失败:', error);
      next(error);
    }
  }

  // 创建播放列表
  static async createPlaylist(req, res, next) {
    try {
      const userId = req.user.id;
      const playlistData = req.body;

      const playlist = await PlaylistService.createPlaylist(userId, playlistData);
      const formattedPlaylist = PlaylistUtils.formatPlaylistData(playlist);

      PlaylistUtils.logPlaylistAction('CREATE', userId, { playlistId: playlist.id, name: playlist.name });

      res.status(201).json({
        success: true,
        message: '创建播放列表成功',
        data: {
          playlist: formattedPlaylist
        }
      });
    } catch (error) {
      logger.error('创建播放列表失败:', error);
      next(error);
    }
  }

  // 更新播放列表
  static async updatePlaylist(req, res, next) {
    try {
      const { playlistId } = req.params;
      const userId = req.user.id;
      const updates = req.body;

      const playlist = await PlaylistService.updatePlaylist(playlistId, userId, updates);
      const formattedPlaylist = PlaylistUtils.formatPlaylistData(playlist);

      PlaylistUtils.logPlaylistAction('UPDATE', userId, { playlistId, updates });

      res.json({
        success: true,
        message: '更新播放列表成功',
        data: {
          playlist: formattedPlaylist
        }
      });
    } catch (error) {
      logger.error('更新播放列表失败:', error);
      next(error);
    }
  }

  // 删除播放列表
  static async deletePlaylist(req, res, next) {
    try {
      const { playlistId } = req.params;
      const userId = req.user.id;

      await PlaylistService.deletePlaylist(playlistId, userId);

      PlaylistUtils.logPlaylistAction('DELETE', userId, { playlistId });

      res.json({
        success: true,
        message: '删除播放列表成功'
      });
    } catch (error) {
      logger.error('删除播放列表失败:', error);
      next(error);
    }
  }

  // 添加视频到播放列表
  static async addVideoToPlaylist(req, res, next) {
    try {
      const { playlistId } = req.params;
      const { videoId, position } = req.body;
      const userId = req.user.id;

      await PlaylistService.addVideoToPlaylist(playlistId, videoId, userId, position);

      PlaylistUtils.logPlaylistAction('ADD_VIDEO', userId, { playlistId, videoId, position });

      res.json({
        success: true,
        message: '添加视频到播放列表成功'
      });
    } catch (error) {
      logger.error('添加视频到播放列表失败:', error);
      next(error);
    }
  }

  // 从播放列表移除视频
  static async removeVideoFromPlaylist(req, res, next) {
    try {
      const { playlistId, videoId } = req.params;
      const userId = req.user.id;

      const result = await PlaylistService.removeVideoFromPlaylist(playlistId, videoId, userId);

      if (!result) {
        throw new AppError('视频不在播放列表中', 404, 'VIDEO_NOT_IN_PLAYLIST');
      }

      PlaylistUtils.logPlaylistAction('REMOVE_VIDEO', userId, { playlistId, videoId });

      res.json({
        success: true,
        message: '从播放列表移除视频成功'
      });
    } catch (error) {
      logger.error('从播放列表移除视频失败:', error);
      next(error);
    }
  }

  // 批量添加视频到播放列表
  static async addMultipleVideos(req, res, next) {
    try {
      const { playlistId } = req.params;
      const { videoIds } = req.body;
      const userId = req.user.id;

      const results = await PlaylistService.addMultipleVideos(playlistId, videoIds, userId);

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      PlaylistUtils.logPlaylistAction('BATCH_ADD_VIDEOS', userId, { 
        playlistId, 
        totalVideos: videoIds.length,
        successCount,
        failureCount
      });

      res.json({
        success: true,
        message: `批量添加完成，成功 ${successCount} 个，失败 ${failureCount} 个`,
        data: {
          results,
          summary: {
            total: videoIds.length,
            success: successCount,
            failure: failureCount
          }
        }
      });
    } catch (error) {
      logger.error('批量添加视频失败:', error);
      next(error);
    }
  }

  // 重新排序播放列表项目
  static async reorderPlaylistItems(req, res, next) {
    try {
      const { playlistId } = req.params;
      const { items } = req.body;
      const userId = req.user.id;

      await PlaylistService.reorderPlaylistItems(playlistId, userId, items);

      PlaylistUtils.logPlaylistAction('REORDER_ITEMS', userId, { playlistId, itemCount: items.length });

      res.json({
        success: true,
        message: '重新排序播放列表项目成功'
      });
    } catch (error) {
      logger.error('重新排序播放列表项目失败:', error);
      next(error);
    }
  }

  // 复制播放列表
  static async duplicatePlaylist(req, res, next) {
    try {
      const { playlistId } = req.params;
      const { newName } = req.body;
      const userId = req.user.id;

      const newPlaylist = await PlaylistService.duplicatePlaylist(playlistId, userId, newName);
      const formattedPlaylist = PlaylistUtils.formatPlaylistData(newPlaylist);

      PlaylistUtils.logPlaylistAction('DUPLICATE', userId, { 
        sourcePlaylistId: playlistId, 
        newPlaylistId: newPlaylist.id 
      });

      res.status(201).json({
        success: true,
        message: '复制播放列表成功',
        data: {
          playlist: formattedPlaylist
        }
      });
    } catch (error) {
      logger.error('复制播放列表失败:', error);
      next(error);
    }
  }

  // 获取播放列表统计信息
  static async getPlaylistStats(req, res, next) {
    try {
      const userId = req.user.id;

      const stats = await PlaylistService.getPlaylistStats(userId);

      res.json({
        success: true,
        message: '获取播放列表统计信息成功',
        data: {
          stats
        }
      });
    } catch (error) {
      logger.error('获取播放列表统计信息失败:', error);
      next(error);
    }
  }

  // 记录播放历史
  static async recordPlayHistory(req, res, next) {
    try {
      const userId = req.user.id;
      const { videoId, watchDuration, videoDuration, completed } = req.body;

      await PlaylistService.recordPlayHistory(userId, videoId, watchDuration, videoDuration, completed);

      res.json({
        success: true,
        message: '记录播放历史成功'
      });
    } catch (error) {
      logger.error('记录播放历史失败:', error);
      next(error);
    }
  }

  // 获取播放历史
  static async getPlayHistory(req, res, next) {
    try {
      const userId = req.user.id;
      const { limit, offset } = req.query;

      const history = await PlaylistService.getPlayHistory(userId, limit, offset);
      const formattedHistory = history.map(PlaylistUtils.formatPlayHistoryData);

      res.json({
        success: true,
        message: '获取播放历史成功',
        data: {
          history: formattedHistory,
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset),
            total: formattedHistory.length
          }
        }
      });
    } catch (error) {
      logger.error('获取播放历史失败:', error);
      next(error);
    }
  }

  // 获取最近播放
  static async getRecentlyPlayed(req, res, next) {
    try {
      const userId = req.user.id;
      const { limit } = req.query;

      const recentVideos = await PlaylistService.getRecentlyPlayed(userId, limit || 10);
      const formattedVideos = recentVideos.map(video => ({
        videoId: video.video_id,
        title: video.title,
        thumbnail: video.thumbnail_url,
        duration: video.duration,
        mediaType: video.media_type,
        playedAt: video.played_at
      }));

      res.json({
        success: true,
        message: '获取最近播放成功',
        data: {
          videos: formattedVideos
        }
      });
    } catch (error) {
      logger.error('获取最近播放失败:', error);
      next(error);
    }
  }

  // 清除播放历史
  static async clearPlayHistory(req, res, next) {
    try {
      const userId = req.user.id;
      const { videoId } = req.body; // 可选，清除特定视频的历史

      const result = await PlaylistService.clearPlayHistory(userId, videoId);

      PlaylistUtils.logPlaylistAction('CLEAR_HISTORY', userId, { videoId, deletedCount: result.deletedCount });

      res.json({
        success: true,
        message: videoId ? '清除指定视频播放历史成功' : '清除所有播放历史成功',
        data: result
      });
    } catch (error) {
      logger.error('清除播放历史失败:', error);
      next(error);
    }
  }

  // 获取播放统计
  static async getPlayStats(req, res, next) {
    try {
      const userId = req.user.id;

      const stats = await PlaylistService.getPlayStats(userId);

      res.json({
        success: true,
        message: '获取播放统计成功',
        data: {
          stats: {
            ...stats,
            totalWatchTimeFormatted: PlaylistUtils.formatDuration(stats.total_watch_time || 0),
            avgWatchTimeFormatted: PlaylistUtils.formatDuration(stats.avg_watch_time || 0)
          }
        }
      });
    } catch (error) {
      logger.error('获取播放统计失败:', error);
      next(error);
    }
  }
}

module.exports = PlaylistController;
