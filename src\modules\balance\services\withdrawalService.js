const connectionManager = require('../../../database/ConnectionManager');
const Withdrawal = require('../../../database/models/Withdrawal');
const User = require('../../../database/models/User');
const { AppError } = require('../../../middleware/errorHandler');
const { getPagination, getPagingData } = require('../../../utils/pagination');

class WithdrawalService {

  /**
   * 创建一个新的提现请求
   * @param {number} userId - 用户ID
   * @param {number} amount - 提现金额
   * @param {string} walletType - 钱包类型
   * @param {string} walletAddress - 钱包地址
   */
  async createWithdrawalRequest(userId, amount, walletType, walletAddress) {
    const withdrawalAmount = parseFloat(amount);
    if (isNaN(withdrawalAmount) || withdrawalAmount <= 0) {
      throw new AppError('无效的提现金额', 400, 'INVALID_AMOUNT');
    }

    return connectionManager.transaction(async (connection) => {
      const user = await User.findById(userId, 'FOR UPDATE', connection);
      if (!user) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      if (parseFloat(user.balance) < withdrawalAmount) {
        throw new AppError('余额不足', 400, 'INSUFFICIENT_FUNDS');
      }

      // 冻结用户余额
      const newBalance = parseFloat(user.balance) - withdrawalAmount;
      await User.update(userId, { balance: newBalance }, connection);

      const withdrawalData = {
        user_id: userId,
        amount: withdrawalAmount,
        wallet_type: walletType,
        wallet_address: walletAddress,
        status: 'pending',
      };
      
      const withdrawalId = await Withdrawal.create(withdrawalData, connection);
      const newWithdrawal = await Withdrawal.findById(withdrawalId, null, connection);

      return newWithdrawal;
    });
  }

  /**
   * 获取用户的提现历史记录
   * @param {number} userId - 用户ID
   * @param {object} options - 分页和过滤选项
   */
  async getWithdrawalHistory(userId, { page, limit, status }) {
    const { limit: queryLimit, offset } = getPagination(page, limit);
    
    let whereConditions = ['user_id = ?'];
    let queryParams = [userId];

    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }
    
    const whereClause = whereConditions.join(' AND ');

    const countQuery = `SELECT COUNT(*) as total FROM withdrawals WHERE ${whereClause}`;
    const totalResult = await connectionManager.executeQuery(countQuery, queryParams);
    const totalItems = totalResult[0]?.total || 0;

    const dataQuery = `
      SELECT *
      FROM withdrawals
      WHERE ${whereClause}
      ORDER BY requested_at DESC
      LIMIT ${parseInt(queryLimit, 10)} OFFSET ${parseInt(offset, 10)}
    `;
    const withdrawals = await connectionManager.executeQuery(dataQuery, queryParams);
    
    return getPagingData({ data: withdrawals, totalItems, page, limit });
  }
}

module.exports = new WithdrawalService(); 