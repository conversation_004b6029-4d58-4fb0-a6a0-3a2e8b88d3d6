import React, { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import clsx from 'clsx';
import { toggleLike } from '@/lib/api';

interface LikeButtonProps {
  videoId: string | number;
  initialLiked?: boolean;
  initialLikeCount?: number;
  onLikeChange?: (liked: boolean, newCount: number) => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showText?: boolean;
  showCount?: boolean;
  requireAuthAction?: (action: () => void) => void;
}

const LikeButton: React.FC<LikeButtonProps> = ({
  videoId,
  initialLiked = false,
  initialLikeCount = 0,
  onLikeChange,
  size = 'md',
  className = '',
  showText = true,
  showCount = true,
  requireAuthAction
}) => {
  const [isLiked, setIsLiked] = useState(initialLiked);
  const [likeCount, setLikeCount] = useState(initialLikeCount);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 更新初始状态
  useEffect(() => {
    setIsLiked(initialLiked);
  }, [initialLiked]);

  useEffect(() => {
    setLikeCount(initialLikeCount);
  }, [initialLikeCount]);

  // 图标大小映射
  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 20
  };

  // 按钮样式映射
  const buttonStyles = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const handleLike = async (e: React.MouseEvent) => {
    e.stopPropagation(); // 防止触发父元素的点击事件
    
    const likeAction = async () => {
    if (isLoading) return;
    setIsLoading(true);
    setError(null);
    try {
      const response = await toggleLike(videoId, 'video');
      const newLikedState = response.data.data.liked;
      const newCount = newLikedState ? likeCount + 1 : Math.max(0, likeCount - 1);
      setIsLiked(newLikedState);
      setLikeCount(newCount);
      if (onLikeChange) {
        onLikeChange(newLikedState, newCount);
      }
    } catch (error: any) {
      console.error('点赞操作失败:', error);
      setError('操作失败，请稍后重试');
      setTimeout(() => setError(null), 3000);
    } finally {
      setIsLoading(false);
      }
    };

    if (requireAuthAction) {
      requireAuthAction(likeAction);
    } else {
      likeAction();
    }
  };

  return (
    <div className="relative">
      <button
        onClick={handleLike}
        disabled={isLoading}
        className={clsx(
          'flex items-center space-x-1 rounded-md transition-all duration-200',
          buttonStyles[size],
          isLiked
            ? 'bg-red-50 text-red-600 hover:bg-red-100 border border-red-200'
            : 'bg-gray-50 text-gray-600 hover:bg-gray-100 border border-gray-200',
          isLoading && 'opacity-50 cursor-not-allowed',
          'hover:scale-105 active:scale-95',
          className
        )}
        title={isLiked ? '取消点赞' : '点赞'}
      >
        <Heart
          size={iconSizes[size]}
          className={clsx(
            'transition-all duration-200',
            isLiked ? 'fill-current text-red-500' : 'text-gray-500',
            isLoading && 'animate-pulse'
          )}
        />
        {showText && (
          <span className={clsx(
            'transition-colors duration-200',
            isLiked ? 'text-red-600' : 'text-gray-600'
          )}>
            {isLoading ? '...' : (isLiked ? '已点赞' : '点赞')}
          </span>
        )}
        {showCount && (
          <span className={clsx(
            'transition-colors duration-200',
            isLiked ? 'text-red-600' : 'text-gray-600'
          )}>
            {likeCount}
          </span>
        )}
      </button>
      
      {/* 错误提示 */}
      {error && (
        <div className="absolute top-full left-0 mt-1 px-2 py-1 bg-red-100 text-red-600 text-xs rounded shadow-lg whitespace-nowrap z-10">
          {error}
        </div>
      )}
    </div>
  );
};

export default LikeButton;
