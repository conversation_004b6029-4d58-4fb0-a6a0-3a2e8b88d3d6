const BaseModel = require('../BaseModel');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');

class CreemPlan extends BaseModel {
  constructor() {
    super('creem_plans');
  }

  /**
   * 创建一个新的Creem计划
   * @param {object} planData - 计划数据
   * @returns {Promise<object>} 创建的计划对象
   */
  async createPlan(planData) {
    const { name, price, currency, description = null, creem_product_id = null, duration_days = null, tax_category = null } = planData;

    if (!name || !price || !currency) {
      throw new AppError('名称、价格和货币是必填项', 400);
    }

    const existingPlan = await this.findOne({ name });
    if (existingPlan) {
      throw new AppError('具有此名称的Creem计划已存在', 409, 'CREEM_PLAN_NAME_EXISTS');
    }

    const plan = await this.create({
      name,
      price,
      currency,
      description,
      creem_product_id,
      duration_days,
      tax_category,
      is_active: true,
    });

    logger.info(`Creem计划创建成功: ${plan.id}`, { name, price });
    return plan;
  }

  /**
   * 更新一个Creem计划
   * @param {number} planId - 计划ID
   * @param {object} updateData - 要更新的数据
   * @returns {Promise<object>} 更新后的计划对象
   */
  async updatePlan(planId, updateData) {
    const allowedFields = [
      'name', 'description', 'price', 'currency', 'creem_product_id', 'is_active', 'duration_days', 'tax_category'
    ];

    const filteredData = {};
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    if (Object.keys(filteredData).length === 0) {
      throw new AppError('没有可更新的字段', 400, 'NO_UPDATE_FIELDS');
    }

    if (filteredData.name) {
      const existingPlan = await this.findOne({ name: filteredData.name });
      if (existingPlan && existingPlan.id !== parseInt(planId, 10)) {
        throw new AppError('具有此名称的Creem计划已存在', 409, 'CREEM_PLAN_NAME_EXISTS');
      }
    }

    const result = await this.update(planId, filteredData);
    if (!result) {
      throw new AppError('Creem计划不存在或更新失败', 404, 'CREEM_PLAN_NOT_FOUND');
    }

    logger.info(`Creem计划更新成功: ${planId}`);
    return this.findById(planId);
  }

  /**
   * 删除一个Creem计划
   * @param {number} planId - 计划ID
   * @returns {Promise<boolean>}
   */
  async deletePlan(planId) {
    const result = await this.delete(planId);
    if (!result) {
      throw new AppError('Creem计划不存在或删除失败', 404, 'CREEM_PLAN_NOT_FOUND');
    }

    logger.info(`Creem计划已被删除: ${planId}`);
    return true;
  }
}

module.exports = new CreemPlan(); 