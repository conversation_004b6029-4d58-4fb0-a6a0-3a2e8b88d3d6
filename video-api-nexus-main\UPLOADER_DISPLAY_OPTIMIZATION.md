# 视频上传者显示优化

## 问题描述

在原始实现中，视频界面存在以下问题：
1. 管理员上传的视频显示"系统管理员"作为上传者
2. 用户上传的视频显示"匿名作者"而不是实际用户名

## 优化目标

1. **管理员上传的视频**：不显示上传者信息（隐藏上传者字段）
2. **用户上传的视频**：显示实际的用户名称而不是"匿名作者"

## 修改的组件

### 1. VideoCard.tsx (主要视频卡片组件)
- **文件路径**: `src/components/VideoCard.tsx`
- **修改内容**:
  - 添加上传者显示逻辑判断
  - 当上传者昵称为"系统管理员"时，隐藏上传者信息
  - 显示实际用户昵称而不是"匿名作者"

### 2. 用户VideoCard.tsx (用户版本视频卡片)
- **文件路径**: `src/components/user/VideoCard.tsx`
- **修改内容**:
  - 添加国际化支持
  - 实现相同的上传者显示逻辑
  - 支持紧凑模式和默认模式

### 3. VideoPlayerWithFeatures.tsx (视频播放器)
- **文件路径**: `src/components/video/VideoPlayerWithFeatures.tsx`
- **修改内容**:
  - 在创作者信息区域应用相同逻辑
  - 系统管理员上传的视频不显示创作者信息

### 4. VideoPlayer.tsx (用户版本视频播放器)
- **文件路径**: `src/components/user/VideoPlayer.tsx`
- **修改内容**:
  - 在频道信息区域应用相同逻辑
  - 隐藏系统管理员的频道信息

### 5. AdminVideos.tsx (管理员视频管理)
- **文件路径**: `src/components/admin/AdminVideos.tsx`
- **修改内容**:
  - 在管理员界面中，系统视频显示为"系统视频"而不是"by 系统管理员"

### 6. UserApp.tsx (用户应用主页)
- **文件路径**: `src/pages/UserApp.tsx`
- **修改内容**:
  - 在观看历史部分应用相同逻辑

## 核心逻辑

```typescript
// 优化上传者显示逻辑
const uploaderNickname = video.uploader?.nickname;
const isSystemAdmin = uploaderNickname === t('videoInfo.systemAdmin');
const author = uploaderNickname || t('videoInfo.anonymousAuthor');

// 条件渲染
{!isSystemAdmin && (
  <p className="text-xs sm:text-sm text-muted-foreground truncate">
    {author}
  </p>
)}
```

## 国际化支持

使用了现有的国际化键值：
- `videoInfo.systemAdmin`: "系统管理员"
- `videoInfo.anonymousAuthor`: "匿名作者"

## 测试覆盖

创建了完整的测试套件：
- **VideoCard测试**: `src/components/__tests__/VideoCard.test.tsx`
- **用户VideoCard测试**: `src/components/user/__tests__/VideoCard.test.tsx`

测试覆盖场景：
1. 系统管理员上传的视频应隐藏上传者信息
2. 普通用户上传的视频应显示用户名
3. 没有上传者信息时应显示"匿名作者"
4. 紧凑模式下的正确显示

## 测试结果

```
✓ VideoCard 上传者显示优化 (4)
  ✓ 应该隐藏系统管理员上传的视频的上传者信息
  ✓ 应该显示普通用户上传的视频的用户名
  ✓ 应该显示匿名作者当没有上传者信息时
  ✓ 应该显示视频标题和其他信息

✓ 用户VideoCard 上传者显示优化 (6)
  ✓ 应该隐藏系统管理员上传的视频的上传者信息
  ✓ 应该显示普通用户上传的视频的用户名
  ✓ 应该显示匿名作者当没有用户名时
  ✓ 应该显示视频标题和其他信息
  ✓ 在紧凑模式下也应该正确显示上传者信息
  ✓ 在紧凑模式下应该隐藏系统管理员

Test Files: 2 passed (2)
Tests: 10 passed (10)
```

## 影响范围

这些修改影响了所有显示视频上传者信息的界面：
- 主页视频列表
- 搜索结果
- 分类页面
- 用户个人页面
- 视频播放页面
- 管理员视频管理页面
- 收藏列表
- 观看历史

## 向后兼容性

所有修改都保持了向后兼容性：
- 不影响现有的API接口
- 不改变数据结构
- 仅优化前端显示逻辑
- 保持了所有现有功能
