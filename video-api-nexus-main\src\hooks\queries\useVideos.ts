import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getVideos, getVideoDetails, getPopularVideos, getRecommendedVideos, searchVideos } from '@/lib/api';

// 查询键工厂
export const videoKeys = {
  all: ['videos'] as const,
  lists: () => [...videoKeys.all, 'list'] as const,
  list: (filters: any) => [...videoKeys.lists(), filters] as const,
  details: () => [...videoKeys.all, 'detail'] as const,
  detail: (id: string | number) => [...videoKeys.details(), id] as const,
  popular: (timeRange?: string, limit?: number) => [...videoKeys.all, 'popular', timeRange, limit] as const,
  recommended: () => [...videoKeys.all, 'recommended'] as const,
  search: (query: string) => [...videoKeys.all, 'search', query] as const,
};

// 获取视频列表
export const useVideos = (params: any = {}) => {
  return useQuery({
    queryKey: videoKeys.list(params),
    queryFn: () => getVideos(params),
    select: (data) => {
      // 安全地访问数据，处理不同的响应结构
      return data?.data?.data?.data || data?.data?.data || data?.data || [];
    },
    staleTime: 2 * 60 * 1000, // 2分钟
    cacheTime: 5 * 60 * 1000, // 5分钟
  });
};

// 获取视频详情
export const useVideoDetail = (id: string | number) => {
  return useQuery({
    queryKey: videoKeys.detail(id),
    queryFn: () => getVideoDetails(id),
    select: (data) => {
      return data?.data?.data?.video || data?.data?.video || data?.data;
    },
    enabled: !!id, // 只有当id存在时才执行查询
    staleTime: 5 * 60 * 1000, // 5分钟
    cacheTime: 10 * 60 * 1000, // 10分钟
  });
};

// 获取热门视频
export const usePopularVideos = (timeRange = '7d', limit = 10) => {
  return useQuery({
    queryKey: videoKeys.popular(timeRange, limit),
    queryFn: () => getPopularVideos(),
    select: (data) => {
      return data?.data?.data?.videos || data?.data?.videos || data?.data || [];
    },
    staleTime: 10 * 60 * 1000, // 10分钟
    cacheTime: 15 * 60 * 1000, // 15分钟
  });
};

// 获取推荐视频
export const useRecommendedVideos = () => {
  return useQuery({
    queryKey: videoKeys.recommended(),
    queryFn: () => getRecommendedVideos(),
    select: (data) => {
      return data?.data?.data?.videos || data?.data?.videos || data?.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5分钟
    cacheTime: 10 * 60 * 1000, // 10分钟
  });
};

// 搜索视频
export const useSearchVideos = (query: string) => {
  return useQuery({
    queryKey: videoKeys.search(query),
    queryFn: () => searchVideos(query),
    select: (data) => {
      return data?.data?.data?.videos || data?.data?.videos || data?.data || [];
    },
    enabled: !!query && query.length > 0, // 只有当查询字符串存在时才执行
    staleTime: 3 * 60 * 1000, // 3分钟
    cacheTime: 5 * 60 * 1000, // 5分钟
  });
};

// 预取视频详情（用于悬停预加载等场景）
export const usePrefetchVideoDetail = () => {
  const queryClient = useQueryClient();
  
  return (id: string | number) => {
    queryClient.prefetchQuery({
      queryKey: videoKeys.detail(id),
      queryFn: () => getVideoDetails(id),
      staleTime: 5 * 60 * 1000,
    });
  };
};
