const connectionManager = require('../../../database/ConnectionManager');
const User = require('../../../database/models/User');
const { getPagination, getPagingData } = require('../../../utils/pagination');

class EarningsService {

  async getSummary(userId) {
    // 1. Get current balance from users table
    const user = await User.findById(userId);
    const balance = user ? user.balance : 0;

    // 2. Get total earnings from earnings table
    const totalEarningsQuery = `
      SELECT SUM(creator_earning) as total
      FROM earnings
      WHERE creator_id = ?
    `;
    const totalResult = await connectionManager.executeQuery(totalEarningsQuery, [userId]);
    const totalEarnings = totalResult[0]?.total || 0;

    // 3. Get this month's earnings
    const monthEarningsQuery = `
      SELECT SUM(creator_earning) as total
      FROM earnings
      WHERE creator_id = ? AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())
    `;
    const monthResult = await connectionManager.executeQuery(monthEarningsQuery, [userId]);
    const monthEarnings = monthResult[0]?.total || 0;

    return {
      totalEarnings: parseFloat(totalEarnings),
      monthEarnings: parseFloat(monthEarnings),
      balance: parseFloat(balance)
    };
  }

  async getDetails(userId, { page, limit }) {
    const { limit: queryLimit, offset } = getPagination(page, limit);

    // Get total count
    const countQuery = 'SELECT COUNT(*) as total FROM earnings WHERE creator_id = ?';
    const totalResult = await connectionManager.executeQuery(countQuery, [userId]);
    const totalItems = totalResult[0]?.total || 0;

    // Get paginated data
    const dataQuery = `
      SELECT e.id, e.total_amount, e.creator_earning, e.created_at, v.title as video_title
      FROM earnings e
      LEFT JOIN videos v ON e.video_id = v.id
      WHERE e.creator_id = ?
      ORDER BY e.created_at DESC
      LIMIT ${parseInt(queryLimit, 10)} OFFSET ${parseInt(offset, 10)}
    `;
    const earnings = await connectionManager.executeQuery(dataQuery, [userId]);

    return getPagingData({ data: earnings, totalItems, page, limit });
  }

  async getBalanceHistory(userId, { page, limit }) {
    const { limit: queryLimit, offset } = getPagination(page, limit);

    // Get total count
    const countQuery = 'SELECT COUNT(*) as total FROM balance_logs WHERE user_id = ?';
    const totalResult = await connectionManager.executeQuery(countQuery, [userId]);
    const totalItems = totalResult[0]?.total || 0;

    // Get paginated data
    const dataQuery = `
      SELECT id, amount, balance_after, type, description, created_at
      FROM balance_logs
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ${parseInt(queryLimit, 10)} OFFSET ${parseInt(offset, 10)}
    `;
    const logs = await connectionManager.executeQuery(dataQuery, [userId]);

    return getPagingData({ data: logs, totalItems, page, limit });
  }

}

module.exports = new EarningsService(); 