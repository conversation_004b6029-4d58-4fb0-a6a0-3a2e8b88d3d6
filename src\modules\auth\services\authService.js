const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const { generateToken, generateRefreshToken } = require('../../../middleware/auth');
const logger = require('../../../utils/logger');
const User = require('../../../database/models/User');
const emailService = require('../../../services/emailService');
const settingService = require('../../../services/settingService');

class AuthService {
  // 生成邀请码
  async generateInvitationCode(inviterId) {
    const code = crypto.randomBytes(4).toString('hex').toUpperCase();
    const invitationKey = cache.generateKey(CACHE_KEYS.USER, 'invitation', code);
    
    // 存储邀请码信息
    const invitationData = {
      inviterId,
      code,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天有效
    };
    
    await cache.set(invitationKey, invitationData, 7 * 24 * 3600);
    
    logger.info(`生成邀请码: ${code}`, { inviterId });
    return code;
  }
  
  // 验证邀请码
  async validateInvitationCode(code) {
    const invitationKey = cache.generateKey(CACHE_KEYS.USER, 'invitation', code);
    const invitationData = await cache.get(invitationKey);
    
    if (!invitationData) {
      return { valid: false, message: '邀请码不存在或已过期' };
    }
    
    if (new Date() > new Date(invitationData.expiresAt)) {
      await cache.del(invitationKey);
      return { valid: false, message: '邀请码已过期' };
    }
    
    return { valid: true, data: invitationData };
  }
  
  // 使用邀请码
  async useInvitationCode(code, inviteeId) {
    const validation = await this.validateInvitationCode(code);
    if (!validation.valid) {
      return validation;
    }
    
    const invitationKey = cache.generateKey(CACHE_KEYS.USER, 'invitation', code);
    const invitationData = validation.data;
    
    // 记录邀请关系
    // 这里应该在数据库中记录邀请关系，暂时简化
    logger.info(`邀请码使用成功: ${code}`, {
      inviterId: invitationData.inviterId,
      inviteeId
    });
    
    // 删除已使用的邀请码
    await cache.del(invitationKey);
    
    return { valid: true, inviterId: invitationData.inviterId };
  }
  
  // 检查登录尝试次数
  async checkLoginAttempts(identifier, maxAttempts = null, windowMs = 15 * 60 * 1000) {
    // 从系统配置获取最大登录尝试次数
    if (maxAttempts === null) {
      const configMaxAttempts = await settingService.getSetting('security.maxLoginAttempts');
      maxAttempts = configMaxAttempts ? parseInt(configMaxAttempts) : 5;
    }

    const attemptsKey = cache.generateKey(CACHE_KEYS.USER, 'login_attempts', identifier);
    const attempts = await cache.get(attemptsKey) || [];

    const now = Date.now();
    const recentAttempts = attempts.filter(time => now - time < windowMs);

    if (recentAttempts.length >= maxAttempts) {
      const oldestAttempt = Math.min(...recentAttempts);
      const remainingTime = Math.ceil((oldestAttempt + windowMs - now) / 1000 / 60);

      return {
        allowed: false,
        remainingTime,
        message: `登录尝试次数过多，请 ${remainingTime} 分钟后再试`
      };
    }

    return { allowed: true };
  }

  // 记录登录尝试
  async recordLoginAttempt(identifier) {
    const attemptsKey = cache.generateKey(CACHE_KEYS.USER, 'login_attempts', identifier);
    const attempts = await cache.get(attemptsKey) || [];
    const now = Date.now();

    attempts.push(now);

    // 保留最近24小时的尝试记录
    const recentAttempts = attempts.filter(time => now - time < 24 * 60 * 60 * 1000);

    await cache.set(attemptsKey, recentAttempts, 24 * 3600);
    logger.info(`记录登录尝试: ${identifier}, 总计: ${recentAttempts.length}`);
  }

  // 清除登录尝试记录
  async clearLoginAttempts(identifier) {
    const attemptsKey = cache.generateKey(CACHE_KEYS.USER, 'login_attempts', identifier);
    await cache.del(attemptsKey);
    logger.info(`清除登录尝试记录: ${identifier}`);
  }
  
  // 记录登录尝试
  async recordLoginAttempt(identifier, success = false) {
    const attemptsKey = cache.generateKey(CACHE_KEYS.USER, 'login_attempts', identifier);
    
    if (success) {
      // 登录成功，清除尝试记录
      await cache.del(attemptsKey);
    } else {
      // 登录失败，记录尝试
      const attempts = await cache.get(attemptsKey) || [];
      attempts.push(Date.now());
      await cache.set(attemptsKey, attempts, 15 * 60); // 15分钟
    }
  }
  
  // 生成双因子认证密钥
  async generateTwoFactorSecret(userId, userEmail) {
    const secret = speakeasy.generateSecret({
      name: `视频平台 (${userEmail})`,
      issuer: '视频平台',
      length: 32
    });

    const secretKey = cache.generateKey(CACHE_KEYS.USER, '2fa_secret', userId);

    // 存储密钥（临时，等待用户确认）
    await cache.set(secretKey, {
      secret: secret.base32,
      tempSecret: true,
      createdAt: new Date()
    }, 24 * 3600); // 24小时有效

    // 生成二维码
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

    logger.info(`生成双因子认证密钥: ${userId}`);

    return {
      secret: secret.base32,
      qrCode: qrCodeUrl,
      manualEntryKey: secret.base32
    };
  }

  // 确认并启用双因子认证
  async enableTwoFactor(userId, verificationCode) {
    const secretKey = cache.generateKey(CACHE_KEYS.USER, '2fa_secret', userId);
    const secretData = await cache.get(secretKey);

    if (!secretData || !secretData.tempSecret) {
      throw new Error('未找到待确认的双因子认证密钥');
    }

    // 验证用户输入的验证码
    const verified = speakeasy.totp.verify({
      secret: secretData.secret,
      encoding: 'base32',
      token: verificationCode,
      window: 2 // 允许时间偏差
    });

    if (!verified) {
      throw new Error('验证码错误，请重试');
    }

    // 验证成功，正式启用双因子认证
    await cache.set(secretKey, {
      secret: secretData.secret,
      tempSecret: false,
      enabledAt: new Date()
    }, 365 * 24 * 3600); // 1年有效

    // 更新用户数据库记录
    await User.updateUser(userId, {
      two_factor_enabled: true,
      two_factor_secret: secretData.secret
    });

    logger.info(`启用双因子认证: ${userId}`);

    return { success: true, message: '双因子认证已启用' };
  }

  // 验证双因子认证码
  async verifyTwoFactorCode(userId, code) {
    try {
      // 从缓存获取密钥
      const secretKey = cache.generateKey(CACHE_KEYS.USER, '2fa_secret', userId);
      let secretData = await cache.get(secretKey);

      // 如果缓存中没有，从数据库获取
      if (!secretData) {
        const user = await User.findById(userId);
        if (!user || !user.two_factor_enabled || !user.two_factor_secret) {
          return { valid: false, message: '未启用双因子认证' };
        }

        secretData = { secret: user.two_factor_secret };
        // 重新缓存
        await cache.set(secretKey, secretData, 365 * 24 * 3600);
      }

      // 验证TOTP码
      const verified = speakeasy.totp.verify({
        secret: secretData.secret,
        encoding: 'base32',
        token: code,
        window: 2 // 允许±2个时间窗口的偏差（约60秒）
      });

      if (verified) {
        logger.info(`双因子认证验证成功: ${userId}`);
        return { valid: true };
      } else {
        logger.warn(`双因子认证验证失败: ${userId}`, { code });
        return { valid: false, message: '验证码错误或已过期' };
      }

    } catch (error) {
      logger.error(`双因子认证验证异常: ${userId}`, error);
      return { valid: false, message: '验证过程出现错误' };
    }
  }

  // 禁用双因子认证
  async disableTwoFactor(userId, verificationCode) {
    // 先验证当前的双因子认证码
    const verification = await this.verifyTwoFactorCode(userId, verificationCode);
    if (!verification.valid) {
      throw new Error('验证码错误，无法禁用双因子认证');
    }

    // 删除缓存中的密钥
    const secretKey = cache.generateKey(CACHE_KEYS.USER, '2fa_secret', userId);
    await cache.del(secretKey);

    // 更新数据库
    await User.updateUser(userId, {
      two_factor_enabled: false,
      two_factor_secret: null
    });

    logger.info(`禁用双因子认证: ${userId}`);

    return { success: true, message: '双因子认证已禁用' };
  }

  // 生成备用恢复码
  async generateBackupCodes(userId) {
    const codes = [];
    for (let i = 0; i < 10; i++) {
      codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
    }

    const backupKey = cache.generateKey(CACHE_KEYS.USER, '2fa_backup', userId);
    await cache.set(backupKey, {
      codes: codes.map(code => ({ code, used: false })),
      generatedAt: new Date()
    }, 365 * 24 * 3600); // 1年有效

    logger.info(`生成双因子认证备用码: ${userId}`);

    return codes;
  }

  // 使用备用恢复码
  async useBackupCode(userId, code) {
    const backupKey = cache.generateKey(CACHE_KEYS.USER, '2fa_backup', userId);
    const backupData = await cache.get(backupKey);

    if (!backupData) {
      return { valid: false, message: '未找到备用恢复码' };
    }

    const codeEntry = backupData.codes.find(entry =>
      entry.code === code.toUpperCase() && !entry.used
    );

    if (!codeEntry) {
      return { valid: false, message: '备用恢复码无效或已使用' };
    }

    // 标记为已使用
    codeEntry.used = true;
    codeEntry.usedAt = new Date();

    await cache.set(backupKey, backupData, 365 * 24 * 3600);

    logger.info(`使用双因子认证备用码: ${userId}`, { code });

    return { valid: true, message: '备用恢复码验证成功' };
  }
  
  // 生成API密钥
  async generateApiKey(userId, name, permissions = []) {
    const apiKey = `vp_${crypto.randomBytes(32).toString('hex')}`;
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');
    
    const keyData = {
      userId,
      name,
      permissions,
      hashedKey,
      createdAt: new Date(),
      lastUsed: null,
      isActive: true
    };
    
    const keyStorageKey = cache.generateKey(CACHE_KEYS.USER, 'api_key', hashedKey);
    await cache.set(keyStorageKey, keyData, 365 * 24 * 3600); // 1年有效
    
    logger.info(`生成API密钥: ${name}`, { userId });
    return apiKey;
  }
  
  // 验证API密钥
  async validateApiKey(apiKey) {
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');
    const keyStorageKey = cache.generateKey(CACHE_KEYS.USER, 'api_key', hashedKey);
    const keyData = await cache.get(keyStorageKey);
    
    if (!keyData || !keyData.isActive) {
      return { valid: false, message: 'API密钥无效' };
    }
    
    // 更新最后使用时间
    keyData.lastUsed = new Date();
    await cache.set(keyStorageKey, keyData, 365 * 24 * 3600);
    
    return { valid: true, data: keyData };
  }
  
  // 撤销API密钥
  async revokeApiKey(apiKey) {
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');
    const keyStorageKey = cache.generateKey(CACHE_KEYS.USER, 'api_key', hashedKey);
    
    await cache.del(keyStorageKey);
    logger.info(`撤销API密钥: ${hashedKey.substring(0, 8)}...`);
  }
  
  // 检查会话有效性
  async validateSession(sessionId, userId) {
    const sessionKey = cache.generateKey(CACHE_KEYS.SESSION, sessionId);
    const sessionData = await cache.get(sessionKey);
    
    if (!sessionData || sessionData.userId !== userId) {
      return { valid: false, message: '会话无效' };
    }
    
    if (new Date() > new Date(sessionData.expiresAt)) {
      await cache.del(sessionKey);
      return { valid: false, message: '会话已过期' };
    }
    
    return { valid: true, data: sessionData };
  }
  
  // 创建会话
  async createSession(userId, deviceInfo = {}) {
    // 从系统配置获取会话超时时间（秒）
    const configTimeout = await settingService.getSetting('security.sessionTimeout');
    const sessionTimeoutSeconds = configTimeout ? parseInt(configTimeout) : 30 * 24 * 3600; // 默认30天
    const sessionTimeoutMs = sessionTimeoutSeconds * 1000;

    const sessionId = crypto.randomBytes(32).toString('hex');
    const sessionData = {
      userId,
      sessionId,
      deviceInfo,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + sessionTimeoutMs),
      lastActivity: new Date()
    };

    const sessionKey = cache.generateKey(CACHE_KEYS.SESSION, sessionId);
    await cache.set(sessionKey, sessionData, sessionTimeoutSeconds);

    logger.info(`创建会话: ${sessionId}, 超时时间: ${sessionTimeoutSeconds}秒`);

    return sessionId;
  }
  
  // 销毁会话
  async destroySession(sessionId) {
    const sessionKey = cache.generateKey(CACHE_KEYS.SESSION, sessionId);
    await cache.del(sessionKey);
  }
  
  // 获取用户所有会话
  async getUserSessions(userId) {
    const pattern = `${CACHE_KEYS.SESSION}:*`;
    const keys = await cache.redis.keys(pattern);
    const sessions = [];
    
    for (const key of keys) {
      const sessionData = await cache.get(key);
      if (sessionData && sessionData.userId === userId) {
        sessions.push(sessionData);
      }
    }
    
    return sessions;
  }
  
  // 销毁用户所有会话
  async destroyAllUserSessions(userId) {
    const sessions = await this.getUserSessions(userId);
    
    for (const session of sessions) {
      await this.destroySession(session.sessionId);
    }
    
    logger.info(`销毁用户所有会话: ${userId}`, { count: sessions.length });
  }
  
  // 检查密码强度
  async checkPasswordStrength(password) {
    // 从系统配置获取密码要求
    const configMinLength = await settingService.getSetting('security.passwordMinLength');
    const configRequireStrong = await settingService.getSetting('security.requireStrongPassword');

    const minLength = configMinLength ? parseInt(configMinLength) : 8;
    const requireStrong = configRequireStrong === 'true' || configRequireStrong === true;

    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    let score = 0;
    let feedback = [];

    // 基本长度检查
    if (password.length >= minLength) {
      score++;
    } else {
      feedback.push(`密码长度至少${minLength}位`);
    }

    // 如果要求强密码，则检查各种字符类型
    if (requireStrong) {
      if (hasUpperCase) score++;
      else feedback.push('包含大写字母');

      if (hasLowerCase) score++;
      else feedback.push('包含小写字母');

      if (hasNumbers) score++;
      else feedback.push('包含数字');

      if (hasSpecialChar) score++;
      else feedback.push('包含特殊字符');

      const strength = score < 3 ? 'weak' : score < 4 ? 'medium' : 'strong';

      return {
        score,
        strength,
        feedback,
        isValid: score >= 3, // 强密码要求至少3项
        requireStrong: true
      };
    } else {
      // 不要求强密码，只检查长度
      return {
        score: password.length >= minLength ? 5 : 0,
        strength: password.length >= minLength ? 'sufficient' : 'weak',
        feedback,
        isValid: password.length >= minLength,
        requireStrong: false
      };
    }
  }
  
  // 生成安全的随机密码
  generateSecurePassword(length = 12) {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = uppercase + lowercase + numbers + symbols;
    let password = '';
    
    // 确保包含每种类型的字符
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // 填充剩余长度
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // 打乱字符顺序
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }
}

module.exports = new AuthService();
