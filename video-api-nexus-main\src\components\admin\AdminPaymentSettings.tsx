import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from '@/hooks/use-toast';
import { adminApi } from '@/services/adminApi';
import { Loader2 } from 'lucide-react';

// 定义支付通道的类型
interface PaymentGateway {
  id: number;
  name: string;
  key: string;
  enabled: boolean;
  config: Record<string, any>;
}

const AdminPaymentSettings = () => {
  const [gateways, setGateways] = useState<PaymentGateway[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null);
  const [currentConfig, setCurrentConfig] = useState<Record<string, any>>({});
  const { toast } = useToast();

  useEffect(() => {
    fetchGateways();
  }, []);

  const fetchGateways = async () => {
    setLoading(true);
    try {
      const response = await adminApi.getPaymentGateways();
      if (response.data && Array.isArray(response.data.data)) {
        setGateways(response.data.data);
      } else {
        setGateways([]);
        console.error("API apyload for payment gateways is not an array:", response.data);
        toast({
          title: '数据格式错误',
          description: '从服务器获取的支付通道数据格式不正确。',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: '加载失败',
        description: '无法获取支付通道列表，请稍后重试。',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = async (gatewayId: number, enabled: boolean) => {
    try {
      await adminApi.updatePaymentGateway(gatewayId, { enabled });
      setGateways(gateways.map(g => g.id === gatewayId ? { ...g, enabled } : g));
      toast({
        title: '更新成功',
        description: `支付通道已${enabled ? '启用' : '禁用'}。`,
      });
    } catch (error) {
      toast({
        title: '更新失败',
        description: '无法更新支付通道状态。',
        variant: 'destructive',
      });
    }
  };

  const openConfigDialog = (gateway: PaymentGateway) => {
    setSelectedGateway(gateway);
    setCurrentConfig(gateway.config);
    setIsDialogOpen(true);
  };

  const handleConfigChange = (key: string, value: string) => {
    setCurrentConfig({ ...currentConfig, [key]: value });
  };

  const handleSaveConfig = async () => {
    if (!selectedGateway) return;

    try {
      const updatedGateway = await adminApi.updatePaymentGateway(selectedGateway.id, { config: currentConfig });
      setGateways(gateways.map(g => g.id === selectedGateway.id ? { ...g, config: updatedGateway.data.config } : g));
      setIsDialogOpen(false);
      toast({
        title: '配置已保存',
        description: `${selectedGateway.name} 的配置已成功更新。`,
      });
    } catch (error) {
      toast({
        title: '保存失败',
        description: '无法保存支付通道配置。',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">支付设置</h1>
      <p className="text-muted-foreground">
        管理和配置您的支付通道。在此处启用、禁用和编辑每个支付方式的API密钥和其他设置。
      </p>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {gateways.map((gateway) => (
          <Card key={gateway.id}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {gateway.name}
                <Switch
                  checked={gateway.enabled}
                  onCheckedChange={(checked) => handleToggle(gateway.id, checked)}
                />
              </CardTitle>
              <CardDescription>
                管理 {gateway.name} 支付通道的设置。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => openConfigDialog(gateway)}>配置</Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑 {selectedGateway?.name} 配置</DialogTitle>
            <DialogDescription>
              请在此处输入正确的API凭据。不正确的凭据将导致支付失败。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {selectedGateway && Object.entries(currentConfig).map(([key, value]) => (
              <div className="grid grid-cols-4 items-center gap-4" key={key}>
                <Label htmlFor={key} className="text-right">
                  {key}
                </Label>
                <Input
                  id={key}
                  value={value}
                  onChange={(e) => handleConfigChange(key, e.target.value)}
                  className="col-span-3"
                />
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>取消</Button>
            <Button onClick={handleSaveConfig}>保存配置</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminPaymentSettings; 