#!/usr/bin/env node

/**
 * 管理员账户管理工具
 * 用于创建、重置、管理管理员账户
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const readline = require('readline');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 工具函数：询问用户输入
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

// 工具函数：询问密码（隐藏输入）
function askPassword(question) {
  return new Promise((resolve) => {
    process.stdout.write(question);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    process.stdin.on('data', function(char) {
      char = char + '';
      
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdout.write('\n');
          resolve(password);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    });
  });
}

// 验证邮箱格式
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证密码强度
function isValidPassword(password) {
  // 至少8位，包含大小写字母、数字和特殊字符
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

// 创建管理员账户
async function createAdmin() {
  console.log('\n🔧 创建管理员账户\n');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    // 获取管理员信息
    const email = await askQuestion('请输入管理员邮箱: ');
    if (!isValidEmail(email)) {
      console.log('❌ 邮箱格式无效');
      return;
    }
    
    // 检查邮箱是否已存在
    const [existingUser] = await connection.execute(
      'SELECT id, role FROM users WHERE email = ?',
      [email]
    );
    
    if (existingUser.length > 0) {
      console.log('❌ 该邮箱已被使用');
      await connection.end();
      return;
    }
    
    const username = await askQuestion('请输入管理员用户名: ');
    if (!username) {
      console.log('❌ 用户名不能为空');
      return;
    }
    
    // 检查用户名是否已存在
    const [existingUsername] = await connection.execute(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );
    
    if (existingUsername.length > 0) {
      console.log('❌ 该用户名已被使用');
      await connection.end();
      return;
    }
    
    const password = await askPassword('请输入管理员密码: ');
    if (!isValidPassword(password)) {
      console.log('\n❌ 密码强度不足！密码必须至少8位，包含大小写字母、数字和特殊字符');
      await connection.end();
      return;
    }
    
    const confirmPassword = await askPassword('请确认管理员密码: ');
    if (password !== confirmPassword) {
      console.log('\n❌ 两次输入的密码不一致');
      await connection.end();
      return;
    }
    
    const nickname = await askQuestion('请输入管理员昵称 (可选): ') || '系统管理员';
    
    // 加密密码
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    // 插入管理员账户
    const [result] = await connection.execute(
      `INSERT INTO users (
        email, password, username, nickname, role, status, email_verified, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [email, hashedPassword, username, nickname, 'admin', 'active', true]
    );
    
    console.log('\n✅ 管理员账户创建成功！');
    console.log(`📧 邮箱: ${email}`);
    console.log(`👤 用户名: ${username}`);
    console.log(`🆔 用户ID: ${result.insertId}`);
    console.log(`💡 昵称: ${nickname}`);
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 创建管理员账户失败:', error.message);
  }
}

// 重置管理员密码
async function resetAdminPassword() {
  console.log('\n🔄 重置管理员密码\n');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    // 显示所有管理员账户
    const [admins] = await connection.execute(
      'SELECT id, email, username, nickname FROM users WHERE role = ?',
      ['admin']
    );
    
    if (admins.length === 0) {
      console.log('❌ 没有找到管理员账户');
      await connection.end();
      return;
    }
    
    console.log('📋 现有管理员账户:');
    admins.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.username} (${admin.email}) - ${admin.nickname}`);
    });
    
    const choice = await askQuestion('\n请选择要重置密码的管理员 (输入序号): ');
    const adminIndex = parseInt(choice) - 1;
    
    if (adminIndex < 0 || adminIndex >= admins.length) {
      console.log('❌ 无效的选择');
      await connection.end();
      return;
    }
    
    const selectedAdmin = admins[adminIndex];
    
    const newPassword = await askPassword('请输入新密码: ');
    if (!isValidPassword(newPassword)) {
      console.log('\n❌ 密码强度不足！密码必须至少8位，包含大小写字母、数字和特殊字符');
      await connection.end();
      return;
    }
    
    const confirmPassword = await askPassword('请确认新密码: ');
    if (newPassword !== confirmPassword) {
      console.log('\n❌ 两次输入的密码不一致');
      await connection.end();
      return;
    }
    
    // 加密新密码
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // 更新密码
    await connection.execute(
      'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?',
      [hashedPassword, selectedAdmin.id]
    );
    
    console.log(`\n✅ 管理员 ${selectedAdmin.username} 的密码重置成功！`);
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 重置密码失败:', error.message);
  }
}

// 列出所有管理员
async function listAdmins() {
  console.log('\n📋 管理员账户列表\n');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    const [admins] = await connection.execute(
      `SELECT id, email, username, nickname, status, email_verified, 
              created_at, last_login_at 
       FROM users WHERE role = ? 
       ORDER BY created_at ASC`,
      ['admin']
    );
    
    if (admins.length === 0) {
      console.log('❌ 没有找到管理员账户');
    } else {
      console.log(`找到 ${admins.length} 个管理员账户:\n`);
      
      admins.forEach((admin, index) => {
        console.log(`${index + 1}. 管理员信息:`);
        console.log(`   🆔 ID: ${admin.id}`);
        console.log(`   📧 邮箱: ${admin.email}`);
        console.log(`   👤 用户名: ${admin.username}`);
        console.log(`   💡 昵称: ${admin.nickname || '未设置'}`);
        console.log(`   📊 状态: ${admin.status}`);
        console.log(`   ✅ 邮箱验证: ${admin.email_verified ? '已验证' : '未验证'}`);
        console.log(`   📅 创建时间: ${admin.created_at}`);
        console.log(`   🕐 最后登录: ${admin.last_login_at || '从未登录'}`);
        console.log('');
      });
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 获取管理员列表失败:', error.message);
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
🔧 管理员账户管理工具

使用方法:
  node scripts/admin-manager.js <命令>

可用命令:
  create    创建新的管理员账户
  reset     重置管理员密码
  list      列出所有管理员账户
  help      显示此帮助信息

示例:
  node scripts/admin-manager.js create
  node scripts/admin-manager.js reset
  node scripts/admin-manager.js list

注意事项:
- 密码必须至少8位，包含大小写字母、数字和特殊字符
- 邮箱和用户名必须唯一
- 请妥善保管管理员账户信息
`);
}

// 主函数
async function main() {
  const command = process.argv[2];
  
  console.log('🔧 管理员账户管理工具');
  console.log('========================\n');
  
  try {
    switch (command) {
      case 'create':
        await createAdmin();
        break;
      case 'reset':
        await resetAdminPassword();
        break;
      case 'list':
        await listAdmins();
        break;
      case 'help':
      case '--help':
      case '-h':
        showHelp();
        break;
      default:
        console.log('❌ 未知命令，使用 help 查看可用命令');
        showHelp();
        break;
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
  } finally {
    rl.close();
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  createAdmin,
  resetAdminPassword,
  listAdmins
};
