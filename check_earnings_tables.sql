-- 检查收益相关表是否存在
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    CREATE_TIME
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('earnings', 'balance_logs')
ORDER BY TABLE_NAME;

-- 检查earnings表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'earnings'
ORDER BY ORDINAL_POSITION;

-- 检查balance_logs表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'balance_logs'
ORDER BY ORDINAL_POSITION;

-- 检查earnings表数据
SELECT COUNT(*) as earnings_count FROM earnings;

-- 检查balance_logs表数据
SELECT COUNT(*) as balance_logs_count FROM balance_logs;
