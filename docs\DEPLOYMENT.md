# 部署指南

本文档详细说明如何在不同环境中部署视频网站API框架。

## 🚀 生产环境部署

### 系统要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **内存**: 最少4GB，推荐8GB+
- **存储**: 最少50GB，推荐SSD

### 1. 服务器准备

#### Ubuntu/Debian
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装MySQL
sudo apt install mysql-server -y
sudo mysql_secure_installation

# 安装Redis
sudo apt install redis-server -y

# 安装PM2
sudo npm install -g pm2
```

#### CentOS/RHEL
```bash
# 更新系统
sudo yum update -y

# 安装Node.js
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 安装MySQL
sudo yum install mysql-server -y
sudo systemctl start mysqld
sudo mysql_secure_installation

# 安装Redis
sudo yum install redis -y
sudo systemctl start redis

# 安装PM2
sudo npm install -g pm2
```

### 2. 数据库配置

#### MySQL配置
```sql
-- 创建数据库
CREATE DATABASE video_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'video_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON video_platform.* TO 'video_user'@'localhost';
FLUSH PRIVILEGES;

-- 导入表结构
mysql -u video_user -p video_platform < database/schema.sql
```

#### 初始化数据库和管理员账户
```bash
# 初始化数据库（会自动创建默认管理员账户）
node scripts/db-manager.js init

# 或者重置数据库（清空所有数据并重新创建）
node scripts/db-manager.js reset
```

**默认管理员账户信息**：
- **邮箱**: `<EMAIL>`
- **用户名**: `admin`
- **密码**: `Admin123456!`

⚠️ **重要安全提醒**：
1. 请在首次登录后立即修改管理员密码
2. 可通过环境变量自定义管理员信息：
   ```bash
   ADMIN_EMAIL=<EMAIL>
   ADMIN_USERNAME=your_admin
   ADMIN_PASSWORD=YourSecurePassword123!
   ```

#### 管理员账户管理
```bash
# 创建新管理员账户
node scripts/admin-manager.js create

# 重置管理员密码
node scripts/admin-manager.js reset

# 查看所有管理员
node scripts/admin-manager.js list

# 紧急情况快速重置
node scripts/quick-admin-reset.js
```

#### Redis配置
```bash
# 编辑Redis配置
sudo nano /etc/redis/redis.conf

# 修改以下配置
bind 127.0.0.1
port 6379
requirepass your_redis_password
maxmemory 2gb
maxmemory-policy allkeys-lru

# 重启Redis
sudo systemctl restart redis
```

### 3. 应用部署

#### 下载和配置
```bash
# 克隆项目
git clone <repository-url> /opt/video-platform-api
cd /opt/video-platform-api

# 安装依赖
npm ci --production

# 创建环境配置
cp .env.example .env
nano .env
```

#### 环境变量配置
```env
# 生产环境配置
NODE_ENV=production
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=video_user
DB_PASSWORD=strong_password
DB_NAME=video_platform
DB_POOL_MIN=5
DB_POOL_MAX=20

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_very_long_and_secure_jwt_secret
JWT_REFRESH_SECRET=your_very_long_and_secure_refresh_secret
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# 文件上传配置
MAX_FILE_SIZE=500MB
# 使用相对路径，相对于项目根目录
UPLOAD_PATH=./uploads

# 安全配置
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
ALLOWED_ORIGINS=https://yourdomain.com

# 日志配置
LOG_LEVEL=info
# 使用相对路径，相对于项目根目录
LOG_FILE=./logs/app.log
```

#### 创建目录和权限
```bash
# 在项目根目录下创建必要目录
mkdir -p uploads/videos/original
mkdir -p uploads/videos/processed
mkdir -p uploads/audios/original
mkdir -p uploads/audios/processed
mkdir -p uploads/thumbnails
mkdir -p uploads/avatars
mkdir -p uploads/waveforms
mkdir -p logs

# 设置权限（Linux/macOS）
chmod 755 uploads
chmod 755 logs

# 如果部署到生产环境，可能需要调整所有者
# sudo chown -R www-data:www-data uploads
# sudo chown -R www-data:www-data logs
```

### 4. PM2配置

#### 创建PM2配置文件
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'video-platform-api',
    script: './app.js',
    // cwd: 使用当前项目目录，不指定绝对路径
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    // 使用相对路径存储日志
    error_file: './logs/error.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

#### 启动应用
```bash
# 启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME

# 查看状态
pm2 status
pm2 logs
```

### 5. Nginx反向代理

#### 安装Nginx
```bash
sudo apt install nginx -y
```

#### 配置Nginx
```nginx
# /etc/nginx/sites-available/video-platform-api
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL证书配置（请根据实际证书路径调整）
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # 文件上传大小限制
    client_max_body_size 500M;

    # API代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 静态文件服务
    # 注意：请根据实际部署路径调整alias路径
    location /uploads/ {
        alias /path/to/your/project/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";

        # 视频和音频文件特殊处理
        location ~* \.(mp4|webm|ogg|mp3|wav|flac|aac)$ {
            add_header Accept-Ranges bytes;
        }
    }

    # 健康检查
    location /health {
        proxy_pass http://localhost:3000;
        access_log off;
    }
}
```

#### 启用站点
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/video-platform-api /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 6. SSL证书配置

#### 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. 防火墙配置

```bash
# 启用UFW
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 查看状态
sudo ufw status
```

### 8. 监控和日志

#### 日志轮转
```bash
# 创建logrotate配置
sudo nano /etc/logrotate.d/video-platform

# 添加以下内容
/var/log/video-platform/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        pm2 reloadLogs
    endscript
}
```

#### 系统监控
```bash
# 安装htop
sudo apt install htop -y

# 监控系统资源
htop

# 监控应用状态
pm2 monit
```

## 🔧 维护操作

### 应用更新
```bash
# 拉取最新代码（在项目根目录下执行）
git pull origin main

# 安装新依赖
npm ci --production

# 重启应用
pm2 restart video-platform-api

# 查看日志
pm2 logs video-platform-api
```

### 数据库备份
```bash
# 创建备份脚本
#!/bin/bash
# 使用相对路径或用户可配置的路径
BACKUP_DIR="./backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

mysqldump -u video_user -p video_platform > $BACKUP_DIR/video_platform_$DATE.sql
gzip $BACKUP_DIR/video_platform_$DATE.sql

# 删除30天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

### 性能优化
```bash
# 清理Redis缓存
redis-cli -a your_redis_password FLUSHDB

# 优化MySQL
sudo mysql_secure_installation

# 监控性能
pm2 monit
```

## 🚨 故障排除

### 常见问题

1. **应用无法启动**
   - 检查环境变量配置
   - 查看PM2日志：`pm2 logs`
   - 检查端口占用：`netstat -tlnp | grep 3000`

2. **数据库连接失败**
   - 检查MySQL服务状态：`sudo systemctl status mysql`
   - 验证数据库凭据
   - 检查防火墙设置

3. **Redis连接失败**
   - 检查Redis服务状态：`sudo systemctl status redis`
   - 验证Redis密码
   - 检查Redis配置文件

4. **文件上传失败**
   - 检查上传目录权限
   - 验证Nginx文件大小限制
   - 检查磁盘空间

### 日志查看
```bash
# 应用日志
pm2 logs video-platform-api

# 系统日志
sudo journalctl -u nginx
sudo journalctl -u mysql
sudo journalctl -u redis

# 错误日志（使用相对路径）
tail -f ./logs/error.log
```

---

**注意**: 请根据实际环境调整配置参数，确保在生产环境中使用强密码和适当的安全设置。
