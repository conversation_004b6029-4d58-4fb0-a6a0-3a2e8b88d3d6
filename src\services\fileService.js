const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const logger = require('../utils/logger');
const { toAbsolutePath } = require('../utils/pathResolver');

class FileService {
  constructor() {
    this.avatarSizes = {
      small: { width: 64, height: 64 },
      medium: { width: 128, height: 128 },
      large: { width: 256, height: 256 }
    };
    
    this.thumbnailSizes = {
      small: { width: 160, height: 90 },
      medium: { width: 320, height: 180 },
      large: { width: 640, height: 360 }
    };
  }

  // 确保目录存在
  async ensureDirectory(dirPath) {
    try {
      await fs.access(dirPath);
    } catch (error) {
      await fs.mkdir(dirPath, { recursive: true });
      logger.info(`创建目录: ${dirPath}`);
    }
  }

  // 生成文件名
  generateFileName(originalName, suffix = '') {
    const ext = path.extname(originalName);
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    return `${timestamp}_${random}${suffix}${ext}`;
  }

  // 处理头像
  async processAvatar(inputPath, userId) {
    try {
      const outputDir = path.join(process.cwd(), 'uploads', 'avatars');
      await this.ensureDirectory(outputDir);

      const fileName = this.generateFileName(`avatar_${userId}.jpg`);
      const outputPath = path.join(outputDir, fileName);

      // 使用Sharp处理图片
      await sharp(inputPath)
        .resize(256, 256, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({
          quality: 85,
          progressive: true
        })
        .toFile(outputPath);

      logger.info(`头像处理完成: ${outputPath}`);
      return outputPath;

    } catch (error) {
      logger.error('头像处理失败:', error);
      throw new Error('头像处理失败');
    }
  }

  // 生成多尺寸头像
  async generateAvatarSizes(inputPath, userId) {
    const results = {};
    const outputDir = path.join(process.cwd(), 'uploads', 'avatars');
    await this.ensureDirectory(outputDir);

    for (const [sizeName, dimensions] of Object.entries(this.avatarSizes)) {
      try {
        const fileName = this.generateFileName(`avatar_${userId}_${sizeName}.jpg`);
        const outputPath = path.join(outputDir, fileName);

        await sharp(inputPath)
          .resize(dimensions.width, dimensions.height, {
            fit: 'cover',
            position: 'center'
          })
          .jpeg({
            quality: sizeName === 'large' ? 90 : 80,
            progressive: true
          })
          .toFile(outputPath);

        results[sizeName] = `/uploads/avatars/${fileName}`;
        logger.debug(`生成头像尺寸 ${sizeName}: ${outputPath}`);

      } catch (error) {
        logger.error(`生成头像尺寸 ${sizeName} 失败:`, error);
      }
    }

    return results;
  }

  // 处理视频缩略图
  async processVideoThumbnail(inputPath, videoId, timeOffset = 5) {
    try {
      const outputDir = path.join(process.cwd(), 'uploads', 'thumbnails');
      await this.ensureDirectory(outputDir);

      const fileName = this.generateFileName(`thumb_${videoId}.jpg`);
      const outputPath = path.join(outputDir, fileName);

      // 这里应该使用FFmpeg生成视频缩略图
      // 暂时使用占位符实现
      const placeholderPath = await this.generatePlaceholderThumbnail(outputPath);

      logger.info(`视频缩略图生成完成: ${placeholderPath}`);
      return placeholderPath;

    } catch (error) {
      logger.error('视频缩略图生成失败:', error);
      throw new Error('视频缩略图生成失败');
    }
  }

  // 生成占位符缩略图
  async generatePlaceholderThumbnail(outputPath) {
    try {
      // 生成一个简单的占位符图片
      await sharp({
        create: {
          width: 640,
          height: 360,
          channels: 3,
          background: { r: 100, g: 100, b: 100 }
        }
      })
      .jpeg({ quality: 80 })
      .toFile(outputPath);

      return outputPath;
    } catch (error) {
      logger.error('生成占位符缩略图失败:', error);
      throw error;
    }
  }

  // 生成指定颜色的空白图片
  async generateBlankImage(outputPath, options = {}) {
    const {
      width = 640,
      height = 360,
      color = '#000000', // 默认为黑色
      format = 'jpeg',
      quality = 80
    } = options;
    
    try {
      let pipeline = sharp({
        create: {
          width,
          height,
          channels: 4, // 使用4通道以支持透明度
          background: color
        }
      });
      
      switch (format.toLowerCase()) {
        case 'jpeg':
        case 'jpg':
          pipeline = pipeline.jpeg({ quality });
          break;
        case 'png':
          pipeline = pipeline.png({ quality });
          break;
        case 'webp':
            pipeline = pipeline.webp({ quality });
            break;
        default:
          throw new Error(`不支持的图片格式: ${format}`);
      }
      
      await pipeline.toFile(outputPath);
      logger.info(`生成空白图片成功: ${outputPath}`);
      return outputPath;

    } catch (error) {
      logger.error(`生成空白图片失败: ${outputPath}`, error);
      throw error;
    }
  }

  // 生成多尺寸缩略图
  async generateThumbnailSizes(inputPath, videoId) {
    const results = {};
    const outputDir = path.join(process.cwd(), 'uploads', 'thumbnails');
    await this.ensureDirectory(outputDir);

    for (const [sizeName, dimensions] of Object.entries(this.thumbnailSizes)) {
      try {
        const fileName = this.generateFileName(`thumb_${videoId}_${sizeName}.jpg`);
        const outputPath = path.join(outputDir, fileName);

        await sharp(inputPath)
          .resize(dimensions.width, dimensions.height, {
            fit: 'cover',
            position: 'center'
          })
          .jpeg({
            quality: 80,
            progressive: true
          })
          .toFile(outputPath);

        results[sizeName] = `/uploads/thumbnails/${fileName}`;
        logger.debug(`生成缩略图尺寸 ${sizeName}: ${outputPath}`);

      } catch (error) {
        logger.error(`生成缩略图尺寸 ${sizeName} 失败:`, error);
      }
    }

    return results;
  }

  // 压缩图片
  async compressImage(inputPath, outputPath, options = {}) {
    const {
      quality = 80,
      width = null,
      height = null,
      format = 'jpeg'
    } = options;

    try {
      let pipeline = sharp(inputPath);

      // 调整尺寸
      if (width || height) {
        pipeline = pipeline.resize(width, height, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }

      // 设置输出格式和质量
      switch (format.toLowerCase()) {
        case 'jpeg':
        case 'jpg':
          pipeline = pipeline.jpeg({ quality, progressive: true });
          break;
        case 'png':
          pipeline = pipeline.png({ quality, progressive: true });
          break;
        case 'webp':
          pipeline = pipeline.webp({ quality });
          break;
        default:
          pipeline = pipeline.jpeg({ quality, progressive: true });
      }

      await pipeline.toFile(outputPath);
      logger.info(`图片压缩完成: ${outputPath}`);
      return outputPath;

    } catch (error) {
      logger.error('图片压缩失败:', error);
      throw new Error('图片压缩失败');
    }
  }

  // 获取图片信息
  async getImageInfo(imagePath) {
    try {
      const metadata = await sharp(imagePath).metadata();
      const stats = await fs.stat(imagePath);

      return {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        size: stats.size,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha,
        channels: metadata.channels
      };
    } catch (error) {
      logger.error('获取图片信息失败:', error);
      throw new Error('获取图片信息失败');
    }
  }

  // 验证图片文件
  async validateImage(imagePath) {
    try {
      const info = await this.getImageInfo(imagePath);
      
      // 检查图片尺寸
      const maxWidth = 4096;
      const maxHeight = 4096;
      if (info.width > maxWidth || info.height > maxHeight) {
        throw new Error(`图片尺寸过大，最大支持 ${maxWidth}x${maxHeight}`);
      }

      // 检查文件大小
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (info.size > maxSize) {
        throw new Error('图片文件过大，最大支持10MB');
      }

      // 检查格式
      const allowedFormats = ['jpeg', 'png', 'gif', 'webp'];
      if (!allowedFormats.includes(info.format)) {
        throw new Error('不支持的图片格式');
      }

      return true;
    } catch (error) {
      logger.error('图片验证失败:', error);
      throw error;
    }
  }

  // 删除文件
  async deleteFile(filePath) {
    try {
      await fs.unlink(filePath);
      logger.info(`文件删除成功: ${filePath}`);
      return true;
    } catch (error) {
      if (error.code !== 'ENOENT') {
        logger.error(`文件删除失败: ${filePath}`, error);
      }
      return false;
    }
  }

  // 清理临时文件
  async cleanupTempFiles(tempDirRelative = 'uploads/temp') {
    const tempDirAbsolute = toAbsolutePath(tempDirRelative);
    try {
      const files = await fs.readdir(tempDirAbsolute);
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24小时

      let cleanedCount = 0;
      for (const file of files) {
        const filePath = path.join(tempDirAbsolute, file);
        const stats = await fs.stat(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await this.deleteFile(filePath);
          cleanedCount++;
        }
      }

      logger.info(`清理临时文件完成，删除 ${cleanedCount} 个文件`);
      return cleanedCount;
    } catch (error) {
      logger.error('清理临时文件失败:', error);
      return 0;
    }
  }

  // 获取文件存储统计
  async getStorageStats() {
    const stats = {
      avatars: { count: 0, size: 0 },
      thumbnails: { count: 0, size: 0 },
      videos: { count: 0, size: 0 },
      temp: { count: 0, size: 0 }
    };

    const directories = {
      avatars: path.join(process.cwd(), 'uploads', 'avatars'),
      thumbnails: path.join(process.cwd(), 'uploads', 'thumbnails'),
      videos: path.join(process.cwd(), 'uploads', 'videos'),
      temp: path.join(process.cwd(), 'uploads', 'temp')
    };

    for (const [type, dir] of Object.entries(directories)) {
      try {
        const files = await fs.readdir(dir, { withFileTypes: true });
        
        for (const file of files) {
          if (file.isFile()) {
            const filePath = path.join(dir, file.name);
            const fileStat = await fs.stat(filePath);
            stats[type].count++;
            stats[type].size += fileStat.size;
          }
        }
      } catch (error) {
        // 目录不存在或无法访问
        logger.warn(`无法访问目录 ${dir}:`, error.message);
      }
    }

    // 转换为可读格式
    for (const type of Object.keys(stats)) {
      stats[type].sizeFormatted = this.formatFileSize(stats[type].size);
    }

    return stats;
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async processAndSaveAvatar(buffer) {
    try {
      const outputDir = toAbsolutePath(path.join('uploads', 'avatars'));
      await this.ensureDirectoryExists(outputDir);
      const filename = `${Date.now()}_${Math.random().toString(36).substring(2, 8)}.webp`;
      const relativePath = path.join('uploads', 'avatars', filename);
      const absolutePath = toAbsolutePath(relativePath);

      await sharp(buffer)
        .resize(256, 256, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({
          quality: 85,
          progressive: true
        })
        .toFile(absolutePath);

      logger.info(`头像已保存至: ${absolutePath}`);
      return `/uploads/avatars/${filename}`; // 返回公共可访问的URL
    } catch (error) {
      logger.error('头像保存失败:', error);
      throw new Error('头像保存失败');
    }
  }

  async processAndSaveThumbnail(buffer, videoId) {
    try {
      const outputDir = toAbsolutePath(path.join('uploads', 'thumbnails'));
      await this.ensureDirectoryExists(outputDir);
      const filename = `${videoId}_thumbnail.webp`;
      const relativePath = path.join('uploads', 'thumbnails', filename);
      const absolutePath = toAbsolutePath(relativePath);

      await sharp(buffer)
        .resize(256, 256, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({
          quality: 85,
          progressive: true
        })
        .toFile(absolutePath);

      logger.info(`视频 ${videoId} 的缩略图已保存至: ${absolutePath}`);
      return `/uploads/thumbnails/${filename}`; // 返回公共URL
    } catch (error) {
      logger.error('缩略图保存失败:', error);
      throw new Error('缩略图保存失败');
    }
  }

  getUploadDirectories() {
    return {
      avatars: toAbsolutePath('uploads/avatars'),
      thumbnails: toAbsolutePath('uploads/thumbnails'),
      videos: toAbsolutePath('uploads/videos'),
      temp: toAbsolutePath('uploads/temp')
    };
  }

  // 获取文件的公共访问URL
  getPublicUrl(filePath) {
    if (!filePath) {
      return null;
    }

    // 如果已经是完整URL，直接返回
    if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
      return filePath;
    }

    // 如果不是以/开头，添加/
    if (!filePath.startsWith('/')) {
      filePath = '/' + filePath;
    }

    return filePath;
  }
}

// 创建文件服务实例
const fileService = new FileService();

// 定期清理临时文件
setInterval(() => {
  fileService.cleanupTempFiles().catch(error => {
    logger.error('定期清理临时文件失败:', error);
  });
}, 60 * 60 * 1000); // 每小时执行一次

module.exports = fileService;
