const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');
const { operationLogger } = require('./requestLogger');

// 文件类型配置
const FILE_TYPES = {
  image: {
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'],
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'],
    maxSize: 5 * 1024 * 1024, // 5MB
    path: 'uploads/images'
  },
  video: {
    extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'],
    mimeTypes: ['video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv', 'video/x-flv', 'video/webm', 'video/x-matroska'],
    maxSize: 500 * 1024 * 1024, // 500MB
    path: 'uploads/videos'
  },
  audio: {
    extensions: ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', '.wma'],
    mimeTypes: ['audio/mpeg', 'audio/wav', 'audio/flac', 'audio/aac', 'audio/ogg', 'audio/mp4', 'audio/x-ms-wma'],
    maxSize: 100 * 1024 * 1024, // 100MB
    path: 'uploads/audios'
  },
  avatar: {
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxSize: 2 * 1024 * 1024, // 2MB
    path: 'uploads/avatars'
  },
  thumbnail: {
    extensions: ['.jpg', '.jpeg', '.png', '.webp'],
    mimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
    maxSize: 1 * 1024 * 1024, // 1MB
    path: 'uploads/thumbnails'
  },
  document: {
    extensions: ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
    mimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'application/rtf'],
    maxSize: 10 * 1024 * 1024, // 10MB
    path: 'uploads/documents'
  }
};

// 确保上传目录存在
const ensureUploadDir = async (dirPath) => {
  try {
    await fs.access(dirPath);
  } catch (error) {
    await fs.mkdir(dirPath, { recursive: true });
    logger.info(`创建上传目录: ${dirPath}`);
  }
};

// 生成唯一文件名
const generateUniqueFilename = (originalName) => {
  const ext = path.extname(originalName);
  const timestamp = Date.now();
  const random = crypto.randomBytes(8).toString('hex');
  return `${timestamp}_${random}${ext}`;
};

// 文件类型验证
const validateFileType = (file, allowedTypes) => {
  const ext = path.extname(file.originalname).toLowerCase();
  const mimeType = file.mimetype.toLowerCase();
  
  const typeConfig = FILE_TYPES[allowedTypes] || allowedTypes;
  
  if (Array.isArray(allowedTypes)) {
    // 多种类型支持
    return allowedTypes.some(type => {
      const config = FILE_TYPES[type];
      return config && 
             config.extensions.includes(ext) && 
             config.mimeTypes.includes(mimeType);
    });
  } else {
    // 单一类型
    return typeConfig.extensions.includes(ext) && 
           typeConfig.mimeTypes.includes(mimeType);
  }
};

// 创建存储配置
const createStorage = (uploadType) => {
  return multer.diskStorage({
    destination: async (req, file, cb) => {
      try {
        let finalUploadType;
        if (Array.isArray(uploadType)) {
          const mime = file.mimetype.toLowerCase();
          finalUploadType = uploadType.find(type => FILE_TYPES[type].mimeTypes.includes(mime));
          if (!finalUploadType) {
            return cb(new AppError(`不支持的文件MIME类型: ${mime}`, 400, 'INVALID_MIME_TYPE'));
          }
        } else {
          finalUploadType = uploadType;
        }

        const typeConfig = FILE_TYPES[finalUploadType];
        if (!typeConfig) {
          return cb(new AppError(`不支持的上传类型: ${finalUploadType}`, 400, 'INVALID_UPLOAD_TYPE'));
        }
        
        let uploadPath = typeConfig.path;
        
        // 按日期分组存储
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        uploadPath = path.join(uploadPath, `${year}/${month}/${day}`);
        
        await ensureUploadDir(uploadPath);
        cb(null, uploadPath);
      } catch (error) {
        cb(error);
      }
    },
    
    filename: (req, file, cb) => {
      try {
        const uniqueName = generateUniqueFilename(file.originalname);
        cb(null, uniqueName);
      } catch (error) {
        cb(error);
      }
    }
  });
};

// 创建文件过滤器
const createFileFilter = (uploadType, options = {}) => {
  const { allowedTypes = uploadType, customValidator } = options;
  
  return (req, file, cb) => {
    try {
      // 自定义验证器
      if (customValidator && !customValidator(file, req)) {
        return cb(new AppError('文件验证失败', 400, 'FILE_VALIDATION_FAILED'));
      }
      
      // 文件类型验证
      if (!validateFileType(file, allowedTypes)) {
        operationLogger.logSecurityEvent(
          req, 
          'invalid_file_type', 
          'medium', 
          `无效文件类型: ${file.originalname}, MIME: ${file.mimetype}`
        );
        return cb(new AppError('不支持的文件类型', 400, 'INVALID_FILE_TYPE'));
      }
      
      // 文件名安全检查
      const filename = file.originalname;
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        operationLogger.logSecurityEvent(
          req, 
          'malicious_filename', 
          'high', 
          `恶意文件名: ${filename}`
        );
        return cb(new AppError('文件名包含非法字符', 400, 'MALICIOUS_FILENAME'));
      }
      
      cb(null, true);
    } catch (error) {
      cb(error);
    }
  };
};

// 创建上传中间件
const createUploadMiddleware = (uploadType, options = {}) => {
  const {
    maxFiles = 1,
    fieldName = 'file',
    preservePath = false,
    customLimits = {}
  } = options;
  
  // 对于多类型上传，需要特殊处理
  let typeConfig;
  if (Array.isArray(uploadType)) {
    // 当支持多种类型时，限制应该更加通用。
    // 这里我们选择所有支持类型中最大的size作为限制。
    const maxAllowedSize = Math.max(...uploadType.map(type => FILE_TYPES[type].maxSize));
    typeConfig = { maxSize: maxAllowedSize };
  } else {
    typeConfig = FILE_TYPES[uploadType];
  }

  if (!typeConfig) {
    throw new Error(`不支持的上传类型: ${uploadType}`);
  }
  
  const limits = {
    fileSize: typeConfig.maxSize,
    files: maxFiles,
    fields: 10,
    fieldNameSize: 100,
    fieldSize: 1024 * 1024, // 1MB
    ...customLimits
  };
  
  const upload = multer({
    storage: createStorage(uploadType),
    fileFilter: createFileFilter(uploadType, options),
    limits,
    preservePath
  });
  
  // 根据文件数量选择合适的方法
  let uploadHandler;
  if (maxFiles === 1) {
    uploadHandler = upload.single(fieldName);
  } else if (maxFiles > 1) {
    uploadHandler = upload.array(fieldName, maxFiles);
  } else {
    uploadHandler = upload.any();
  }
  
  return (req, res, next) => {
    uploadHandler(req, res, (err) => {
      if (err) {
        // 处理multer错误
        if (err instanceof multer.MulterError) {
          let message = '文件上传失败';
          let code = 'UPLOAD_ERROR';
          
          switch (err.code) {
            case 'LIMIT_FILE_SIZE':
              message = `文件大小超出限制，最大允许 ${Math.round(limits.fileSize / 1024 / 1024)}MB`;
              code = 'FILE_TOO_LARGE';
              break;
            case 'LIMIT_FILE_COUNT':
              message = `文件数量超出限制，最多允许 ${maxFiles} 个文件`;
              code = 'TOO_MANY_FILES';
              break;
            case 'LIMIT_UNEXPECTED_FILE':
              message = '不支持的文件字段';
              code = 'UNEXPECTED_FILE';
              break;
            case 'LIMIT_FIELD_COUNT':
              message = '表单字段过多';
              code = 'TOO_MANY_FIELDS';
              break;
          }
          
          operationLogger.logSecurityEvent(req, 'upload_error', 'medium', `上传错误: ${err.code}`);
          return next(new AppError(message, 400, code));
        }
        
        return next(err);
      }
      
      // 记录上传成功
      if (req.file || req.files) {
        const files = req.files || [req.file];
        operationLogger.logUserOperation(
          req, 
          'file_upload', 
          uploadType, 
          `上传 ${files.length} 个文件`,
          { files: files.map(f => ({ originalName: f.originalname, size: f.size, path: f.path })) }
        );
      }
      
      next();
    });
  };
};

// 预定义的上传中间件
const uploadMiddlewares = {
  // 头像上传
  avatar: createUploadMiddleware('avatar', {
    fieldName: 'avatar',
    maxFiles: 1
  }),

  // 视频上传
  video: createUploadMiddleware('video', {
    fieldName: 'video',
    maxFiles: 1
  }),

  // 音频上传
  audio: createUploadMiddleware('audio', {
    fieldName: 'audio',
    maxFiles: 1
  }),

  // 统一媒体上传（支持视频和音频）
  media: createUploadMiddleware(['video', 'audio'], {
    fieldName: 'media',
    maxFiles: 1
  }),

  // 缩略图上传
  thumbnail: createUploadMiddleware('thumbnail', {
    fieldName: 'thumbnail',
    maxFiles: 1
  }),

  // 多图片上传
  images: createUploadMiddleware('image', {
    fieldName: 'images',
    maxFiles: 10
  }),

  // 文档上传
  document: createUploadMiddleware('document', {
    fieldName: 'document',
    maxFiles: 5
  })
};

// 文件清理中间件
const fileCleanup = (req, res, next) => {
  const originalEnd = res.end;
  
  res.end = function(chunk, encoding) {
    // 如果请求失败，清理已上传的文件
    if (res.statusCode >= 400 && (req.file || req.files)) {
      const files = req.files || [req.file];
      
      files.forEach(async (file) => {
        try {
          await fs.unlink(file.path);
          logger.info(`清理失败上传文件: ${file.path}`);
        } catch (error) {
          logger.error(`清理文件失败: ${file.path}`, error);
        }
      });
    }
    
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

// 文件信息提取中间件
const extractFileInfo = (req, res, next) => {
  if (req.file) {
    req.fileInfo = {
      originalName: req.file.originalname,
      filename: req.file.filename,
      path: req.file.path,
      size: req.file.size,
      mimeType: req.file.mimetype,
      uploadTime: new Date()
    };
  } else if (req.files && req.files.length > 0) {
    req.filesInfo = req.files.map(file => ({
      originalName: file.originalname,
      filename: file.filename,
      path: file.path,
      size: file.size,
      mimeType: file.mimetype,
      uploadTime: new Date()
    }));
  }
  
  next();
};

// 文件安全扫描中间件（简化版）
const fileSecurity = (req, res, next) => {
  const scanFile = async (file) => {
    try {
      // 检查文件头部魔数
      const buffer = await fs.readFile(file.path, { start: 0, end: 10 });
      const header = buffer.toString('hex');
      
      // 简单的文件类型验证
      const fileSignatures = {
        'ffd8ff': 'jpeg',
        '89504e': 'png',
        '474946': 'gif',
        '000000': 'mp4', // 简化检查
        '52494646': 'avi'
      };
      
      const detectedType = Object.keys(fileSignatures).find(sig => 
        header.toLowerCase().startsWith(sig)
      );
      
      if (!detectedType) {
        operationLogger.logSecurityEvent(
          req, 
          'suspicious_file', 
          'medium', 
          `可疑文件头: ${file.originalname}, 头部: ${header}`
        );
      }
      
    } catch (error) {
      logger.error(`文件安全扫描失败: ${file.path}`, error);
    }
  };
  
  if (req.file) {
    scanFile(req.file);
  } else if (req.files) {
    req.files.forEach(scanFile);
  }
  
  next();
};

/**
 * 动态文件上传处理器
 */
const autoSelectUpload = (req, res, next) => {
  // 简单的逻辑：检查URL或请求体中的特定字段来决定上传类型
  // 例如: /api/upload/avatar -> 'avatar'
  //      /api/upload/video -> 'video'
  const uploadType = req.params.type || req.body.uploadType || 'media'; // 默认为 'media'

  const middleware = uploadMiddlewares[uploadType];

  if (middleware) {
    middleware(req, res, next);
  } else {
    next(new AppError(`不支持的动态上传类型: ${uploadType}`, 400));
  }
};

module.exports = {
  FILE_TYPES,
  createUploadMiddleware,
  uploadMiddlewares,
  fileCleanup,
  extractFileInfo,
  fileSecurity,
  ensureUploadDir,
  generateUniqueFilename,
  validateFileType,
  autoSelectUpload
};
