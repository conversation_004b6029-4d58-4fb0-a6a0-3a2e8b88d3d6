const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'video_user',
  password: process.env.DB_PASSWORD || 'secure_password',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

async function debugAdminAPIResponse() {
  let connection;
  
  try {
    console.log('🔍 调试管理员API响应数据...\n');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    
    // 模拟后端getUserList方法的SQL查询
    console.log('1. 执行与后端API相同的SQL查询...');
    
    const sql = `
      SELECT 
        u.*,
        (SELECT COUNT(*) FROM videos v WHERE v.user_id = u.id AND v.status != 'deleted') as video_count,
        (SELECT COUNT(*) FROM comments c WHERE c.user_id = u.id AND c.status = 'active') as comment_count
      FROM users u
      WHERE u.status IN ('active', 'banned', 'inactive')
      ORDER BY u.created_at DESC
      LIMIT 10 OFFSET 0
    `;
    
    const [users] = await connection.execute(sql);
    
    console.log(`📊 查询结果: 找到 ${users.length} 个用户\n`);
    
    // 查找用户GGG
    const gggUser = users.find(user => user.username === 'GGG');
    
    if (gggUser) {
      console.log('👤 用户GGG的原始数据库记录:');
      console.log(`   ID: ${gggUser.id}`);
      console.log(`   用户名: ${gggUser.username}`);
      console.log(`   邮箱: ${gggUser.email}`);
      console.log(`   角色: ${gggUser.role}`);
      console.log(`   状态: ${gggUser.status}`);
      console.log(`   注册时间: ${gggUser.created_at}`);
      console.log(`   最后登录: ${gggUser.last_login_at}`);
      console.log(`   视频数量: ${gggUser.video_count}`);
      console.log(`   评论数量: ${gggUser.comment_count}`);
      
      // 模拟后端的数据格式化过程
      console.log('\n🔄 模拟后端数据格式化...');
      const { password, video_count, comment_count, last_login_at, ...userWithoutPassword } = gggUser;
      const formattedUser = {
        ...userWithoutPassword,
        last_login: last_login_at, // 映射字段名
        content_counts: {
          videos: video_count,
          comments: comment_count
        }
      };
      
      console.log('📤 格式化后的用户数据 (API应该返回的数据):');
      console.log(JSON.stringify(formattedUser, null, 2));
      
      // 验证角色映射
      console.log('\n🎯 角色验证:');
      console.log(`   数据库中的角色: ${gggUser.role}`);
      console.log(`   API返回的角色: ${formattedUser.role}`);
      console.log(`   前端应该显示: ${gggUser.role === 'member' ? '会员' : '普通用户'}`);
      
    } else {
      console.log('❌ 未找到用户GGG');
      
      // 显示所有用户的基本信息
      console.log('\n📋 所有用户的角色信息:');
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.username} - 角色: ${user.role} - 邮箱: ${user.email}`);
      });
    }
    
    // 额外检查：直接查询用户GGG
    console.log('\n2. 直接查询用户GGG...');
    const [directQuery] = await connection.execute(
      'SELECT * FROM users WHERE username = ? OR email LIKE ?',
      ['GGG', '%GGG%']
    );
    
    if (directQuery.length > 0) {
      const user = directQuery[0];
      console.log('🎯 直接查询结果:');
      console.log(`   ID: ${user.id}`);
      console.log(`   用户名: ${user.username}`);
      console.log(`   邮箱: ${user.email}`);
      console.log(`   角色: ${user.role}`);
      console.log(`   状态: ${user.status}`);
      console.log(`   更新时间: ${user.updated_at}`);
    }
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  debugAdminAPIResponse()
    .then(() => {
      console.log('\n🎉 调试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { debugAdminAPIResponse };
