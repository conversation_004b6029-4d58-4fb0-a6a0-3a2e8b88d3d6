#!/usr/bin/env node

/**
 * 生成测试SSL证书脚本
 * 使用Node.js内置模块生成自签名证书
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🔐 生成测试SSL证书\n');

/**
 * 生成自签名证书（使用Node.js crypto模块）
 */
function generateSelfSignedCertificate() {
  try {
    console.log('📁 创建证书目录...');
    const certDir = path.join(__dirname, '..', 'certs');
    if (!fs.existsSync(certDir)) {
      fs.mkdirSync(certDir, { recursive: true });
      console.log(`✅ 证书目录创建成功: ${certDir}`);
    }

    console.log('🔑 生成RSA密钥对...');
    
    // 生成RSA密钥对
    const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });

    console.log('📜 生成自签名证书...');
    
    // 创建证书
    const cert = createSelfSignedCertificate(publicKey, privateKey);
    
    // 保存文件
    const certPath = path.join(certDir, 'certificate.crt');
    const keyPath = path.join(certDir, 'private.key');
    
    fs.writeFileSync(certPath, cert);
    fs.writeFileSync(keyPath, privateKey);
    
    // 设置文件权限（仅在Unix系统上）
    if (process.platform !== 'win32') {
      fs.chmodSync(keyPath, 0o600);
      fs.chmodSync(certPath, 0o644);
    }
    
    console.log('✅ 测试证书生成成功！');
    console.log(`📄 证书文件: ${certPath}`);
    console.log(`🔑 私钥文件: ${keyPath}`);
    console.log('⚠️ 注意: 这是自签名证书，仅用于开发测试');
    
    return true;
  } catch (error) {
    console.error('❌ 生成证书失败:', error.message);
    return false;
  }
}

/**
 * 创建自签名证书
 */
function createSelfSignedCertificate(publicKey, privateKey) {
  // 简化的证书生成（实际生产环境应使用专业工具）
  const now = new Date();
  const oneYear = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
  
  // 基本的X.509证书结构
  const certData = {
    version: 3,
    serialNumber: crypto.randomBytes(16).toString('hex'),
    issuer: {
      C: 'CN',
      ST: 'State',
      L: 'City',
      O: 'Test Organization',
      CN: 'localhost'
    },
    subject: {
      C: 'CN',
      ST: 'State', 
      L: 'City',
      O: 'Test Organization',
      CN: 'localhost'
    },
    notBefore: now,
    notAfter: oneYear,
    publicKey: publicKey
  };
  
  // 创建简化的PEM格式证书
  const certContent = createPEMCertificate(certData, privateKey);
  
  return certContent;
}

/**
 * 创建PEM格式证书
 */
function createPEMCertificate(certData, privateKey) {
  // 这是一个简化的实现，实际应用中应使用专业的证书生成库
  const certInfo = [
    `Subject: C=${certData.subject.C}, ST=${certData.subject.ST}, L=${certData.subject.L}, O=${certData.subject.O}, CN=${certData.subject.CN}`,
    `Issuer: C=${certData.issuer.C}, ST=${certData.issuer.ST}, L=${certData.issuer.L}, O=${certData.issuer.O}, CN=${certData.issuer.CN}`,
    `Serial Number: ${certData.serialNumber}`,
    `Not Before: ${certData.notBefore.toISOString()}`,
    `Not After: ${certData.notAfter.toISOString()}`
  ].join('\n');
  
  // 创建证书内容的哈希
  const certHash = crypto.createHash('sha256').update(certInfo + certData.publicKey).digest('base64');
  
  // 使用私钥签名
  const sign = crypto.createSign('SHA256');
  sign.update(certHash);
  const signature = sign.sign(privateKey, 'base64');
  
  // 构建PEM格式证书
  const pemCert = [
    '-----BEGIN CERTIFICATE-----',
    // 将证书数据编码为base64
    Buffer.from(JSON.stringify({
      certInfo,
      publicKey: certData.publicKey,
      signature
    })).toString('base64').match(/.{1,64}/g).join('\n'),
    '-----END CERTIFICATE-----'
  ].join('\n');
  
  return pemCert;
}

/**
 * 验证生成的证书
 */
function validateGeneratedCertificate() {
  try {
    const certPath = path.join(__dirname, '..', 'certs', 'certificate.crt');
    const keyPath = path.join(__dirname, '..', 'certs', 'private.key');
    
    if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
      console.log('❌ 证书文件不存在');
      return false;
    }
    
    const cert = fs.readFileSync(certPath, 'utf8');
    const key = fs.readFileSync(keyPath, 'utf8');
    
    const certValid = cert.includes('BEGIN CERTIFICATE');
    const keyValid = key.includes('BEGIN PRIVATE KEY') || key.includes('BEGIN RSA PRIVATE KEY');
    
    console.log('\n🔍 验证生成的证书:');
    console.log(`证书格式: ${certValid ? '✅ 有效' : '❌ 无效'}`);
    console.log(`私钥格式: ${keyValid ? '✅ 有效' : '❌ 无效'}`);
    
    if (certValid && keyValid) {
      console.log('✅ 证书验证通过');
      return true;
    } else {
      console.log('❌ 证书验证失败');
      return false;
    }
  } catch (error) {
    console.error('❌ 验证证书时出错:', error.message);
    return false;
  }
}

/**
 * 显示使用说明
 */
function showUsageInstructions() {
  console.log('\n📋 使用说明:');
  console.log('='.repeat(50));
  console.log('1. 在.env文件中启用HTTPS:');
  console.log('   ENABLE_HTTPS=true');
  console.log('   HTTPS_PORT=3443');
  console.log('   SSL_CERT_PATH=./certs/certificate.crt');
  console.log('   SSL_KEY_PATH=./certs/private.key');
  console.log('');
  console.log('2. 启动服务器:');
  console.log('   npm start');
  console.log('');
  console.log('3. 访问HTTPS接口:');
  console.log('   https://localhost:3443/docs');
  console.log('   https://localhost:3443/health');
  console.log('');
  console.log('⚠️ 浏览器可能显示安全警告，这是正常的（自签名证书）');
  console.log('   在开发环境中可以选择"继续访问"');
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始生成测试SSL证书');
  console.log('='.repeat(50));
  
  if (process.env.NODE_ENV === 'production') {
    console.log('⚠️ 警告: 生产环境不应使用测试证书');
    console.log('请使用有效的SSL证书（如Let\'s Encrypt）');
    return false;
  }
  
  const success = generateSelfSignedCertificate();
  
  if (success) {
    const valid = validateGeneratedCertificate();
    if (valid) {
      showUsageInstructions();
      console.log('\n🎉 测试证书生成完成！');
      return true;
    }
  }
  
  console.log('\n❌ 测试证书生成失败');
  return false;
}

// 运行脚本
if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = {
  generateSelfSignedCertificate,
  validateGeneratedCertificate,
  main
};
