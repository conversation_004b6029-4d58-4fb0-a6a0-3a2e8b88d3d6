const Follow = require('../models/Follow');
const User = require('../../../database/models/User');
const notificationService = require('../../../services/notificationService');
const { AppError } = require('../../../middleware/errorHandler');
const logger = require('../../../utils/logger');

class FollowService {
  // 关注用户
  async followUser(followerId, followedId) {
    try {
      // 验证被关注用户存在
      const targetUser = await User.findById(followedId);
      if (!targetUser) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      const followId = await Follow.followUser(followerId, followedId);

      // 发送关注通知
      await this.sendFollowNotification(followerId, followedId);

      logger.info(`用户关注成功: ${followerId} -> ${followedId}`);
      return { success: true, followId };
    } catch (error) {
      logger.error(`用户关注失败: ${followerId} -> ${followedId}`, error);
      throw error;
    }
  }

  // 取消关注
  async unfollowUser(followerId, followedId) {
    try {
      await Follow.unfollowUser(followerId, followedId);
      logger.info(`取消关注成功: ${followerId} -> ${followedId}`);
      return { success: true };
    } catch (error) {
      logger.error(`取消关注失败: ${followerId} -> ${followedId}`, error);
      throw error;
    }
  }

  // 获取粉丝列表
  async getFollowers(userId, page = 1, limit = 20, currentUserId = null) {
    try {
      const result = await Follow.getFollowers(userId, page, limit);

      // 如果有当前用户，检查关注状态
      if (currentUserId && result.data.length > 0) {
        const userIds = result.data.map(user => user.id);
        const followStatus = await Follow.batchCheckFollowStatus(currentUserId, userIds);
        
        result.data = result.data.map(user => ({
          ...user,
          is_following: followStatus[user.id] || false
        }));
      }

      return result;
    } catch (error) {
      logger.error(`获取粉丝列表失败: userId=${userId}`, error);
      throw error;
    }
  }

  // 获取关注列表
  async getFollowing(userId, page = 1, limit = 20, currentUserId = null) {
    try {
      const result = await Follow.getFollowing(userId, page, limit);

      // 如果有当前用户且不是本人，检查关注状态
      if (currentUserId && currentUserId !== userId && result.data.length > 0) {
        const userIds = result.data.map(user => user.id);
        const followStatus = await Follow.batchCheckFollowStatus(currentUserId, userIds);
        
        result.data = result.data.map(user => ({
          ...user,
          is_following: followStatus[user.id] || false
        }));
      }

      return result;
    } catch (error) {
      logger.error(`获取关注列表失败: userId=${userId}`, error);
      throw error;
    }
  }

  // 获取用户关注统计
  async getFollowStats(userId) {
    try {
      return await Follow.getUserFollowStats(userId);
    } catch (error) {
      logger.error(`获取关注统计失败: userId=${userId}`, error);
      throw error;
    }
  }

  // 检查关注状态
  async checkFollowStatus(followerId, followedId) {
    try {
      const isFollowing = await Follow.checkFollowStatus(followerId, followedId);
      return { is_following: isFollowing };
    } catch (error) {
      logger.error(`检查关注状态失败: ${followerId} -> ${followedId}`, error);
      throw error;
    }
  }

  // 发送关注通知
  async sendFollowNotification(followerId, followedId) {
    try {
      const follower = await User.findById(followerId);
      
      await notificationService.createNotification({
        user_id: followedId,
        type: 'follow',
        title: '新增粉丝',
        message: `${follower.nickname || follower.username} 关注了你`,
        reference_id: followerId,
        reference_type: 'user'
      });

      logger.info(`关注通知发送成功: ${followerId} -> ${followedId}`);
    } catch (error) {
      // 通知发送失败不影响关注操作
      logger.error(`发送关注通知失败: ${followerId} -> ${followedId}`, error);
    }
  }

  // 获取关注者的最新视频 (用于推荐)
  async getFollowingVideos(userId, page = 1, limit = 20) {
    try {
      return await Follow.getFollowingVideos(userId, page, limit);
    } catch (error) {
      logger.error(`获取关注动态失败: userId=${userId}`, error);
      throw error;
    }
  }

  // 批量检查关注状态
  async batchCheckFollowStatus(followerId, userIds) {
    try {
      return await Follow.batchCheckFollowStatus(followerId, userIds);
    } catch (error) {
      logger.error(`批量检查关注状态失败: followerId=${followerId}`, error);
      throw error;
    }
  }

  // 获取用户的互相关注列表 (相互关注的用户)
  async getMutualFollows(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      
      const sql = `
        SELECT 
          u.id, u.username, u.nickname, u.avatar, u.bio,
          us.follower_count, us.following_count, us.video_count,
          f1.created_at as follow_time
        FROM follows f1
        JOIN follows f2 ON f1.followed_id = f2.follower_id AND f1.follower_id = f2.followed_id
        JOIN users u ON f1.followed_id = u.id
        LEFT JOIN user_stats us ON u.id = us.user_id
        WHERE f1.follower_id = ? AND f1.status = 'active' AND f2.status = 'active'
        ORDER BY f1.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const mutualFollows = await Follow.query(sql, [userId, limit, offset]);

      const countSql = `
        SELECT COUNT(*) as total
        FROM follows f1
        JOIN follows f2 ON f1.followed_id = f2.follower_id AND f1.follower_id = f2.followed_id
        WHERE f1.follower_id = ? AND f1.status = 'active' AND f2.status = 'active'
      `;
      const countResult = await Follow.query(countSql, [userId]);

      return {
        data: mutualFollows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: countResult[0].total,
          totalPages: Math.ceil(countResult[0].total / limit)
        }
      };
    } catch (error) {
      logger.error(`获取相互关注列表失败: userId=${userId}`, error);
      throw error;
    }
  }

  // 推荐关注用户 (基于共同关注)
  async getRecommendedUsers(userId, limit = 10) {
    try {
      const sql = `
        SELECT 
          u.id, u.username, u.nickname, u.avatar, u.bio,
          us.follower_count, us.following_count, us.video_count,
          COUNT(f2.followed_id) as mutual_follows_count
        FROM follows f1
        JOIN follows f2 ON f1.followed_id = f2.follower_id
        JOIN users u ON f2.followed_id = u.id
        LEFT JOIN user_stats us ON u.id = us.user_id
        WHERE f1.follower_id = ? 
          AND f1.status = 'active' 
          AND f2.status = 'active'
          AND f2.followed_id != ?
          AND f2.followed_id NOT IN (
            SELECT followed_id 
            FROM follows 
            WHERE follower_id = ? AND status = 'active'
          )
        GROUP BY u.id, u.username, u.nickname, u.avatar, u.bio, 
                 us.follower_count, us.following_count, us.video_count
        ORDER BY mutual_follows_count DESC, us.follower_count DESC
        LIMIT ?
      `;

      const recommendedUsers = await Follow.query(sql, [userId, userId, userId, limit]);
      return recommendedUsers;
    } catch (error) {
      logger.error(`获取推荐用户失败: userId=${userId}`, error);
      throw error;
    }
  }
  // 更新所有用户的统计数据
  async updateAllUserStats() {
    try {
      const sql = `
        INSERT INTO user_stats (user_id, follower_count, following_count, video_count, total_views, total_likes)
        SELECT 
          u.id,
          COALESCE(followers.count, 0) as follower_count,
          COALESCE(following.count, 0) as following_count,
          COALESCE(videos.count, 0) as video_count,
          0 as total_views,
          0 as total_likes
        FROM users u
        LEFT JOIN (
          SELECT followed_id, COUNT(*) as count 
          FROM follows 
          WHERE status = 'active' 
          GROUP BY followed_id
        ) followers ON u.id = followers.followed_id
        LEFT JOIN (
          SELECT follower_id, COUNT(*) as count 
          FROM follows 
          WHERE status = 'active' 
          GROUP BY follower_id
        ) following ON u.id = following.follower_id
        LEFT JOIN (
          SELECT user_id, COUNT(*) as count 
          FROM videos 
          WHERE status = 'published' 
          GROUP BY user_id
        ) videos ON u.id = videos.user_id
        ON DUPLICATE KEY UPDATE
        follower_count = VALUES(follower_count),
        following_count = VALUES(following_count),
        video_count = VALUES(video_count)
      `;

      const result = await Follow.query(sql);
      logger.info('所有用户统计数据已更新');
      return { updated: true, affectedRows: result.affectedRows };
    } catch (error) {
      logger.error('更新用户统计数据失败:', error);
      throw error;
    }
  }
}

module.exports = new FollowService();