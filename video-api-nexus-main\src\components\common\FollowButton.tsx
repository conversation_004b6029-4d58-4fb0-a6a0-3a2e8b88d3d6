import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { followApi } from '../../services/followApi';
import { useAuth } from '../../hooks/useAuth';
import { useFollowContext } from '../../contexts/FollowContext';
import { toast } from 'sonner';
import { UserPlus, UserCheck } from 'lucide-react';

interface FollowButtonProps {
  userId: number;
  initialFollowStatus?: boolean;
  onFollowChange?: (isFollowing: boolean) => void;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
  showIcon?: boolean;
  className?: string;
}

export const FollowButton: React.FC<FollowButtonProps> = ({
  userId,
  initialFollowStatus = false,
  onFollowChange,
  size = 'default',
  variant = 'default',
  showIcon = true,
  className = ''
}) => {
  const { user } = useAuth();
  const { updateFollowStatus, refreshCounter } = useFollowContext();
  const [isFollowing, setIsFollowing] = useState(initialFollowStatus);
  const [loading, setLoading] = useState(false);

  // 如果是自己，不显示关注按钮
  if (user?.id === userId) {
    return null;
  }

  // 如果用户未登录，显示登录提示按钮
  if (!user) {
    return (
      <Button
        size={size}
        variant="outline"
        className={className}
        onClick={() => toast.error('请先登录')}
      >
        {showIcon && <UserPlus className="w-4 h-4 mr-1" />}
        关注
      </Button>
    );
  }

  // 检查关注状态
  useEffect(() => {
    const checkStatus = async () => {
      try {
        const response = await followApi.checkFollowStatus(userId);
        console.log('Follow status response:', response); // 调试日志
        // 安全地提取数据
        const isFollowing = response?.data?.data?.is_following ?? response?.data?.is_following ?? false;
        setIsFollowing(isFollowing);
      } catch (error) {
        console.error('检查关注状态失败:', error);
        setIsFollowing(false);
      }
    };

    if (userId && user) {
      checkStatus();
    }
  }, [userId, user, refreshCounter]);

  const handleFollowToggle = async () => {
    setLoading(true);
    try {
      if (isFollowing) {
        await followApi.unfollowUser(userId);
        setIsFollowing(false);
        toast.success('取消关注成功');
        onFollowChange?.(false);
        updateFollowStatus(userId, false);
      } else {
        await followApi.followUser(userId);
        setIsFollowing(true);
        toast.success('关注成功');
        onFollowChange?.(true);
        updateFollowStatus(userId, true);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '操作失败';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={handleFollowToggle}
      disabled={loading}
      size={size}
      variant={isFollowing ? 'outline' : variant}
      className={`
        ${isFollowing ? 'text-gray-600 hover:text-red-600 hover:border-red-300' : ''}
        ${className}
      `}
    >
      {loading ? (
        <div className="w-4 h-4 mr-1 animate-spin border-2 border-current border-t-transparent rounded-full" />
      ) : (
        showIcon && (
          isFollowing ? (
            <UserCheck className="w-4 h-4 mr-1" />
          ) : (
            <UserPlus className="w-4 h-4 mr-1" />
          )
        )
      )}
      {loading ? '处理中...' : (isFollowing ? '已关注' : '+ 关注')}
    </Button>
  );
};