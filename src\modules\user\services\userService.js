const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const User = require('../../../database/models/User');
const emailService = require('../../../services/emailService');
const { AppError } = require('../../../middleware/errorHandler');
const crypto = require('crypto');
const VerificationCodeUtil = require('../../../utils/verificationCode');

class UserService {
  constructor() {
    this.userModel = User;
  }

  // 获取用户完整信息（包含统计数据）
  async getUserFullInfo(userId) {
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'full_info', userId);
    let userInfo = await cache.get(cacheKey);
    
    if (!userInfo) {
      userInfo = await User.getUserStats(userId);
      if (userInfo) {
        // 缓存5分钟
        await cache.set(cacheKey, userInfo, 300);
      }
    }
    
    return userInfo;
  }
  
  // 更新用户活跃状态
  async updateUserActivity(userId, activityType = 'general') {
    const activityKey = cache.generateKey(CACHE_KEYS.USER, 'activity', userId);
    const now = new Date();
    
    const activityData = {
      lastActivity: now,
      activityType,
      timestamp: now.getTime()
    };
    
    await cache.set(activityKey, activityData, 3600); // 1小时缓存
    
    // 每10分钟更新一次数据库
    const lastUpdateKey = cache.generateKey(CACHE_KEYS.USER, 'last_update', userId);
    const lastUpdate = await cache.get(lastUpdateKey);
    
    if (!lastUpdate || now.getTime() - lastUpdate > 10 * 60 * 1000) {
      await User.update(userId, { last_activity_at: now });
      await cache.set(lastUpdateKey, now.getTime(), 600);
    }
  }
  
  // 获取用户活跃状态
  async getUserActivity(userId) {
    const activityKey = cache.generateKey(CACHE_KEYS.USER, 'activity', userId);
    return await cache.get(activityKey);
  }
  
  // 检查用户是否在线
  async isUserOnline(userId, threshold = 5 * 60 * 1000) { // 5分钟内活跃算在线
    const activity = await this.getUserActivity(userId);
    if (!activity) return false;
    
    const now = Date.now();
    return (now - activity.timestamp) < threshold;
  }
  
  // 获取用户偏好设置
  async getUserPreferences(userId) {
    const prefsKey = cache.generateKey(CACHE_KEYS.USER, 'preferences', userId);
    let preferences = await cache.get(prefsKey);
    
    if (!preferences) {
      // 从数据库获取或设置默认值
      preferences = {
        language: 'zh-CN',
        theme: 'light',
        notifications: {
          email: true,
          push: true,
          comments: true,
          likes: true,
          follows: true
        },
        privacy: {
          profileVisible: true,
          showEmail: false,
          showPhone: false
        },
        video: {
          autoplay: true,
          quality: 'auto',
          subtitles: false
        }
      };
      
      await cache.set(prefsKey, preferences, 24 * 3600); // 24小时缓存
    }
    
    return preferences;
  }
  
  // 更新用户偏好设置
  async updateUserPreferences(userId, newPreferences) {
    const prefsKey = cache.generateKey(CACHE_KEYS.USER, 'preferences', userId);
    const currentPrefs = await this.getUserPreferences(userId);
    
    // 合并设置
    const updatedPrefs = this.deepMerge(currentPrefs, newPreferences);
    
    // 更新缓存
    await cache.set(prefsKey, updatedPrefs, 24 * 3600);
    
    // 这里应该更新数据库中的用户偏好设置
    // 暂时只更新缓存
    
    logger.info(`用户偏好设置已更新: ${userId}`);
    return updatedPrefs;
  }
  
  // 深度合并对象
  deepMerge(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }
  
  // 获取用户关注列表
  async getUserFollowing(userId, page = 1, pageSize = 20) {
    // 这里应该从数据库获取关注列表
    // 暂时返回空数组
    return {
      data: [],
      pagination: {
        page,
        pageSize,
        total: 0,
        totalPages: 0
      }
    };
  }
  
  // 获取用户粉丝列表
  async getUserFollowers(userId, page = 1, pageSize = 20) {
    // 这里应该从数据库获取粉丝列表
    // 暂时返回空数组
    return {
      data: [],
      pagination: {
        page,
        pageSize,
        total: 0,
        totalPages: 0
      }
    };
  }
  
  // 关注用户
  async followUser(followerId, followeeId) {
    if (followerId === followeeId) {
      throw new Error('不能关注自己');
    }
    
    // 检查是否已关注
    const isFollowing = await this.isUserFollowing(followerId, followeeId);
    if (isFollowing) {
      throw new Error('已经关注了该用户');
    }
    
    // 这里应该在数据库中创建关注关系
    // 暂时只记录日志
    logger.info(`用户关注: ${followerId} -> ${followeeId}`);
    
    // 清除相关缓存
    await this.clearUserRelationCache(followerId, followeeId);
    
    return true;
  }
  
  // 取消关注用户
  async unfollowUser(followerId, followeeId) {
    // 检查是否已关注
    const isFollowing = await this.isUserFollowing(followerId, followeeId);
    if (!isFollowing) {
      throw new Error('未关注该用户');
    }
    
    // 这里应该在数据库中删除关注关系
    // 暂时只记录日志
    logger.info(`取消关注: ${followerId} -> ${followeeId}`);
    
    // 清除相关缓存
    await this.clearUserRelationCache(followerId, followeeId);
    
    return true;
  }
  
  // 检查是否关注某用户
  async isUserFollowing(followerId, followeeId) {
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'following', followerId, followeeId);
    let isFollowing = await cache.get(cacheKey);
    
    if (isFollowing === null) {
      // 从数据库查询
      // 暂时返回false
      isFollowing = false;
      await cache.set(cacheKey, isFollowing, 3600); // 1小时缓存
    }
    
    return isFollowing;
  }
  
  // 清除用户关系缓存
  async clearUserRelationCache(userId1, userId2) {
    const patterns = [
      `${CACHE_KEYS.USER}:following:${userId1}:*`,
      `${CACHE_KEYS.USER}:following:*:${userId2}`,
      `${CACHE_KEYS.USER}:followers:${userId1}:*`,
      `${CACHE_KEYS.USER}:followers:*:${userId2}`
    ];
    
    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
  }
  
  // 获取用户推荐列表
  async getRecommendedUsers(userId, limit = 10) {
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'recommendations', userId);
    let recommendations = await cache.get(cacheKey);
    
    if (!recommendations) {
      // 简单的推荐算法：获取最新注册的活跃用户
      recommendations = await User.findAll(
        { status: 'active' },
        { 
          orderBy: 'created_at',
          order: 'DESC',
          limit 
        }
      );
      
      // 过滤掉自己
      recommendations = recommendations.filter(user => user.id !== userId);
      
      // 移除敏感信息
      recommendations = recommendations.map(user => ({
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        avatar: user.avatar,
        role: user.role,
        created_at: user.created_at
      }));
      
      await cache.set(cacheKey, recommendations, 3600); // 1小时缓存
    }
    
    return recommendations;
  }
  
  // 搜索用户
  async searchUsers(keyword, page = 1, pageSize = 20, filters = {}) {
    // 构建缓存键
    const cacheKey = cache.generateKey(
      CACHE_KEYS.SEARCH, 
      'users', 
      keyword, 
      page, 
      pageSize, 
      JSON.stringify(filters)
    );
    
    let result = await cache.get(cacheKey);
    
    if (!result) {
      result = await User.searchUsers(keyword, page, pageSize);
      
      // 移除敏感信息
      result.data = result.data.map(user => ({
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        avatar: user.avatar,
        role: user.role,
        email_verified: user.email_verified,
        created_at: user.created_at
      }));
      
      await cache.set(cacheKey, result, 300); // 5分钟缓存
    }
    
    return result;
  }

  async sendChangeEmailCode(userId, newEmail) {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    const existingUser = await this.userModel.findByEmail(newEmail);
    if (existingUser && existingUser.id !== userId) {
      throw new AppError('该邮箱已被其他用户注册', 409);
    }

    // 使用新的验证码生成工具
    const codeConfig = VerificationCodeUtil.generateCodeConfig('email_change');
    const code = codeConfig.code;

    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'change_email_code', userId);
    await cache.set(cacheKey, { code, email: newEmail }, codeConfig.ttl); // 使用配置的TTL

    const subject = '您的邮箱更改验证码';
    const text = `您正在申请更改您的邮箱地址。您的验证码是: ${code}。该验证码10分钟内有效。如果您没有申请此操作，请忽略此邮件。`;
    const html = `
      <p>您好,</p>
      <p>您正在申请更改您的邮箱地址。您的验证码是: <strong>${code}</strong></p>
      <p>该验证码10分钟内有效。如果您没有申请此操作，请忽略此邮件。</p>
      <p>祝好！</p>
    `;

    // 发送邮件
    const result = await emailService.sendMail({
      to: newEmail,
      subject,
      text,
      html
    });

    if (!result.success) {
      // 检查是否因为服务未配置而失败
      if (result.error === 'Mail service not configured') {
        if (process.env.NODE_ENV === 'development') {
          logger.warn(`邮件服务未配置，验证码将打印到控制台 (仅限开发环境)`);
          logger.info(`--- 邮箱更改验证码 for user ${userId} to ${newEmail}: ${code} ---`);
          // 在开发模式下，即使邮件发送失败（因为未配置），也应成功返回
          return;
        } else {
          // 生产环境下邮件服务未配置是严重错误
          logger.error('生产环境邮件服务未配置，无法发送验证码');
          throw new AppError('邮件服务暂时不可用，请稍后重试', 503);
        }
      }

      // 其他邮件发送错误
      logger.error(`向 ${newEmail} 发送邮箱更改验证码失败`, {
        error: result.error,
        userId,
        newEmail: VerificationCodeUtil.formatCodeForDisplay(newEmail, 'email')
      });

      // 提供用户友好的错误信息
      const userFriendlyError = this.getUserFriendlyEmailError(result.error);
      throw new AppError(userFriendlyError, 500);
    }

    logger.info(`已向 ${newEmail} 发送邮箱更改验证码`, {
      userId,
      codeType: 'email_change',
      ttl: codeConfig.ttl
    });
  }
  
  async verifyChangeEmail(userId, newEmail, code) {
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'change_email_code', userId);
    const cachedData = await cache.get(cacheKey);

    if (!cachedData) {
      throw new AppError('验证码已过期或不存在', 400, 'INVALID_OR_EXPIRED_CODE');
    }

    const { email: cachedEmail, code: cachedCode } = cachedData;

    // 验证邮箱和验证码
    if (cachedEmail !== newEmail) {
      throw new AppError('邮箱地址不匹配', 400, 'EMAIL_MISMATCH');
    }

    // 使用验证码工具进行比较（支持大小写不敏感）
    if (!VerificationCodeUtil.compareCode(code, cachedCode)) {
      throw new AppError('验证码不正确', 400, 'INVALID_CODE');
    }

    // 更新用户邮箱
    await this.userModel.update(userId, { email: newEmail });

    // 清除缓存
    await cache.del(cacheKey);

    // 记录操作日志
    logger.info(`用户 ${userId} 成功更换邮箱至: ${newEmail}`);

    return true;
  }

  // 获取用户徽章
  async getUserBadges(userId) {
    const badges = [];
    const user = await User.findById(userId);
    
    if (!user) return badges;
    
    // 根据用户信息添加徽章
    if (user.email_verified) {
      badges.push({
        id: 'verified_email',
        name: '邮箱已验证',
        icon: 'email-check',
        color: 'green'
      });
    }
    
    if (user.role === 'admin') {
      badges.push({
        id: 'admin',
        name: '管理员',
        icon: 'shield',
        color: 'red'
      });
    } else if (user.role === 'vip') {
      badges.push({
        id: 'vip',
        name: 'VIP会员',
        icon: 'crown',
        color: 'gold'
      });
    } else if (user.role === 'member') {
      badges.push({
        id: 'member',
        name: '会员',
        icon: 'star',
        color: 'blue'
      });
    }
    
    // 注册时长徽章
    const daysSinceRegistration = Math.floor(
      (Date.now() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysSinceRegistration >= 365) {
      badges.push({
        id: 'veteran',
        name: '老用户',
        icon: 'calendar',
        color: 'purple'
      });
    } else if (daysSinceRegistration >= 30) {
      badges.push({
        id: 'regular',
        name: '常驻用户',
        icon: 'clock',
        color: 'blue'
      });
    }
    
    return badges;
  }
  
  // 清除用户所有缓存
  async clearUserCache(userId) {
    const patterns = [
      `${CACHE_KEYS.USER}:*:${userId}`,
      `${CACHE_KEYS.USER}:*:${userId}:*`
    ];

    let totalCleared = 0;
    for (const pattern of patterns) {
      const cleared = await cache.delPattern(pattern);
      totalCleared += cleared;
    }

    logger.info(`清除用户缓存: ${userId}, 共清除 ${totalCleared} 个缓存项`);
    return totalCleared;
  }

  /**
   * 将技术错误信息转换为用户友好的错误信息
   * @param {string} technicalError - 技术错误信息
   * @returns {string} 用户友好的错误信息
   */
  getUserFriendlyEmailError(technicalError) {
    if (!technicalError) {
      return '发送邮件时出现未知错误，请稍后重试';
    }

    const errorLower = technicalError.toLowerCase();

    if (errorLower.includes('timeout') || errorLower.includes('timed out')) {
      return '邮件发送超时，请检查网络连接后重试';
    }

    if (errorLower.includes('authentication') || errorLower.includes('auth')) {
      return '邮件服务认证失败，请联系管理员';
    }

    if (errorLower.includes('invalid') && errorLower.includes('email')) {
      return '邮箱地址格式不正确，请检查后重试';
    }

    if (errorLower.includes('connection') || errorLower.includes('connect')) {
      return '无法连接到邮件服务器，请稍后重试';
    }

    if (errorLower.includes('rate limit') || errorLower.includes('too many')) {
      return '发送频率过高，请稍后再试';
    }

    // 默认用户友好错误信息
    return '邮件发送失败，请稍后重试或联系客服';
  }
}

module.exports = new UserService();
