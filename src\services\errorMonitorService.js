const { logger, securityLogger, logUtils } = require('../utils/advancedLogger');
const { cache, CACHE_KEYS } = require('../utils/cache');

class ErrorMonitorService {
  constructor() {
    this.errorCounts = new Map();
    this.alertThresholds = {
      error_rate: 10,        // 10个错误/分钟
      response_time: 5000,   // 5秒响应时间
      memory_usage: 0.9,     // 90%内存使用率
      cpu_usage: 0.8,        // 80%CPU使用率
      disk_usage: 0.9        // 90%磁盘使用率
    };
    
    this.startMonitoring();
  }

  // 启动监控
  startMonitoring() {
    // 每分钟检查错误率
    setInterval(() => {
      this.checkErrorRate();
    }, 60000);

    // 每5分钟检查系统资源
    setInterval(() => {
      this.checkSystemResources();
    }, 300000);

    // 每小时生成错误报告
    setInterval(() => {
      this.generateErrorReport();
    }, 3600000);

    logger.info('错误监控系统已启动');
  }

  // 记录错误
  recordError(error, context = {}) {
    const errorKey = this.generateErrorKey(error);
    const timestamp = Date.now();
    
    // 增加错误计数
    if (!this.errorCounts.has(errorKey)) {
      this.errorCounts.set(errorKey, []);
    }
    
    this.errorCounts.get(errorKey).push(timestamp);
    
    // 清理1小时前的错误记录
    this.cleanupOldErrors(errorKey);
    
    // 记录详细错误信息
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      code: error.code || 'UNKNOWN',
      context,
      timestamp: new Date().toISOString(),
      count: this.errorCounts.get(errorKey).length
    };
    
    logger.error('应用错误', errorInfo);
    
    // 检查是否需要告警
    this.checkErrorAlert(errorKey, errorInfo);
    
    return errorInfo;
  }

  // 生成错误键
  generateErrorKey(error) {
    const message = error.message || 'Unknown Error';
    const code = error.code || 'UNKNOWN';
    return `${code}:${message.substring(0, 50)}`;
  }

  // 清理旧错误记录
  cleanupOldErrors(errorKey) {
    const oneHourAgo = Date.now() - 3600000;
    const errors = this.errorCounts.get(errorKey);
    
    if (errors) {
      const recentErrors = errors.filter(timestamp => timestamp > oneHourAgo);
      this.errorCounts.set(errorKey, recentErrors);
    }
  }

  // 检查错误告警
  checkErrorAlert(errorKey, errorInfo) {
    const errorCount = this.errorCounts.get(errorKey).length;
    const oneMinuteAgo = Date.now() - 60000;
    const recentErrors = this.errorCounts.get(errorKey).filter(
      timestamp => timestamp > oneMinuteAgo
    );
    
    // 如果1分钟内错误超过阈值，发送告警
    if (recentErrors.length >= this.alertThresholds.error_rate) {
      this.sendAlert('HIGH_ERROR_RATE', {
        errorKey,
        count: recentErrors.length,
        threshold: this.alertThresholds.error_rate,
        errorInfo
      });
    }
    
    // 如果同一错误累计超过100次，发送告警
    if (errorCount >= 100) {
      this.sendAlert('HIGH_ERROR_COUNT', {
        errorKey,
        totalCount: errorCount,
        errorInfo
      });
    }
  }

  // 检查错误率
  checkErrorRate() {
    const oneMinuteAgo = Date.now() - 60000;
    let totalErrors = 0;
    
    for (const [errorKey, timestamps] of this.errorCounts) {
      const recentErrors = timestamps.filter(timestamp => timestamp > oneMinuteAgo);
      totalErrors += recentErrors.length;
    }
    
    if (totalErrors >= this.alertThresholds.error_rate) {
      this.sendAlert('SYSTEM_ERROR_RATE', {
        totalErrors,
        threshold: this.alertThresholds.error_rate,
        timeWindow: '1分钟'
      });
    }
    
    // 记录错误率指标
    logUtils.logPerformance('error_rate_check', 0, {
      totalErrors,
      threshold: this.alertThresholds.error_rate
    });
  }

  // 检查系统资源
  async checkSystemResources() {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // 内存使用率检查
      const memoryUsagePercent = memoryUsage.heapUsed / memoryUsage.heapTotal;
      if (memoryUsagePercent > this.alertThresholds.memory_usage) {
        this.sendAlert('HIGH_MEMORY_USAGE', {
          usage: `${(memoryUsagePercent * 100).toFixed(2)}%`,
          threshold: `${(this.alertThresholds.memory_usage * 100).toFixed(2)}%`,
          heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
          heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`
        });
      }
      
      // 记录系统资源指标
      logUtils.logPerformance('system_resources', 0, {
        memory: {
          heapUsed: memoryUsage.heapUsed,
          heapTotal: memoryUsage.heapTotal,
          external: memoryUsage.external,
          rss: memoryUsage.rss
        },
        cpu: cpuUsage,
        uptime: process.uptime()
      });
      
    } catch (error) {
      logger.error('系统资源检查失败', { error: error.message });
    }
  }

  // 发送告警
  sendAlert(type, data) {
    const alert = {
      type,
      data,
      timestamp: new Date().toISOString(),
      severity: this.getAlertSeverity(type)
    };
    
    // 记录安全日志
    securityLogger.warn('系统告警', alert);
    
    // 这里可以集成邮件、短信、Slack等告警渠道
    this.processAlert(alert);
    
    // 缓存告警信息
    this.cacheAlert(alert);
  }

  // 获取告警严重程度
  getAlertSeverity(type) {
    const severityMap = {
      'HIGH_ERROR_RATE': 'critical',
      'HIGH_ERROR_COUNT': 'high',
      'SYSTEM_ERROR_RATE': 'critical',
      'HIGH_MEMORY_USAGE': 'high',
      'HIGH_CPU_USAGE': 'high',
      'HIGH_DISK_USAGE': 'critical',
      'DATABASE_CONNECTION_FAILED': 'critical',
      'REDIS_CONNECTION_FAILED': 'high'
    };
    
    return severityMap[type] || 'medium';
  }

  // 处理告警
  async processAlert(alert) {
    try {
      // 检查是否在冷却期内（避免重复告警）
      const cooldownKey = `alert_cooldown:${alert.type}`;
      const lastAlert = await cache.get(cooldownKey);
      
      if (lastAlert) {
        logger.debug('告警在冷却期内，跳过发送', { type: alert.type });
        return;
      }
      
      // 设置冷却期（5分钟）
      await cache.set(cooldownKey, Date.now(), 300);
      
      // 根据严重程度决定处理方式
      switch (alert.severity) {
        case 'critical':
          await this.sendCriticalAlert(alert);
          break;
        case 'high':
          await this.sendHighAlert(alert);
          break;
        default:
          await this.sendNormalAlert(alert);
      }
      
    } catch (error) {
      logger.error('处理告警失败', { error: error.message, alert });
    }
  }

  // 发送严重告警
  async sendCriticalAlert(alert) {
    logger.error('🚨 严重告警', alert);
    
    // 这里可以集成紧急通知渠道
    // 例如：短信、电话、PagerDuty等
    
    // 记录到特殊的严重告警日志
    securityLogger.error('严重系统告警', alert);
  }

  // 发送高级告警
  async sendHighAlert(alert) {
    logger.warn('⚠️ 高级告警', alert);
    
    // 这里可以集成邮件、Slack等通知
  }

  // 发送普通告警
  async sendNormalAlert(alert) {
    logger.info('ℹ️ 系统告警', alert);
  }

  // 缓存告警信息
  async cacheAlert(alert) {
    try {
      const alertKey = `${CACHE_KEYS.ADMIN}:alerts:${Date.now()}`;
      await cache.set(alertKey, alert, 86400); // 24小时
      
      // 维护告警列表
      const alertListKey = `${CACHE_KEYS.ADMIN}:alert_list`;
      const alertList = await cache.get(alertListKey) || [];
      alertList.unshift(alertKey);
      
      // 只保留最近100个告警
      if (alertList.length > 100) {
        const oldAlerts = alertList.splice(100);
        for (const oldAlertKey of oldAlerts) {
          await cache.del(oldAlertKey);
        }
      }
      
      await cache.set(alertListKey, alertList, 86400);
      
    } catch (error) {
      logger.error('缓存告警失败', { error: error.message });
    }
  }

  // 生成错误报告
  async generateErrorReport() {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        period: '1小时',
        summary: {
          totalErrors: 0,
          uniqueErrors: this.errorCounts.size,
          topErrors: []
        },
        details: []
      };
      
      // 统计错误
      const errorStats = [];
      for (const [errorKey, timestamps] of this.errorCounts) {
        const count = timestamps.length;
        report.summary.totalErrors += count;
        
        errorStats.push({
          errorKey,
          count,
          lastOccurrence: new Date(Math.max(...timestamps)).toISOString()
        });
      }
      
      // 排序获取Top错误
      errorStats.sort((a, b) => b.count - a.count);
      report.summary.topErrors = errorStats.slice(0, 10);
      report.details = errorStats;
      
      // 记录报告
      logger.info('错误报告生成', report);
      
      // 缓存报告
      const reportKey = `${CACHE_KEYS.ADMIN}:error_report:${Date.now()}`;
      await cache.set(reportKey, report, 604800); // 7天
      
    } catch (error) {
      logger.error('生成错误报告失败', { error: error.message });
    }
  }

  // 获取错误统计
  getErrorStats() {
    const stats = {
      totalUniqueErrors: this.errorCounts.size,
      totalErrorCount: 0,
      recentErrors: []
    };
    
    const oneHourAgo = Date.now() - 3600000;
    
    for (const [errorKey, timestamps] of this.errorCounts) {
      const recentTimestamps = timestamps.filter(t => t > oneHourAgo);
      stats.totalErrorCount += timestamps.length;
      
      if (recentTimestamps.length > 0) {
        stats.recentErrors.push({
          errorKey,
          count: timestamps.length,
          recentCount: recentTimestamps.length,
          lastOccurrence: new Date(Math.max(...timestamps)).toISOString()
        });
      }
    }
    
    stats.recentErrors.sort((a, b) => b.recentCount - a.recentCount);
    
    return stats;
  }

  // 清理错误监控数据
  cleanup() {
    this.errorCounts.clear();
    logger.info('错误监控数据已清理');
  }
}

// 创建全局错误监控实例
const errorMonitor = new ErrorMonitorService();

// 全局错误处理
process.on('uncaughtException', (error) => {
  errorMonitor.recordError(error, { type: 'uncaughtException' });
  logger.error('未捕获的异常', { error: error.message, stack: error.stack });
  
  // 给应用一些时间来记录错误，然后退出
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

process.on('unhandledRejection', (reason, promise) => {
  const error = reason instanceof Error ? reason : new Error(String(reason));
  errorMonitor.recordError(error, { type: 'unhandledRejection', promise });
  logger.error('未处理的Promise拒绝', { reason, promise });
});

module.exports = errorMonitor;
