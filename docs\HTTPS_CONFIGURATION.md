# 🔒 HTTPS配置指南

> 完整的HTTPS配置和SSL证书管理指南

## 📋 概述

本API系统支持HTTP和HTTPS双模式运行，提供灵活的SSL配置选项：

- ✅ **自动SSL检测** - 自动检测并加载SSL证书
- ✅ **HTTP/HTTPS双模式** - 同时支持HTTP和HTTPS访问
- ✅ **自动重定向** - 可配置HTTP到HTTPS的自动重定向
- ✅ **安全头配置** - 自动添加HTTPS安全头
- ✅ **证书验证** - 自动验证SSL证书有效性

## ⚙️ 环境变量配置

在`.env`文件中配置以下HTTPS相关变量：

```bash
# HTTPS配置
ENABLE_HTTPS=false              # 是否启用HTTPS
HTTPS_PORT=3443                 # HTTPS端口
SSL_CERT_PATH=./certs/certificate.crt  # SSL证书文件路径
SSL_KEY_PATH=./certs/private.key        # SSL私钥文件路径
FORCE_HTTPS=false               # 是否强制使用HTTPS
AUTO_REDIRECT_HTTPS=true        # 是否自动重定向HTTP到HTTPS
```

### 配置说明

| 变量 | 默认值 | 说明 |
|------|--------|------|
| `ENABLE_HTTPS` | `false` | 启用HTTPS服务器 |
| `HTTPS_PORT` | `3443` | HTTPS服务器端口 |
| `SSL_CERT_PATH` | `./certs/certificate.crt` | SSL证书文件路径 |
| `SSL_KEY_PATH` | `./certs/private.key` | SSL私钥文件路径 |
| `FORCE_HTTPS` | `false` | 强制HTTPS，拒绝HTTP请求 |
| `AUTO_REDIRECT_HTTPS` | `true` | 自动将HTTP重定向到HTTPS |

## 🔐 SSL证书配置

### 方式1: 使用现有证书

如果您已有SSL证书，请将证书文件放置到指定位置：

```bash
# 创建证书目录
mkdir -p certs

# 复制证书文件
cp your-certificate.crt certs/certificate.crt
cp your-private-key.key certs/private.key

# 设置适当的权限
chmod 600 certs/private.key
chmod 644 certs/certificate.crt
```

### 方式2: Let's Encrypt证书

使用Certbot获取免费SSL证书：

```bash
# 安装Certbot
sudo apt-get install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem certs/certificate.crt
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem certs/private.key
```

### 方式3: 自签名证书（仅用于开发）

生成自签名证书用于开发测试：

```bash
# 使用OpenSSL生成自签名证书
openssl genrsa -out certs/private.key 2048
openssl req -new -x509 -key certs/private.key -out certs/certificate.crt -days 365 \
  -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
```

或使用内置脚本：

```bash
# 运行HTTPS配置验证脚本，会自动生成测试证书
node scripts/verify-https-config.js
```

## 🚀 启动配置

### 配置示例

#### 1. 仅HTTP模式（默认）
```bash
# .env
ENABLE_HTTPS=false
PORT=3000
```

访问地址：
- API文档: http://localhost:3000/docs
- 健康检查: http://localhost:3000/health

#### 2. HTTP + HTTPS双模式
```bash
# .env
ENABLE_HTTPS=true
PORT=3000
HTTPS_PORT=3443
SSL_CERT_PATH=./certs/certificate.crt
SSL_KEY_PATH=./certs/private.key
AUTO_REDIRECT_HTTPS=true
```

访问地址：
- HTTP API文档: http://localhost:3000/docs (会重定向到HTTPS)
- HTTPS API文档: https://localhost:3443/docs
- HTTP健康检查: http://localhost:3000/health
- HTTPS健康检查: https://localhost:3443/health

#### 3. 强制HTTPS模式
```bash
# .env
ENABLE_HTTPS=true
FORCE_HTTPS=true
HTTPS_PORT=3443
SSL_CERT_PATH=./certs/certificate.crt
SSL_KEY_PATH=./certs/private.key
```

访问地址：
- 仅HTTPS API文档: https://localhost:3443/docs
- HTTP请求将被拒绝并返回426状态码

## 🔧 验证配置

使用内置验证脚本检查HTTPS配置：

```bash
# 验证HTTPS配置
node scripts/verify-https-config.js
```

验证内容包括：
- ✅ 环境变量配置检查
- ✅ SSL证书文件检查
- ✅ 证书格式验证
- ✅ HTTPS服务器启动测试
- ✅ 中间件功能检查

## 🛡️ 安全特性

### 自动安全头

HTTPS连接会自动添加以下安全头：

```http
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'
```

### HTTP重定向

当启用`AUTO_REDIRECT_HTTPS=true`时：
- HTTP请求自动重定向到HTTPS（301重定向）
- 排除特定路径（如支付回调）
- 保持原始URL参数和路径

### 强制HTTPS

当启用`FORCE_HTTPS=true`时：
- 拒绝所有HTTP请求
- 返回426状态码（Upgrade Required）
- 提供HTTPS升级建议

## 🔍 调试和监控

### 调试接口

访问调试接口查看HTTPS状态：

```bash
# 开发环境下可访问
GET /debug/https
```

返回信息包括：
- 请求协议信息
- SSL配置状态
- 环境变量设置

### 日志监控

系统会记录以下HTTPS相关日志：

```bash
# SSL配置日志
✅ SSL证书加载成功
✅ HTTPS服务器启动成功，端口: 3443

# 重定向日志
HTTP重定向到HTTPS: /api/videos -> https://localhost:3443/api/videos

# 安全日志
拒绝HTTP请求: GET /api/auth/login from 127.0.0.1
```

## 📱 客户端配置

### JavaScript/Node.js客户端

```javascript
// 支持HTTPS的API客户端
const axios = require('axios');

const apiClient = axios.create({
  baseURL: 'https://localhost:3443/api',
  timeout: 10000,
  // 开发环境下忽略自签名证书错误
  httpsAgent: process.env.NODE_ENV === 'development' ? 
    new require('https').Agent({ rejectUnauthorized: false }) : undefined
});
```

### 移动端配置

```javascript
// React Native
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api'  // 开发环境
  : 'https://yourdomain.com/api'; // 生产环境
```

## 🚀 生产环境部署

### Nginx反向代理配置

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Docker配置

```dockerfile
# 在Dockerfile中暴露HTTPS端口
EXPOSE 3000 3443

# 复制SSL证书
COPY certs/ /app/certs/
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "3000:3000"
      - "3443:3443"
    environment:
      - ENABLE_HTTPS=true
      - SSL_CERT_PATH=/app/certs/certificate.crt
      - SSL_KEY_PATH=/app/certs/private.key
    volumes:
      - ./certs:/app/certs:ro
```

## ❓ 常见问题

### Q: 为什么HTTPS服务器启动失败？
A: 检查以下几点：
1. SSL证书文件是否存在且路径正确
2. 证书文件格式是否正确
3. 私钥文件权限是否正确（建议600）
4. 端口是否被占用

### Q: 如何在开发环境中使用HTTPS？
A: 使用自签名证书：
```bash
node scripts/verify-https-config.js
```
脚本会自动生成测试证书。

### Q: 生产环境如何配置SSL？
A: 建议使用Let's Encrypt免费证书或购买商业证书，并配置Nginx反向代理。

### Q: 如何强制所有请求使用HTTPS？
A: 设置环境变量：
```bash
FORCE_HTTPS=true
```

## 🎯 最佳实践

1. **生产环境**：使用有效的SSL证书，启用FORCE_HTTPS
2. **开发环境**：使用自签名证书，启用AUTO_REDIRECT_HTTPS
3. **测试环境**：使用HTTP模式以简化测试
4. **证书管理**：定期更新SSL证书，监控证书过期时间
5. **安全配置**：启用所有安全头，使用强加密算法

## 🔗 相关链接

- [SSL证书申请](https://letsencrypt.org/)
- [Nginx HTTPS配置](https://nginx.org/en/docs/http/configuring_https_servers.html)
- [Node.js HTTPS文档](https://nodejs.org/api/https.html)
