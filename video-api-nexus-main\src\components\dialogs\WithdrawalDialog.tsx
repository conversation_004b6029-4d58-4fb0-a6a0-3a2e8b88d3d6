import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { requestWithdrawal } from '@/services/api';

// Placeholder for the actual API call
/*
const requestWithdrawal = async (data) => {
  console.log("Submitting withdrawal request:", data);
  // Simulate an API call
  await new Promise(resolve => setTimeout(resolve, 1000));
  // In a real scenario, this would throw an error on failure
  if (data.amount > 1000) throw new Error("提现金额不能超过1000元");
  return { success: true, data: { ...data, id: Date.now(), status: 'pending' } };
};
*/

const withdrawalSchema = z.object({
  amount: z.coerce
    .number()
    .positive({ message: '提现金额必须为正数。' })
    .min(1, { message: '最低提现金额为1元。' }),
  walletAddress: z.string().min(34, { message: '请输入有效的TRON钱包地址。' }),
});

interface WithdrawalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

const WithdrawalDialog: React.FC<WithdrawalDialogProps> = ({ open, onOpenChange, onSuccess }) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<z.infer<typeof withdrawalSchema>>({
    resolver: zodResolver(withdrawalSchema),
    defaultValues: {
      amount: 0,
      walletAddress: '',
    },
  });

  const mutation = useMutation({
    mutationFn: (values: z.infer<typeof withdrawalSchema>) => 
      requestWithdrawal({ ...values, walletType: 'TRON_USDT' }),
    onSuccess: () => {
      toast({
        title: '提现申请成功',
        description: '您的提现请求已提交，正在等待管理员审核。',
      });
      onSuccess();
      onOpenChange(false);
      form.reset();
      // Invalidate queries to refetch user balance and withdrawal history
      queryClient.invalidateQueries({ queryKey: ['earningsSummary'] });
      queryClient.invalidateQueries({ queryKey: ['withdrawalHistory'] });
    },
    onError: (error: any) => {
      toast({
        title: '提现申请失败',
        description: error.message || '处理您的请求时发生错误。',
        variant: 'destructive',
      });
    },
  });

  function onSubmit(values: z.infer<typeof withdrawalSchema>) {
    mutation.mutate(values);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>申请提现</DialogTitle>
          <DialogDescription>
            提现将通过TRON (USDT-TRC20)网络处理。请确保您的钱包地址正确无误。
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>提现金额 (元)</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="请输入提现金额" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="walletAddress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>USDT-TRC20 钱包地址</FormLabel>
                  <FormControl>
                    <Input placeholder="T..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={mutation.isPending}>
                取消
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                提交申请
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default WithdrawalDialog; 