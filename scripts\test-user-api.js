#!/usr/bin/env node

/**
 * 测试用户API返回的字段
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const User = require('../src/database/models/User');
const logger = require('../src/utils/logger');

async function testUserAPI() {
  try {
    logger.info('测试用户API字段...');
    
    // 测试getUserList方法
    const result = await User.getUserList(1, 5);
    
    logger.info('getUserList 返回的数据结构:');
    if (result.data && result.data.length > 0) {
      const firstUser = result.data[0];
      logger.info('第一个用户的字段:');
      Object.keys(firstUser).forEach(key => {
        logger.info(`  ${key}: ${firstUser[key]}`);
      });
      
      // 检查关键字段
      if (firstUser.last_login !== undefined) {
        logger.info('✅ last_login 字段存在');
      } else {
        logger.error('❌ last_login 字段缺失');
      }
      
      if (firstUser.last_login_at !== undefined) {
        logger.warn('⚠️ last_login_at 字段仍然存在（应该被移除）');
      } else {
        logger.info('✅ last_login_at 字段已正确移除');
      }
    } else {
      logger.warn('没有找到用户数据');
    }
    
  } catch (error) {
    logger.error('测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testUserAPI()
    .then(() => {
      logger.info('测试完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testUserAPI };
