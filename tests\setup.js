// 测试环境设置
process.env.NODE_ENV = 'test';
process.env.DB_NAME = 'video_platform_test';
process.env.REDIS_DB = '1'; // 使用不同的Redis数据库

const { expect } = require('chai');

// 全局测试配置
global.expect = expect;

// 测试前清理
before(async () => {
  console.log('🧪 初始化测试环境...');
  
  // 这里可以添加测试数据库初始化逻辑
  // 例如：清空测试数据库、插入测试数据等
});

// 测试后清理
after(async () => {
  console.log('🧹 清理测试环境...');
  
  // 清理测试数据
});

// 每个测试前的设置
beforeEach(() => {
  // 可以在这里重置某些状态
});

// 每个测试后的清理
afterEach(() => {
  // 清理单个测试的副作用
});

module.exports = {
  // 测试工具函数
  createTestUser: async (userData = {}) => {
    const defaultUser = {
      username: `test_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'password123',
      nickname: '测试用户'
    };
    
    return { ...defaultUser, ...userData };
  },
  
  // 生成随机测试数据
  generateRandomString: (length = 10) => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  // 等待函数
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms))
};
