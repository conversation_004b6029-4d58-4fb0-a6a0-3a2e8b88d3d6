const express = require('express');
const { auth } = require('../../../middleware/auth');
const { createCreemOrder } = require('../controllers/creemController');
const { handleCreemWebhook } = require('../controllers/webhookController');

const router = express.Router();

// Creem Webhook - 必须在其他路由之前，不需要认证
router.post('/webhook', (req, res, next) => {
  // 直接处理，跳过全局的JSON解析中间件
  handleCreemWebhook(req, res, next);
});

// 创建Creem订单 - 需要认证
router.post('/create-order', auth, createCreemOrder);

module.exports = router; 