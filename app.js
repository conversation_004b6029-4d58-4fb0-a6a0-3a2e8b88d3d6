require('dotenv').config();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
const http = require('http');
const https = require('https');
const fs = require('fs');

// 确保 uploads 目录存在
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 导入配置和工具
const logger = require('./src/utils/logger');
const { errorHandler } = require('./src/middleware/errorHandler');
const authMiddleware = require('./src/middleware/auth');

// 导入HTTPS相关模块
const { autoConfigureSSL, getSSLStatus } = require('./src/config/ssl');
const {
  httpsRedirect,
  securityHeaders,
  httpsStatus,
  httpsDebug
} = require('./src/middleware/httpsRedirect');

// 导入监控系统
const { performanceMonitor, startPeriodicMonitoring, healthCheck } = require('./src/middleware/performanceMonitor');
const errorMonitor = require('./src/services/errorMonitorService');

// 导入模块路由
const authRoutes = require('./src/modules/auth/routes');
const userRoutes = require('./src/modules/user/routes');
const videoRoutes = require('./src/modules/video/routes');
const interactionRoutes = require('./src/modules/interaction/routes');
const memberRoutes = require('./src/modules/member/routes');
const commentRoutes = require('./src/modules/comment/routes');
const adminRoutes = require('./src/modules/admin/routes');
const paymentRoutes = require('./src/modules/payment/routes');
const creemRoutes = require('./src/modules/creem/routes');
const creemPaymentRoutes = require('./src/modules/payment/routes/creemRoutes');
const watchRoutes = require('./src/modules/watch/routes');
const playlistRoutes = require('./src/routes/playlistRoutes');
const balanceRoutes = require('./src/modules/balance/routes');
const notificationRoutes = require('./src/modules/notification/routes');
const tronTransactionScannerService = require('./src/services/tronTransactionScannerService');
const earningsRoutes = require('./src/modules/earnings/routes'); // 1. Import earnings routes
const settingsRoutes = require('./src/routes/settingsRoutes');
const followRoutes = require('./src/modules/follow/routes');

// 启动TRON交易扫描服务
if (process.env.NODE_ENV !== 'test') {
  tronTransactionScannerService.start();
}


const app = express();

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS配置
const allowedOrigins = (process.env.ALLOWED_ORIGINS || 'http://localhost:8081,http://localhost:5173').split(',');
app.use(cors({
  origin: (origin, callback) => {
    // 允许没有源的请求 (如服务器到服务器, REST工具) 或 在白名单中的源
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true
}));

/*
// 请求限流
const limiter = rateLimit({
  windowMs: (process.env.RATE_LIMIT_WINDOW || 15) * 60 * 1000, // 15分钟
  max: process.env.RATE_LIMIT_MAX || 100, // 限制每个IP 100个请求
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 'RATE_LIMIT_EXCEEDED'
  }
});
app.use('/api/', limiter);
*/

// 解析请求体
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.resolve('./uploads')));

// HTTPS相关中间件
app.use(httpsStatus);
app.use(httpsRedirect());
app.use(securityHeaders);
app.use(httpsDebug);

// 性能监控中间件
app.use(performanceMonitor);

// 请求日志中间件
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.url} - ${req.ip}`);
  next();
});

// 健康检查
app.get('/health', async (req, res) => {
  try {
    const { dbHealthMonitor } = require('./src/utils/dbHealth');
    const connectionManager = require('./src/database/ConnectionManager');

    // 执行健康检查
    const dbHealth = await dbHealthMonitor.performHealthCheck();
    const poolStatus = connectionManager.getPoolStatus();

    const healthStatus = {
      status: dbHealth.status === 'healthy' ? 'ok' : 'degraded',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      database: {
        mysql: dbHealth.mysql,
        redis: dbHealth.redis
      },
      connections: poolStatus
    };

    const statusCode = healthStatus.status === 'ok' ? 200 : 503;
    res.status(statusCode).json(healthStatus);

  } catch (error) {
    res.status(503).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      error: error.message
    });
  }
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/user', userRoutes);
app.use('/api/video', videoRoutes);
app.use('/api/interaction', interactionRoutes);
app.use('/api/member', memberRoutes);
app.use('/api/creem', creemRoutes);
app.use('/api/comment', commentRoutes);
app.use('/api/admin', authMiddleware.verifyToken, authMiddleware.requireAdmin, adminRoutes);
app.use('/api/payment', paymentRoutes);
app.use('/api/payment/creem', creemPaymentRoutes);
app.use('/api/watch', watchRoutes);
app.use('/api/playlists', playlistRoutes);
app.use('/api/balance', balanceRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/earnings', earningsRoutes); // 2. Use earnings routes
app.use('/api/settings', settingsRoutes);
app.use('/api', followRoutes);

// API文档路由
app.get('/docs', (req, res) => {
  res.sendFile(path.join(__dirname, 'docs', 'api.html'));
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    code: 'NOT_FOUND'
  });
});

// 全局错误处理
app.use(errorHandler);

// 启动服务器函数
async function startServer() {
  const PORT = process.env.PORT || 3000;
  const HTTPS_PORT = process.env.HTTPS_PORT || 3443;

  // 1. 首先，强制初始化数据库连接
  // 移除try...catch，如果数据库连接失败，则直接让应用启动失败
  logger.info('正在初始化数据库连接...');
  const { initDatabase } = require('./src/config/database');
  await initDatabase();
  logger.info('✅ 数据库连接初始化完成');
  
  // 😬 新增: 初始化支付模块
  logger.info('正在初始化支付模块...');
  const { initializePaymentModule } = require('./src/modules/payment');
  await initializePaymentModule();
  logger.info('✅ 支付模块初始化完成');

  // 2. 尝试配置SSL
  const sslOptions = await autoConfigureSSL();

  // 3. 启动HTTP服务器
  const httpServer = http.createServer(app);
  httpServer.listen(PORT, () => {
    logger.info(`HTTP服务器启动成功，端口: ${PORT}`);
    logger.info(`环境: ${process.env.NODE_ENV || 'development'}`);
    logger.info(`API文档: http://localhost:${PORT}/docs`);
    logger.info(`健康检查: http://localhost:${PORT}/health`);
  });

  // 如果SSL配置成功，启动HTTPS服务器
  if (sslOptions) {
    const httpsServer = https.createServer(sslOptions, app);
    httpsServer.listen(HTTPS_PORT, () => {
      logger.info(`HTTPS服务器启动成功，端口: ${HTTPS_PORT}`);
      logger.info(`安全API文档: https://localhost:${HTTPS_PORT}/docs`);
      logger.info(`安全健康检查: https://localhost:${HTTPS_PORT}/health`);
    });

    httpsServer.on('error', (error) => {
      logger.error('HTTPS服务器启动失败:', error);
    });
  } else {
    logger.info('SSL未配置，仅运行HTTP服务器');
    if (process.env.ENABLE_HTTPS === 'true') {
      logger.warn('HTTPS已启用但SSL配置失败，请检查证书文件');
    }
  }

  // 启动监控系统
  startPeriodicMonitoring();
  logger.info('性能监控系统已启动');

  // 显示SSL状态
  const sslStatus = getSSLStatus();
  logger.info('SSL状态:', {
    enabled: sslStatus.enabled,
    certificatesExist: sslStatus.certificatesExist,
    sslOptionsLoaded: sslStatus.sslOptionsLoaded
  });

  // 启动TRON交易扫描服务
  // tronTransactionScannerService.start(); // This line is now handled above
}

// 启动服务器
startServer().catch(error => {
  logger.error('服务器启动失败:', error);
  process.exit(1);
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

module.exports = app;
