const path = require('path');
require('dotenv').config();

const connectionManager = require('./src/database/connection');
const Membership = require('./src/database/models/Membership');

async function fixDDDRole() {
  let connection;
  
  try {
    console.log('=== 开始修复用户DDD的角色问题 ===');
    
    connection = await connectionManager.getMySQLConnection();
    await connection.beginTransaction();
    
    // 查找用户DDD
    const [users] = await connection.execute('SELECT id, username, role FROM users WHERE username = ?', ['DDD']);
    
    if (users.length === 0) {
      console.log('❌ 未找到用户DDD');
      return;
    }
    
    const user = users[0];
    console.log('👤 找到用户DDD:', user);
    
    // 检查会员记录
    const membershipSql = 'SELECT * FROM memberships WHERE user_id = ? AND status = ? AND end_date > NOW()';
    const [memberships] = await connection.execute(membershipSql, [user.id, 'active']);
    
    console.log('📋 会员记录数量:', memberships.length);
    
    if (memberships.length > 0) {
      console.log('🔧 开始同步用户角色...');
      
      // 使用Membership模型的syncUserRole方法
      const syncResult = await Membership.syncUserRole(user.id, connection);
      
      console.log('✅ 角色同步结果:', syncResult);
      
      // 验证修复结果
      const [updatedUsers] = await connection.execute('SELECT id, username, role FROM users WHERE id = ?', [user.id]);
      console.log('🔍 修复后的用户信息:', updatedUsers[0]);
      
      await connection.commit();
      console.log('✅ 修复完成');
    } else {
      console.log('❌ 用户DDD没有活跃的会员记录');
      await connection.rollback();
    }
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
    if (connection) {
      await connection.rollback();
    }
  } finally {
    if (connection) {
      connection.release();
    }
    await connectionManager.closeConnections();
  }
}

fixDDDRole()
  .then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
