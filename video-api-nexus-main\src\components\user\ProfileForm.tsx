import React, { useState, useEffect } from 'react';
import { Save, X, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { updateUserProfile } from '@/lib/api';
import { useTranslation } from 'react-i18next';

interface UserProfile {
  id?: number;
  username?: string;
  email?: string;
  bio?: string;
  nickname?: string;
}

interface ProfileFormProps {
  userProfile: UserProfile;
  onProfileUpdate: (updatedProfile: UserProfile) => void;
  isEditing: boolean;
  onEditToggle: () => void;
}

const ProfileForm: React.FC<ProfileFormProps> = ({
  userProfile,
  onProfileUpdate,
  isEditing,
  onEditToggle
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<UserProfile>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 初始化表单数据
  useEffect(() => {
    setFormData({
      username: userProfile.username || '',
      nickname: userProfile.nickname || userProfile.username || ''
    });
  }, [userProfile, isEditing]);

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // 用户名验证
    if (!formData.username?.trim()) {
      newErrors.username = '用户名不能为空';
    } else if (formData.username.length < 2) {
      newErrors.username = '用户名至少需要2个字符';
    } else if (formData.username.length > 20) {
      newErrors.username = '用户名不能超过20个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await updateUserProfile(formData);
      
      // 安全地访问更新后的用户数据
      const updatedUserData = response?.data?.data?.user ||
                             response?.data?.user ||
                             response?.data ||
                             null;

      if (updatedUserData) {
        onProfileUpdate(updatedUserData);
        onEditToggle(); // 退出编辑模式
        toast({
          title: "资料更新成功",
          description: "您的个人资料已成功更新",
        });
      } else {
        throw new Error('更新响应数据格式错误');
      }
    } catch (error: any) {
      console.error('更新资料失败:', error);
      const errorMessage = error?.response?.data?.message || '更新资料失败，请重试';
      toast({
        title: "更新失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    setFormData({
      username: userProfile.username || '',
      nickname: userProfile.nickname || userProfile.username || ''
    });
    setErrors({});
    onEditToggle();
  };

  if (!isEditing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {t('profile.personalInfo')}
            <Button variant="outline" size="sm" onClick={onEditToggle}>
              {t('profile.editProfile')}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">{t('profile.username')}</Label>
              <p className="mt-1">{userProfile.username || '未设置'}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">邮箱</Label>
              <p className="mt-1">{userProfile.email || '未设置'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('profile.editPersonalInfo')}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 用户名 */}
          <div>
            <Label htmlFor="username">{t('profile.username')} *</Label>
            <Input
              id="username"
              value={formData.username || ''}
              onChange={(e) => handleInputChange('username', e.target.value)}
              placeholder="请输入用户名"
              className={errors.username ? 'border-destructive' : ''}
            />
            {errors.username && (
              <p className="text-sm text-destructive mt-1">{errors.username}</p>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center space-x-2"
            >
              {isSubmitting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{isSubmitting ? '保存中...' : '保存修改'}</span>
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
              className="flex items-center space-x-2"
            >
              <X className="h-4 w-4" />
              <span>取消</span>
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ProfileForm;
