# Creem Webhook 故障排查和解决方案

## 当前状态
- ✅ 用户已完成支付（订单ID: ord_7LrhSleHiyxI8e0WrOeC82）
- ✅ Webhook 端点已修复并可正常处理数据
- ❌ Creem 发送 webhook 时收到 404 错误

## 解决方案

### 方案 1：重新发送失败的 Webhook（推荐）

1. **登录 Creem.io 后台**
   - 访问 https://creem.io
   - 使用你的账户登录

2. **找到失败的 Webhook**
   - 进入 **Developers** > **Webhooks**
   - 查看 webhook 发送历史
   - 找到状态为 404 的 `checkout.completed` 事件

3. **重新发送 Webhook**
   - 点击失败的 webhook 事件
   - 点击 **Resend** 或 **Retry** 按钮
   - 系统会重新发送到你配置的 webhook URL

### 方案 2：使用 ngrok 暴露本地服务器

如果 Creem 无法访问你的 localhost，需要使用 ngrok：

1. **安装 ngrok**
   ```bash
   # 下载并安装 ngrok
   # 或使用 npm: npm install -g ngrok
   ```

2. **启动 ngrok 隧道**
   ```bash
   ngrok http 3000
   ```

3. **更新 Creem Webhook 配置**
   - 复制 ngrok 生成的 URL（如：https://abc123.ngrok.io）
   - 在 Creem 后台更新 Webhook URL 为：
     `https://abc123.ngrok.io/api/payment/creem/webhook`

4. **重新发送 Webhook**
   - 使用新的 URL 重新发送失败的 webhook

### 方案 3：手动激活会员（临时解决方案）

如果上述方案都不可行，可以手动激活会员：

```bash
# 连接到你的数据库，执行以下 SQL
INSERT INTO memberships (
  user_id, 
  plan_name, 
  start_date, 
  end_date, 
  status, 
  payment_method, 
  payment_reference, 
  amount, 
  currency, 
  created_at, 
  updated_at
) VALUES (
  2, 
  '普通会员sssssssssd', 
  NOW(), 
  DATE_ADD(NOW(), INTERVAL 30 DAY), 
  'active', 
  'creem', 
  'ord_7LrhSleHiyxI8e0WrOeC82', 
  18.00, 
  'USD', 
  NOW(), 
  NOW()
);
```

## 验证方案

完成任一方案后，检查：

1. **后端日志** - 查看是否收到 webhook 处理日志
2. **用户会员状态** - 访问 `/api/member/my-membership` 检查会员状态
3. **数据库记录** - 检查 `memberships` 表是否有新记录

## 预期结果

成功后，你应该在日志中看到类似信息：
```
[info]: 收到 Creem webhook 请求
[info]: 处理支付完成事件: orderId: ord_7LrhSleHiyxI8e0WrOeC82
[info]: 用户 <EMAIL> 会员激活成功
```

## 联系支持

如果所有方案都失败，可能需要：
1. 检查 Creem.io 官方文档的最新 webhook 格式
2. 联系 Creem.io 技术支持确认 webhook 配置
3. 检查防火墙或网络设置是否阻止了 webhook 请求