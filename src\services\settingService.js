const SystemConfig = require('../database/models/SystemConfig');
const { cache, CACHE_KEYS } = require('../utils/cache');
const logger = require('../utils/logger');
const emailService = require('./emailService');

class SettingService {

  // 获取所有设置并按分组格式化
  async getAllSettings() {
    const cacheKey = cache.generateKey(CACHE_KEYS.SYSTEM, 'all_settings');
    let settings = await cache.get(cacheKey);

    if (!settings) {
      const allConfigs = await SystemConfig.getAll();
      settings = allConfigs.reduce((acc, item) => {
        const key = item.config_group ? `${item.config_group}.${item.config_key}` : item.config_key;
        // 对从数据库读取的值进行类型转换
        acc[key] = this.autoConvert(item.config_value);
        return acc;
      }, {});
      await cache.set(cacheKey, settings, 3600); // 缓存1小时
    }
    return settings;
  }

  // 按分组更新设置
  async updateSettingsByGroup(settings, group) {
    logger.info(`正在更新配置分组: ${group}`, { settings });
    await SystemConfig.bulkUpdate(settings, group);

    // 如果是邮件设置更新，则重置邮件服务
    if (group === 'email') {
      emailService.resetTransporter();
    }
    
    await this.clearSettingsCache();
    return true;
  }

  /**
   * 自动转换从数据库读取的字符串值为合适的JS类型
   * @param {string | null | undefined} value 
   * @returns {string | number | boolean | null}
   */
  autoConvert(value) {
    if (value === null || value === undefined) return null;
    
    const lowerValue = String(value).toLowerCase();

    if (lowerValue === 'true' || lowerValue === '1') return true;
    if (lowerValue === 'false' || lowerValue === '0') return false;
    
    // 检查是否为纯数字字符串
    if (/^-?\d+(\.\d+)?$/.test(value.trim())) {
        const num = Number(value);
        if (!isNaN(num)) return num;
    }
    
    return value;
  }

  // 清除设置缓存
  async clearSettingsCache() {
    const cacheKey = cache.generateKey(CACHE_KEYS.SYSTEM, 'all_settings');
    await cache.del(cacheKey);
    logger.info('系统配置缓存已清除');
  }

  // 获取邮件设置
  async getEmailSettings() {
    const allSettings = await this.getAllSettings();
    const emailSettings = {};
    for (const key in allSettings) {
      if (key.startsWith('email.')) {
        const newKey = key.replace('email.', '');
        emailSettings[newKey] = allSettings[key];
      }
    }
    return emailSettings;
  }

  // 获取单个配置值
  async getSetting(key) {
    const allSettings = await this.getAllSettings();
    return allSettings[key];
  }

  // 获取用户设置
  async getUserSettings() {
    const allSettings = await this.getAllSettings();
    const userSettings = {};
    for (const key in allSettings) {
      if (key.startsWith('user.')) {
        const newKey = key.replace('user.', '');
        userSettings[newKey] = allSettings[key];
      }
    }
    return userSettings;
  }

  // 获取公共设置
  async getPublicSettings() {
    const allSettings = await this.getAllSettings();
    const publicSettings = {};
    const publicKeys = [
      'site.site_name',
      'site.site_description',
      'site.site_logo',
      'site.site_favicon',
    ];

    for (const key of publicKeys) {
      if (allSettings[key] !== undefined) {
        publicSettings[key] = allSettings[key];
      }
    }
    
    return publicSettings;
  }
}

module.exports = new SettingService(); 