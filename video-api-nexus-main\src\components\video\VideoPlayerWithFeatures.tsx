import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import CommentSection from '@/pages/CommentSection';
import FavoriteButton from '@/components/common/FavoriteButton';
import LikeButton from '@/components/common/LikeButton';
import { FollowButton } from '@/components/common/FollowButton';
import { CreatorProfileDialog } from '@/components/creator/CreatorProfileDialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useBatchCheckInteractions } from '@/hooks/queries/useInteractions';
import { useVideoTimer } from '@/hooks/useVideoTimer';
import { usePlaylistSync } from '@/hooks/usePlaylistSync';
import VideoTimerPanel from '@/components/video/VideoTimerPanel';
import TimerDisplay from '@/components/video/TimerDisplay';
import PlaylistPanel from '@/components/playlist/PlaylistPanel';
import PlaylistDisplay from '@/components/playlist/PlaylistDisplay';
import { Clock, Plus, List } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { PlaylistItem } from '@/types/playlist';
import { useTranslation } from 'react-i18next';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export interface Video {
  id: string;
  title: string;
  description?: string;
  like_count?: number;
  view_count?: number;
  duration?: number;
  thumbnail?: string;
  thumbnail_url?: string;
  media_type?: 'video' | 'audio';
  url?: string;
  user_id?: number; // 视频上传者的用户ID
  uploader?: { 
    id?: number;
    nickname: string;
    avatar?: string;
  };
  uploader_id?: number;
  uploader_username?: string;
  uploader_nickname?: string;
  category?: { name: string };
  price?: number;
}

interface VideoPlayerWithFeaturesProps {
  video: Video;
}

const VideoPlayerWithFeatures: React.FC<VideoPlayerWithFeaturesProps> = ({ video }) => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const { t } = useTranslation();

  // 调试日志：检查video数据
  console.log('VideoPlayerWithFeatures - video data:', video);
  console.log('Video uploader info:', {
    uploader_nickname: video.uploader_nickname,
    uploader_username: video.uploader_username,
    uploader: video.uploader,
    user_id: video.user_id,
    uploader_id: video.uploader_id
  });

  const { data: interactionStates = {} } = useBatchCheckInteractions(
    video ? [video.id] : []
  );

  const videoInteraction = interactionStates[String(video.id)] || { isLiked: false, isFavorited: false };
  const [likeCount, setLikeCount] = useState(video.like_count || 0);
  const [isFavorited, setIsFavorited] = useState(videoInteraction.isFavorited);
  const [isLoginPromptOpen, setIsLoginPromptOpen] = useState(false);
  const [isTimerPanelOpen, setIsTimerPanelOpen] = useState(false);
  const [isPlaylistPanelOpen, setIsPlaylistPanelOpen] = useState(false);
  const [isCreatorDialogOpen, setIsCreatorDialogOpen] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const prevIndexRef = useRef<number | undefined>();
  const [forceRender, setForceRender] = useState(0);

  const playlist = usePlaylistSync({
    autoSync: false,
    onSyncSuccess: () => {
      setTimeout(() => setForceRender(prev => prev + 1), 200);
    },
  });

  useEffect(() => {
    const currentIndex = playlist.currentPlaylist?.currentIndex;
    const prevIndex = prevIndexRef.current;
    if (typeof currentIndex === 'number' && typeof prevIndex === 'number' && currentIndex !== prevIndex) {
      const currentItem = playlist.getCurrentItem();
      if (currentItem && currentItem.videoId && currentItem.videoId !== video.id) {
        navigate(`/video/${currentItem.videoId}`);
      }
    }
    prevIndexRef.current = currentIndex;
  }, [playlist.currentPlaylist?.currentIndex, video.id, navigate, playlist.getCurrentItem]);

  const timer = useVideoTimer({
    playlistMode: !!playlist.currentPlaylist,
    onTimerEnd: () => {
      if (videoRef.current) videoRef.current.pause();
      toast({ title: '定时结束', description: '视频播放已自动暂停' });
    },
    onPlaylistTimerEnd: () => {
      if (videoRef.current) videoRef.current.pause();
      toast({ title: '定时结束', description: '播放列表已暂停' });
    },
    onTimerTick: (timeRemaining) => {
      if (timeRemaining === 60) {
        toast({ title: '定时提醒', description: '还有1分钟定时器将结束' });
      }
    },
  });

  useEffect(() => {
    setLikeCount(video.like_count || 0);
    setIsFavorited(videoInteraction.isFavorited);
  }, [video, videoInteraction]);

  const handleVideoEnded = () => {
    if (playlist.currentPlaylist && playlist.currentPlaylist.items.length > 0) {
      const { currentIndex, items, playMode } = playlist.currentPlaylist;
      let shouldContinue = playMode === 'loop' || playMode === 'random' || currentIndex < items.length - 1;
      if (shouldContinue) {
        setTimeout(() => playlist.playNext(), 1000);
      }
    }
  };

  const requireAuthAction = (action: () => void) => {
    if (!isAuthenticated) {
      setIsLoginPromptOpen(true);
    } else {
      action();
    }
  };

  const handleAddToPlaylist = () => {
    requireAuthAction(() => {
      const playlistItem: PlaylistItem = {
        id: `item_${video.id}_${Date.now()}`,
        videoId: video.id,
        title: video.title,
        duration: video.duration || 0,
        thumbnail: video.thumbnail_url || video.thumbnail,
        mediaType: video.media_type || 'video',
        addedAt: new Date(),
        url: video.url,
      };
      playlist.addToPlaylistSync(playlistItem);
      toast({
        title: '已添加到播放列表',
        description: `"${video.title}" 已添加到播放列表并同步到服务器`,
      });
    });
  };

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (videoRef.current.paused) {
        videoRef.current.play();
        playlist.setPlaying(true);
      } else {
        videoRef.current.pause();
        playlist.setPlaying(false);
      }
    }
  };

  return (
    <div className="container mx-auto p-4">
      {timer.isActive && (
        <div className="mb-4">
          <TimerDisplay
            isActive={timer.isActive}
            isPaused={timer.isPaused}
            timeRemaining={timer.timeRemaining}
            progress={timer.progress}
            formatTime={timer.formatTime}
            onPause={timer.pauseTimer}
            onResume={timer.resumeTimer}
            onStop={timer.stopTimer}
          />
        </div>
      )}

      {playlist.currentPlaylist && (
        <div className="mb-4" key={`playlist-${forceRender}`}>
          <PlaylistDisplay
            playlist={playlist.currentPlaylist}
            currentItem={playlist.getCurrentItem()}
            onPlayPause={handlePlayPause}
            onPlayNext={playlist.playNext}
            onPlayPrevious={playlist.playPrevious}
            onOpenPlaylist={() => setIsPlaylistPanelOpen(true)}
            onSetPlayMode={playlist.setPlayMode}
          />
        </div>
      )}

      <div className="bg-black mb-4">
        {video.url ? (
           <video
             ref={videoRef}
             controls
             autoPlay
             className="w-full max-h-[70vh]"
             src={video.url}
             key={video.url}
             onEnded={handleVideoEnded}
           >
            抱歉，您的浏览器不支持嵌入式视频。
          </video>
        ) : (
          <div className="w-full aspect-video bg-gray-200 flex items-center justify-center">
            <p>视频加载中或无法播放。</p>
          </div>
        )}
      </div>
      <div className="flex-grow">
        <h1 className="text-2xl font-bold mb-3">{video.title}</h1>
        
        {/* 创作者信息区域 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            {/* 优化上传者显示逻辑 */}
            {(() => {
              const uploaderNickname = video.uploader_nickname || video.uploader?.nickname;
              const uploaderUsername = video.uploader_username || video.uploader?.username;
              const isSystemAdmin = uploaderNickname === t('videoInfo.systemAdmin');
              const displayName = uploaderNickname || uploaderUsername || t('videoInfo.anonymousAuthor');

              // 如果是系统管理员，不显示创作者信息
              if (isSystemAdmin) {
                return null;
              }

              return (
                <>
                  {/* 创作者头像 - 可点击 */}
                  {(video.user_id || video.uploader?.id || video.uploader_id) && (
                    <Avatar
                      className="h-12 w-12 cursor-pointer hover:ring-2 hover:ring-primary transition-all"
                      onClick={() => setIsCreatorDialogOpen(true)}
                      title="点击查看创作者信息"
                    >
                      <AvatarImage
                        src={video.uploader?.avatar || video.uploader_avatar || '/placeholder.svg'}
                        alt={displayName}
                      />
                      <AvatarFallback>
                        {displayName?.[0]?.toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  )}

                  <div className="flex flex-col">
                    <p
                      className="text-base font-semibold text-gray-900 cursor-pointer hover:text-primary transition-colors"
                      onClick={() => setIsCreatorDialogOpen(true)}
                      title="点击查看创作者信息"
                    >
                      {displayName}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      <span>{video.category?.name || t('videoInfo.uncategorized')}</span>
                      <span className="mx-2">•</span>
                      <span>{video.view_count || 0} {t('videoInfo.views')}</span>
                    </p>
                  </div>
                </>
              );
            })()}
          </div>
          
          {/* 关注按钮 */}
          {(video.user_id || video.uploader?.id || video.uploader_id) && (
            <FollowButton 
              userId={video.user_id || video.uploader?.id || video.uploader_id!}
              size="default"
              variant="outline"
            />
          )}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-gray-500">
            <span>播放量: {video.view_count || 0}</span>
        </div>
        <div className="flex items-center space-x-2">
            <LikeButton
            videoId={video.id}
            initialLiked={videoInteraction.isLiked}
            initialLikeCount={likeCount}
            onLikeChange={(_, newCount) => setLikeCount(newCount)}
            requireAuthAction={requireAuthAction}
            />
            <FavoriteButton 
            videoId={video.id} 
            isFavorited={isFavorited}
            onFavoriteChange={setIsFavorited}
            requireAuthAction={requireAuthAction}
            />
        </div>
      </div>

      <div className="flex items-center space-x-2 mt-4">
        <Button variant="outline" onClick={handleAddToPlaylist}>
          <Plus className="h-4 w-4 mr-2" />
          <span>添加到列表</span>
        </Button>
        <Button variant="outline" onClick={() => setIsPlaylistPanelOpen(true)}>
          <List className="h-4 w-4 mr-2" />
          <span>播放列表</span>
          {playlist.currentPlaylist?.items.length > 0 && (
            <span className="ml-1 text-xs bg-primary text-primary-foreground rounded-full px-1.5 py-0.5">
              {playlist.currentPlaylist.items.length}
            </span>
          )}
        </Button>
        <Button variant="outline" onClick={() => setIsTimerPanelOpen(true)}>
          <Clock className="h-4 w-4 mr-2" />
          <span>定时</span>
        </Button>
      </div>

      <p className="mt-4 text-muted-foreground">{video.description || '暂无简介'}</p>
      
      <CommentSection videoId={video.id} />

      <VideoTimerPanel
        isOpen={isTimerPanelOpen}
        onClose={() => setIsTimerPanelOpen(false)}
        onStartTimer={timer.startTimer}
        onStopTimer={timer.stopTimer}
        isTimerActive={timer.isActive}
      />

      <PlaylistPanel
        isOpen={isPlaylistPanelOpen}
        onClose={() => setIsPlaylistPanelOpen(false)}
        playlist={playlist.currentPlaylist}
        currentItem={playlist.getCurrentItem()}
        onPlayItem={(itemId) => requireAuthAction(() => playlist.playItem(itemId))}
        onPlayNext={() => requireAuthAction(() => playlist.playNext())}
        onPlayPrevious={() => requireAuthAction(() => playlist.playPrevious())}
        onSetPlayMode={(mode) => requireAuthAction(() => playlist.setPlayMode(mode))}
        onRemoveItem={(itemId) => requireAuthAction(() => playlist.removeFromPlaylistSync(itemId))}
      />

      {/* 创作者信息弹窗 */}
      {(video.user_id || video.uploader?.id || video.uploader_id) && (
        <CreatorProfileDialog
          open={isCreatorDialogOpen}
          onOpenChange={setIsCreatorDialogOpen}
          creatorId={video.user_id || video.uploader?.id || video.uploader_id!}
          creatorInfo={{
            username: video.uploader_username,
            nickname: video.uploader_nickname || video.uploader?.nickname,
            avatar: video.uploader?.avatar,
          }}
        />
      )}

      <AlertDialog open={isLoginPromptOpen} onOpenChange={setIsLoginPromptOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>需要登录</AlertDialogTitle>
            <AlertDialogDescription>
              此功能需要登录后才能使用。要立即登录吗？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={() => navigate('/login')}>
              前往登录
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default VideoPlayerWithFeatures; 