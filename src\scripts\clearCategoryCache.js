// 加载环境变量
require('dotenv').config();

const { cache, CACHE_KEYS } = require('../utils/cache');
const logger = require('../utils/logger');

async function clearCategoryCache() {
  try {
    logger.info('开始清除分类缓存...');

    // 检查Redis是否可用
    if (!cache.isAvailable()) {
      logger.warn('Redis不可用，无法清除缓存');
      return false;
    }

    logger.info('Redis连接正常');

    // 清除分类相关的缓存
    const patterns = [
      `${CACHE_KEYS.CATEGORY}:*`,
      'category:*'
    ];

    let totalDeleted = 0;

    for (const pattern of patterns) {
      try {
        logger.info(`尝试清除模式: ${pattern}`);

        // 使用缓存管理器的批量删除方法
        const deleted = await cache.delPattern(pattern);
        totalDeleted += deleted;

        if (deleted > 0) {
          logger.info(`删除了 ${deleted} 个匹配模式 "${pattern}" 的缓存键`);
        } else {
          logger.info(`没有找到匹配模式 "${pattern}" 的缓存键`);
        }
      } catch (error) {
        logger.error(`清除模式 "${pattern}" 的缓存失败:`, error.message);
      }
    }

    logger.info(`\n=== 缓存清除完成 ===`);
    logger.info(`总共删除了 ${totalDeleted} 个缓存键`);

    return true;

  } catch (error) {
    logger.error('清除分类缓存失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  clearCategoryCache()
    .then(() => {
      logger.info('缓存清除脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('缓存清除脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { clearCategoryCache };
