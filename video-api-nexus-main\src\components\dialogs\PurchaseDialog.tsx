import React, { useState, useEffect } from 'react';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { createVideoOrder, payOrderWithBalance } from '@/lib/api';
import { useMutation } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';

// 定义订单对象的接口
interface Order {
  id: string;
  order_no: string;
  final_amount: number;
}

interface PurchaseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  price?: number;
  videoId?: string | number; // videoId is now optional
  order?: Order | null; // New optional order prop
  onPurchaseSuccess: () => void;
}

const PurchaseDialog: React.FC<PurchaseDialogProps> = ({
  open,
  onOpenChange,
  price,
  videoId,
  order: initialOrder, // Use the new prop
  onPurchaseSuccess,
}) => {
  const [currentOrder, setCurrentOrder] = useState<Order | null>(initialOrder || null);

  // When the dialog is opened with a new order, update the state
  useEffect(() => {
    if (open) {
      setCurrentOrder(initialOrder || null);
    }
  }, [open, initialOrder]);

  const createOrderMutation = useMutation({
    mutationFn: () => {
      if (!videoId) throw new Error("Video ID is required to create an order.");
      return createVideoOrder(videoId);
    },
    onSuccess: (response) => {
      setCurrentOrder(response.data.data);
      toast({
        title: "订单已创建",
        description: "请使用余额完成支付。",
      });
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || "创建订单失败，请稍后再试";
      toast({
        title: "操作失败",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const payOrderMutation = useMutation({
    mutationFn: (orderId: string) => payOrderWithBalance(orderId),
    onSuccess: () => {
      toast({
        title: "购买成功!",
        description: "您现在可以观看此视频了。",
      });
      onPurchaseSuccess();
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || "支付失败，请稍后再试";
      toast({
        title: "支付失败",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const handlePrimaryAction = () => {
    if (!currentOrder) {
      createOrderMutation.mutate();
    } else {
      payOrderMutation.mutate(currentOrder.id);
    }
  };

  const isLoading = createOrderMutation.isPending || payOrderMutation.isPending;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {currentOrder ? '支付订单' : '确认购买'}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {currentOrder ? `订单号: ${currentOrder.order_no}` : '此内容需要付费才能观看。'}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div className="text-lg font-bold my-4 text-center">
            价格: ${Number(currentOrder?.final_amount || price || 0).toFixed(2)}
        </div>
        <AlertDialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            取消
          </Button>
          <Button onClick={handlePrimaryAction} disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {currentOrder ? '使用余额支付' : '创建订单'}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default PurchaseDialog; 