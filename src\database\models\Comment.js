const BaseModel = require('../BaseModel');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');

class Comment extends BaseModel {
  constructor() {
    super('comments');
  }

  // 创建评论
  async createComment(commentData) {
    const { videoId, userId, parentId = null, content } = commentData;

    // 验证视频是否存在
    const videoExists = await this.query('SELECT id FROM videos WHERE id = ? AND status = "published"', [videoId]);
    if (videoExists.length === 0) {
      throw new AppError('视频不存在或未发布', 404, 'VIDEO_NOT_FOUND');
    }

    // 如果是回复评论，验证父评论是否存在
    if (parentId) {
      const parentComment = await this.findById(parentId);
      if (!parentComment || parentComment.video_id !== videoId) {
        throw new AppError('父评论不存在', 404, 'PARENT_COMMENT_NOT_FOUND');
      }
    }

    const comment = await this.create({
      video_id: videoId,
      user_id: userId,
      parent_id: parentId,
      content,
      status: 'active',
      like_count: 0,
      reply_count: 0
    });

    // 如果是回复，更新父评论的回复数
    if (parentId) {
      await this.query('UPDATE comments SET reply_count = reply_count + 1 WHERE id = ?', [parentId]);
    }

    // 更新视频的评论数
    await this.query('UPDATE videos SET comment_count = comment_count + 1 WHERE id = ?', [videoId]);

    logger.info(`评论创建成功: ${comment.id}`, { videoId, userId, parentId });
    return comment;
  }

  // 获取评论详情
  async getCommentDetails(commentId) {
    const sql = `
      SELECT 
        c.*,
        u.username,
        u.nickname,
        u.avatar as user_avatar,
        parent_u.username as parent_username,
        parent_u.nickname as parent_nickname
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      LEFT JOIN comments parent_c ON c.parent_id = parent_c.id
      LEFT JOIN users parent_u ON parent_c.user_id = parent_u.id
      WHERE c.id = ?
    `;

    const result = await this.query(sql, [commentId]);
    return result[0] || null;
  }

  // 获取视频评论列表
  async getVideoComments(videoId, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      parentId = null
    } = options;

    let sql = `
      SELECT 
        c.*,
        u.username,
        u.nickname,
        u.avatar as user_avatar
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.video_id = ? AND c.status = 'active'
    `;

    const params = [videoId];

    // 筛选顶级评论或回复
    if (parentId === null) {
      sql += ' AND c.parent_id IS NULL';
    } else {
      sql += ' AND c.parent_id = ?';
      params.push(parentId);
    }

    // 排序
    const allowedSortFields = ['created_at', 'like_count'];
    const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    sql += ` ORDER BY c.${sortField} ${order}`;

    // 分页
    const offset = (page - 1) * pageSize;
    sql += ` LIMIT ${parseInt(pageSize, 10)} OFFSET ${parseInt(offset, 10)}`;

    const comments = await this.query(sql, params);

    // 获取总数
    let countSql = 'SELECT COUNT(*) as total FROM comments WHERE video_id = ? AND status = "active"';
    const countParams = [videoId];

    if (parentId === null) {
      countSql += ' AND parent_id IS NULL';
    } else {
      countSql += ' AND parent_id = ?';
      countParams.push(parentId);
    }

    const countResult = await this.query(countSql, countParams);
    const total = countResult[0].total;

    return {
      data: comments,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
        hasNext: page < Math.ceil(total / pageSize),
        hasPrev: page > 1
      }
    };
  }

  // 获取评论的回复列表
  async getCommentReplies(commentId, options = {}) {
    const comment = await this.findById(commentId);
    if (!comment) {
      throw new AppError('评论不存在', 404, 'COMMENT_NOT_FOUND');
    }

    return await this.getVideoComments(comment.video_id, {
      ...options,
      parentId: commentId
    });
  }

  // 更新评论
  async updateComment(commentId, updateData) {
    const allowedFields = ['content', 'status'];
    const filteredData = {};

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    if (Object.keys(filteredData).length === 0) {
      throw new AppError('没有可更新的字段', 400, 'NO_UPDATE_FIELDS');
    }

    const updatedComment = await this.update(commentId, filteredData);
    if (!updatedComment) {
      throw new AppError('评论不存在', 404, 'COMMENT_NOT_FOUND');
    }

    return updatedComment;
  }

  // 删除评论
  async deleteComment(commentId, userId, userRole = 'user') {
    const comment = await this.findById(commentId);
    if (!comment) {
      throw new AppError('评论不存在', 404, 'COMMENT_NOT_FOUND');
    }

    // 检查权限：只有评论作者或管理员可以删除
    if (comment.user_id !== userId && userRole !== 'admin') {
      throw new AppError('无权删除此评论', 403, 'ACCESS_DENIED');
    }

    // 软删除评论
    await this.update(commentId, { 
      status: 'deleted',
      deleted_at: new Date()
    });

    // 更新父评论的回复数（如果是回复）
    if (comment.parent_id) {
      await this.query('UPDATE comments SET reply_count = GREATEST(reply_count - 1, 0) WHERE id = ?', [comment.parent_id]);
    }

    // 更新视频的评论数
    await this.query('UPDATE videos SET comment_count = GREATEST(comment_count - 1, 0) WHERE id = ?', [comment.video_id]);

    // 递归删除所有回复
    await this.deleteCommentReplies(commentId);

    logger.info(`评论删除成功: ${commentId}`, { userId, userRole });
    return true;
  }

  // 删除评论的所有回复
  async deleteCommentReplies(parentId) {
    const replies = await this.findAll({ parent_id: parentId, status: 'active' });
    
    for (const reply of replies) {
      await this.update(reply.id, { 
        status: 'deleted',
        deleted_at: new Date()
      });
      
      // 递归删除子回复
      await this.deleteCommentReplies(reply.id);
    }
  }

  // 增加评论点赞数
  async incrementLikeCount(commentId) {
    await this.query('UPDATE comments SET like_count = like_count + 1 WHERE id = ?', [commentId]);
  }

  // 减少评论点赞数
  async decrementLikeCount(commentId) {
    await this.query('UPDATE comments SET like_count = GREATEST(like_count - 1, 0) WHERE id = ?', [commentId]);
  }

  // 获取用户评论列表
  async getUserComments(userId, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      status = 'active'
    } = options;

    const sql = `
      SELECT 
        c.*,
        v.title as video_title,
        v.thumbnail as video_thumbnail
      FROM comments c
      LEFT JOIN videos v ON c.video_id = v.id
      WHERE c.user_id = ? AND c.status = ?
      ORDER BY c.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const offset = (page - 1) * pageSize;
    const comments = await this.query(sql, [userId, status, pageSize, offset]);

    // 获取总数
    const countResult = await this.query(
      'SELECT COUNT(*) as total FROM comments WHERE user_id = ? AND status = ?',
      [userId, status]
    );
    const total = countResult[0].total;

    return {
      data: comments,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 获取热门评论
  async getPopularComments(videoId, limit = 5) {
    const sql = `
      SELECT 
        c.*,
        u.username,
        u.nickname,
        u.avatar as user_avatar
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      WHERE c.video_id = ? 
        AND c.status = 'active'
        AND c.parent_id IS NULL
        AND c.like_count > 0
      ORDER BY c.like_count DESC, c.created_at DESC
      LIMIT ?
    `;

    return await this.query(sql, [videoId, limit]);
  }

  // 搜索评论
  async searchComments(keyword, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      videoId = null
    } = options;

    let sql = `
      SELECT 
        c.*,
        u.username,
        u.nickname,
        u.avatar as user_avatar,
        v.title as video_title
      FROM comments c
      LEFT JOIN users u ON c.user_id = u.id
      LEFT JOIN videos v ON c.video_id = v.id
      WHERE c.content LIKE ? AND c.status = 'active'
    `;

    const params = [`%${keyword}%`];

    if (videoId) {
      sql += ' AND c.video_id = ?';
      params.push(videoId);
    }

    sql += ' ORDER BY c.created_at DESC';

    const offset = (page - 1) * pageSize;
    sql += ` LIMIT ${parseInt(pageSize, 10)} OFFSET ${parseInt(offset, 10)}`;

    const comments = await this.query(sql, params);

    // 获取总数
    let countSql = 'SELECT COUNT(*) as total FROM comments c WHERE c.content LIKE ? AND c.status = "active"';
    const countParams = [`%${keyword}%`];

    if (videoId) {
      countSql += ' AND c.video_id = ?';
      countParams.push(videoId);
    }

    const countResult = await this.query(countSql, countParams);
    const total = countResult[0].total;

    return {
      data: comments,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 获取评论统计信息
  async getCommentStats(videoId = null) {
    let sql = `
      SELECT 
        COUNT(*) as total_comments,
        COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as top_level_comments,
        COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as replies,
        AVG(like_count) as avg_likes,
        MAX(like_count) as max_likes
      FROM comments
      WHERE status = 'active'
    `;

    const params = [];

    if (videoId) {
      sql += ' AND video_id = ?';
      params.push(videoId);
    }

    const result = await this.query(sql, params);
    return result[0];
  }
}

module.exports = new Comment();
