const express = require('express');
const router = express.Router();

// 导入控制器和中间件
const adminController = require('./controllers/adminController');
const { requireAdmin } = require('../../middleware/auth');
const userController = require('../user/controllers/userController');
const videoController = require('../video/controllers/videoController');
const paymentController = require('../payment/controllers/paymentController');
const commentController = require('../comment/controllers');
const paymentGatewayController = require('./controllers/paymentGatewayController');
const adminWithdrawalController = require('./controllers/adminWithdrawalController');
const creemRoutes = require('../creem/routes');

// 新增：公开访问的路由，不需要管理员权限
router.get('/public-settings', adminController.getPublicSettings);

// 管理员权限已在app.js中检查，这里不需要重复检查

// 仪表板和统计
router.get('/dashboard', adminController.getDashboardStats);

// 用户管理
router.get('/users', adminController.getUserManagement);
router.post('/users/batch', adminController.batchUserAction);

// 视频管理
router.get('/videos', adminController.getVideoManagement);
router.post('/videos/batch', adminController.batchVideoAction);

// 评论管理
router.get('/comments', adminController.getCommentManagement);
router.delete('/comments/:id', adminController.deleteComment);

// 视频审核管理
router.get('/videos/pending', adminController.getPendingVideos);
router.post('/videos/:id/review', adminController.reviewVideo);

// 支付管理
router.get('/payments', adminController.getPaymentManagement);
router.put('/payments/:id/status', adminController.updateOrderStatus);
router.get('/payments/:orderNo', adminController.getOrderDetail);

// 提现管理
router.get('/withdrawals', adminWithdrawalController.getWithdrawals);
router.post('/withdrawals/:id/approve', adminWithdrawalController.approveWithdrawal);
router.post('/withdrawals/:id/reject', adminWithdrawalController.rejectWithdrawal);

// 支付通道管理
router.get('/payment-gateways', paymentGatewayController.getGateways);
router.put('/payment-gateways/:id', paymentGatewayController.updateGateway);

// 将Creem产品管理的路由挂载到admin路由下
router.use('/creem-plans', creemRoutes);

// 数据库维护
router.get('/videos/check-orphaned', adminController.checkOrphanedVideos);
router.post('/videos/cleanup-orphaned', adminController.cleanupOrphanedVideos);

// 系统管理
router.get('/system/logs', adminController.getSystemLogs);
router.get('/system/config', adminController.getSystemConfig);
router.put('/system/config', adminController.updateSystemConfig);
router.post('/system/cache/clear', adminController.clearSystemCache);

// 统计分析
router.get('/statistics/access/overview', adminController.getAccessOverview);
router.get('/statistics/access/detailed', adminController.getDetailedAccessStats);
router.get('/statistics/revenue/overview', adminController.getRevenueOverview);
router.get('/statistics/revenue/detailed', adminController.getDetailedRevenueStats);
router.get('/statistics/users/behavior', adminController.getUserBehaviorAnalysis);
router.get('/statistics/content/popular', adminController.getPopularContentAnalysis);

// 测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '管理模块测试接口',
    module: 'admin',
    user: req.user || null
  });
});

module.exports = router;
