import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import VideoCard from '../VideoCard';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'videoInfo.systemAdmin': '系统管理员',
        'videoInfo.anonymousAuthor': '匿名作者',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock API calls
vi.mock('@/lib/api', () => ({
  toggleLike: vi.fn(),
  toggleFavorite: vi.fn(),
}));

describe('用户VideoCard 上传者显示优化', () => {
  const baseVideo = {
    id: '1',
    title: '测试视频',
    thumbnail: '/test.jpg',
    duration: 120,
    category_name: '测试分类',
    view_count: 100,
    like_count: 10,
    comment_count: 5,
    is_liked: false,
    is_favorited: false,
  };

  const mockOnPlay = vi.fn();

  it('应该隐藏系统管理员上传的视频的上传者信息', () => {
    const systemAdminVideo = {
      ...baseVideo,
      username: '系统管理员',
    };

    render(<VideoCard video={systemAdminVideo} onPlay={mockOnPlay} />);

    // 应该不显示上传者信息
    expect(screen.queryByText('系统管理员')).not.toBeInTheDocument();
  });

  it('应该显示普通用户上传的视频的用户名', () => {
    const userVideo = {
      ...baseVideo,
      username: '普通用户',
    };

    render(<VideoCard video={userVideo} onPlay={mockOnPlay} />);

    // 应该显示用户名
    expect(screen.getByText('普通用户')).toBeInTheDocument();
  });

  it('应该显示匿名作者当没有用户名时', () => {
    const anonymousVideo = {
      ...baseVideo,
      username: '',
    };

    render(<VideoCard video={anonymousVideo} onPlay={mockOnPlay} />);

    // 应该显示匿名作者
    expect(screen.getByText('匿名作者')).toBeInTheDocument();
  });

  it('应该显示视频标题和其他信息', () => {
    const normalVideo = {
      ...baseVideo,
      username: '测试用户',
    };

    render(<VideoCard video={normalVideo} onPlay={mockOnPlay} />);

    // 应该显示视频标题
    expect(screen.getByText('测试视频')).toBeInTheDocument();
    // 应该显示用户名
    expect(screen.getByText('测试用户')).toBeInTheDocument();
  });

  it('在紧凑模式下也应该正确显示上传者信息', () => {
    const userVideo = {
      ...baseVideo,
      username: '测试用户',
    };

    render(<VideoCard video={userVideo} onPlay={mockOnPlay} variant="compact" />);

    // 应该显示用户名
    expect(screen.getByText('测试用户')).toBeInTheDocument();
  });

  it('在紧凑模式下应该隐藏系统管理员', () => {
    const systemAdminVideo = {
      ...baseVideo,
      username: '系统管理员',
    };

    render(<VideoCard video={systemAdminVideo} onPlay={mockOnPlay} variant="compact" />);

    // 应该不显示上传者信息
    expect(screen.queryByText('系统管理员')).not.toBeInTheDocument();
  });
});
