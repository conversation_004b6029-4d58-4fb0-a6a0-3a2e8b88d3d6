
import React, { useState, useEffect } from 'react';
import { Save, Database, Mail, Shield, Globe } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '@/services/adminApi';
import { Skeleton } from '@/components/ui/skeleton';
import { SystemSettings } from '@/types/settings';

const AdminSettings = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: settingsData, isLoading, isError, error } = useQuery<SystemSettings, Error>({
    queryKey: ['systemSettings'],
    queryFn: async () => {
      const response = await adminApi.getSystemConfig();
      // 修复：axios返回的数据在response.data.data中
      return response.data.data; 
    },
  });

  const [config, setConfig] = useState<SystemSettings>({});

  useEffect(() => {
    if (settingsData) {
      const newConfig = { ...settingsData };

      // 键名映射 - 确保与后端返回的键名格式一致
      const keyMigrationMap = {
        'site.name': 'site.site_name',
        'site.description': 'site.site_description', 
        'site.contactEmail': 'site.site_contactEmail',
        'media.maxUploadSize': 'media.media_maxUploadSize',
        'media.allowedVideoFormats': 'media.media_allowedVideoFormats',
      };

      for (const oldKey in keyMigrationMap) {
        const newKey = keyMigrationMap[oldKey];
        if (newConfig[oldKey] !== undefined) {
          // 如果新键还没有值，或者新键的值为空，则使用旧键的值
          if (newConfig[newKey] === undefined || newConfig[newKey] === null || newConfig[newKey] === '') {
            newConfig[newKey] = newConfig[oldKey];
          }
          delete newConfig[oldKey];
        }
      }

      setConfig(newConfig);
    }
  }, [settingsData]);
  
  const createUpdateMutation = (groupName: string) => {
    return useMutation({
      mutationFn: (newConfig: SystemSettings) => {
        return adminApi.updateSystemConfig(newConfig);
      },
      onSuccess: (response) => {
        // 检查response.data.success而不是response.success
        if (response.data.success) {
          toast({
            title: "保存成功",
            description: `${groupName}已更新`,
          });
          queryClient.invalidateQueries({ queryKey: ['systemSettings'] });
        } else {
          toast({
            title: "保存失败",
            description: response.data.message || `更新${groupName}时发生错误`,
            variant: "destructive",
          });
        }
      },
      onError:(error: any) => {
        toast({
          title: "保存失败",
          description: error.response?.data?.message || error.message || `更新${groupName}时发生错误`,
          variant: "destructive",
        });
      },
    });
  };

  const basicSettingsMutation = createUpdateMutation('基础设置');
  const emailSettingsMutation = createUpdateMutation('邮件设置');
  const securitySettingsMutation = createUpdateMutation('安全设置');


  const handleConfigChange = (key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };
  
  const handleSave = (groups: string[]) => {
    const changes: SystemSettings = {};
    for (const key in config) {
      const group = key.split('.')[0];
      if (groups.includes(group)) {
        changes[key] = config[key];
      }
    }

    // 根据分组决定使用哪个 mutation
    if (groups.includes('email')) {
      emailSettingsMutation.mutate(changes);
    } else if (groups.includes('security')) {
      securitySettingsMutation.mutate(changes);
    } else {
      basicSettingsMutation.mutate(changes);
    }
  };

  const handleClearCache = () => {
    toast({
      title: "清理完成",
      description: "系统缓存已清理",
    });
  };

  const handleBackupDatabase = () => {
    toast({
      title: "备份完成",
      description: "数据库备份已生成",
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-28" />
          </div>
        </div>
        <Skeleton className="h-[400px] w-full" />
        <Skeleton className="h-[300px] w-full" />
        <Skeleton className="h-[250px] w-full" />
      </div>
    );
  }

  if (isError) {
    return <div className="text-red-500">加载设置失败: {error.message}</div>;
  }
  
  if (!config) {
    return <div>加载中...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">系统设置</h1>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleClearCache}>
            清理缓存
          </Button>
          <Button variant="outline" onClick={handleBackupDatabase}>
            <Database className="h-4 w-4 mr-2" />
            备份数据
          </Button>
        </div>
      </div>

      {/* 基础设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>基础设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="siteName">网站名称</Label>
              <Input
                id="siteName"
                value={String(config['site.site_name'] || '')}
                onChange={(e) => handleConfigChange('site.site_name', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="contactEmail">联系邮箱</Label>
              <Input
                id="contactEmail"
                type="email"
                value={String(config['site.site_contactEmail'] || '')}
                onChange={(e) => handleConfigChange('site.site_contactEmail', e.target.value)}
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="siteDescription">网站描述</Label>
            <Textarea
              id="siteDescription"
              value={String(config['site.site_description'] || '')}
              onChange={(e) => handleConfigChange('site.site_description', e.target.value)}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="maxUploadSize">最大上传大小(MB)</Label>
              <Input
                id="maxUploadSize"
                type="number"
                value={Number(config['media.media_maxUploadSize'] || 0)}
                onChange={(e) => handleConfigChange('media.media_maxUploadSize', Number(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="allowedFormats">允许的视频格式</Label>
              <Input
                id="allowedFormats"
                value={String(config['media.media_allowedVideoFormats'] || '')}
                onChange={(e) => handleConfigChange('media.media_allowedVideoFormats', e.target.value)}
                placeholder="mp4,avi,mov"
              />
            </div>
          </div>

          <div className="flex items-center space-x-4 flex-wrap">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={!!config['user.enableComments']}
                onChange={(e) => handleConfigChange('user.enableComments', e.target.checked)}
              />
              <span>启用评论功能</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={!!config['user.enableRegistration']}
                onChange={(e) => handleConfigChange('user.enableRegistration', e.target.checked)}
              />
              <span>允许用户注册</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={!!config['user.requireEmailVerification']}
                onChange={(e) => handleConfigChange('user.requireEmailVerification', e.target.checked)}
              />
              <span>注册时需要邮箱验证</span>
            </label>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
           <Button 
              onClick={() => handleSave(['site', 'media', 'user'])}
              disabled={basicSettingsMutation.isPending}
            >
              <Save className="h-4 w-4 mr-2" />
              {basicSettingsMutation.isPending ? '保存中...' : '保存基础设置'}
            </Button>
        </CardFooter>
      </Card>

      {/* 邮件设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Mail className="h-5 w-5" />
            <span>邮件设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="smtpHost">SMTP服务器</Label>
              <Input
                id="smtpHost"
                value={String(config['email.smtpHost'] || '')}
                onChange={(e) => handleConfigChange('email.smtpHost', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="smtpPort">SMTP端口</Label>
              <Input
                id="smtpPort"
                type="number"
                value={Number(config['email.smtpPort'] || 0)}
                onChange={(e) => handleConfigChange('email.smtpPort', Number(e.target.value))}
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="smtpUser">SMTP用户名</Label>
              <Input
                id="smtpUser"
                value={String(config['email.smtpUser'] || '')}
                onChange={(e) => handleConfigChange('email.smtpUser', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="smtpPassword">SMTP密码</Label>
              <Input
                id="smtpPassword"
                type="password"
                placeholder="********"
                value={String(config['email.smtpPassword'] || '')}
                onChange={(e) => handleConfigChange('email.smtpPassword', e.target.value)}
              />
            </div>
          </div>
          {/* 代理设置 */}
          <div className="space-y-3 p-4 border rounded-lg bg-gray-50">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">代理设置</Label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={!!config['email.proxyEnabled']}
                  onChange={(e) => handleConfigChange('email.proxyEnabled', e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">启用代理</span>
              </label>
            </div>

            {config['email.proxyEnabled'] && (
              <div className="space-y-3">
                <div>
                  <Label htmlFor="proxy" className="text-sm">代理服务器URL</Label>
                  <Input
                    id="proxy"
                    placeholder="例如: http://127.0.0.1:7890 或 socks5://127.0.0.1:10809"
                    value={String(config['email.proxy'] || '')}
                    onChange={(e) => handleConfigChange('email.proxy', e.target.value)}
                    className="mt-1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    支持 HTTP/HTTPS 和 SOCKS4/SOCKS5 代理协议
                  </p>
                </div>
              </div>
            )}

            {!config['email.proxyEnabled'] && (
              <p className="text-sm text-gray-500">
                代理已禁用，邮件服务将直接连接到SMTP服务器
              </p>
            )}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fromEmail">发件人邮箱</Label>
              <Input
                id="fromEmail"
                type="email"
                value={String(config['email.fromEmail'] || '')}
                onChange={(e) => handleConfigChange('email.fromEmail', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="fromName">发件人名称</Label>
              <Input
                id="fromName"
                value={String(config['email.fromName'] || '')}
                onChange={(e) => handleConfigChange('email.fromName', e.target.value)}
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button 
            onClick={() => handleSave(['email'])}
            disabled={emailSettingsMutation.isPending}
          >
            <Save className="h-4 w-4 mr-2" />
            {emailSettingsMutation.isPending ? '保存中...' : '保存邮件设置'}
          </Button>
        </CardFooter>
      </Card>

      {/* 安全设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>安全设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="sessionTimeout">会话超时时间(秒)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                value={Number(config['security.sessionTimeout'] || 0)}
                onChange={(e) => handleConfigChange('security.sessionTimeout', Number(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="maxLoginAttempts">最大登录尝试次数</Label>
              <Input
                id="maxLoginAttempts"
                type="number"
                value={Number(config['security.maxLoginAttempts'] || 0)}
                onChange={(e) => handleConfigChange('security.maxLoginAttempts', Number(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="passwordMinLength">最小密码长度</Label>
              <Input
                id="passwordMinLength"
                type="number"
                value={Number(config['security.passwordMinLength'] || 0)}
                onChange={(e) => handleConfigChange('security.passwordMinLength', Number(e.target.value))}
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={!!config['security.enableTwoFactor']}
                onChange={(e) => handleConfigChange('security.enableTwoFactor', e.target.checked)}
              />
              <span>启用双因素认证</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={!!config['security.requireStrongPassword']}
                onChange={(e) => handleConfigChange('security.requireStrongPassword', e.target.checked)}
              />
              <span>要求强密码</span>
            </label>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button 
            onClick={() => handleSave(['security'])}
            disabled={securitySettingsMutation.isPending}
          >
            <Save className="h-4 w-4 mr-2" />
            {securitySettingsMutation.isPending ? '保存中...' : '保存安全设置'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AdminSettings;
