const BasePaymentService = require('./BasePaymentService');

/**
 * 易支付服务
 * 支持易支付平台的支付接口
 */
class EpayService extends BasePaymentService {
  constructor(config) {
    super(config);
    this.name = 'epay';
    this.apiUrl = config.apiUrl || 'https://pay.example.com';
    this.partnerId = config.partnerId;
    this.partnerKey = config.partnerKey;
    this.notifyUrl = config.notifyUrl;
    this.returnUrl = config.returnUrl;
  }

  /**
   * 创建支付订单
   * @param {Object} orderData 订单数据
   * @returns {Promise<Object>} 支付结果
   */
  async createPayment(orderData) {
    try {
      this.validateRequiredFields(orderData, [
        'orderNo', 'amount', 'subject', 'paymentMethod'
      ]);

      const paymentData = {
        pid: this.partnerId,
        type: this.mapPaymentMethod(orderData.paymentMethod),
        out_trade_no: orderData.orderNo,
        notify_url: this.notifyUrl,
        return_url: this.returnUrl,
        name: orderData.subject,
        money: orderData.amount.toFixed(2),
        clientip: orderData.clientIp || '127.0.0.1',
        device: orderData.device || 'pc'
      };

      // 生成签名
      paymentData.sign = this.generateSignature(paymentData);
      paymentData.sign_type = 'MD5';

      this.log('info', '创建易支付订单', { orderNo: orderData.orderNo, amount: orderData.amount });

      // 易支付通常返回支付页面URL
      const paymentUrl = `${this.apiUrl}/submit.php?${this.objectToUrlParams(paymentData)}`;

      return {
        success: true,
        paymentUrl,
        orderNo: orderData.orderNo,
        paymentMethod: 'epay',
        data: paymentData
      };

    } catch (error) {
      this.log('error', '创建易支付订单失败', { 
        orderNo: orderData.orderNo, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 查询支付状态
   * @param {string} orderNo 订单号
   * @returns {Promise<Object>} 支付状态
   */
  async queryPayment(orderNo) {
    try {
      const queryData = {
        pid: this.partnerId,
        out_trade_no: orderNo,
        act: 'order'
      };

      queryData.sign = this.generateSignature(queryData);
      queryData.sign_type = 'MD5';

      const response = await this.httpRequest(`${this.apiUrl}/api.php`, {
        method: 'POST',
        data: queryData
      });

      this.log('info', '查询易支付订单状态', { orderNo, response });

      return {
        success: response.code === 1,
        status: this.mapOrderStatus(response.status),
        orderNo,
        transactionId: response.trade_no,
        amount: parseFloat(response.money || 0),
        payTime: response.endtime,
        rawData: response
      };

    } catch (error) {
      this.log('error', '查询易支付订单失败', { orderNo, error: error.message });
      throw error;
    }
  }

  /**
   * 处理支付回调
   * @param {Object} callbackData 回调数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleCallback(callbackData) {
    try {
      this.log('info', '处理易支付回调', callbackData);

      // 验证签名
      if (!this.verifySignature(callbackData, callbackData.sign)) {
        throw new Error('签名验证失败');
      }

      const result = {
        success: callbackData.trade_status === 'TRADE_SUCCESS',
        orderNo: callbackData.out_trade_no,
        transactionId: callbackData.trade_no,
        amount: parseFloat(callbackData.money),
        payTime: callbackData.verify_time,
        status: this.mapCallbackStatus(callbackData.trade_status),
        rawData: callbackData
      };

      this.log('info', '易支付回调处理完成', result);

      return result;

    } catch (error) {
      this.log('error', '处理易支付回调失败', { 
        callbackData, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 申请退款
   * @param {Object} refundData 退款数据
   * @returns {Promise<Object>} 退款结果
   */
  async refund(refundData) {
    try {
      this.validateRequiredFields(refundData, [
        'orderNo', 'transactionId', 'refundAmount'
      ]);

      // 易支付退款接口
      const refundParams = {
        pid: this.partnerId,
        out_trade_no: refundData.orderNo,
        trade_no: refundData.transactionId,
        money: refundData.refundAmount.toFixed(2),
        act: 'refund'
      };

      refundParams.sign = this.generateSignature(refundParams);
      refundParams.sign_type = 'MD5';

      const response = await this.httpRequest(`${this.apiUrl}/api.php`, {
        method: 'POST',
        data: refundParams
      });

      this.log('info', '易支付退款申请', { 
        orderNo: refundData.orderNo, 
        response 
      });

      return {
        success: response.code === 1,
        refundId: response.refund_id,
        message: response.msg,
        rawData: response
      };

    } catch (error) {
      this.log('error', '易支付退款失败', { 
        orderNo: refundData.orderNo, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 验证回调签名
   * @param {Object} data 回调数据
   * @param {string} signature 签名
   * @returns {boolean} 验证结果
   */
  verifySignature(data, signature) {
    const signData = { ...data };
    delete signData.sign;
    delete signData.sign_type;

    const expectedSign = this.generateSignature(signData);
    return expectedSign === signature;
  }

  /**
   * 生成签名
   * @param {Object} data 数据
   * @returns {string} 签名
   */
  generateSignature(data) {
    const signString = this.objectToUrlParams(data) + this.partnerKey;
    return this.md5(signString);
  }

  /**
   * 映射支付方式
   * @param {string} method 支付方式
   * @returns {string} 易支付支付方式
   */
  mapPaymentMethod(method) {
    const methodMap = {
      'alipay': 'alipay',
      'wechat': 'wxpay',
      'qq': 'qqpay',
      'union': 'bank'
    };
    return methodMap[method] || 'alipay';
  }

  /**
   * 映射订单状态
   * @param {string} status 易支付状态
   * @returns {string} 标准状态
   */
  mapOrderStatus(status) {
    const statusMap = {
      '0': 'pending',
      '1': 'paid',
      '2': 'failed'
    };
    return statusMap[status] || 'pending';
  }

  /**
   * 映射回调状态
   * @param {string} status 回调状态
   * @returns {string} 标准状态
   */
  mapCallbackStatus(status) {
    const statusMap = {
      'TRADE_SUCCESS': 'paid',
      'TRADE_FINISHED': 'paid',
      'TRADE_CLOSED': 'failed'
    };
    return statusMap[status] || 'pending';
  }

  /**
   * 检查配置是否有效
   * @returns {boolean} 配置是否有效
   */
  isConfigValid() {
    return !!(this.partnerId && this.partnerKey && this.apiUrl);
  }
}

module.exports = EpayService;
