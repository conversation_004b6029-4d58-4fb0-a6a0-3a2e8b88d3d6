import api from './api';

interface CreateOrderPayload {
  type: 'membership' | 'recharge' | 'video';
  targetId: number;
  paymentMethod: string;
  description?: string;
  amount?: number; // 充值时需要
}

const createOrder = (payload: CreateOrderPayload) => {
  return api.post('/payment/orders', payload);
};

export const paymentApi = {
  // 创建订单
  createOrder(orderData: {
    type: 'membership' | 'recharge' | 'video';
    targetId: number;
    paymentMethod: string;
    description: string;
  }) {
    return api.post('/payment/orders', orderData);
  },

  // 为待处理订单重新生成支付信息
  regenerateOrderPayment(orderNo: string) {
    return api.post(`/payment/orders/${orderNo}/regenerate-payment`);
  },
}; 