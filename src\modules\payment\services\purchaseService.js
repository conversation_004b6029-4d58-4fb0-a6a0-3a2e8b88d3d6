const connectionManager = require('../../../database/ConnectionManager');
const Video = require('../../../database/models/Video');
const Order = require('../../../database/models/Order');
const balanceService = require('../../balance/services/balanceService');
const { AppError } = require('../../../middleware/errorHandler');
const { generateOrderNo } = require('../../../utils/orderHelper');
const orderProcessingService = require('./orderProcessingService');

class PurchaseService {
  /**
   * 为购买视频创建一个待处理的订单
   * @param {number} userId - 用户ID
   * @param {number} videoId - 视频ID
   */
  async createVideoOrder(userId, videoId) {
    const video = await Video.findById(videoId);
    if (!video) {
      throw new AppError('视频不存在', 404, 'VIDEO_NOT_FOUND');
    }
    if (video.visibility !== 'paid' || !video.price || video.price <= 0) {
      throw new AppError('此视频为非卖品', 400, 'VIDEO_NOT_FOR_SALE');
    }

    const hasPurchased = await Order.hasUserPurchasedVideo(userId, videoId);
    if (hasPurchased) {
      throw new AppError('您已购买此视频，无需重复购买', 409, 'ALREADY_PURCHASED');
    }

    const videoPrice = video.price;
    const orderData = {
      user_id: userId,
      type: 'video', // 修正订单类型以匹配数据库ENUM定义
      target_id: videoId,
      amount: videoPrice,
      final_amount: videoPrice,
      payment_method: null, // 待支付，暂无支付方式
      payment_status: 'pending',
      order_no: generateOrderNo(),
      description: `购买视频: ${video.title}`,
      expires_at: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后过期
    };
    
    const orderId = await Order.createOrder(orderData);
    const newOrder = await Order.findById(orderId);

    return newOrder;
  }

  /**
   * 使用余额支付一个待处理的订单
   * @param {number} userId - 用户ID
   * @param {string} orderId - 订单ID
   */
  async payForOrderWithBalance(userId, orderId) {
    return connectionManager.transaction(async (connection) => {
      const order = await Order.findById(orderId, 'FOR UPDATE', connection);

      if (!order) {
        throw new AppError('订单不存在', 404, 'ORDER_NOT_FOUND');
      }
      if (order.user_id !== userId) {
        throw new AppError('无权操作此订单', 403, 'FORBIDDEN');
      }
      if (order.payment_status !== 'pending') {
        throw new AppError('订单状态不正确，无法支付', 400, 'INVALID_ORDER_STATUS');
      }

      const orderAmount = order.final_amount;
      await balanceService.deductUserBalance(userId, orderAmount, connection);

      await Order.update(orderId, {
        payment_status: 'paid', // 修正状态以匹配数据库ENUM定义
        payment_method: 'balance',
        payment_time: new Date(),
      }, connection);

      // 关键修复：重新获取完整的订单对象
      const paidOrder = await Order.findById(orderId, null, connection);
      if (!paidOrder) {
        // 这个错误理论上不应该发生，因为我们在同一个事务中
        throw new Error(`支付成功后无法重新获取订单: ${orderId}`);
      }
      
      // 在订单支付成功后，调用统一的订单后处理服务
      await orderProcessingService.processOrderBusiness(paidOrder, connection);

      return paidOrder;
    });
  }
}

module.exports = new PurchaseService(); 