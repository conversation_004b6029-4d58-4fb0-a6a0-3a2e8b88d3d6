const followService = require('../services/followService');
const { AppError } = require('../../../middleware/errorHandler');
const logger = require('../../../utils/logger');

class FollowController {
  // 关注用户
  async followUser(req, res, next) {
    try {
      const followerId = req.user.id;
      const { userId: followedId } = req.params;

      if (!followedId) {
        throw new AppError('缺少用户ID参数', 400, 'MISSING_USER_ID');
      }

      const result = await followService.followUser(followerId, parseInt(followedId));

      res.json({
        success: true,
        message: '关注成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 取消关注
  async unfollowUser(req, res, next) {
    try {
      const followerId = req.user.id;
      const { userId: followedId } = req.params;

      if (!followedId) {
        throw new AppError('缺少用户ID参数', 400, 'MISSING_USER_ID');
      }

      const result = await followService.unfollowUser(followerId, parseInt(followedId));

      res.json({
        success: true,
        message: '取消关注成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取粉丝列表
  async getFollowers(req, res, next) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 20 } = req.query;
      const currentUserId = req.user?.id;

      if (!userId) {
        throw new AppError('缺少用户ID参数', 400, 'MISSING_USER_ID');
      }

      const result = await followService.getFollowers(
        parseInt(userId), 
        parseInt(page), 
        parseInt(limit),
        currentUserId
      );

      res.json({
        success: true,
        message: '获取粉丝列表成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取关注列表
  async getFollowing(req, res, next) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 20 } = req.query;
      const currentUserId = req.user?.id;

      if (!userId) {
        throw new AppError('缺少用户ID参数', 400, 'MISSING_USER_ID');
      }

      const result = await followService.getFollowing(
        parseInt(userId), 
        parseInt(page), 
        parseInt(limit),
        currentUserId
      );

      res.json({
        success: true,
        message: '获取关注列表成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 检查关注状态
  async checkFollowStatus(req, res, next) {
    try {
      const followerId = req.user.id;
      const { userId: followedId } = req.params;

      if (!followedId) {
        throw new AppError('缺少用户ID参数', 400, 'MISSING_USER_ID');
      }

      const result = await followService.checkFollowStatus(followerId, parseInt(followedId));

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取关注统计
  async getFollowStats(req, res, next) {
    try {
      const { userId } = req.params;

      if (!userId) {
        throw new AppError('缺少用户ID参数', 400, 'MISSING_USER_ID');
      }

      const stats = await followService.getFollowStats(parseInt(userId));

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取关注者的视频动态
  async getFollowingVideos(req, res, next) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 20 } = req.query;

      const result = await followService.getFollowingVideos(
        userId, 
        parseInt(page), 
        parseInt(limit)
      );

      res.json({
        success: true,
        message: '获取关注动态成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取相互关注列表
  async getMutualFollows(req, res, next) {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 20 } = req.query;

      if (!userId) {
        throw new AppError('缺少用户ID参数', 400, 'MISSING_USER_ID');
      }

      const result = await followService.getMutualFollows(
        parseInt(userId),
        parseInt(page),
        parseInt(limit)
      );

      res.json({
        success: true,
        message: '获取相互关注列表成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 获取推荐关注用户
  async getRecommendedUsers(req, res, next) {
    try {
      const userId = req.user.id;
      const { limit = 10 } = req.query;

      const result = await followService.getRecommendedUsers(
        userId,
        parseInt(limit)
      );

      res.json({
        success: true,
        message: '获取推荐用户成功',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 更新用户统计数据
  async updateUserStats(req, res, next) {
    try {
      const followService = require('../services/followService');
      const result = await followService.updateAllUserStats();

      res.json({
        success: true,
        message: '用户统计数据更新完成',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  // 批量检查关注状态
  async batchCheckFollowStatus(req, res, next) {
    try {
      const followerId = req.user.id;
      const { userIds } = req.body;

      if (!Array.isArray(userIds) || userIds.length === 0) {
        throw new AppError('缺少有效的用户ID列表', 400, 'INVALID_USER_IDS');
      }

      const result = await followService.batchCheckFollowStatus(
        followerId,
        userIds.map(id => parseInt(id))
      );

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new FollowController();