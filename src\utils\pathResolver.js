const path = require('path');

/**
 * 项目根目录的绝对路径
 * process.cwd() 返回Node.js进程的当前工作目录
 */
const projectRoot = process.cwd();

/**
 * 将相对于项目根目录的路径解析为绝对路径。
 * @param {string} relativePath - 从项目根目录开始的相对路径 (例如 'uploads/videos').
 * @returns {string} - 操作系统的绝对路径.
 */
function toAbsolutePath(relativePath) {
  if (!relativePath || typeof relativePath !== 'string') {
    // 或者可以抛出错误，取决于你希望如何处理无效输入
    return null; 
  }
  return path.join(projectRoot, relativePath);
}

module.exports = {
  toAbsolutePath,
  projectRoot,
}; 