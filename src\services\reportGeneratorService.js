const fs = require('fs').promises;
const path = require('path');
const { logger } = require('../utils/advancedLogger');
const statisticsService = require('./statisticsService');
const PDFDocument = require('pdfkit');
const { toAbsolutePath } = require('../utils/pathResolver');

class ReportGeneratorService {
  constructor() {
    this.reportDir = toAbsolutePath('reports');
    this.ensureDirectoryExists();
  }

  // 确保报告目录存在
  async ensureDirectoryExists() {
    try {
      await fs.access(this.reportDir);
    } catch {
      await fs.mkdir(this.reportDir, { recursive: true });
    }
  }

  // 生成日报
  async generateDailyReport(date = new Date()) {
    const dateStr = date.toISOString().split('T')[0];
    
    try {
      // 获取昨日数据
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);

      const [accessStats, revenueStats, behaviorAnalysis, popularContent] = await Promise.all([
        statisticsService.getDetailedAccessStats({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupBy: 'hour'
        }),
        statisticsService.getDetailedRevenueStats({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupBy: 'hour'
        }),
        statisticsService.getUserBehaviorAnalysis('1d'),
        statisticsService.getPopularContentAnalysis('1d')
      ]);

      const report = {
        type: 'daily',
        date: dateStr,
        generatedAt: new Date().toISOString(),
        summary: this.generateDailySummary(accessStats, revenueStats),
        access: accessStats,
        revenue: revenueStats,
        behavior: behaviorAnalysis,
        popular: popularContent
      };

      // 保存报告
      const filename = `daily_report_${dateStr}.json`;
      await this.saveReport(filename, report);

      // 生成HTML版本
      const htmlReport = this.generateHTMLReport(report);
      await this.saveReport(`daily_report_${dateStr}.html`, htmlReport);

      logger.info(`日报生成成功: ${dateStr}`);
      return report;

    } catch (error) {
      logger.error(`生成日报失败: ${dateStr}`, error);
      throw error;
    }
  }

  // 生成周报
  async generateWeeklyReport(weekStart = new Date()) {
    const startDate = new Date(weekStart);
    startDate.setDate(startDate.getDate() - startDate.getDay()); // 周一
    startDate.setHours(0, 0, 0, 0);
    
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 6); // 周日
    endDate.setHours(23, 59, 59, 999);

    const weekStr = `${startDate.toISOString().split('T')[0]}_to_${endDate.toISOString().split('T')[0]}`;

    try {
      const [accessStats, revenueStats, behaviorAnalysis, popularContent] = await Promise.all([
        statisticsService.getDetailedAccessStats({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupBy: 'day'
        }),
        statisticsService.getDetailedRevenueStats({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupBy: 'day'
        }),
        statisticsService.getUserBehaviorAnalysis('7d'),
        statisticsService.getPopularContentAnalysis('7d')
      ]);

      const report = {
        type: 'weekly',
        period: weekStr,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        generatedAt: new Date().toISOString(),
        summary: this.generateWeeklySummary(accessStats, revenueStats),
        access: accessStats,
        revenue: revenueStats,
        behavior: behaviorAnalysis,
        popular: popularContent,
        trends: this.calculateWeeklyTrends(accessStats, revenueStats)
      };

      const filename = `weekly_report_${weekStr}.json`;
      await this.saveReport(filename, report);

      const htmlReport = this.generateHTMLReport(report);
      await this.saveReport(`weekly_report_${weekStr}.html`, htmlReport);

      logger.info(`周报生成成功: ${weekStr}`);
      return report;

    } catch (error) {
      logger.error(`生成周报失败: ${weekStr}`, error);
      throw error;
    }
  }

  // 生成月报
  async generateMonthlyReport(month = new Date()) {
    const startDate = new Date(month.getFullYear(), month.getMonth(), 1);
    const endDate = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59, 999);
    
    const monthStr = `${month.getFullYear()}-${String(month.getMonth() + 1).padStart(2, '0')}`;

    try {
      const [accessStats, revenueStats, behaviorAnalysis, popularContent] = await Promise.all([
        statisticsService.getDetailedAccessStats({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupBy: 'day'
        }),
        statisticsService.getDetailedRevenueStats({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          groupBy: 'day'
        }),
        statisticsService.getUserBehaviorAnalysis('30d'),
        statisticsService.getPopularContentAnalysis('30d')
      ]);

      const report = {
        type: 'monthly',
        month: monthStr,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        generatedAt: new Date().toISOString(),
        summary: this.generateMonthlySummary(accessStats, revenueStats),
        access: accessStats,
        revenue: revenueStats,
        behavior: behaviorAnalysis,
        popular: popularContent,
        trends: this.calculateMonthlyTrends(accessStats, revenueStats)
      };

      const filename = `monthly_report_${monthStr}.json`;
      await this.saveReport(filename, report);

      const htmlReport = this.generateHTMLReport(report);
      await this.saveReport(`monthly_report_${monthStr}.html`, htmlReport);

      logger.info(`月报生成成功: ${monthStr}`);
      return report;

    } catch (error) {
      logger.error(`生成月报失败: ${monthStr}`, error);
      throw error;
    }
  }

  // 生成自定义报告
  async generateCustomReport(options) {
    const {
      startDate,
      endDate,
      groupBy = 'day',
      metrics = ['access', 'revenue', 'behavior', 'popular'],
      title = '自定义报告'
    } = options;

    try {
      const reportData = {};

      if (metrics.includes('access')) {
        reportData.access = await statisticsService.getDetailedAccessStats({
          startDate, endDate, groupBy
        });
      }

      if (metrics.includes('revenue')) {
        reportData.revenue = await statisticsService.getDetailedRevenueStats({
          startDate, endDate, groupBy
        });
      }

      if (metrics.includes('behavior')) {
        const days = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24));
        reportData.behavior = await statisticsService.getUserBehaviorAnalysis(`${days}d`);
      }

      if (metrics.includes('popular')) {
        const days = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24));
        reportData.popular = await statisticsService.getPopularContentAnalysis(`${days}d`);
      }

      const report = {
        type: 'custom',
        title,
        startDate,
        endDate,
        groupBy,
        metrics,
        generatedAt: new Date().toISOString(),
        data: reportData
      };

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `custom_report_${timestamp}.json`;
      await this.saveReport(filename, report);

      logger.info(`自定义报告生成成功: ${title}`);
      return report;

    } catch (error) {
      logger.error(`生成自定义报告失败: ${title}`, error);
      throw error;
    }
  }

  // 保存报告
  async saveReport(filename, content) {
    const filePath = path.join(this.reportDir, filename);
    
    if (typeof content === 'object') {
      await fs.writeFile(filePath, JSON.stringify(content, null, 2));
    } else {
      await fs.writeFile(filePath, content);
    }
  }

  // 生成HTML报告
  generateHTMLReport(report) {
    const { type, generatedAt, summary } = report;
    
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${type === 'daily' ? '日报' : type === 'weekly' ? '周报' : '月报'} - 视频平台统计</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric { background: white; border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .metric h3 { margin: 0 0 10px 0; color: #333; }
        .metric .value { font-size: 24px; font-weight: bold; color: #007bff; }
        .section { margin: 30px 0; }
        .section h2 { border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${type === 'daily' ? '日报' : type === 'weekly' ? '周报' : '月报'}</h1>
        <p>生成时间: ${new Date(generatedAt).toLocaleString('zh-CN')}</p>
    </div>
    
    <div class="section">
        <h2>概览</h2>
        <div class="summary">
            ${summary ? Object.entries(summary).map(([key, value]) => `
                <div class="metric">
                    <h3>${this.getMetricName(key)}</h3>
                    <div class="value">${value}</div>
                </div>
            `).join('') : ''}
        </div>
    </div>
    
    <div class="section">
        <h2>详细数据</h2>
        <p>详细数据请查看对应的JSON文件</p>
    </div>
</body>
</html>`;
  }

  // 获取指标中文名称
  getMetricName(key) {
    const names = {
      totalUsers: '总用户数',
      activeUsers: '活跃用户',
      newUsers: '新增用户',
      totalViews: '总观看数',
      totalRevenue: '总收入',
      newSubscriptions: '新增订阅',
      avgOrderValue: '平均订单价值'
    };
    return names[key] || key;
  }

  // 生成摘要
  generateDailySummary(accessStats, revenueStats) {
    return {
      totalUsers: accessStats.users?.reduce((sum, item) => sum + (item.unique_users || 0), 0) || 0,
      totalViews: accessStats.videos?.reduce((sum, item) => sum + (item.total_views || 0), 0) || 0,
      totalRevenue: revenueStats.summary?.totalRevenue || 0,
      newSubscriptions: revenueStats.summary?.totalSubscriptions || 0
    };
  }

  generateWeeklySummary(accessStats, revenueStats) {
    return this.generateDailySummary(accessStats, revenueStats);
  }

  generateMonthlySummary(accessStats, revenueStats) {
    return this.generateDailySummary(accessStats, revenueStats);
  }

  // 计算趋势
  calculateWeeklyTrends(accessStats, revenueStats) {
    return {
      userGrowth: this.calculateGrowthTrend(accessStats.users, 'unique_users'),
      revenueGrowth: this.calculateGrowthTrend(revenueStats.data, 'revenue')
    };
  }

  calculateMonthlyTrends(accessStats, revenueStats) {
    return this.calculateWeeklyTrends(accessStats, revenueStats);
  }

  calculateGrowthTrend(data, field) {
    if (!data || data.length < 2) return 0;
    
    const latest = data[0][field] || 0;
    const previous = data[1][field] || 0;
    
    return previous > 0 ? ((latest - previous) / previous * 100).toFixed(2) : 0;
  }

  // 获取报告列表
  async getReportList() {
    try {
      const files = await fs.readdir(this.reportDir);
      const reports = files
        .filter(file => file.endsWith('.json'))
        .map(file => {
          const [type, , ...dateParts] = file.replace('.json', '').split('_');
          return {
            filename: file,
            type,
            date: dateParts.join('_'),
            path: path.join(this.reportDir, file)
          };
        })
        .sort((a, b) => b.date.localeCompare(a.date));

      return reports;
    } catch (error) {
      logger.error('获取报告列表失败:', error);
      return [];
    }
  }

  // 删除旧报告
  async cleanupOldReports(daysToKeep = 90) {
    try {
      const files = await fs.readdir(this.reportDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      let deletedCount = 0;
      for (const file of files) {
        const filePath = path.join(this.reportDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime < cutoffDate) {
          await fs.unlink(filePath);
          deletedCount++;
        }
      }

      logger.info(`清理旧报告完成，删除 ${deletedCount} 个文件`);
      return deletedCount;
    } catch (error) {
      logger.error('清理旧报告失败:', error);
      return 0;
    }
  }
}

module.exports = new ReportGeneratorService();
