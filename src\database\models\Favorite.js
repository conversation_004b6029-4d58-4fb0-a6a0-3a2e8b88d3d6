const BaseModel = require('../BaseModel');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');

class Favorite extends BaseModel {
  constructor() {
    super('favorites');
  }

  // 收藏/取消收藏视频
  async toggleFavorite(userId, videoId) {
    // 验证视频是否存在
    const video = await this.query('SELECT id, title FROM videos WHERE id = ? AND status = "published"', [videoId]);
    if (video.length === 0) {
      throw new AppError('视频不存在或未发布', 404, 'VIDEO_NOT_FOUND');
    }

    // 检查是否已经收藏
    const existingFavorite = await this.findOne({
      user_id: userId,
      video_id: videoId
    });

    if (existingFavorite) {
      // 取消收藏
      await this.delete(existingFavorite.id);
      
      logger.info(`取消收藏视频: ${videoId}`, { userId });
      return { action: 'unfavorited', favorited: false };
    } else {
      // 添加收藏
      const favorite = await this.create({
        user_id: userId,
        video_id: videoId
      });
      
      logger.info(`收藏视频: ${videoId}`, { userId });
      return { action: 'favorited', favorited: true, favorite };
    }
  }

  // 检查是否已收藏
  async isFavorited(userId, videoId) {
    const favorite = await this.findOne({
      user_id: userId,
      video_id: videoId
    });

    return !!favorite;
  }

  // 获取用户收藏列表
  async getUserFavorites(userId, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = options;

    // 排序
    const allowedSortFields = ['created_at', 'video_created_at', 'view_count', 'like_count'];
    const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    let orderByClause;
    if (sortField === 'created_at') {
      orderByClause = `f.${sortField} ${order}`;
    } else if (sortField === 'video_created_at') {
      orderByClause = `v.created_at ${order}`;
    } else {
      orderByClause = `v.${sortField} ${order}`;
    }

    const offset = (page - 1) * pageSize;

    // 验证并清理分页参数以防止SQL注入
    const safePageSize = Math.max(1, Math.min(100, parseInt(pageSize) || 20));
    const safeOffset = Math.max(0, parseInt(offset) || 0);

    const sql = `SELECT
      f.*,
      v.title,
      v.description,
      v.thumbnail_url as thumbnail,
      v.duration,
      v.view_count,
      v.like_count,
      v.comment_count,
      v.created_at as video_created_at,
      u.username,
      u.nickname,
      u.avatar as user_avatar,
      c.name as category_name
    FROM favorites f
    LEFT JOIN videos v ON f.video_id = v.id
    LEFT JOIN users u ON v.user_id = u.id
    LEFT JOIN categories c ON v.category_id = c.id
    WHERE f.user_id = ? AND v.status = ?
    ORDER BY ${orderByClause}
    LIMIT ${safePageSize} OFFSET ${safeOffset}`;

    const params = [userId, 'published'];

    const favorites = await this.query(sql, params);

    // 获取总数
    const countResult = await this.query(`
      SELECT COUNT(*) as total 
      FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      WHERE f.user_id = ? AND v.status = 'published'
    `, [userId]);
    
    const total = countResult[0].total;

    return {
      data: favorites,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
        hasNext: page < Math.ceil(total / pageSize),
        hasPrev: page > 1
      }
    };
  }

  // 批量检查收藏状态
  async batchCheckFavorited(userId, videoIds) {
    if (!videoIds || videoIds.length === 0) {
      return {};
    }

    const placeholders = videoIds.map(() => '?').join(',');
    const sql = `
      SELECT video_id
      FROM favorites
      WHERE user_id = ? AND video_id IN (${placeholders})
    `;

    const params = [userId, ...videoIds];
    const favorites = await this.query(sql, params);
    
    const favoritedMap = {};
    favorites.forEach(favorite => {
      favoritedMap[favorite.video_id] = true;
    });

    return favoritedMap;
  }

  // 获取视频的收藏用户列表
  async getVideoFavorites(videoId, options = {}) {
    const {
      page = 1,
      pageSize = 20
    } = options;

    const offset = (page - 1) * pageSize;

    // 验证并清理分页参数以防止SQL注入
    const safePageSize = Math.max(1, Math.min(100, parseInt(pageSize) || 20));
    const safeOffset = Math.max(0, parseInt(offset) || 0);

    const sql = `
      SELECT
        f.*,
        u.username,
        u.nickname,
        u.avatar as user_avatar
      FROM favorites f
      LEFT JOIN users u ON f.user_id = u.id
      WHERE f.video_id = ?
      ORDER BY f.created_at DESC
      LIMIT ${safePageSize} OFFSET ${safeOffset}
    `;

    const favorites = await this.query(sql, [videoId]);

    // 获取总数
    const countResult = await this.query(
      'SELECT COUNT(*) as total FROM favorites WHERE video_id = ?',
      [videoId]
    );
    const total = countResult[0].total;

    return {
      data: favorites,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 获取收藏统计
  async getFavoriteStats(userId = null) {
    let sql = `
      SELECT 
        COUNT(*) as total_favorites,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(DISTINCT video_id) as unique_videos,
        DATE(created_at) as date,
        COUNT(*) as daily_favorites
      FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      WHERE v.status = 'published'
    `;

    const params = [];

    if (userId) {
      sql += ' AND f.user_id = ?';
      params.push(userId);
    }

    sql += ' AND f.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
    sql += ' GROUP BY DATE(created_at)';
    sql += ' ORDER BY date DESC';

    return await this.query(sql, params);
  }

  // 获取热门收藏视频
  async getPopularFavorites(options = {}) {
    const {
      limit = 10,
      timeRange = '7d'
    } = options;

    let timeCondition = '';
    switch (timeRange) {
      case '1d':
        timeCondition = 'AND f.created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)';
        break;
      case '7d':
        timeCondition = 'AND f.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case '30d':
        timeCondition = 'AND f.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      default:
        timeCondition = '';
    }

    const sql = `
      SELECT
        v.id,
        v.title,
        v.thumbnail_url as thumbnail,
        v.view_count,
        v.like_count,
        COUNT(f.id) as favorite_count,
        u.username,
        u.nickname
      FROM videos v
      LEFT JOIN favorites f ON v.id = f.video_id ${timeCondition}
      LEFT JOIN users u ON v.user_id = u.id
      WHERE v.status = 'published' AND v.visibility = 'public'
      GROUP BY v.id
      HAVING favorite_count > 0
      ORDER BY favorite_count DESC, v.view_count DESC
      LIMIT ${Math.max(1, Math.min(100, parseInt(limit) || 10))}
    `;

    return await this.query(sql);
  }

  // 获取用户收藏的分类统计
  async getUserFavoriteCategories(userId) {
    const sql = `
      SELECT 
        c.id,
        c.name,
        c.slug,
        COUNT(f.id) as favorite_count
      FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE f.user_id = ? AND v.status = 'published' AND c.id IS NOT NULL
      GROUP BY c.id
      ORDER BY favorite_count DESC
    `;

    return await this.query(sql, [userId]);
  }

  // 推荐相似收藏
  async getRecommendedFavorites(userId, limit = 10) {
    // 基于用户收藏历史推荐相似视频
    const sql = `
      SELECT DISTINCT
        v.id,
        v.title,
        v.thumbnail_url as thumbnail,
        v.view_count,
        v.like_count,
        u.username,
        u.nickname
      FROM videos v
      LEFT JOIN users u ON v.user_id = u.id
      WHERE v.status = 'published'
        AND v.visibility = 'public'
        AND v.id NOT IN (
          SELECT video_id FROM favorites WHERE user_id = ?
        )
        AND (
          v.category_id IN (
            SELECT DISTINCT v2.category_id
            FROM favorites f2
            JOIN videos v2 ON f2.video_id = v2.id
            WHERE f2.user_id = ?
            LIMIT 5
          )
          OR v.user_id IN (
            SELECT DISTINCT v3.user_id
            FROM favorites f3
            JOIN videos v3 ON f3.video_id = v3.id
            WHERE f3.user_id = ?
            LIMIT 3
          )
        )
      ORDER BY v.view_count DESC, v.like_count DESC
      LIMIT ${Math.max(1, Math.min(100, parseInt(limit) || 10))}
    `;

    return await this.query(sql, [userId, userId, userId]);
  }

  // 导出用户收藏列表
  async exportUserFavorites(userId) {
    const sql = `
      SELECT 
        v.title,
        v.description,
        v.duration,
        v.view_count,
        v.like_count,
        v.created_at as video_created_at,
        f.created_at as favorited_at,
        u.username as video_author,
        c.name as category_name
      FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      LEFT JOIN users u ON v.user_id = u.id
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE f.user_id = ? AND v.status = 'published'
      ORDER BY f.created_at DESC
    `;

    return await this.query(sql, [userId]);
  }

  // 清理用户所有收藏
  async clearUserFavorites(userId) {
    const result = await this.query('DELETE FROM favorites WHERE user_id = ?', [userId]);

    logger.info(`清理用户所有收藏: ${userId}`, { count: result.affectedRows });
    return result.affectedRows;
  }

  // 清理已删除视频的收藏记录
  async cleanupDeletedVideoFavorites() {
    const sql = `
      DELETE f FROM favorites f
      LEFT JOIN videos v ON f.video_id = v.id
      WHERE v.id IS NULL OR v.status = 'deleted'
    `;

    const result = await this.query(sql);

    logger.info('清理已删除视频的收藏记录', { count: result.affectedRows });
    return result.affectedRows;
  }
}

module.exports = new Favorite();
