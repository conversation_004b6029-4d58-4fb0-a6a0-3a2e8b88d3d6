const express = require('express');
const router = express.Router();

// 导入控制器和中间件
const { VideoController, CategoryController } = require('./controllers/videoController');
const { verifyToken, optionalAuth, requireAdmin } = require('../../middleware/auth');
const { disabledRateLimiter } = require('../../middleware/rateLimiter');
const { uploadMiddlewares, fileCleanup, extractFileInfo } = require('../../middleware/upload');
const {
  validateVideoUpload,
  validateVideoUpdate,
  validateVideoList,
  validateCategoryCreate,
  validateCategoryUpdate,
  validateCategorySearch,
  validateId,
  validatePagination
} = require('../../middleware/validation');
const { trackActivity } = require('../user/middleware/activityTracker');

// 公开路由（无需认证）
router.get('/list',
  validateVideoList,
  VideoController.getVideoList
);

// 专用音频列表接口
router.get('/audio-list',
  validateVideoList,
  VideoController.getAudioList
);

// 专用视频列表接口
router.get('/video-list',
  validateVideoList,
  VideoController.getVideoOnlyList
);

router.get('/popular',
  VideoController.getPopularVideos
);

router.get('/search',
  disabledRateLimiter,
  trackActivity.search,
  VideoController.searchVideos
);

router.get('/my-videos',
  verifyToken, // Needs to be here to get req.user
  VideoController.getMyVideos
);

router.get('/:id',
  validateId,
  optionalAuth,
  trackActivity.videoWatch,
  VideoController.getVideoDetails
);

// 分类相关路由（公开访问）
const categoryRouter = express.Router();

// 公开分类路由
categoryRouter.get('/tree', CategoryController.getCategoryTree);
categoryRouter.get('/list', CategoryController.getCategoryList);
categoryRouter.get('/popular', CategoryController.getPopularCategories);
categoryRouter.get('/search', validateCategorySearch, CategoryController.searchCategories);
categoryRouter.get('/:id', validateId, CategoryController.getCategoryDetails);
categoryRouter.get('/:id/videos', validateId, CategoryController.getCategoryVideos);

// 管理员分类路由 - 需要先验证token再检查管理员权限
categoryRouter.use(verifyToken);
categoryRouter.use(requireAdmin);
categoryRouter.post('/', validateCategoryCreate, CategoryController.createCategory);
categoryRouter.put('/:id', validateId, validateCategoryUpdate, CategoryController.updateCategory);
categoryRouter.delete('/:id', validateId, CategoryController.deleteCategory);

// 挂载分类路由
router.use('/categories', categoryRouter);

// 需要认证的路由
router.use(verifyToken);

router.get('/recommended',
  VideoController.getRecommendedVideos
);

// This route now requires an ID and should be placed after more specific routes
router.get('/user/:id',
  validateId,
  VideoController.getUserVideos
);

// 统一媒体上传（支持视频和音频）
router.post('/upload',
  verifyToken,
  disabledRateLimiter,
  uploadMiddlewares.media,
  fileCleanup,
  extractFileInfo,
  validateVideoUpload,
  trackActivity.videoUpload,
  VideoController.uploadVideo
);

// 专用音频上传接口
router.post('/upload-audio',
  verifyToken,
  disabledRateLimiter,
  uploadMiddlewares.audio,
  fileCleanup,
  extractFileInfo,
  validateVideoUpload,
  trackActivity.videoUpload,
  VideoController.uploadAudio
);

router.post('/upload-from-url',
  verifyToken,
  requireAdmin, // 仅限管理员
  disabledRateLimiter,
  VideoController.uploadVideoFromUrl
);

router.put('/:id',
  validateId,
  validateVideoUpdate,
  VideoController.updateVideo
);

router.delete('/:id',
  validateId,
  VideoController.deleteVideo
);

router.get('/:id/stats',
  validateId,
  VideoController.getVideoStats
);

router.get('/:id/processing-status',
  validateId,
  VideoController.getVideoProcessingStatus
);

router.post('/:id/reprocess',
  validateId,
  VideoController.reprocessVideo
);

// 测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '媒体模块测试接口（支持视频和音频）',
    module: 'media',
    supportedTypes: ['video', 'audio'],
    user: req.user || null
  });
});

module.exports = router;
