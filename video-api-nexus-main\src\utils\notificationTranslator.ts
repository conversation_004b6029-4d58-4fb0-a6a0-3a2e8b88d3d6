import { TFunction } from 'i18next';

/**
 * 通知内容翻译工具
 */
export interface NotificationData {
  id: number;
  type: string;
  title: string;
  message: string;
  metadata?: string | object;
  is_read: boolean;
  created_at: string;
}

export interface TranslatedNotification {
  title: string;
  message: string;
}

/**
 * 从消息中提取视频标题
 */
const extractVideoTitle = (message: string): string => {
  // 匹配《视频标题》格式
  const titleMatch = message.match(/《([^》]+)》/);
  return titleMatch ? titleMatch[1] : '';
};

/**
 * 从消息中提取用户名
 */
const extractUsername = (message: string): string => {
  // 匹配 "用户名 关注了你" 格式
  const usernameMatch = message.match(/^([^\s]+)\s+关注了你/);
  return usernameMatch ? usernameMatch[1] : '';
};

/**
 * 从消息中提取拒绝原因
 */
const extractRejectionReason = (message: string): string => {
  // 匹配 "原因：xxx" 格式
  const reasonMatch = message.match(/原因：(.+)$/);
  return reasonMatch ? reasonMatch[1] : '';
};

/**
 * 翻译通知内容
 * @param notification 通知数据
 * @param t 翻译函数
 * @returns 翻译后的通知内容
 */
export const translateNotificationContent = (
  notification: NotificationData,
  t: TFunction
): TranslatedNotification => {
  const { type, title, message } = notification;

  // 根据通知类型和现有数据进行翻译
  switch (type) {
    case 'video_review':
      // 判断是审核通过还是拒绝
      if (title.includes('通过审核') || message.includes('成功发布')) {
        const videoTitle = extractVideoTitle(message);
        return {
          title: t('notification.types.video_approved'),
          message: t('notification.messages.videoApproved', {
            title: videoTitle || t('common.unknown')
          })
        };
      } else if (title.includes('未通过审核') || message.includes('未通过审核')) {
        const videoTitle = extractVideoTitle(message);
        const reason = extractRejectionReason(message);
        return {
          title: t('notification.types.video_rejected'),
          message: t('notification.messages.videoRejected', {
            title: videoTitle || t('common.unknown'),
            reason: reason || t('common.noReason')
          })
        };
      }
      break;

    case 'follow':
      const username = extractUsername(message);
      return {
        title: t('notification.types.follow'),
        message: t('notification.messages.newFollower', {
          username: username || t('common.unknownUser')
        })
      };

    case 'video_upload':
      const uploadedVideoTitle = extractVideoTitle(message);
      return {
        title: t('notification.types.video_uploaded'),
        message: t('notification.messages.videoUploaded', {
          title: uploadedVideoTitle || t('common.unknown')
        })
      };

    case 'comment':
      // 从消息中提取评论相关信息
      const commentVideoTitle = extractVideoTitle(message);
      // 简单的用户名提取（可能需要根据实际消息格式调整）
      const commenterMatch = message.match(/^([^\s]+)\s+评论了/);
      const commenterName = commenterMatch ? commenterMatch[1] : t('common.unknownUser');

      return {
        title: t('notification.types.comment'),
        message: t('notification.messages.videoCommented', {
          username: commenterName,
          title: commentVideoTitle || t('common.unknown'),
          comment: '' // 评论内容可能需要从reference_id获取
        })
      };

    case 'system':
    default:
      // 对于系统通知或其他类型，使用系统消息模板
      return {
        title: title || t('notification.types.system'),
        message: t('notification.messages.systemMessage', { message: message || '' })
      };
  }

  // 如果没有匹配的类型，返回原始内容
  return {
    title: title || t('notification.types.system'),
    message: message || ''
  };
};

/**
 * 获取通知类型的翻译
 * @param type 通知类型
 * @param t 翻译函数
 * @returns 翻译后的类型名称
 */
export const getNotificationTypeTranslation = (type: string, t: TFunction): string => {
  const typeKey = `notification.types.${type}`;
  const translated = t(typeKey);
  return translated !== typeKey ? translated : t('notification.types.system');
};
