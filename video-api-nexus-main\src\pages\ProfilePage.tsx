import React, { useState, useEffect } from 'react';
import { User, Heart, Eye, Clock, Crown, Settings, Shield, Lock, Mail, Palette, Wallet, MessageSquare, QrCode, Copy, Users, UserPlus } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardFooter, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import AvatarUpload from '@/components/user/AvatarUpload';
import ProfileForm from '@/components/user/ProfileForm';
import PasswordChangeDialog from '@/components/dialogs/PasswordChangeDialog';
import EmailChangeDialog from '@/components/dialogs/EmailChangeDialog';
import { ThemeSwitcher } from '@/components/theme-switcher';
import { ThemePreview } from '@/components/theme-preview';
import { getUserProfile, createRechargeOrder, createPaymentForRecharge } from '@/services/api';
import { followApi } from '@/services/followApi';
import { UserStats } from '@/components/user/UserStats';
import { useAuth } from '@/hooks/useAuth';
import { useFollowContext } from '@/contexts/FollowContext';
import { useMembership } from '@/hooks/useMembership';
import { useTranslation } from 'react-i18next';

interface UserProfile {
  id?: number;
  username?: string;
  email?: string;
  phone?: string;
  gender?: string;
  birthday?: string;
  bio?: string;
  nickname?: string;
  avatar?: string;
  role?: string;
  status?: string;
  created_at?: string;
  watch_time?: number;
  favorite_count?: number;
  like_count?: number;
  comment_count?: number;
  balance?: number;
  follower_count?: number;
  following_count?: number;
  video_count?: number;
}

const ProfilePage: React.FC = () => {
  const { t } = useTranslation();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);
  const [rechargeAmount, setRechargeAmount] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAutoSubmitting, setIsAutoSubmitting] = useState(false);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [paymentData, setPaymentData] = useState<any>(null);
  const { user, refreshUser } = useAuth();
  const { refreshCounter } = useFollowContext();
  const { isMember, isVip } = useMembership();
  const { toast } = useToast();

  // 获取用户资料
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await getUserProfile();

        // 安全地访问用户数据
        const userData = response?.data?.data?.user ||
                        response?.data?.user ||
                        response?.data ||
                        null;

        if (userData) {
          // 获取关注统计数据
          try {
            const statsResponse = await followApi.getFollowStats(userData.id);
            const stats = statsResponse.data;
            
            // 合并用户数据和统计数据
            const enrichedUserData = {
              ...userData,
              follower_count: stats.follower_count || 0,
              following_count: stats.following_count || 0,
              video_count: stats.video_count || 0
            };
            
            setUserProfile(enrichedUserData);
          } catch (statsError) {
            console.warn('获取用户统计失败，使用默认值:', statsError);
            // 即使统计数据获取失败，也要设置基本用户数据
            setUserProfile({
              ...userData,
              follower_count: 0,
              following_count: 0,
              video_count: 0
            });
          }
        } else {
          throw new Error('用户资料数据格式错误');
        }
      } catch (error: any) {
        console.error('获取用户资料失败:', error);
        toast({
          title: t('common.loadFailed'),
          description: t('common.cannotLoadProfile'),
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [refreshCounter]);

  // 处理充值请求
  const handleRecharge = async () => {
    const amount = parseFloat(rechargeAmount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: "无效金额",
        description: "请输入一个大于0的有效数字。",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await createRechargeOrder(amount);
      if (response.success) {
        toast({
          title: t('common.submitSuccess'),
          description: t('common.rechargeSubmitted', { amount }),
          variant: "default",
        });
        setRechargeAmount('');
      } else {
        throw new Error(response.message || '提交失败');
      }
    } catch (error: any) {
      toast({
        title: t('common.operationFailed'),
        description: error.message || t('common.cannotSubmitRecharge'),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理自动充值
  const handleAutoRecharge = async () => {
    const amount = parseFloat(rechargeAmount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: "无效金额",
        description: "请输入一个大于0的有效数字。",
        variant: "destructive",
      });
      return;
    }

    setIsAutoSubmitting(true);
    try {
      // 'epay' is used as the default automatic payment method
      const response = await createPaymentForRecharge(amount, 'epay');
      if (response.success) {
        setPaymentData(response.data.order);
        setPaymentDialogOpen(true);
        toast({
          title: t('common.success'),
          description: t('common.completePayment'),
        });
      } else {
        throw new Error(response.message || '创建支付订单失败');
      }
    } catch (error: any) {
      toast({
        title: t('common.operationFailed'),
        description: error.message || t('common.cannotCreatePaymentOrder'),
        variant: "destructive",
      });
    } finally {
      setIsAutoSubmitting(false);
    }
  };

  // 处理头像更新
  const handleAvatarUpdate = (avatarUrl: string) => {
    setUserProfile(prev => prev ? { ...prev, avatar: avatarUrl } : null);
  };

  // 处理头像删除
  const handleAvatarDelete = () => {
    setUserProfile(prev => prev ? { ...prev, avatar: undefined } : null);
  };

  // 处理资料更新
  const handleProfileUpdate = (updatedProfile: UserProfile) => {
    setUserProfile(prev => prev ? { ...prev, ...updatedProfile } : null);
  };

  // 切换编辑模式
  const toggleEditMode = () => {
    setIsEditing(!isEditing);
  };

  // 获取用户角色显示
  const getRoleDisplay = (role?: string) => {
    switch (role) {
      case 'admin':
        return { label: t('role.admin'), variant: 'destructive' as const };
      case 'vip':
        return { label: t('role.vip'), variant: 'default' as const };
      case 'member':
        return { label: t('role.member'), variant: 'secondary' as const };
      default:
        return { label: t('role.user'), variant: 'outline' as const };
    }
  };

  // 获取账户状态显示
  const getStatusDisplay = (status?: string) => {
    switch (status) {
      case 'active':
        return { label: t('status.active'), variant: 'default' as const };
      case 'inactive':
        return { label: t('status.inactive'), variant: 'secondary' as const };
      case 'banned':
        return { label: t('status.banned'), variant: 'destructive' as const };
      default:
        return { label: t('status.unknown'), variant: 'outline' as const };
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">{t('common.loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive">{t('common.loadFailed')}</h1>
          <p className="mt-2 text-muted-foreground">{t('common.cannotLoadProfile')}</p>
        </div>
      </div>
    );
  }

  const roleDisplay = getRoleDisplay(userProfile.role);
  const statusDisplay = getStatusDisplay(userProfile.status);

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold">{t('page.profile.title')}</h1>
          <p className="text-muted-foreground mt-2">{t('page.profile.subtitle')}</p>
        </div>

        {/* 用户概览卡片 */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
              {/* 头像上传 */}
              <div className="flex-shrink-0">
                <AvatarUpload
                  currentAvatar={userProfile.avatar}
                  username={userProfile.username}
                  onAvatarUpdate={handleAvatarUpdate}
                  onAvatarDelete={handleAvatarDelete}
                />
              </div>

              {/* 用户基本信息 */}
              <div className="flex-1 text-center md:text-left">
                <div className="space-y-2">
                  <h2 className="text-2xl font-bold">{userProfile.username}</h2>
                  <p className="text-muted-foreground">{userProfile.email}</p>

                  <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                    <Badge variant={roleDisplay.variant}>
                      <Crown className="h-3 w-3 mr-1" />
                      {roleDisplay.label}
                    </Badge>
                    <Badge variant={statusDisplay.variant}>
                      <Shield className="h-3 w-3 mr-1" />
                      {statusDisplay.label}
                    </Badge>
                  </div>

                  {userProfile.bio && (
                    <p className="text-sm text-muted-foreground max-w-md">
                      {userProfile.bio}
                    </p>
                  )}

                  {userProfile.created_at && (
                    <p className="text-xs text-muted-foreground">
                      {t('membership.joinedAt')}: {new Date(userProfile.created_at).toLocaleDateString('zh-CN')}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 钱包与充值 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Wallet className="w-6 h-6 mr-2" />
              {t('page.profile.myWallet')}
            </CardTitle>
            <CardDescription>{t('page.myMoney.viewBalance')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
              <span className="text-muted-foreground">{t('page.profile.currentBalance')}</span>
              <span className="text-2xl font-bold">$ {userProfile.balance?.toFixed(2) || '0.00'}</span>
            </div>
            <div className="space-y-2">
              <label htmlFor="rechargeAmount" className="font-medium">{t('page.profile.rechargeAmount')}</label>
                <Input
                id="rechargeAmount"
                  type="number"
                  placeholder={t('page.profile.customAmount')}
                  value={rechargeAmount}
                  onChange={(e) => setRechargeAmount(e.target.value)}
                disabled={isSubmitting || isAutoSubmitting}
                />
              <p className="text-xs text-muted-foreground">
                {t('page.profile.rechargeNote')}
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col md:flex-row md:justify-end gap-2">
            <Button
              onClick={handleRecharge}
              disabled={isSubmitting || isAutoSubmitting || !rechargeAmount}
              variant="outline"
              className="w-full md:w-auto"
            >
              {isSubmitting ? t('page.profile.submitting') : t('page.profile.manualSubmit')}
            </Button>
            <Button
              onClick={handleAutoRecharge}
              disabled={isSubmitting || isAutoSubmitting || !rechargeAmount}
              className="w-full md:w-auto"
            >
              {isAutoSubmitting ? t('page.profile.creatingOrder') : t('page.profile.autoRecharge')}
            </Button>
          </CardFooter>
        </Card>

        {/* 用户统计 */}
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">{userProfile.watch_time || 0}</p>
                  <p className="text-xs text-muted-foreground">{t('stats.watchTime')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <Heart className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-2xl font-bold">{userProfile.favorite_count || 0}</p>
                  <p className="text-xs text-muted-foreground">{t('stats.favoriteCount')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <Heart className="h-5 w-5 text-pink-500" />
                <div>
                  <p className="text-2xl font-bold">{userProfile.like_count || 0}</p>
                  <p className="text-xs text-muted-foreground">{t('stats.likeCount')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="text-2xl font-bold">{userProfile.comment_count || 0}</p>
                  <p className="text-xs text-muted-foreground">{t('stats.commentCount')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card 
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => window.location.href = `/users/${userProfile.id}/followers?tab=followers`}
          >
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-2xl font-bold">{userProfile.follower_count || 0}</p>
                  <p className="text-xs text-muted-foreground">{t('stats.followerCount')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card 
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => window.location.href = `/users/${userProfile.id}/following?tab=following`}
          >
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <UserPlus className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-2xl font-bold">{userProfile.following_count || 0}</p>
                  <p className="text-xs text-muted-foreground">{t('stats.followingCount')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 个人信息表单 */}
        <ProfileForm
          userProfile={userProfile}
          onProfileUpdate={handleProfileUpdate}
          isEditing={isEditing}
          onEditToggle={toggleEditMode}
        />

        {/* 账户安全 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>{t('page.profile.accountSecurity')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">{t('page.profile.emailVerification')}</p>
                <p className="text-sm text-muted-foreground">{t('page.accountSafety.ensureAccountSecurity')}</p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="default">{t('page.profile.emailVerified')}</Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEmailDialogOpen(true)}
                >
                  <Mail className="h-4 w-4 mr-1" />
                  {t('page.profile.modify')}
                </Button>
              </div>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">{t('page.profile.twoFactorAuth')}</p>
                <p className="text-sm text-muted-foreground">{t('page.accountSafety.addExtraProtection')}</p>
              </div>
              <Badge variant="outline">{t('page.profile.twoFactorNotEnabled')}</Badge>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">{t('page.profile.passwordStrength')}</p>
                <p className="text-sm text-muted-foreground">{t('page.accountSafety.updatePasswordRegularly')}</p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{t('page.profile.passwordMedium')}</Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPasswordDialogOpen(true)}
                >
                  <Lock className="h-4 w-4 mr-1" />
                  {t('page.profile.modify')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 主题设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Palette className="h-5 w-5" />
              <span>{t('page.profile.themeSettings')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{t('page.profile.interfaceTheme')}</p>
                  <p className="text-sm text-muted-foreground">{t('page.profile.selectThemeStyle')}</p>
                </div>
                <div className="flex items-center gap-2">
                  <ThemePreview />
                  <ThemeSwitcher />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 会员信息 */}
        {userProfile.role !== 'admin' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Crown className="h-5 w-5 text-yellow-500" />
                <span>{t('membership.info')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{t('membership.currentStatus')}</p>
                  <p className="text-sm text-muted-foreground">
                    {userProfile.role === 'vip' ? t('membership.vipUser') :
                     userProfile.role === 'member' ? t('membership.memberUser') : t('membership.regularUser')}
                  </p>
                </div>
                {isMember ? (
                  <div className="text-right">
                    <div className="flex items-center justify-end space-x-2 mb-2">
                      <Crown className="h-5 w-5 text-blue-500" />
                      <span className="text-lg font-semibold text-blue-600">{t('membership.memberUser')}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {t('membership.thankYouSupport')}
                    </p>
                  </div>
                ) : userProfile.role !== 'admin' && (
                  <div className="text-right">
                    <p className="text-sm text-muted-foreground mb-2">
                      {t('membership.upgradePrompt')}
                    </p>
                    <Badge
                      variant="outline"
                      className="cursor-pointer hover:bg-primary hover:text-primary-foreground border-orange-500 text-orange-600 hover:bg-orange-500"
                    >
                      {t('membership.upgradeNow')}
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* 密码修改对话框 */}
      <PasswordChangeDialog
        open={passwordDialogOpen}
        onOpenChange={setPasswordDialogOpen}
      />

      {/* 邮箱换绑对话框 */}
      <EmailChangeDialog
        open={emailDialogOpen}
        onOpenChange={setEmailDialogOpen}
        currentEmail={userProfile?.email}
      />

      {/* 支付对话框 */}
      <Dialog 
        open={paymentDialogOpen} 
        onOpenChange={(isOpen) => {
          setPaymentDialogOpen(isOpen);
          if (!isOpen) {
            // 当对话框关闭时，刷新用户数据
            refreshUser();
            // 也可以选择性地刷新当前页面的数据
            // fetchProfile(); // 如果需要的话
          }
        }}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <QrCode className="w-6 h-6 mr-2" />
              {t('common.completePayment')}
            </DialogTitle>
            <DialogDescription>
              {t('common.paymentInstructions')}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="p-4 bg-muted rounded-lg text-center">
              <p className="text-sm text-muted-foreground">{t('common.paymentAmount')}</p>
              <p className="text-3xl font-bold">¥ {paymentData?.amount?.toFixed(2)}</p>
            </div>
            {paymentData?.qrCode ? (
              <div className="flex justify-center">
                {/* QR Code would be rendered here. For now, showing URL. */}
                <img src={paymentData.qrCode} alt="QR Code" className="w-48 h-48" />
              </div>
            ) : paymentData?.paymentUrl && (
              <div className="text-center">
                <a
                  href={paymentData.paymentUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  {t('common.clickToPayment')}
                </a>
              </div>
            )}
            {paymentData?.codeUrl && (
               <div className="space-y-2">
                 <Label htmlFor="payment-url">{t('common.orCopyLink')}</Label>
                 <div className="flex items-center space-x-2">
                    <Input id="payment-url" value={paymentData.codeUrl} readOnly />
                    <Button variant="secondary" size="icon" onClick={() => navigator.clipboard.writeText(paymentData.codeUrl)}>
                      <Copy className="h-4 w-4" />
                    </Button>
                 </div>
               </div>
            )}
          </div>
          <DialogFooter>
            <Button onClick={() => setPaymentDialogOpen(false)} variant="outline">{t('common.close')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProfilePage;