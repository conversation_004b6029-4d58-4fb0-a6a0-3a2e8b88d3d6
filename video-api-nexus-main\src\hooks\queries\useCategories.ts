import { useQuery } from '@tanstack/react-query';
import { getCategories, getCategoryTree, getCategoryDetails, searchCategories } from '@/lib/api';

// 查询键工厂
export const categoryKeys = {
  all: ['categories'] as const,
  lists: () => [...categoryKeys.all, 'list'] as const,
  list: (filters?: any) => [...categoryKeys.lists(), filters] as const,
  tree: () => [...categoryKeys.all, 'tree'] as const,
  details: () => [...categoryKeys.all, 'detail'] as const,
  detail: (id: string | number) => [...categoryKeys.details(), id] as const,
  search: (keyword: string) => [...categoryKeys.all, 'search', keyword] as const,
};

// 获取分类列表
export const useCategories = (filters?: any) => {
  return useQuery({
    queryKey: categoryKeys.list(filters),
    queryFn: () => getCategories(),
    select: (data) => {
      // 安全地访问数据，处理不同的响应结构
      return data?.data?.data?.categories || data?.data?.categories || data?.data || [];
    },
    staleTime: 15 * 60 * 1000, // 15分钟，分类数据变化较少
    cacheTime: 30 * 60 * 1000, // 30分钟
  });
};

// 获取分类树
export const useCategoryTree = () => {
  return useQuery({
    queryKey: categoryKeys.tree(),
    queryFn: () => getCategoryTree(),
    select: (data) => {
      return data?.data?.data?.categories || data?.data?.categories || data?.data || [];
    },
    staleTime: 15 * 60 * 1000, // 15分钟
    cacheTime: 30 * 60 * 1000, // 30分钟
  });
};

// 获取分类详情
export const useCategoryDetail = (id: string | number) => {
  return useQuery({
    queryKey: categoryKeys.detail(id),
    queryFn: () => getCategoryDetails(id),
    select: (data) => {
      return data?.data?.data?.category || data?.data?.category || data?.data;
    },
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10分钟
    cacheTime: 20 * 60 * 1000, // 20分钟
  });
};

// 搜索分类
export const useSearchCategories = (keyword: string) => {
  return useQuery({
    queryKey: categoryKeys.search(keyword),
    queryFn: () => searchCategories(keyword),
    select: (data) => {
      return data?.data?.data?.categories || data?.data?.categories || data?.data || [];
    },
    enabled: !!keyword && keyword.length > 0,
    staleTime: 5 * 60 * 1000, // 5分钟
    cacheTime: 10 * 60 * 1000, // 10分钟
  });
};
