const BaseModel = require('../BaseModel');

class WatchHistory extends BaseModel {
  constructor() {
    super('watch_history');
  }

  // 记录或更新观看记录
  async recordWatch(userId, videoId, watchData) {
    const { watch_duration, progress, last_position, device_type, ip_address } = watchData;
    
    const sql = `
      INSERT INTO ${this.tableName} 
      (user_id, video_id, watch_duration, progress, last_position, device_type, ip_address)
      VALUES (?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        watch_duration = GREATEST(watch_duration, VALUES(watch_duration)),
        progress = VALUES(progress),
        last_position = VALUES(last_position),
        device_type = VALUES(device_type),
        ip_address = VALUES(ip_address),
        updated_at = CURRENT_TIMESTAMP
    `;
    
    return await this.query(sql, [
      userId, videoId, watch_duration, progress, last_position, device_type, ip_address
    ]);
  }

  // 获取用户观看记录
  async getUserWatchHistory(userId, options = {}) {
    const { page = 1, pageSize = 20, sortBy = 'updated_at', sortOrder = 'DESC' } = options;
    const offset = (page - 1) * pageSize;

    const sql = `
      SELECT 
        wh.*,
        v.title,
        v.thumbnail,
        v.duration,
        v.media_type
      FROM ${this.tableName} wh
      LEFT JOIN videos v ON wh.video_id = v.id
      WHERE wh.user_id = ? AND v.status = 'published'
      ORDER BY wh.${sortBy} ${sortOrder}
      LIMIT ${parseInt(pageSize, 10)} OFFSET ${parseInt(offset, 10)}
    `;

    const countSql = `
      SELECT COUNT(*) as total 
      FROM ${this.tableName} wh
      LEFT JOIN videos v ON wh.video_id = v.id
      WHERE wh.user_id = ? AND v.status = 'published'
    `;

    const [data, countResult] = await Promise.all([
      this.query(sql, [userId]),
      this.query(countSql, [userId])
    ]);

    const total = countResult[0].total;

    return {
      data,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 获取用户总观看时长(小时)
  async getUserTotalWatchTime(userId) {
    const sql = `
      SELECT 
        COALESCE(SUM(watch_duration), 0) as total_seconds,
        ROUND(COALESCE(SUM(watch_duration), 0) / 3600, 1) as total_hours
      FROM ${this.tableName} wh
      LEFT JOIN videos v ON wh.video_id = v.id
      WHERE wh.user_id = ? AND v.status = 'published'
    `;
    
    const result = await this.query(sql, [userId]);
    return result[0];
  }

  // 获取视频观看统计
  async getVideoWatchStats(videoId) {
    const sql = `
      SELECT 
        COUNT(DISTINCT user_id) as unique_viewers,
        COUNT(*) as total_views,
        AVG(progress) as avg_progress,
        SUM(watch_duration) as total_watch_time
      FROM ${this.tableName}
      WHERE video_id = ?
    `;
    
    const result = await this.query(sql, [videoId]);
    return result[0];
  }

  // 获取用户观看统计
  async getUserWatchStats(userId) {
    const sql = `
      SELECT 
        COUNT(DISTINCT video_id) as videos_watched,
        COUNT(CASE WHEN progress >= 90 THEN 1 END) as videos_completed,
        ROUND(AVG(progress), 2) as avg_progress,
        ROUND(SUM(watch_duration) / 3600, 1) as total_hours
      FROM ${this.tableName} wh
      LEFT JOIN videos v ON wh.video_id = v.id
      WHERE wh.user_id = ? AND v.status = 'published'
    `;
    
    const result = await this.query(sql, [userId]);
    return result[0];
  }

  // 删除观看记录
  async deleteWatchRecord(userId, videoId) {
    const sql = `DELETE FROM ${this.tableName} WHERE user_id = ? AND video_id = ?`;
    return await this.query(sql, [userId, videoId]);
  }

  // 清理用户观看记录
  async clearUserWatchHistory(userId) {
    const sql = `DELETE FROM ${this.tableName} WHERE user_id = ?`;
    return await this.query(sql, [userId]);
  }

  // 获取热门观看内容
  async getPopularContent(timeRange = '7d', limit = 10) {
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    
    const sql = `
      SELECT 
        v.id,
        v.title,
        v.thumbnail,
        v.media_type,
        COUNT(DISTINCT wh.user_id) as unique_viewers,
        COUNT(wh.id) as total_views,
        SUM(wh.watch_duration) as total_watch_time,
        AVG(wh.progress) as avg_completion_rate
      FROM videos v
      LEFT JOIN ${this.tableName} wh ON v.id = wh.video_id
      WHERE v.status = 'published' 
        AND wh.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY v.id
      ORDER BY unique_viewers DESC, total_watch_time DESC
      LIMIT ?
    `;
    
    return await this.query(sql, [days, limit]);
  }
}

module.exports = WatchHistory;
