import api from './api';

export interface User {
  id: number;
  username: string;
  nickname: string;
  avatar: string;
  bio: string;
  follower_count: number;
  following_count: number;
  video_count: number;
  is_following?: boolean;
}

export interface FollowResponse {
  success: boolean;
  message: string;
  data: any;
}

export interface UserListResponse {
  data: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface FollowStats {
  follower_count: number;
  following_count: number;
  video_count: number;
  total_views: number;
  total_likes: number;
}

export interface Video {
  id: number;
  title: string;
  description: string;
  thumbnail_url: string;
  url: string;
  duration: number;
  view_count: number;
  like_count: number;
  comment_count: number;
  published_at: string;
  uploader_id: number;
  username: string;
  nickname: string;
  avatar: string;
}

export interface VideoListResponse {
  data: Video[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const followApi = {
  // 关注用户
  followUser: (userId: number): Promise<FollowResponse> =>
    api.post(`/follow/${userId}`),

  // 取消关注
  unfollowUser: (userId: number): Promise<FollowResponse> =>
    api.delete(`/follow/${userId}`),

  // 检查关注状态
  checkFollowStatus: (userId: number): Promise<{ data: { is_following: boolean } }> =>
    api.get(`/follow/${userId}/status`),

  // 获取粉丝列表
  getFollowers: (userId: number, page = 1, limit = 20): Promise<{ data: UserListResponse }> =>
    api.get(`/users/${userId}/followers`, { params: { page, limit } }),

  // 获取关注列表  
  getFollowing: (userId: number, page = 1, limit = 20): Promise<{ data: UserListResponse }> =>
    api.get(`/users/${userId}/following`, { params: { page, limit } }),

  // 获取关注统计
  getFollowStats: (userId: number): Promise<{ data: FollowStats }> =>
    api.get(`/users/${userId}/stats`),

  // 获取相互关注列表
  getMutualFollows: (userId: number, page = 1, limit = 20): Promise<{ data: UserListResponse }> =>
    api.get(`/users/${userId}/mutual`, { params: { page, limit } }),

  // 获取关注动态
  getFollowingVideos: (page = 1, limit = 20): Promise<{ data: VideoListResponse }> =>
    api.get('/following/videos', { params: { page, limit } }),

  // 获取推荐关注用户
  getRecommendedUsers: (limit = 10): Promise<{ data: User[] }> =>
    api.get('/recommendations/users', { params: { limit } }),

  // 批量检查关注状态
  batchCheckFollowStatus: (userIds: number[]): Promise<{ data: Record<number, boolean> }> =>
    api.post('/follow/batch/status', { userIds })
};