const asyncHandler = require('express-async-handler');
const earningsService = require('../services/earningsService');

class EarningsController {
  
  /**
   * @desc    获取收益摘要
   * @route   GET /api/earnings/summary
   * @access  Private
   */
  getSummary = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const summary = await earningsService.getSummary(userId);
    res.json({ success: true, data: summary });
  });

  /**
   * @desc    获取收益明细列表
   * @route   GET /api/earnings/details
   * @access  Private
   */
  getDetails = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;
    const details = await earningsService.getDetails(userId, { page, limit });
    res.json({ success: true, ...details });
  });

  /**
   * @desc    获取余额变动历史
   * @route   GET /api/earnings/balance-history
   * @access  Private
   */
  getBalanceHistory = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;
    const history = await earningsService.getBalanceHistory(userId, { page, limit });
    res.json({ success: true, ...history });
  });

}

module.exports = new EarningsController(); 