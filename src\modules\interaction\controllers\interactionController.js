const { AppError, asyncHandler } = require('../../../middleware/errorHandler');
const { operationLogger } = require('../../../middleware/requestLogger');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const Comment = require('../../../database/models/Comment');
const Like = require('../../../database/models/Like');
const Favorite = require('../../../database/models/Favorite');

class InteractionController {
  // 创建评论
  createComment = asyncHandler(async (req, res) => {
    const { videoId, content, parentId } = req.body;
    const userId = req.user.id;

    const comment = await Comment.createComment({
      videoId,
      userId,
      parentId,
      content
    });

    // 获取评论详情
    const commentDetails = await Comment.getCommentDetails(comment.id);

    // 清除相关缓存
    await this.clearCommentCache(videoId);

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      'comment_create',
      comment.id,
      '发表评论',
      { videoId, parentId, content: content.substring(0, 50) }
    );

    res.status(201).json({
      success: true,
      message: '评论发表成功',
      data: {
        comment: commentDetails
      }
    });
  });

  // 获取视频评论列表
  getVideoComments = asyncHandler(async (req, res) => {
    const { videoId } = req.params;
    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const cacheKey = cache.generateKey(
      CACHE_KEYS.VIDEO,
      'comments',
      videoId,
      page,
      pageSize,
      sortBy,
      sortOrder
    );

    let result = await cache.get(cacheKey);

    if (!result) {
      result = await Comment.getVideoComments(videoId, {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        sortBy,
        sortOrder
      });

      await cache.set(cacheKey, result, 300); // 5分钟缓存
    }

    res.json({
      success: true,
      data: result
    });
  });

  // 获取评论回复
  getCommentReplies = asyncHandler(async (req, res) => {
    const { commentId } = req.params;
    const {
      page = 1,
      pageSize = 10
    } = req.query;

    const result = await Comment.getCommentReplies(commentId, {
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

    res.json({
      success: true,
      data: result
    });
  });

  // 更新评论
  updateComment = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    // 获取评论信息
    const comment = await Comment.findById(id);
    if (!comment) {
      throw new AppError('评论不存在', 404, 'COMMENT_NOT_FOUND');
    }

    // 检查权限
    if (comment.user_id !== userId && req.user.role !== 'admin') {
      throw new AppError('无权修改此评论', 403, 'ACCESS_DENIED');
    }

    // 更新评论
    const updatedComment = await Comment.updateComment(id, { content });

    // 清除相关缓存
    await this.clearCommentCache(comment.video_id);

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      'comment_update',
      id,
      '更新评论',
      { content: content.substring(0, 50) }
    );

    res.json({
      success: true,
      message: '评论更新成功',
      data: {
        comment: updatedComment
      }
    });
  });

  // 删除评论
  deleteComment = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    const comment = await Comment.findById(id);
    if (!comment) {
      throw new AppError('评论不存在', 404, 'COMMENT_NOT_FOUND');
    }

    await Comment.deleteComment(id, userId, userRole);

    // 清除相关缓存
    await this.clearCommentCache(comment.video_id);

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      'comment_delete',
      id,
      '删除评论',
      { videoId: comment.video_id }
    );

    res.json({
      success: true,
      message: '评论删除成功'
    });
  });

  // 点赞/取消点赞
  toggleLike = asyncHandler(async (req, res) => {
    const { targetId, targetType } = req.body;
    const userId = req.user.id;

    const result = await Like.toggleLike(userId, targetId, targetType);

    // 清除相关缓存
    if (targetType === 'video') {
      await this.clearVideoCache(targetId);
    } else if (targetType === 'comment') {
      const comment = await Comment.findById(targetId);
      if (comment) {
        await this.clearCommentCache(comment.video_id);
      }
    }

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      result.action === 'liked' ? 'like_add' : 'like_remove',
      targetId,
      `${result.action === 'liked' ? '点赞' : '取消点赞'}${targetType === 'video' ? '视频' : '评论'}`,
      { targetType }
    );

    res.json({
      success: true,
      message: result.action === 'liked' ? '点赞成功' : '取消点赞成功',
      data: result
    });
  });

  // 收藏/取消收藏视频
  toggleFavorite = asyncHandler(async (req, res) => {
    const { videoId } = req.body;
    const userId = req.user.id;

    const result = await Favorite.toggleFavorite(userId, videoId);

    // 清除相关缓存
    await this.clearVideoCache(videoId);
    await this.clearUserCache(userId);

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      result.action === 'favorited' ? 'favorite_add' : 'favorite_remove',
      videoId,
      `${result.action === 'favorited' ? '收藏' : '取消收藏'}视频`
    );

    res.json({
      success: true,
      message: result.action === 'favorited' ? '收藏成功' : '取消收藏成功',
      data: result
    });
  });

  // 获取用户收藏列表
  getUserFavorites = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const requesterId = req.user?.id;
    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    // 检查权限：只能查看自己的收藏
    if (parseInt(id) !== requesterId && req.user?.role !== 'admin') {
      throw new AppError('无权查看此用户的收藏', 403, 'ACCESS_DENIED');
    }

    const result = await Favorite.getUserFavorites(id, {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      sortBy,
      sortOrder
    });

    res.json({
      success: true,
      data: result
    });
  });

  // 获取用户点赞列表
  getUserLikes = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const requesterId = req.user?.id;
    const {
      page = 1,
      pageSize = 20,
      targetType
    } = req.query;

    // 检查权限：只能查看自己的点赞
    if (parseInt(id) !== requesterId && req.user?.role !== 'admin') {
      throw new AppError('无权查看此用户的点赞', 403, 'ACCESS_DENIED');
    }

    const result = await Like.getUserLikes(id, {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      targetType
    });

    res.json({
      success: true,
      data: result
    });
  });

  // 获取用户评论列表
  getUserComments = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const requesterId = req.user?.id;
    const {
      page = 1,
      pageSize = 20,
      status = 'active'
    } = req.query;

    // 检查权限：只能查看自己的评论，或管理员可以查看所有
    if (parseInt(id) !== requesterId && req.user?.role !== 'admin') {
      throw new AppError('无权查看此用户的评论', 403, 'ACCESS_DENIED');
    }

    const result = await Comment.getUserComments(id, {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      status
    });

    res.json({
      success: true,
      data: result
    });
  });

  // 批量检查互动状态
  batchCheckInteractions = asyncHandler(async (req, res) => {
    const { videoIds } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.json({
        success: true,
        data: {
          likes: {},
          favorites: {}
        }
      });
    }

    if (!videoIds || !Array.isArray(videoIds) || videoIds.length === 0) {
      throw new AppError('视频ID列表不能为空', 400, 'INVALID_VIDEO_IDS');
    }

    // 检查点赞状态
    const likeTargets = videoIds.map(id => ({ id, type: 'video' }));
    const likes = await Like.batchCheckLiked(userId, likeTargets);

    // 检查收藏状态
    const favorites = await Favorite.batchCheckFavorited(userId, videoIds);

    res.json({
      success: true,
      data: {
        likes,
        favorites
      }
    });
  });

  // 获取热门评论
  getPopularComments = asyncHandler(async (req, res) => {
    const { videoId } = req.params;
    const { limit = 5 } = req.query;

    const cacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'popular_comments', videoId, limit);
    let comments = await cache.get(cacheKey);

    if (!comments) {
      comments = await Comment.getPopularComments(videoId, parseInt(limit));
      await cache.set(cacheKey, comments, 600); // 10分钟缓存
    }

    res.json({
      success: true,
      data: {
        comments
      }
    });
  });

  // 搜索评论
  searchComments = asyncHandler(async (req, res) => {
    const {
      keyword,
      page = 1,
      pageSize = 20,
      videoId
    } = req.query;

    if (!keyword || keyword.trim().length === 0) {
      throw new AppError('搜索关键词不能为空', 400, 'KEYWORD_REQUIRED');
    }

    const result = await Comment.searchComments(keyword.trim(), {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      videoId: videoId ? parseInt(videoId) : null
    });

    res.json({
      success: true,
      data: result
    });
  });

  // 获取互动统计
  getInteractionStats = asyncHandler(async (req, res) => {
    const { videoId } = req.params;

    const cacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'interaction_stats', videoId);
    let stats = await cache.get(cacheKey);

    if (!stats) {
      const [commentStats, likeStats, favoriteStats] = await Promise.all([
        Comment.getCommentStats(videoId),
        Like.getLikeStats(videoId, 'video'),
        Favorite.getFavoriteStats()
      ]);

      stats = {
        comments: commentStats,
        likes: likeStats,
        favorites: favoriteStats
      };

      await cache.set(cacheKey, stats, 1800); // 30分钟缓存
    }

    res.json({
      success: true,
      data: {
        stats
      }
    });
  });

  // 清除评论缓存
  async clearCommentCache(videoId) {
    const patterns = [
      `${CACHE_KEYS.VIDEO}:comments:${videoId}:*`,
      `${CACHE_KEYS.VIDEO}:popular_comments:${videoId}:*`,
      `${CACHE_KEYS.VIDEO}:interaction_stats:${videoId}`
    ];

    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
  }

  // 清除视频缓存
  async clearVideoCache(videoId) {
    const patterns = [
      `${CACHE_KEYS.VIDEO}:details:${videoId}:*`,
      `${CACHE_KEYS.VIDEO}:list:*`,
      `${CACHE_KEYS.VIDEO}:popular:*`,
      `${CACHE_KEYS.VIDEO}:recommended:*`,
      `${CACHE_KEYS.VIDEO}:interaction_stats:${videoId}`
    ];

    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
  }

  // 清除用户缓存
  async clearUserCache(userId) {
    const patterns = [
      `${CACHE_KEYS.USER}:*:${userId}:*`
    ];

    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
  }
}

module.exports = new InteractionController();
