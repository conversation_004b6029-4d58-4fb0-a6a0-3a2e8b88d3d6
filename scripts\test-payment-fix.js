const mysql = require('mysql2/promise');
const logger = require('../src/utils/logger');

// 加载环境变量
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'video_user',
  password: process.env.DB_PASSWORD || 'secure_password',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

async function testPaymentFix() {
  let connection;
  
  try {
    console.log('🧪 开始测试支付修复...\n');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功\n');
    
    // 1. 创建测试用户（如果不存在）
    const testEmail = '<EMAIL>';
    const testUsername = 'test_payment_user';
    
    console.log('👤 准备测试用户...');
    
    // 检查用户是否存在
    const [existingUsers] = await connection.execute(
      'SELECT * FROM users WHERE email = ? OR username = ?',
      [testEmail, testUsername]
    );
    
    let testUserId;
    if (existingUsers.length > 0) {
      testUserId = existingUsers[0].id;
      console.log(`使用现有测试用户 ID: ${testUserId}`);
    } else {
      // 创建测试用户
      const [userResult] = await connection.execute(`
        INSERT INTO users (username, email, password, role, created_at, updated_at)
        VALUES (?, ?, '$2b$12$dummy.hash.for.test', 'user', NOW(), NOW())
      `, [testUsername, testEmail]);
      
      testUserId = userResult.insertId;
      console.log(`创建新测试用户 ID: ${testUserId}`);
    }
    
    // 2. 获取一个会员计划
    console.log('\n📋 获取会员计划...');
    const [plans] = await connection.execute(
      'SELECT * FROM membership_plans WHERE is_active = 1 ORDER BY price ASC LIMIT 1'
    );
    
    if (plans.length === 0) {
      console.log('❌ 没有找到可用的会员计划');
      return;
    }
    
    const testPlan = plans[0];
    console.log(`使用会员计划: ${testPlan.name} (ID: ${testPlan.id}, 价格: ¥${testPlan.price})`);
    
    // 3. 创建测试订单
    console.log('\n📝 创建测试订单...');
    const orderNo = `TEST_${Date.now()}`;
    const transactionId = `TXN_${Date.now()}`;
    
    const [orderResult] = await connection.execute(`
      INSERT INTO orders (
        user_id, type, target_id, amount, final_amount, 
        payment_method, payment_status, order_no, transaction_id,
        description, created_at, updated_at
      ) VALUES (?, 'membership', ?, ?, ?, 'alipay', 'pending', ?, ?, ?, NOW(), NOW())
    `, [
      testUserId, testPlan.id, testPlan.price, testPlan.price,
      orderNo, transactionId, `测试订单 - 购买会员: ${testPlan.name}`
    ]);
    
    const testOrderId = orderResult.insertId;
    console.log(`创建测试订单 ID: ${testOrderId}, 订单号: ${orderNo}`);
    
    // 4. 模拟支付成功处理
    console.log('\n💳 模拟支付成功处理...');
    
    // 导入支付控制器
    const PaymentController = require('../src/modules/payment/controllers/paymentController');
    const paymentController = new PaymentController();
    
    // 开始事务
    await connection.beginTransaction();
    
    try {
      // 更新订单状态为已支付
      await connection.execute(`
        UPDATE orders SET 
          payment_status = 'paid',
          payment_time = NOW(),
          updated_at = NOW()
        WHERE id = ?
      `, [testOrderId]);
      
      console.log('✅ 订单状态已更新为已支付');
      
      // 获取更新后的订单信息
      const [updatedOrders] = await connection.execute(
        'SELECT * FROM orders WHERE id = ?',
        [testOrderId]
      );
      const order = updatedOrders[0];
      
      // 调用业务处理逻辑
      await paymentController.processOrderBusiness(order, connection);
      
      console.log('✅ 业务处理逻辑执行完成');
      
      // 提交事务
      await connection.commit();
      console.log('✅ 事务提交成功');
      
    } catch (error) {
      await connection.rollback();
      console.log('❌ 事务回滚:', error.message);
      throw error;
    }
    
    // 5. 验证结果
    console.log('\n🔍 验证处理结果...');
    
    // 检查会员记录
    const [memberships] = await connection.execute(`
      SELECT 
        m.*,
        mp.name as plan_name
      FROM memberships m
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.user_id = ? AND m.transaction_id = ?
    `, [testUserId, transactionId]);
    
    if (memberships.length > 0) {
      const membership = memberships[0];
      console.log('✅ 会员记录创建成功:');
      console.log(`   - 会员ID: ${membership.id}`);
      console.log(`   - 计划: ${membership.plan_name}`);
      console.log(`   - 状态: ${membership.status}`);
      console.log(`   - 开始时间: ${membership.start_date}`);
      console.log(`   - 结束时间: ${membership.end_date}`);
      console.log(`   - 支付方式: ${membership.payment_method}`);
      console.log(`   - 交易ID: ${membership.transaction_id}`);
    } else {
      console.log('❌ 会员记录创建失败');
    }
    
    // 检查用户角色
    const [users] = await connection.execute(
      'SELECT * FROM users WHERE id = ?',
      [testUserId]
    );
    
    if (users.length > 0) {
      const user = users[0];
      console.log(`✅ 用户角色: ${user.role}`);
      
      if (user.role === 'member') {
        console.log('✅ 用户角色更新成功');
      } else {
        console.log('❌ 用户角色更新失败');
      }
    }
    
    console.log('\n🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    logger.error('测试支付修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testPaymentFix()
    .then(() => {
      console.log('\n✅ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testPaymentFix };
