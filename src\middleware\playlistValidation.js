const Joi = require('joi');
const { AppError } = require('./errorHandler');

// 播放列表验证规则
const playlistSchemas = {
  // 创建播放列表
  createPlaylist: Joi.object({
    name: Joi.string()
      .trim()
      .min(1)
      .max(255)
      .required()
      .messages({
        'string.empty': '播放列表名称不能为空',
        'string.min': '播放列表名称不能为空',
        'string.max': '播放列表名称不能超过255个字符',
        'any.required': '播放列表名称是必填项'
      }),
    description: Joi.string()
      .max(1000)
      .allow('')
      .optional()
      .messages({
        'string.max': '描述不能超过1000个字符'
      }),
    isPublic: Joi.boolean()
      .default(false)
      .optional(),
    playMode: Joi.string()
      .valid('sequence', 'loop', 'random')
      .default('sequence')
      .optional()
      .messages({
        'any.only': '播放模式必须是 sequence、loop 或 random 之一'
      })
  }),

  // 更新播放列表
  updatePlaylist: Joi.object({
    name: Joi.string()
      .trim()
      .min(1)
      .max(255)
      .optional()
      .messages({
        'string.empty': '播放列表名称不能为空',
        'string.min': '播放列表名称不能为空',
        'string.max': '播放列表名称不能超过255个字符'
      }),
    description: Joi.string()
      .max(1000)
      .allow('')
      .optional()
      .messages({
        'string.max': '描述不能超过1000个字符'
      }),
    isPublic: Joi.boolean()
      .optional(),
    playMode: Joi.string()
      .valid('sequence', 'loop', 'random')
      .optional()
      .messages({
        'any.only': '播放模式必须是 sequence、loop 或 random 之一'
      })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  }),

  // 添加视频到播放列表
  addVideoToPlaylist: Joi.object({
    videoId: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': '视频ID必须是数字',
        'number.integer': '视频ID必须是整数',
        'number.positive': '视频ID必须是正数',
        'any.required': '视频ID是必填项'
      }),
    position: Joi.number()
      .integer()
      .min(0)
      .optional()
      .messages({
        'number.base': '位置必须是数字',
        'number.integer': '位置必须是整数',
        'number.min': '位置不能小于0'
      })
  }),

  // 批量添加视频
  addMultipleVideos: Joi.object({
    videoIds: Joi.array()
      .items(
        Joi.number()
          .integer()
          .positive()
          .messages({
            'number.base': '视频ID必须是数字',
            'number.integer': '视频ID必须是整数',
            'number.positive': '视频ID必须是正数'
          })
      )
      .min(1)
      .max(100)
      .required()
      .messages({
        'array.min': '至少需要添加一个视频',
        'array.max': '一次最多只能添加100个视频',
        'any.required': '视频ID列表是必填项'
      })
  }),

  // 重新排序
  reorderItems: Joi.object({
    items: Joi.array()
      .items(
        Joi.object({
          videoId: Joi.number()
            .integer()
            .positive()
            .required(),
          position: Joi.number()
            .integer()
            .min(0)
            .required()
        })
      )
      .min(1)
      .required()
      .messages({
        'array.min': '至少需要一个排序项目',
        'any.required': '排序项目列表是必填项'
      })
  }),

  // 复制播放列表
  duplicatePlaylist: Joi.object({
    newName: Joi.string()
      .trim()
      .min(1)
      .max(255)
      .optional()
      .messages({
        'string.empty': '新播放列表名称不能为空',
        'string.min': '新播放列表名称不能为空',
        'string.max': '新播放列表名称不能超过255个字符'
      })
  }),

  // 记录播放历史
  recordPlayHistory: Joi.object({
    videoId: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': '视频ID必须是数字',
        'number.integer': '视频ID必须是整数',
        'number.positive': '视频ID必须是正数',
        'any.required': '视频ID是必填项'
      }),
    watchDuration: Joi.number()
      .integer()
      .min(0)
      .default(0)
      .messages({
        'number.base': '观看时长必须是数字',
        'number.integer': '观看时长必须是整数',
        'number.min': '观看时长不能小于0'
      }),
    videoDuration: Joi.number()
      .integer()
      .min(0)
      .default(0)
      .messages({
        'number.base': '视频时长必须是数字',
        'number.integer': '视频时长必须是整数',
        'number.min': '视频时长不能小于0'
      }),
    completed: Joi.boolean()
      .default(false)
  }),

  // 查询参数验证
  queryParams: Joi.object({
    includeItems: Joi.boolean()
      .default(false),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .default(50)
      .messages({
        'number.base': '限制数量必须是数字',
        'number.integer': '限制数量必须是整数',
        'number.min': '限制数量不能小于1',
        'number.max': '限制数量不能大于100'
      }),
    offset: Joi.number()
      .integer()
      .min(0)
      .default(0)
      .messages({
        'number.base': '偏移量必须是数字',
        'number.integer': '偏移量必须是整数',
        'number.min': '偏移量不能小于0'
      })
  })
};

// 路径参数验证
const pathParamSchemas = {
  playlistId: Joi.object({
    playlistId: Joi.string()
      .pattern(/^\d+$/)
      .required()
      .custom((value, helpers) => {
        const num = parseInt(value, 10);
        if (isNaN(num) || num <= 0) {
          return helpers.error('number.invalid');
        }
        return num;
      })
      .messages({
        'string.pattern.base': '播放列表ID必须是数字',
        'number.invalid': '播放列表ID必须是正整数',
        'any.required': '播放列表ID是必填项'
      })
  }).unknown(true), // 允许其他未知字段
  videoId: Joi.object({
    videoId: Joi.string()
      .pattern(/^\d+$/)
      .required()
      .custom((value, helpers) => {
        const num = parseInt(value, 10);
        if (isNaN(num) || num <= 0) {
          return helpers.error('number.invalid');
        }
        return num;
      })
      .messages({
        'string.pattern.base': '视频ID必须是数字',
        'number.invalid': '视频ID必须是正整数',
        'any.required': '视频ID是必填项'
      })
  }).unknown(true), // 允许其他未知字段
  playlistAndVideo: Joi.object({
    playlistId: Joi.string()
      .pattern(/^\d+$/)
      .required()
      .custom((value, helpers) => {
        const num = parseInt(value, 10);
        if (isNaN(num) || num <= 0) {
          return helpers.error('number.invalid');
        }
        return num;
      })
      .messages({
        'string.pattern.base': '播放列表ID必须是数字',
        'number.invalid': '播放列表ID必须是正整数',
        'any.required': '播放列表ID是必填项'
      }),
    videoId: Joi.string()
      .pattern(/^\d+$/)
      .required()
      .custom((value, helpers) => {
        const num = parseInt(value, 10);
        if (isNaN(num) || num <= 0) {
          return helpers.error('number.invalid');
        }
        return num;
      })
      .messages({
        'string.pattern.base': '视频ID必须是数字',
        'number.invalid': '视频ID必须是正整数',
        'any.required': '视频ID是必填项'
      })
  })
};

// 创建验证中间件
const createValidator = (schema, source = 'body') => {
  return (req, res, next) => {
    let data;
    
    switch (source) {
      case 'body':
        data = req.body;
        break;
      case 'query':
        data = req.query;
        break;
      case 'params':
        data = req.params;
        break;
      default:
        data = req.body;
    }

    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join('; ');
      console.error('验证错误:', errorMessage, '原始数据:', data, '验证源:', source);
      return next(new AppError(errorMessage, 400, 'VALIDATION_ERROR'));
    }

    // 将验证后的数据重新赋值
    switch (source) {
      case 'body':
        req.body = value;
        break;
      case 'query':
        req.query = value;
        break;
      case 'params':
        req.params = value;
        break;
    }

    next();
  };
};

// 导出验证中间件
module.exports = {
  // 播放列表相关验证
  validateCreatePlaylist: createValidator(playlistSchemas.createPlaylist),
  validateUpdatePlaylist: createValidator(playlistSchemas.updatePlaylist),
  validateAddVideo: createValidator(playlistSchemas.addVideoToPlaylist),
  validateAddMultipleVideos: createValidator(playlistSchemas.addMultipleVideos),
  validateReorderItems: createValidator(playlistSchemas.reorderItems),
  validateDuplicatePlaylist: createValidator(playlistSchemas.duplicatePlaylist),
  
  // 播放历史相关验证
  validateRecordPlayHistory: createValidator(playlistSchemas.recordPlayHistory),
  
  // 查询参数验证
  validateQueryParams: createValidator(playlistSchemas.queryParams, 'query'),
  
  // 路径参数验证
  validatePlaylistId: createValidator(pathParamSchemas.playlistId, 'params'),
  validateVideoId: createValidator(pathParamSchemas.videoId, 'params'),
  
  // 组合验证（同时验证多个部分）
  validatePlaylistAndVideo: createValidator(pathParamSchemas.playlistAndVideo, 'params')
};
