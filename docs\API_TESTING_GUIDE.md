# API测试指南

> 视频平台API接口测试完整指南

## 🎯 测试概述

本指南提供了完整的API测试方法，包括手动测试和自动化测试。

## 🛠️ 测试工具

### 1. Postman测试
- 导入 `docs/postman_collection.json` 到Postman
- 配置环境变量
- 执行测试集合

### 2. 自动化测试
```bash
# 运行所有测试
npm test

# 运行API测试
npm run test:api

# 运行性能测试
npm run test:performance

# 运行集成测试
npm run test:integration
```

### 3. 手动测试工具
- **curl** - 命令行测试
- **HTTPie** - 友好的HTTP客户端
- **Insomnia** - API测试工具

## 🔧 环境配置

### 开发环境
```bash
BASE_URL=http://localhost:3000
API_VERSION=v1
```

### 测试环境
```bash
BASE_URL=https://test-api.yourdomain.com
API_VERSION=v1
```

### 生产环境
```bash
BASE_URL=https://api.yourdomain.com
API_VERSION=v1
```

## 📋 测试流程

### 1. 认证流程测试

#### 用户注册测试
```bash
curl -X POST $BASE_URL/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser001",
    "email": "<EMAIL>",
    "password": "Test123456!",
    "nickname": "测试用户001"
  }'
```

**预期结果**:
- 状态码: 201
- 返回用户信息和Token
- 用户状态为active

#### 用户登录测试
```bash
curl -X POST $BASE_URL/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123456!"
  }'
```

**预期结果**:
- 状态码: 200
- 返回accessToken和refreshToken
- Token有效期正确

#### Token刷新测试
```bash
curl -X POST $BASE_URL/api/auth/refresh-token \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "<refresh_token>"
  }'
```

### 2. 用户模块测试

#### 获取用户信息
```bash
curl -X GET $BASE_URL/api/auth/me \
  -H "Authorization: Bearer <access_token>"
```

#### 更新用户资料
```bash
curl -X PUT $BASE_URL/api/users/profile \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "nickname": "更新后的昵称",
    "bio": "这是我的个人简介"
  }'
```

#### 上传头像
```bash
curl -X POST $BASE_URL/api/users/avatar \
  -H "Authorization: Bearer <access_token>" \
  -F "avatar=@avatar.jpg"
```

### 3. 视频模块测试

#### 获取视频列表
```bash
curl -X GET "$BASE_URL/api/videos/list?page=1&pageSize=10" \
  -H "Content-Type: application/json"
```

#### 上传视频
```bash
curl -X POST $BASE_URL/api/videos/upload \
  -H "Authorization: Bearer <access_token>" \
  -F "video=@test_video.mp4" \
  -F "title=测试视频" \
  -F "description=这是一个测试视频" \
  -F "categoryId=1"
```

#### 获取视频详情
```bash
curl -X GET $BASE_URL/api/videos/1 \
  -H "Content-Type: application/json"
```

### 4. 互动模块测试

#### 发表评论
```bash
curl -X POST $BASE_URL/api/interactions/comments \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "videoId": 1,
    "content": "这是一个测试评论"
  }'
```

#### 点赞视频
```bash
curl -X POST $BASE_URL/api/interactions/likes \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "targetId": 1,
    "targetType": "video"
  }'
```

#### 收藏视频
```bash
curl -X POST $BASE_URL/api/interactions/favorites \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "videoId": 1
  }'
```

### 5. 会员模块测试

#### 获取会员计划
```bash
curl -X GET $BASE_URL/api/members/plans \
  -H "Content-Type: application/json"
```

#### 订阅会员
```bash
curl -X POST $BASE_URL/api/members/subscribe \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": 1,
    "paymentMethod": "alipay"
  }'
```

## 🧪 测试用例

### 正常流程测试

1. **用户注册 → 登录 → 获取信息**
2. **上传视频 → 获取视频 → 更新视频**
3. **发表评论 → 点赞 → 收藏**
4. **订阅会员 → 查看权益**

### 异常情况测试

#### 1. 认证错误
```bash
# 无效Token
curl -X GET $BASE_URL/api/auth/me \
  -H "Authorization: Bearer invalid_token"

# 预期: 401 Unauthorized
```

#### 2. 权限错误
```bash
# 普通用户访问管理员接口
curl -X GET $BASE_URL/api/admin/dashboard/stats \
  -H "Authorization: Bearer <user_token>"

# 预期: 403 Forbidden
```

#### 3. 数据验证错误
```bash
# 无效邮箱格式
curl -X POST $BASE_URL/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "test",
    "email": "invalid-email",
    "password": "123"
  }'

# 预期: 400 Bad Request
```

#### 4. 资源不存在
```bash
# 不存在的视频ID
curl -X GET $BASE_URL/api/videos/999999 \
  -H "Content-Type: application/json"

# 预期: 404 Not Found
```

#### 5. 限流测试
```bash
# 快速连续请求登录接口
for i in {1..10}; do
  curl -X POST $BASE_URL/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"wrong"}'
done

# 预期: 429 Too Many Requests
```

## 📊 性能测试

### 1. 响应时间测试
```bash
# 使用curl测量响应时间
curl -w "@curl-format.txt" -o /dev/null -s $BASE_URL/api/videos/list

# curl-format.txt内容:
#      time_namelookup:  %{time_namelookup}\n
#         time_connect:  %{time_connect}\n
#      time_appconnect:  %{time_appconnect}\n
#     time_pretransfer:  %{time_pretransfer}\n
#        time_redirect:  %{time_redirect}\n
#   time_starttransfer:  %{time_starttransfer}\n
#                      ----------\n
#           time_total:  %{time_total}\n
```

### 2. 并发测试
```bash
# 使用Apache Bench
ab -n 1000 -c 10 $BASE_URL/api/videos/list

# 使用wrk
wrk -t12 -c400 -d30s $BASE_URL/api/videos/list
```

### 3. 负载测试
```bash
# 运行性能测试套件
npm run test:performance
```

## ✅ 测试检查清单

### 功能测试
- [ ] 用户注册/登录/登出
- [ ] 用户资料管理
- [ ] 视频上传/播放/管理
- [ ] 评论/点赞/收藏功能
- [ ] 会员订阅/管理
- [ ] 支付流程
- [ ] 管理员功能

### 安全测试
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] CSRF攻击防护
- [ ] 权限控制
- [ ] 数据验证
- [ ] 限流机制

### 性能测试
- [ ] 响应时间 < 2秒
- [ ] 并发处理能力
- [ ] 内存使用合理
- [ ] 数据库查询优化
- [ ] 缓存机制有效

### 兼容性测试
- [ ] 不同浏览器
- [ ] 移动设备
- [ ] 不同网络环境
- [ ] API版本兼容

## 🐛 常见问题排查

### 1. 连接问题
```bash
# 检查服务状态
curl -I $BASE_URL/health

# 检查网络连通性
ping api.yourdomain.com
```

### 2. 认证问题
```bash
# 验证Token格式
echo "<token>" | base64 -d

# 检查Token过期时间
curl -X GET $BASE_URL/api/auth/me \
  -H "Authorization: Bearer <token>" -v
```

### 3. 数据问题
```bash
# 检查请求格式
curl -X POST $BASE_URL/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123"}' -v
```

## 📈 测试报告

### 自动生成测试报告
```bash
# 生成测试报告
npm run test:report

# 查看覆盖率报告
npm run test:coverage
```

### 手动测试记录
建议使用测试管理工具记录测试结果：
- TestRail
- Zephyr
- qTest
- 或简单的Excel表格

---

> 更多测试相关信息请查看 [API文档](./API.md) 和 [部署指南](./DEPLOYMENT.md)
