/**
 * 根据页码和每页数量计算LIMIT和OFFSET
 * @param {number|string} page - 当前页码 (从1开始)
 * @param {number|string} size - 每页数量
 * @returns {{limit: number, offset: number}}
 */
const getPagination = (page = 1, size = 10) => {
  // 确保 page 和 size 是有效的正整数
  let pageInt = Number(page);
  let sizeInt = Number(size);

  if (isNaN(pageInt) || pageInt <= 0) {
    pageInt = 1;
  }
  if (isNaN(sizeInt) || sizeInt <= 0) {
    sizeInt = 10;
  }

  const limit = sizeInt;
  const offset = (pageInt - 1) * limit;

  return { limit, offset };
};

/**
 * 格式化分页数据
 * @param {object} params
 * @param {Array} params.data - 当前页的数据
 * @param {number} params.totalItems - 总记录数
 * @param {number|string} params.page - 当前页码
 * @param {number|string} params.limit - 每页数量
 * @returns {{data: Array, totalItems: number, totalPages: number, currentPage: number}}
 */
const getPagingData = ({ data, totalItems, page, limit }) => {
  const currentPage = Number(page) || 1;
  const limitInt = Number(limit) || 10;
  const totalPages = Math.ceil(totalItems / limitInt);

  return {
    data,
    totalItems,
    totalPages,
    currentPage,
  };
};

module.exports = {
  getPagination,
  getPagingData,
}; 