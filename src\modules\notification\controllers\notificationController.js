const { asyncHandler } = require('../../../middleware/errorHandler');
const notificationService = require('../../../services/notificationService');
const Notification = require('../../../database/models/Notification');

class NotificationController {
  // 获取通知列表
  getNotifications = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { page = 1, pageSize = 20, unreadOnly = false } = req.query;

    const result = await notificationService.getUserNotifications(userId, {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      unreadOnly: unreadOnly === 'true',
    });

    res.json({
      success: true,
      data: {
        notifications: result.rows, // 修正：从 result.rows 获取通知列表
        pagination: {
          total: result.count,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(result.count / pageSize),
        }
      },
    });
  });

  // 获取通知状态（如未读数量）
  getNotificationStatus = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const unreadCount = await Notification.count({ user_id: userId, is_read: false });
    res.json({ success: true, data: { unreadCount } });
  });

  // 标记为已读
  markAsRead = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { id } = req.params;
    const success = await notificationService.markAsRead(parseInt(id), userId);
    if (success) {
      res.json({ success: true, message: '通知已标记为已读' });
    } else {
      res.status(404).json({ success: false, message: '通知不存在或无权限' });
    }
  });

  // 全部标记为已读
  markAllAsRead = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const count = await notificationService.markAllAsRead(userId);
    res.json({ success: true, message: `已将 ${count} 条通知标记为已读` });
  });
}

module.exports = new NotificationController(); 