<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>媒体平台API文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .nav {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav h3 {
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .nav ul {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .nav a {
            color: #666;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 5px;
            transition: all 0.3s;
            display: block;
        }
        
        .nav a:hover {
            background: #667eea;
            color: white;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-header {
            background: #667eea;
            color: white;
            padding: 20px;
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .section-content {
            padding: 30px;
        }
        
        .endpoint {
            margin-bottom: 40px;
            border-left: 4px solid #667eea;
            padding-left: 20px;
        }
        
        .endpoint h4 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
            margin-right: 10px;
        }
        
        .method.get { background: #28a745; color: white; }
        .method.post { background: #007bff; color: white; }
        .method.put { background: #ffc107; color: black; }
        .method.delete { background: #dc3545; color: white; }
        
        .url {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .auth-required {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 0.9em;
        }
        
        .params, .response {
            margin-top: 20px;
        }
        
        .params h5, .response h5 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
            font-size: 0.9em;
        }
        
        .status-codes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .status-code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .status-code h6 {
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .info-box h5 {
            color: #0c5460;
            margin-bottom: 10px;
        }
        
        .footer {
            text-align: center;
            padding: 40px 0;
            color: #666;
            border-top: 1px solid #e9ecef;
            margin-top: 50px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .nav ul {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 媒体平台API文档</h1>
            <p>完整的RESTful API接口文档 - 共104个接口（支持视频和音频）</p>
        </div>
        
        <div class="nav">
            <h3>📋 快速导航</h3>
            <ul>
                <li><a href="#auth">🔐 认证模块</a></li>
                <li><a href="#users">👤 用户模块</a></li>
                <li><a href="#videos">🎬 媒体模块</a></li>
                <li><a href="#interactions">💬 互动模块</a></li>
                <li><a href="#members">💎 会员模块</a></li>
                <li><a href="#payment">💳 支付模块</a></li>
                <li><a href="#admin">🔧 管理模块</a></li>
                <li><a href="#errors">📊 错误代码</a></li>
            </ul>
        </div>
        
        <div class="info-box">
            <h5>📋 基础信息</h5>
            <ul>
                <li><strong>Base URL:</strong> <code>https://api.yourdomain.com</code></li>
                <li><strong>API版本:</strong> v1</li>
                <li><strong>认证方式:</strong> JWT Bearer Token</li>
                <li><strong>数据格式:</strong> JSON</li>
                <li><strong>字符编码:</strong> UTF-8</li>
                <li><strong>总接口数:</strong> 104个</li>
                <li><strong>模块数:</strong> 7个核心模块</li>
            </ul>
        </div>
        
        <div class="section" id="auth">
            <div class="section-header">🔐 认证模块 (11个接口)</div>
            <div class="section-content">
                <div class="endpoint">
                    <h4>用户注册</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/register</span>
                    </div>
                    <div class="params">
                        <h5>请求参数:</h5>
                        <pre>{
  "username": "string (3-20字符)",
  "email": "string (有效邮箱)",
  "password": "string (8-50字符)",
  "nickname": "string (可选，2-50字符)"
}</pre>
                    </div>
                    <div class="response">
                        <h5>响应示例:</h5>
                        <pre>{
  "success": true,
  "message": "注册成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "nickname": "测试用户",
      "role": "user",
      "status": "active"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIs...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
      "expiresIn": 3600
    }
  }
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>用户登录</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/login</span>
                    </div>
                    <div class="params">
                        <h5>请求参数:</h5>
                        <pre>{
  "email": "string",
  "password": "string"
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>用户登出</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/logout</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>刷新Token</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/refresh-token</span>
                    </div>
                    <div class="params">
                        <h5>请求参数:</h5>
                        <pre>{
  "refreshToken": "string"
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取当前用户信息</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/auth/me</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>修改密码</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/change-password</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>忘记密码</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/forgot-password</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>重置密码</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/reset-password</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>邮箱验证</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/verify-email</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>重发验证邮件</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/auth/resend-verification</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>认证测试</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/auth/test</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section" id="users">
            <div class="section-header">👤 用户模块 (12个接口)</div>
            <div class="section-content">
                <div class="endpoint">
                    <h4>获取个人资料</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/users/profile</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>更新个人资料</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/users/profile</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                    <div class="params">
                        <h5>请求参数:</h5>
                        <pre>{
  "nickname": "string (可选)",
  "bio": "string (可选)",
  "location": "string (可选)"
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>上传头像</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/users/avatar</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>删除头像</h4>
                    <div>
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/users/avatar</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>获取用户列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/users/list</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取用户详情</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/users/:id</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>封禁用户</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/users/:id/ban</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>解封用户</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/users/:id/unban</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>修改用户角色</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/users/:id/role</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>删除账户</h4>
                    <div>
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/users/account</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>用户统计</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/users/stats</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>用户测试</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/users/test</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>
            </div>
        </div>

        <div class="section" id="videos">
            <div class="section-header">🎬 媒体模块 (25个接口，支持视频和音频)</div>
            <div class="section-content">
                <div class="endpoint">
                    <h4>获取媒体列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/list</span>
                    </div>
                    <div class="params">
                        <h5>查询参数:</h5>
                        <ul>
                            <li><code>page</code> - 页码 (默认: 1)</li>
                            <li><code>pageSize</code> - 每页数量 (默认: 20, 最大: 50)</li>
                            <li><code>categoryId</code> - 分类ID (可选)</li>
                            <li><code>mediaType</code> - 媒体类型 (video|audio, 可选)</li>
                            <li><code>keyword</code> - 搜索关键词 (可选)</li>
                            <li><code>sortBy</code> - 排序字段 (created_at|view_count|like_count)</li>
                        </ul>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>上传媒体文件（视频或音频）</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/videos/upload</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                    <div class="params">
                        <h5>请求参数 (multipart/form-data):</h5>
                        <ul>
                            <li><code>video</code> 或 <code>audio</code> - 媒体文件 (必需)</li>
                            <li><strong>支持视频格式:</strong> mp4, avi, mov, wmv, flv, webm, mkv</li>
                            <li><strong>支持音频格式:</strong> mp3, wav, flac, aac, ogg, m4a, wma</li>
                            <li><code>title</code> - 媒体标题 (必需)</li>
                            <li><code>description</code> - 媒体描述 (可选)</li>
                            <li><code>categoryId</code> - 分类ID (可选)</li>
                            <li><code>tags</code> - 标签JSON数组 (可选)</li>
                        </ul>
                        <p><strong>注意:</strong> 系统会自动检测文件类型并进行相应处理。</p>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>专用音频上传接口</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/videos/upload-audio</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                    <div class="params">
                        <h5>请求参数 (multipart/form-data):</h5>
                        <ul>
                            <li><code>audio</code> - 音频文件 (必需)</li>
                            <li><code>title</code> - 音频标题 (必需)</li>
                            <li><code>description</code> - 音频描述 (可选)</li>
                            <li><code>categoryId</code> - 分类ID (可选)</li>
                            <li><code>tags</code> - 标签JSON数组 (可选)</li>
                        </ul>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取音频列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/audio-list</span>
                    </div>
                    <div class="params">
                        <h5>说明:</h5>
                        <p>与媒体列表接口相同，但自动筛选音频类型</p>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取纯视频列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/video-list</span>
                    </div>
                    <div class="params">
                        <h5>说明:</h5>
                        <p>与媒体列表接口相同，但自动筛选视频类型</p>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取媒体详情</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/:id</span>
                    </div>
                    <div class="response">
                        <h5>响应示例:</h5>
                        <pre>{
  "success": true,
  "data": {
    "media": {
      "id": 1,
      "media_type": "video",
      "title": "示例视频",
      "description": "详细描述...",
      "thumbnail": "/uploads/thumbnails/thumb_1.jpg",
      "file_path": "/uploads/videos/video_1.mp4",
      "duration": 120,
      "resolution": "1920x1080",
      "format": "mp4",
      "view_count": 1000,
      "like_count": 50,
      "comment_count": 10,
      // 音频文件特有字段（仅当media_type为audio时）
      "bitrate": 320,
      "sample_rate": 44100,
      "channels": 2
    }
  }
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>更新媒体信息</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/videos/:id</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>删除媒体</h4>
                    <div>
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/videos/:id</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>搜索媒体</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/search</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>热门媒体</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/popular</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>推荐媒体</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/recommended</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>获取用户媒体</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/user/:id</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取处理状态</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/:id/processing-status</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>重新处理媒体</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/videos/:id/reprocess</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>媒体统计</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/:id/stats</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>媒体测试</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/test</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                    <div class="response">
                        <h5>响应示例:</h5>
                        <pre>{
  "success": true,
  "message": "媒体模块测试接口（支持视频和音频）",
  "module": "media",
  "supportedTypes": ["video", "audio"]
}</pre>
                    </div>
                </div>

                <!-- 分类管理接口 -->
                <div class="endpoint">
                    <h4>获取分类树</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/categories/tree</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取分类列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/categories/list</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>热门分类</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/categories/popular</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>搜索分类</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/categories/search</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取分类详情</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/categories/:id</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取分类下的媒体</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/videos/categories/:id/videos</span>
                    </div>
                    <div class="params">
                        <h5>查询参数:</h5>
                        <ul>
                            <li><code>mediaType</code> - 媒体类型筛选 (video|audio, 可选)</li>
                        </ul>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>创建分类</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/videos/categories</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>更新分类</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/videos/categories/:id</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>删除分类</h4>
                    <div>
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/videos/categories/:id</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>
            </div>
        </div>

        <div class="section" id="interactions">
            <div class="section-header">💬 互动模块 (15个接口)</div>
            <div class="section-content">
                <div class="endpoint">
                    <h4>发表评论</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/interactions/comments</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>编辑评论</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/interactions/comments/:id</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>删除评论</h4>
                    <div>
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/interactions/comments/:id</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>获取视频评论</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/interactions/videos/:id/comments</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取评论回复</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/interactions/comments/:commentId/replies</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>点赞/取消点赞</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/interactions/likes</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>收藏/取消收藏</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/interactions/favorites</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>获取用户收藏</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/interactions/users/:id/favorites</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>批量检查互动状态</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/interactions/batch-check</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>搜索评论</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/interactions/comments/search</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>举报评论</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/interactions/comments/:id/report</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>获取用户互动统计</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/interactions/users/:id/stats</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取热门评论</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/interactions/comments/popular</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>互动测试</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/interactions/test</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>管理评论</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/interactions/comments/:id/moderate</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>
            </div>
        </div>

        <div class="section" id="members">
            <div class="section-header">💎 会员模块 (16个接口)</div>
            <div class="section-content">
                <div class="endpoint">
                    <h4>获取会员计划</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/members/plans</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取计划详情</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/members/plans/:id</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取我的会员信息</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/members/my-membership</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>订阅会员</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/members/subscribe</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>取消订阅</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/members/cancel</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>升级会员</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/members/upgrade</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>会员权益检查</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/members/check-privileges</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>会员历史记录</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/members/history</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>创建会员计划</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/members/plans</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>更新会员计划</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/members/plans/:id</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>删除会员计划</h4>
                    <div>
                        <span class="method delete">DELETE</span>
                        <span class="url">/api/members/plans/:id</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>会员统计</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/members/stats</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>会员列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/members/list</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>强制取消会员</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/members/:id/force-cancel</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>会员测试</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/members/test</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>会员权益验证</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/members/validate-access</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>
            </div>
        </div>

        <div class="section" id="payment">
            <div class="section-header">💳 支付模块 (8个接口)</div>
            <div class="section-content">
                <div class="endpoint">
                    <h4>创建支付订单</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/payment/create-order</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                    <div class="params">
                        <h5>请求参数:</h5>
                        <pre>{
  "type": "membership|video|recharge",
  "targetId": 1,
  "amount": 9.99,
  "paymentMethod": "epay|alipay|wechat|epay_alipay|epay_wechat|epay_qq",
  "description": "订单描述 (可选)",
  "tradeType": "NATIVE|JSAPI|APP|H5 (可选，微信支付)"
}</pre>
                        <h5>响应示例:</h5>
                        <pre>{
  "success": true,
  "message": "支付订单创建成功",
  "data": {
    "order": {
      "id": 123,
      "orderNo": "ORDER_1704614400000_ABC123",
      "amount": 9.99,
      "finalAmount": 9.99,
      "paymentStatus": "pending",
      "expiresAt": "2025-01-07T15:30:00Z",
      "paymentUrl": "https://pay.example.com/...",
      "qrCode": "https://qr.alipay.com/...",
      "codeUrl": "weixin://wxpay/bizpayurl?pr=..."
    }
  }
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>查询订单状态</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/payment/orders/:orderNo</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                    <div class="params">
                        <h5>响应示例:</h5>
                        <pre>{
  "success": true,
  "data": {
    "order": {
      "id": 123,
      "orderNo": "ORDER_1704614400000_ABC123",
      "type": "membership",
      "amount": 9.99,
      "finalAmount": 9.99,
      "paymentStatus": "paid|pending|failed|expired|refunded",
      "paymentMethod": "epay",
      "paymentTime": "2025-01-07T15:30:00Z",
      "expiresAt": "2025-01-07T16:00:00Z",
      "createdAt": "2025-01-07T15:00:00Z"
    }
  }
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>获取我的订单</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/payment/my-orders</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                </div>

                <div class="endpoint">
                    <h4>申请退款</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/payment/refund</span>
                    </div>
                    <div class="auth-required">🔐 需要认证</div>
                    <div class="params">
                        <h5>请求参数:</h5>
                        <pre>{
  "orderNo": "ORDER_1704614400000_ABC123",
  "reason": "用户申请退款",
  "amount": 9.99
}</pre>
                        <h5>响应示例:</h5>
                        <pre>{
  "success": true,
  "message": "退款申请成功",
  "data": {
    "refundId": "REFUND_1704614400000_XYZ789",
    "message": "退款将在3-5个工作日内到账"
  }
}</pre>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>支付回调</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/payment/webhook/:provider</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>支付方式列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/payment/methods</span>
                    </div>
                </div>

                <div class="endpoint">
                    <h4>支付统计</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/payment/stats</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>支付测试</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/payment/test</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>
            </div>
        </div>

        <div class="section" id="admin">
            <div class="section-header">🔧 管理模块 (17个接口)</div>
            <div class="section-content">
                <div class="endpoint">
                    <h4>仪表板统计</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/dashboard/stats</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>用户管理列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/users</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>批量用户操作</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/admin/users/batch</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>视频管理列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/videos</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>评论管理列表</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/comments</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>获取系统配置</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/system/config</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>更新系统配置</h4>
                    <div>
                        <span class="method put">PUT</span>
                        <span class="url">/api/admin/system/config</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>清理系统缓存</h4>
                    <div>
                        <span class="method post">POST</span>
                        <span class="url">/api/admin/system/cache/clear</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>访问统计概览</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/statistics/access/overview</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>详细访问统计</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/statistics/access/detailed</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>收费统计概览</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/statistics/revenue/overview</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>详细收费统计</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/statistics/revenue/detailed</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>用户行为分析</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/statistics/users/behavior</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>热门内容分析</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/statistics/content/popular</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>系统日志</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/system/logs</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>系统健康检查</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/system/health</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>

                <div class="endpoint">
                    <h4>管理员测试</h4>
                    <div>
                        <span class="method get">GET</span>
                        <span class="url">/api/admin/test</span>
                    </div>
                    <div class="auth-required">👑 需要管理员权限</div>
                </div>
            </div>
        </div>

        <div class="section" id="errors">
            <div class="section-header">📊 错误代码说明</div>
            <div class="section-content">
                <div class="status-codes">
                    <div class="status-code">
                        <h6>VALIDATION_ERROR (400)</h6>
                        <p>数据验证失败</p>
                    </div>
                    <div class="status-code">
                        <h6>UNAUTHORIZED (401)</h6>
                        <p>未认证</p>
                    </div>
                    <div class="status-code">
                        <h6>ACCESS_DENIED (403)</h6>
                        <p>权限不足</p>
                    </div>
                    <div class="status-code">
                        <h6>NOT_FOUND (404)</h6>
                        <p>资源不存在</p>
                    </div>
                    <div class="status-code">
                        <h6>RATE_LIMIT_EXCEEDED (429)</h6>
                        <p>请求频率超限</p>
                    </div>
                    <div class="status-code">
                        <h6>INTERNAL_ERROR (500)</h6>
                        <p>服务器内部错误</p>
                    </div>
                    <div class="status-code">
                        <h6>PAYMENT_CREATE_FAILED (500)</h6>
                        <p>创建支付订单失败</p>
                    </div>
                    <div class="status-code">
                        <h6>ORDER_NOT_FOUND (404)</h6>
                        <p>订单不存在</p>
                    </div>
                    <div class="status-code">
                        <h6>ORDER_NOT_PAID (400)</h6>
                        <p>订单未支付</p>
                    </div>
                    <div class="status-code">
                        <h6>REFUND_FAILED (500)</h6>
                        <p>退款失败</p>
                    </div>
                    <div class="status-code">
                        <h6>TWO_FACTOR_NOT_ENABLED (400)</h6>
                        <p>未启用双因子认证</p>
                    </div>
                    <div class="status-code">
                        <h6>TWO_FACTOR_CODE_INVALID (400)</h6>
                        <p>双因子认证码无效</p>
                    </div>
                    <div class="status-code">
                        <h6>SERVICE_UNAVAILABLE (503)</h6>
                        <p>服务暂时不可用</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-box">
            <h5>🎵 音频功能支持</h5>
            <p><strong>新增功能：</strong>API框架现已全面支持音频文件处理！</p>
            <ul>
                <li><strong>支持格式：</strong>MP3, WAV, FLAC, AAC, OGG, M4A, WMA</li>
                <li><strong>自动检测：</strong>系统自动识别文件类型并进行相应处理</li>
                <li><strong>专业处理：</strong>音频转码、标准化、元数据提取、波形图生成</li>
                <li><strong>统一接口：</strong>与视频功能完全集成，使用相同的API接口</li>
                <li><strong>向后兼容：</strong>所有现有视频功能保持完全兼容</li>
            </ul>
            <p><strong>新增接口：</strong></p>
            <ul>
                <li><code>POST /api/videos/upload-audio</code> - 专用音频上传</li>
                <li><code>GET /api/videos/audio-list</code> - 音频列表</li>
                <li><code>GET /api/videos/video-list</code> - 纯视频列表</li>
            </ul>
        </div>

        <div class="info-box">
            <h5>📊 接口统计摘要</h5>
            <div class="status-codes">
                <div class="status-code">
                    <h6>🔐 认证模块</h6>
                    <p>11个接口</p>
                </div>
                <div class="status-code">
                    <h6>👤 用户模块</h6>
                    <p>12个接口</p>
                </div>
                <div class="status-code">
                    <h6>🎬 媒体模块</h6>
                    <p>25个接口</p>
                </div>
                <div class="status-code">
                    <h6>💬 互动模块</h6>
                    <p>15个接口</p>
                </div>
                <div class="status-code">
                    <h6>💎 会员模块</h6>
                    <p>16个接口</p>
                </div>
                <div class="status-code">
                    <h6>💳 支付模块</h6>
                    <p>8个接口</p>
                </div>
                <div class="status-code">
                    <h6>🔧 管理模块</h6>
                    <p>17个接口</p>
                </div>
                <div class="status-code">
                    <h6>📊 总计</h6>
                    <p><strong>104个接口</strong></p>
                </div>
            </div>
            <p style="text-align: center; margin-top: 20px;">
                <strong>完整的企业级媒体平台API框架</strong><br>
                支持用户管理、视频/音频处理、互动功能、会员系统、支付集成和管理后台
            </p>
        </div>

        <div class="footer">
            <p>&copy; 2025 媒体平台API. 所有权利保留.</p>
            <p>技术支持: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>文档版本: v2.0 | 最后更新: 2025-01-07 | 新增音频支持</p>
        </div>
    </div>
    
    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // 代码高亮
        document.querySelectorAll('pre').forEach(block => {
            block.style.position = 'relative';
            const copyBtn = document.createElement('button');
            copyBtn.textContent = '复制';
            copyBtn.style.position = 'absolute';
            copyBtn.style.top = '10px';
            copyBtn.style.right = '10px';
            copyBtn.style.padding = '5px 10px';
            copyBtn.style.background = '#667eea';
            copyBtn.style.color = 'white';
            copyBtn.style.border = 'none';
            copyBtn.style.borderRadius = '4px';
            copyBtn.style.cursor = 'pointer';
            copyBtn.style.fontSize = '12px';
            
            copyBtn.addEventListener('click', () => {
                navigator.clipboard.writeText(block.textContent);
                copyBtn.textContent = '已复制!';
                setTimeout(() => {
                    copyBtn.textContent = '复制';
                }, 2000);
            });
            
            block.appendChild(copyBtn);
        });
    </script>
</body>
</html>
