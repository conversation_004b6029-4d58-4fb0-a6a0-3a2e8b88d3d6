import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Cloud, 
  CloudOff, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  Wifi,
  WifiOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PlaylistSyncStatusProps {
  isAuthenticated: boolean;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  syncError: string | null;
  serverPlaylistCount: number;
  localPlaylistCount: number;
  onManualSync: () => void;
  className?: string;
}

const PlaylistSyncStatus: React.FC<PlaylistSyncStatusProps> = ({
  isAuthenticated,
  isSyncing,
  lastSyncTime,
  syncError,
  serverPlaylistCount,
  localPlaylistCount,
  onManualSync,
  className,
}) => {
  const formatLastSyncTime = (time: Date | null) => {
    if (!time) return '从未同步';
    
    const now = new Date();
    const diff = now.getTime() - time.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    
    if (minutes < 1) return '刚刚同步';
    if (minutes < 60) return `${minutes}分钟前`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}小时前`;
    
    const days = Math.floor(hours / 24);
    return `${days}天前`;
  };

  const getSyncStatusIcon = () => {
    if (!isAuthenticated) {
      return <WifiOff className="h-4 w-4 text-gray-400" />;
    }
    
    if (isSyncing) {
      return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
    }
    
    if (syncError) {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    
    if (lastSyncTime) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    
    return <CloudOff className="h-4 w-4 text-gray-400" />;
  };

  const getSyncStatusText = () => {
    if (!isAuthenticated) {
      return '未登录';
    }
    
    if (isSyncing) {
      return '同步中...';
    }
    
    if (syncError) {
      return '同步失败';
    }
    
    if (lastSyncTime) {
      return '已同步';
    }
    
    return '未同步';
  };

  const getSyncStatusColor = () => {
    if (!isAuthenticated) return 'secondary';
    if (isSyncing) return 'default';
    if (syncError) return 'destructive';
    if (lastSyncTime) return 'default';
    return 'secondary';
  };

  return (
    <div className={cn('flex items-center justify-between p-3 bg-muted/50 rounded-lg', className)}>
      <div className="flex items-center space-x-3">
        {/* 同步状态图标 */}
        <div className="flex items-center space-x-2">
          {getSyncStatusIcon()}
          <Badge variant={getSyncStatusColor()}>
            {getSyncStatusText()}
          </Badge>
        </div>

        {/* 同步信息 */}
        <div className="text-sm text-muted-foreground">
          {isAuthenticated ? (
            <div className="flex items-center space-x-4">
              <span className="flex items-center space-x-1">
                <Cloud className="h-3 w-3" />
                <span>服务器: {serverPlaylistCount}</span>
              </span>
              <span className="flex items-center space-x-1">
                <Wifi className="h-3 w-3" />
                <span>本地: {localPlaylistCount}</span>
              </span>
              {lastSyncTime && (
                <span>
                  最后同步: {formatLastSyncTime(lastSyncTime)}
                </span>
              )}
            </div>
          ) : (
            <span>登录后可同步播放列表到云端</span>
          )}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center space-x-2">
        {isAuthenticated && (
          <Button
            variant="outline"
            size="sm"
            onClick={onManualSync}
            disabled={isSyncing}
            className="flex items-center space-x-1"
          >
            <RefreshCw className={cn('h-3 w-3', isSyncing && 'animate-spin')} />
            <span>{isSyncing ? '同步中' : '手动同步'}</span>
          </Button>
        )}
      </div>

      {/* 错误提示 */}
      {syncError && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
          <div className="flex items-center space-x-1">
            <AlertCircle className="h-3 w-3" />
            <span>同步错误: {syncError}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaylistSyncStatus;
