import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent } from '../components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { FollowButton } from '../components/common/FollowButton';
import { UserStats } from '../components/user/UserStats';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Calendar, MapPin, Link as LinkIcon, Shield } from 'lucide-react';
import { toast } from 'sonner';
import api from '../services/api';

interface UserDetail {
  id: number;
  username: string;
  nickname: string;
  avatar: string;
  bio: string;
  role: string;
  created_at: string;
  status: string;
  location?: string;
  website?: string;
  follower_count: number;
  following_count: number;
  video_count: number;
}

interface Video {
  id: number;
  title: string;
  thumbnail_url: string;
  duration: number;
  view_count: number;
  like_count: number;
  created_at: string;
}

const UserDetailPage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const [user, setUser] = useState<UserDetail | null>(null);
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [videosLoading, setVideosLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserDetail = async () => {
      if (!userId) return;
      
      try {
        setLoading(true);
        setError(null);
        
        const response = await api.get(`/user/${userId}`);
        setUser(response.data.data);
      } catch (error: any) {
        console.error('获取用户详情失败:', error);
        setError(error.response?.data?.message || '获取用户信息失败');
        toast.error('获取用户信息失败');
      } finally {
        setLoading(false);
      }
    };

    const fetchUserVideos = async () => {
      if (!userId) return;
      
      try {
        setVideosLoading(true);
        
        const response = await api.get(`/videos/user/${userId}`, {
          params: { page: 1, limit: 12 }
        });
        setVideos(response.data.data?.data || []);
      } catch (error: any) {
        console.error('获取用户视频失败:', error);
      } finally {
        setVideosLoading(false);
      }
    };

    fetchUserDetail();
    fetchUserVideos();
  }, [userId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long'
    });
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      admin: { label: '管理员', variant: 'destructive' as const },
      vip: { label: 'VIP', variant: 'default' as const },
      member: { label: '会员', variant: 'secondary' as const },
      user: { label: '用户', variant: 'outline' as const }
    };
    
    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.user;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse">
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="flex items-start space-x-6">
                <div className="w-24 h-24 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-6 bg-gray-200 rounded w-48 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-32 mb-4"></div>
                  <div className="h-20 bg-gray-200 rounded w-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl text-gray-300 mb-4">👤</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">用户不存在</h2>
          <p className="text-gray-600 mb-6">{error || '找不到指定的用户信息'}</p>
          <Button onClick={() => window.history.back()}>返回上页</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* 用户信息卡片 */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
              <Avatar className="w-24 h-24">
                <AvatarImage src={user.avatar} alt={user.nickname || user.username} />
                <AvatarFallback className="text-2xl">
                  {(user.nickname || user.username)?.[0]?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900 mb-1">
                      {user.nickname || user.username}
                    </h1>
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-gray-600">@{user.username}</span>
                      {getRoleBadge(user.role)}
                      {user.role === 'admin' && (
                        <Shield className="w-4 h-4 text-blue-600" />
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <FollowButton 
                      userId={user.id}
                      onFollowChange={(isFollowing) => {
                        // 更新本地统计数据
                        setUser(prev => prev ? {
                          ...prev,
                          follower_count: prev.follower_count + (isFollowing ? 1 : -1)
                        } : null);
                      }}
                    />
                  </div>
                </div>
                
                {user.bio && (
                  <p className="text-gray-700 mb-4 leading-relaxed">{user.bio}</p>
                )}
                
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                  {user.location && (
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      {user.location}
                    </div>
                  )}
                  {user.website && (
                    <div className="flex items-center">
                      <LinkIcon className="w-4 h-4 mr-1" />
                      <a 
                        href={user.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        个人网站
                      </a>
                    </div>
                  )}
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {formatDate(user.created_at)} 加入
                  </div>
                </div>
              </div>
            </div>
            
            {/* 用户统计 */}
            <div className="mt-6 pt-6 border-t">
              <UserStats 
                userId={user.id} 
                showDetailedStats={true}
                className="w-full"
              />
            </div>
          </CardContent>
        </Card>

        {/* 内容标签页 */}
        <Tabs defaultValue="videos" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="videos">作品 ({user.video_count})</TabsTrigger>
            <TabsTrigger value="followers">粉丝 ({user.follower_count})</TabsTrigger>
            <TabsTrigger value="following">关注 ({user.following_count})</TabsTrigger>
          </TabsList>
          
          <TabsContent value="videos" className="mt-6">
            {videosLoading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="bg-gray-200 aspect-video rounded-lg mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-full mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : videos.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {videos.map(video => (
                  <Card key={video.id} className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                    <div className="relative">
                      <img 
                        src={video.thumbnail_url} 
                        alt={video.title}
                        className="w-full aspect-video object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder-video.png';
                        }}
                      />
                      <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-1 rounded">
                        {formatDuration(video.duration)}
                      </div>
                    </div>
                    <CardContent className="p-3">
                      <h3 className="font-medium text-sm line-clamp-2 mb-1">
                        {video.title}
                      </h3>
                      <div className="flex items-center justify-between text-xs text-gray-600">
                        <span>{video.view_count} 次播放</span>
                        <span>{video.like_count} 点赞</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl text-gray-300 mb-4">📹</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">还没有发布作品</h3>
                <p className="text-gray-600">该用户还没有上传任何视频</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="followers" className="mt-6">
            <div className="bg-white rounded-lg border p-6 text-center">
              <p className="text-gray-600 mb-4">关注/粉丝列表功能即将开放</p>
              <Button 
                variant="outline"
                onClick={() => window.location.href = `/users/${userId}/followers`}
              >
                查看完整粉丝列表
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="following" className="mt-6">
            <div className="bg-white rounded-lg border p-6 text-center">
              <p className="text-gray-600 mb-4">关注/粉丝列表功能即将开放</p>
              <Button 
                variant="outline"
                onClick={() => window.location.href = `/users/${userId}/following`}
              >
                查看完整关注列表
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default UserDetailPage;