import api from './api';
import { PlaylistItem, Playlist, PlaylistStats, PlayHistory, PlayStats } from '@/types/playlist';

export interface CreatePlaylistRequest {
  name: string;
  description?: string;
  isPublic?: boolean;
  playMode?: 'sequence' | 'loop' | 'random';
}

export interface UpdatePlaylistRequest {
  name?: string;
  description?: string;
  isPublic?: boolean;
  playMode?: 'sequence' | 'loop' | 'random';
}

export interface AddVideoRequest {
  videoId: number;
  position?: number;
}

export interface BatchAddVideosRequest {
  videoIds: number[];
}

export interface ReorderItemsRequest {
  items: Array<{
    videoId: number;
    position: number;
  }>;
}

export interface RecordPlayHistoryRequest {
  videoId: number;
  watchDuration: number;
  videoDuration: number;
  completed?: boolean;
}

export interface PlaylistResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}

export interface PlaylistListResponse {
  playlists: Playlist[];
  total: number;
}

export interface PlaylistDetailResponse {
  playlist: Playlist;
  permissions: {
    canView: boolean;
    canEdit: boolean;
    canDelete: boolean;
    isOwner: boolean;
    isPublic: boolean;
  };
}

export interface BatchAddResult {
  results: Array<{
    videoId: number;
    success: boolean;
    error?: string;
  }>;
  summary: {
    total: number;
    success: number;
    failure: number;
  };
}

export interface PlayHistoryResponse {
  history: PlayHistory[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
    hasMore: boolean;
  };
}

export interface RecentVideosResponse {
  videos: Array<{
    videoId: number;
    title: string;
    thumbnail: string;
    duration: number;
    durationFormatted: string;
    mediaType: string;
    playedAt: string;
    playedAtFormatted: string;
  }>;
  total: number;
}

export interface WatchProgressResponse {
  videoId: number;
  hasProgress: boolean;
  watchDuration: number;
  videoDuration: number;
  completionRate: number;
  completed: boolean;
  lastPlayedAt?: string;
  canResume: boolean;
}

class PlaylistApiService {
  // 播放列表管理

  /**
   * 获取用户的所有播放列表
   */
  async getUserPlaylists(includeItems: boolean = false): Promise<PlaylistListResponse> {
    const response = await api.get<PlaylistResponse<PlaylistListResponse>>('/playlists', {
      params: { includeItems }
    });
    return response.data.data;
  }

  /**
   * 获取播放列表详情
   */
  async getPlaylistById(playlistId: number): Promise<PlaylistDetailResponse> {
    const response = await api.get<PlaylistResponse<PlaylistDetailResponse>>(`/playlists/${playlistId}`);
    return response.data.data;
  }

  /**
   * 创建播放列表
   */
  async createPlaylist(data: CreatePlaylistRequest): Promise<Playlist> {
    const response = await api.post<PlaylistResponse<{ playlist: Playlist }>>('/playlists', data);
    return response.data.data.playlist;
  }

  /**
   * 更新播放列表
   */
  async updatePlaylist(playlistId: number, data: UpdatePlaylistRequest): Promise<Playlist> {
    const response = await api.put<PlaylistResponse<{ playlist: Playlist }>>(`/playlists/${playlistId}`, data);
    return response.data.data.playlist;
  }

  /**
   * 删除播放列表
   */
  async deletePlaylist(playlistId: number): Promise<void> {
    await api.delete(`/playlists/${playlistId}`);
  }

  /**
   * 获取播放列表统计信息
   */
  async getPlaylistStats(): Promise<PlaylistStats> {
    const response = await api.get<PlaylistResponse<{ stats: PlaylistStats }>>('/playlists/stats');
    return response.data.data.stats;
  }

  // 播放列表项目管理

  /**
   * 添加视频到播放列表
   */
  async addVideoToPlaylist(playlistId: number, data: AddVideoRequest): Promise<void> {
    await api.post(`/playlists/${playlistId}/items`, data);
  }

  /**
   * 从播放列表移除视频
   */
  async removeVideoFromPlaylist(playlistId: number, videoId: number): Promise<void> {
    await api.delete(`/playlists/${playlistId}/items/${videoId}`);
  }

  /**
   * 批量添加视频到播放列表
   */
  async addMultipleVideos(playlistId: number, data: BatchAddVideosRequest): Promise<BatchAddResult> {
    const response = await api.post<PlaylistResponse<BatchAddResult>>(`/playlists/${playlistId}/items/batch`, data);
    return response.data.data;
  }

  /**
   * 重新排序播放列表项目
   */
  async reorderPlaylistItems(playlistId: number, data: ReorderItemsRequest): Promise<void> {
    await api.put(`/playlists/${playlistId}/items/reorder`, data);
  }

  /**
   * 复制播放列表
   */
  async duplicatePlaylist(playlistId: number, newName?: string): Promise<Playlist> {
    const response = await api.post<PlaylistResponse<{ playlist: Playlist }>>(`/playlists/${playlistId}/duplicate`, {
      newName
    });
    return response.data.data.playlist;
  }

  // 播放历史管理

  /**
   * 记录播放历史
   */
  async recordPlayHistory(data: RecordPlayHistoryRequest): Promise<void> {
    await api.post('/playlists/history/record', data);
  }

  /**
   * 获取播放历史
   */
  async getPlayHistory(limit: number = 50, offset: number = 0, videoId?: number): Promise<PlayHistoryResponse> {
    const response = await api.get<PlaylistResponse<PlayHistoryResponse>>('/playlists/history', {
      params: { limit, offset, videoId }
    });
    return response.data.data;
  }

  /**
   * 获取最近播放的视频
   */
  async getRecentlyPlayed(limit: number = 10): Promise<RecentVideosResponse> {
    const response = await api.get<PlaylistResponse<RecentVideosResponse>>('/playlists/history/recent', {
      params: { limit }
    });
    return response.data.data;
  }

  /**
   * 获取播放统计信息
   */
  async getPlayStats(): Promise<PlayStats> {
    const response = await api.get<PlaylistResponse<{ stats: PlayStats }>>('/playlists/history/stats');
    return response.data.data.stats;
  }

  /**
   * 获取播放趋势数据
   */
  async getPlayTrends(days: number = 7): Promise<any> {
    const response = await api.get('/playlists/history/trends', {
      params: { days }
    });
    return response.data.data;
  }

  /**
   * 获取视频观看进度（断点续播）
   */
  async getWatchProgress(videoId: number): Promise<WatchProgressResponse> {
    const response = await api.get<PlaylistResponse<WatchProgressResponse>>(`/playlists/history/progress/${videoId}`);
    return response.data.data;
  }

  /**
   * 清除播放历史
   */
  async clearPlayHistory(videoId?: number, clearAll?: boolean): Promise<{ deletedCount: number }> {
    const response = await api.delete<PlaylistResponse<{ deletedCount: number }>>('/playlists/history', {
      data: { videoId, clearAll }
    });
    return response.data.data;
  }

  // 便捷方法

  /**
   * 检查视频是否在播放列表中
   */
  async isVideoInPlaylist(playlistId: number, videoId: number): Promise<boolean> {
    try {
      const playlist = await this.getPlaylistById(playlistId);
      return playlist.playlist.items?.some(item => item.videoId === videoId) || false;
    } catch (error) {
      console.error('检查视频是否在播放列表中失败:', error);
      return false;
    }
  }

  /**
   * 获取播放列表的总时长
   */
  async getPlaylistDuration(playlistId: number): Promise<number> {
    try {
      const playlist = await this.getPlaylistById(playlistId);
      return playlist.playlist.items?.reduce((total, item) => {
        return total + (item.video?.duration || 0);
      }, 0) || 0;
    } catch (error) {
      console.error('获取播放列表时长失败:', error);
      return 0;
    }
  }

  /**
   * 搜索用户的播放列表
   */
  async searchPlaylists(query: string): Promise<Playlist[]> {
    try {
      const { playlists } = await this.getUserPlaylists(false);
      return playlists.filter(playlist => 
        playlist.name.toLowerCase().includes(query.toLowerCase()) ||
        (playlist.description && playlist.description.toLowerCase().includes(query.toLowerCase()))
      );
    } catch (error) {
      console.error('搜索播放列表失败:', error);
      return [];
    }
  }
}

// 创建单例实例
export const playlistApi = new PlaylistApiService();
export default playlistApi;
