import apiClient from './api'; // 导入配置好的axios实例

/**
 * 整合的 Admin API
 */
export const adminApi = {
  // 系统设置
  getSystemConfig: () => apiClient.get('/admin/system/config'),
  updateSystemConfig: (config: any) => apiClient.put('/admin/system/config', config),
  
  // 会员计划
  getAllPlans: () => apiClient.get('/member/admin/plans'),
  createPlan: (planData: any) => apiClient.post('/member/admin/plans', planData),
  updatePlan: (id: number, planData: any) => apiClient.put(`/member/admin/plans/${id}`, planData),
  deletePlan: (id: number) => apiClient.delete(`/member/admin/plans/${id}`),
  getPlan: (id: number) => apiClient.get(`/member/admin/plans/${id}`),

  // 会员用户
  getAllMemberUsers: (params: any = {}) => apiClient.get('/member/admin/users', { params }),

  // 订单管理
  getPayments: (params: any) => apiClient.get('/admin/payments', { params }),
  updateOrderStatus: (orderNo: string, status: string, reason?: string) => 
    apiClient.put(`/admin/payments/${orderNo}/status`, { status, reason }),
  getOrderDetail: (orderNo: string) => apiClient.get(`/admin/payments/${orderNo}`),

  // 支付通道管理
  getPaymentGateways: () => apiClient.get('/admin/payment-gateways'),
  updatePaymentGateway: (id: number, data: { enabled?: boolean; config?: Record<string, any> }) =>
    apiClient.put(`/admin/payment-gateways/${id}`, data),
  
  // 视频管理
  getAdminVideos: (params) => apiClient.get('/admin/videos', { params }),
  getPendingVideos: (params) => apiClient.get('/admin/videos/pending', { params }),
  reviewVideo: (id, data) => apiClient.post(`/admin/videos/${id}/review`, data),

  // 评论管理
  getAdminComments: (params) => apiClient.get('/admin/comments', { params }),
  deleteCommentAsAdmin: (commentId) => apiClient.delete(`/admin/comments/${commentId}`),

  // 提现管理
  getWithdrawals: (params: any) => apiClient.get('/admin/withdrawals', { params }),
  approveWithdrawal: (id: number, data: { transaction_hash?: string; notes?: string }) => 
    apiClient.post(`/admin/withdrawals/${id}/approve`, data),
  rejectWithdrawal: (id: number, data: { reason: string }) => 
    apiClient.post(`/admin/withdrawals/${id}/reject`, data),

  // Creem 产品管理
  getCreemPlans: () => apiClient.get('/admin/creem-plans/plans'),
  createCreemPlan: (planData: any) => apiClient.post('/admin/creem-plans', planData),
  updateCreemPlan: (id: number, planData: any) => apiClient.put(`/admin/creem-plans/${id}`, planData),
  deleteCreemPlan: (id: number) => apiClient.delete(`/admin/creem-plans/${id}`),
};

// 导出所有api
export default {
  // a BUNCH of deprecated stuff that should be removed
};