# 视频网站API框架

基于Node.js + Express.js的模块化视频网站API框架，支持视频上传、转码、播放、用户管理等功能。

## 技术栈

- **运行时**: Node.js 18+
- **框架**: Express.js
- **数据库**: MySQL + Redis
- **文件处理**: Multer + FFmpeg
- **认证**: JWT + bcrypt
- **存储**: 本地文件系统

## 项目结构

```
video-api-framework/
├── src/
│   ├── modules/           # 功能模块
│   │   ├── auth/         # 认证模块
│   │   ├── user/         # 用户管理
│   │   ├── video/        # 视频处理
│   │   ├── member/       # 会员系统
│   │   ├── comment/      # 评论系统
│   │   ├── admin/        # 后台管理
│   │   └── payment/      # 支付系统
│   ├── middleware/       # 中间件
│   ├── utils/           # 工具函数
│   ├── config/          # 配置文件
│   ├── database/        # 数据库相关
│   └── services/        # 业务服务
├── uploads/             # 本地文件存储
├── logs/               # 日志文件
├── docs/               # API文档
└── tests/              # 测试文件
```

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
复制 `.env.example` 到 `.env` 并配置相关参数

### 3. 启动开发服务器
```bash
npm run dev
```

### 4. 生产环境启动
```bash
npm start
```

## 功能模块

- ✅ 用户注册登录
- ✅ JWT认证
- ✅ 视频上传
- ✅ 视频转码
- ✅ 视频播放
- ✅ 评论系统
- ✅ 会员系统
- ✅ 后台管理

## API文档

启动服务后访问 `/docs` 查看完整API文档

## 许可证

MIT License
