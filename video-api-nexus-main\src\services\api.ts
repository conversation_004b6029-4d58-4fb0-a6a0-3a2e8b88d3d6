import axios from 'axios';

const api = axios.create({
  baseURL: '/api', // 所有请求都将自动以 /api 开头
  timeout: 10000, // 请求超时时间10秒
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器：在每个请求中附加Token
api.interceptors.request.use(
  (config) => {
    // 从 localStorage 获取 token
    const token = localStorage.getItem('token');
    
    // 如果 token 存在，则添加到请求头
    if (token) {
      // 通常token存储时会带有双引号，这里移除它们
      const cleanedToken = token.replace(/"/g, '');
      config.headers.Authorization = `Bearer ${cleanedToken}`;
    }
    
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 你也可以在这里添加响应拦截器，例如，统一处理错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default api;

// =================================================================
// API Service Functions
// =================================================================

/**
 * 创建一个待处理的余额充值订单
 * @param amount - 要充值的金额
 * @returns Promise with order data
 */
export const createRechargeOrder = async (amount: number) => {
  try {
    const response = await api.post('/balance/recharge', { amount });
    return response.data;
  } catch (error) {
    // 抛出错误，以便在调用处用 try/catch 处理，并显示 toast
    console.error('创建充值订单失败:', error.response?.data?.message || error.message);
    throw error.response?.data || new Error('网络请求失败');
  }
};

/**
 * 创建一个自动处理的支付订单用于充值
 * @param amount - 要充值的金额
 * @param paymentMethod - 支付方式 (e.g., 'epay')
 * @returns Promise with payment data
 */
export const createPaymentForRecharge = async (amount: number, paymentMethod: string) => {
  try {
    const response = await api.post('/payment/create-order', {
      amount,
      paymentMethod,
      type: 'recharge',
      description: `账户充值 ${amount}元`,
    });
    return response.data;
  } catch (error) {
    console.error('创建支付订单失败:', error.response?.data?.message || error.message);
    throw error.response?.data || new Error('网络请求失败');
  }
};

/**
 * 获取当前用户的订单列表
 * @param params - 分页和筛选参数
 * @returns Promise with orders data
 */
export const getMyOrders = async (params: { page?: number; pageSize?: number; status?: string; type?: string }) => {
  try {
    const response = await api.get('/payment/my-orders', { params });
    return response.data;
  } catch (error) {
    console.error('获取订单列表失败:', error.response?.data?.message || error.message);
    throw error.response?.data || new Error('网络请求失败');
  }
};

/**
 * 获取当前登录用户的个人资料
 * @returns Promise with user profile data
 */
export const getUserProfile = async () => {
  try {
    const response = await api.get('/user/profile');
    return response.data;
  } catch (error) {
    console.error('获取用户资料失败:', error.response?.data?.message || error.message);
    throw error.response?.data || new Error('网络请求失败');
  }
};

/**
 * 发送更换邮箱的验证码
 * @param newEmail - 新的邮箱地址
 * @returns Promise
 */
export const sendChangeEmailCode = async (newEmail: string) => {
  try {
    const response = await api.post('/user/send-change-email-code', { newEmail });
    return response.data;
  } catch (error) {
    console.error('发送邮箱验证码失败:', error.response?.data?.message || error.message);
    throw error.response?.data || new Error('网络请求失败');
  }
};

/**
 * 验证并最终更换邮箱
 * @param newEmail - 新的邮箱地址
 * @param code - 6位验证码
 * @returns Promise
 */
export const verifyChangeEmail = async (newEmail: string, code: string) => {
  try {
    const response = await api.post('/user/verify-change-email', { newEmail, code });
    return response.data;
  } catch (error) {
    console.error('更换邮箱失败:', error.response?.data?.message || error.message);
    throw error.response?.data || new Error('网络请求失败');
  }
}; 

// 获取公共系统设置
export const getPublicSettings = async () => {
  const response = await api.get('/auth/public-settings');
  return response.data;
};

// 发送注册验证码
export const sendRegistrationCode = async (email: string) => {
  const response = await api.post('/auth/send-registration-code', { email });
  return response.data;
}; 

// 用户注册
export const register = async (data: any) => {
  const response = await api.post('/auth/register', data);
  return response.data;
}; 

// 用户登录
export const login = async (credentials: any) => {
  const response = await api.post('/auth/login', credentials);
  return response.data;
}; 

/**
 * 提交一个新的提现请求
 * @param data - { amount, walletType, walletAddress }
 * @returns Promise with withdrawal data
 */
export const requestWithdrawal = async (data: { amount: number; walletType: string; walletAddress: string }) => {
  try {
    const response = await api.post('/balance/withdrawals', data);
    return response.data;
  } catch (error) {
    console.error('提交提现请求失败:', error.response?.data?.message || error.message);
    throw error.response?.data || new Error('网络请求失败');
  }
};

/**
 * 获取用户的提现历史
 * @param params - 分页和筛选参数
 * @returns Promise with withdrawal history data
 */
export const getWithdrawalHistory = async (params: { page?: number; limit?: number; status?: string }) => {
  try {
    const response = await api.get('/balance/withdrawals', { params });
    return response.data;
  } catch (error) {
    console.error('获取提现历史失败:', error.response?.data?.message || error.message);
    throw error.response?.data || new Error('网络请求失败');
  }
}; 

/**
 * 获取指定用户的视频列表
 * @param userId - 用户ID
 * @param params - 分页和筛选参数
 * @returns Promise with user videos data
 */
export const getUserVideos = async (userId: number, params: { page?: number; limit?: number; status?: string } = {}) => {
  try {
    const response = await api.get(`/video/user/${userId}`, { params });
    return response.data;
  } catch (error) {
    console.error('获取用户视频列表失败:', error.response?.data?.message || error.message);
    throw error.response?.data || new Error('网络请求失败');
  }
};