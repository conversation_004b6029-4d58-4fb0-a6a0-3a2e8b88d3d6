import { useQuery } from '@tanstack/react-query';
import { getUserVideos } from '@/lib/api';
import VideoCard from '@/components/VideoCard';
import LoadingSpinner from '@/components/LoadingSpinner';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { type VariantProps } from 'class-variance-authority';
import { badgeVariants } from "@/components/ui/badge";
import { useTranslation } from 'react-i18next';

type BadgeVariant = VariantProps<typeof badgeVariants>["variant"];

export function MyWorksPage() {
  const { t } = useTranslation();

  const getStatusMap = (): { [key: string]: { text: string; variant: BadgeVariant } } => ({
    published: { text: t('videoStatus.published'), variant: 'default' },
    pending_review: { text: t('videoStatus.pending_review'), variant: 'secondary' },
    rejected: { text: t('videoStatus.rejected'), variant: 'destructive' },
    processing: { text: t('videoStatus.processing'), variant: 'secondary' },
    private: { text: t('videoStatus.private'), variant: 'outline' },
  });
  const { data, isLoading, error } = useQuery({
    queryKey: ['my-videos'],
    queryFn: getUserVideos
  });

  const statusMap = getStatusMap();

  if (isLoading) {
    return <div className="flex justify-center items-center h-64"><LoadingSpinner /></div>;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>{t('works.loadFailed')}</AlertTitle>
        <AlertDescription>{t('works.loadFailedDesc')}</AlertDescription>
      </Alert>
    );
  }

  const videos = data?.data?.data?.videos || [];

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">{t('works.title')}</h1>

      {videos.length === 0 ? (
        <p>{t('works.noWorks')}</p>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {videos.map((video: any) => (
            <div key={video.id} className="relative">
              <VideoCard video={video} />
              <div className="absolute top-2 right-2">
                <Badge variant={statusMap[video.status]?.variant || 'default'}>
                  {statusMap[video.status]?.text || video.status}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 