import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { adminApi } from '@/services/adminApi';

const approveSchema = z.object({
  transaction_hash: z.string().optional(),
  notes: z.string().optional(),
});

const ApproveWithdrawalDialog = ({ open, onOpenChange, withdrawal, onSuccess }) => {
  const { toast } = useToast();
  const form = useForm({
    resolver: zodResolver(approveSchema),
  });

  const mutation = useMutation({
    mutationFn: (values: z.infer<typeof approveSchema>) => adminApi.approveWithdrawal(withdrawal.id, values),
    onSuccess: () => {
      toast({ title: '操作成功', description: '提现请求已批准。' });
      onSuccess();
      onOpenChange(false);
      form.reset();
    },
    onError: (error) => {
      toast({ title: '操作失败', description: error.message, variant: 'destructive' });
    },
  });

  const onSubmit = (values) => mutation.mutate(values);

  if (!withdrawal) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>批准提现请求</DialogTitle>
          <DialogDescription>
            批准后，提现状态将更新。如果提供了交易哈希，状态将直接标记为“已完成”。
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="transaction_hash"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>交易哈希 (可选)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="输入链上交易哈希..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注 (可选)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="输入备注信息..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={mutation.isPending}>取消</Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                确认批准
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ApproveWithdrawalDialog; 