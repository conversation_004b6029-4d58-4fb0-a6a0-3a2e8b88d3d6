const { asyncHandler } = require('../../../middleware/errorHandler');
const CreemService = require('../services/creemService');

class CreemController {

  /**
   * 创建一个新的Creem计划
   */
  createPlan = asyncHandler(async (req, res) => {
    const planData = req.body;
    const newPlan = await CreemService.createPlan(planData);
    res.status(201).json({
      success: true,
      message: 'Creem计划创建成功',
      data: newPlan,
    });
  });

  /**
   * 获取所有Creem计划
   */
  getAllPlans = asyncHandler(async (req, res) => {
    const plans = await CreemService.getAllPlans();
    res.status(200).json({
      success: true,
      data: plans,
    });
  });

  /**
   * 更新一个Creem计划
   */
  updatePlan = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const updatedPlan = await CreemService.updatePlan(id, updateData);
    res.status(200).json({
      success: true,
      message: 'Creem计划更新成功',
      data: updatedPlan,
    });
  });

  /**
   * 删除一个Creem计划
   */
  deletePlan = asyncHandler(async (req, res) => {
    const { id } = req.params;
    await CreemService.deletePlan(id);
    res.status(200).json({
      success: true,
      message: 'Creem计划删除成功',
    });
  });
}

module.exports = new CreemController(); 