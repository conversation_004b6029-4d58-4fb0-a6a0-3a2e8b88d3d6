const { cache, CACHE_KEYS } = require('../utils/cache');
const logger = require('../utils/logger');
const User = require('../database/models/User');
const Video = require('../database/models/Video');
const Comment = require('../database/models/Comment');
const Like = require('../database/models/Like');
const Favorite = require('../database/models/Favorite');
const Membership = require('../database/models/Membership');

class StatisticsService {
  // 获取访问统计概览
  async getAccessOverview(timeRange = '7d') {
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'access_overview', timeRange);
    let overview = await cache.get(cacheKey);

    if (!overview) {
      const days = this.parseDays(timeRange);
      
      // 用户访问统计
      const userStats = await User.query(`
        SELECT 
          DATE(last_login_at) as date,
          COUNT(DISTINCT id) as active_users,
          COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) THEN 1 END) as new_users
        FROM users 
        WHERE last_login_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY DATE(last_login_at)
        ORDER BY date DESC
      `, [days, days]);

      // 视频观看统计
      const videoStats = await Video.query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as videos_uploaded,
          SUM(view_count) as total_views,
          AVG(view_count) as avg_views
        FROM videos 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
          AND status = 'published'
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `, [days]);

      // 互动统计
      const interactionStats = await this.getInteractionStats(days);

      overview = {
        timeRange,
        userAccess: userStats,
        videoActivity: videoStats,
        interactions: interactionStats,
        summary: {
          totalActiveUsers: userStats.reduce((sum, day) => sum + day.active_users, 0),
          totalNewUsers: userStats.reduce((sum, day) => sum + day.new_users, 0),
          totalViews: videoStats.reduce((sum, day) => sum + day.total_views, 0),
          totalVideos: videoStats.reduce((sum, day) => sum + day.videos_uploaded, 0)
        }
      };

      await cache.set(cacheKey, overview, 1800); // 30分钟缓存
    }

    return overview;
  }

  // 获取详细访问统计
  async getDetailedAccessStats(options = {}) {
    const {
      startDate,
      endDate,
      groupBy = 'day', // day, hour, week, month
      metrics = ['users', 'videos', 'interactions']
    } = options;

    const stats = {};

    if (metrics.includes('users')) {
      stats.users = await this.getUserAccessStats(startDate, endDate, groupBy);
    }

    if (metrics.includes('videos')) {
      stats.videos = await this.getVideoAccessStats(startDate, endDate, groupBy);
    }

    if (metrics.includes('interactions')) {
      stats.interactions = await this.getInteractionAccessStats(startDate, endDate, groupBy);
    }

    return stats;
  }

  // 获取用户访问统计
  async getUserAccessStats(startDate, endDate, groupBy) {
    const dateFormat = this.getDateFormat(groupBy);
    
    const sql = `
      SELECT 
        ${dateFormat} as period,
        COUNT(DISTINCT id) as unique_users,
        COUNT(*) as total_logins,
        COUNT(CASE WHEN created_at BETWEEN ? AND ? THEN 1 END) as new_registrations
      FROM users 
      WHERE last_login_at BETWEEN ? AND ?
      GROUP BY ${dateFormat}
      ORDER BY period DESC
    `;

    return await User.query(sql, [startDate, endDate, startDate, endDate]);
  }

  // 获取视频访问统计
  async getVideoAccessStats(startDate, endDate, groupBy) {
    const dateFormat = this.getDateFormat(groupBy);
    
    const sql = `
      SELECT 
        ${dateFormat} as period,
        COUNT(*) as videos_uploaded,
        SUM(view_count) as total_views,
        SUM(like_count) as total_likes,
        SUM(comment_count) as total_comments,
        AVG(view_count) as avg_views_per_video
      FROM videos 
      WHERE created_at BETWEEN ? AND ?
        AND status = 'published'
      GROUP BY ${dateFormat}
      ORDER BY period DESC
    `;

    return await Video.query(sql, [startDate, endDate]);
  }

  // 获取互动访问统计
  async getInteractionAccessStats(startDate, endDate, groupBy) {
    const dateFormat = this.getDateFormat(groupBy);
    
    // 评论统计
    const commentStats = await Comment.query(`
      SELECT 
        ${dateFormat} as period,
        COUNT(*) as comments_count
      FROM comments 
      WHERE created_at BETWEEN ? AND ?
        AND status = 'active'
      GROUP BY ${dateFormat}
      ORDER BY period DESC
    `, [startDate, endDate]);

    // 点赞统计
    const likeStats = await Like.query(`
      SELECT 
        ${dateFormat} as period,
        COUNT(*) as likes_count,
        COUNT(CASE WHEN target_type = 'video' THEN 1 END) as video_likes,
        COUNT(CASE WHEN target_type = 'comment' THEN 1 END) as comment_likes
      FROM likes 
      WHERE created_at BETWEEN ? AND ?
      GROUP BY ${dateFormat}
      ORDER BY period DESC
    `, [startDate, endDate]);

    // 收藏统计
    const favoriteStats = await Favorite.query(`
      SELECT 
        ${dateFormat} as period,
        COUNT(*) as favorites_count
      FROM favorites 
      WHERE created_at BETWEEN ? AND ?
      GROUP BY ${dateFormat}
      ORDER BY period DESC
    `, [startDate, endDate]);

    return {
      comments: commentStats,
      likes: likeStats,
      favorites: favoriteStats
    };
  }

  // 获取收费统计概览
  async getRevenueOverview(timeRange = '30d') {
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'revenue_overview', timeRange);
    let overview = await cache.get(cacheKey);

    if (!overview) {
      const days = this.parseDays(timeRange);
      
      // 会员收入统计
      const membershipRevenue = await Membership.query(`
        SELECT 
          DATE(m.created_at) as date,
          COUNT(*) as new_subscriptions,
          SUM(mp.price) as daily_revenue,
          mp.name as plan_name
        FROM memberships m
        LEFT JOIN membership_plans mp ON m.plan_id = mp.id
        WHERE m.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
          AND m.status != 'cancelled'
        GROUP BY DATE(m.created_at), mp.id
        ORDER BY date DESC
      `, [days]);

      // 会员统计
      const membershipStats = await Membership.query(`
        SELECT 
          COUNT(*) as total_memberships,
          COUNT(CASE WHEN status = 'active' AND end_date > NOW() THEN 1 END) as active_memberships,
          COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) THEN 1 END) as new_memberships,
          SUM(CASE WHEN status != 'cancelled' THEN mp.price ELSE 0 END) as total_revenue
        FROM memberships m
        LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      `, [days]);

      // 按计划分组的收入
      const revenueByPlan = await Membership.query(`
        SELECT 
          mp.name as plan_name,
          mp.price,
          COUNT(m.id) as subscription_count,
          SUM(mp.price) as plan_revenue,
          COUNT(CASE WHEN m.status = 'active' AND m.end_date > NOW() THEN 1 END) as active_count
        FROM membership_plans mp
        LEFT JOIN memberships m ON mp.id = m.plan_id 
          AND m.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
          AND m.status != 'cancelled'
        WHERE mp.is_active = true
        GROUP BY mp.id
        ORDER BY plan_revenue DESC
      `, [days]);

      overview = {
        timeRange,
        dailyRevenue: membershipRevenue,
        summary: membershipStats[0],
        planBreakdown: revenueByPlan,
        metrics: {
          totalRevenue: membershipStats[0].total_revenue || 0,
          averageDailyRevenue: membershipRevenue.length > 0 ? 
            (membershipStats[0].total_revenue || 0) / days : 0,
          conversionRate: this.calculateConversionRate(membershipStats[0])
        }
      };

      await cache.set(cacheKey, overview, 1800); // 30分钟缓存
    }

    return overview;
  }

  // 获取详细收费统计
  async getDetailedRevenueStats(options = {}) {
    const {
      startDate,
      endDate,
      groupBy = 'day',
      planId = null
    } = options;

    const dateFormat = this.getDateFormat(groupBy);
    
    let sql = `
      SELECT 
        ${dateFormat} as period,
        COUNT(*) as subscriptions,
        SUM(mp.price) as revenue,
        AVG(mp.price) as avg_order_value,
        COUNT(DISTINCT m.user_id) as unique_customers,
        mp.name as plan_name
      FROM memberships m
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.created_at BETWEEN ? AND ?
        AND m.status != 'cancelled'
    `;

    const params = [startDate, endDate];

    if (planId) {
      sql += ' AND m.plan_id = ?';
      params.push(planId);
    }

    sql += ` GROUP BY ${dateFormat}, mp.id ORDER BY period DESC`;

    const revenueData = await Membership.query(sql, params);

    // 计算增长率
    const revenueWithGrowth = this.calculateGrowthRates(revenueData, 'revenue');

    return {
      data: revenueWithGrowth,
      summary: this.calculateRevenueSummary(revenueData)
    };
  }

  // 获取用户行为分析
  async getUserBehaviorAnalysis(timeRange = '30d') {
    const days = this.parseDays(timeRange);
    
    // 用户活跃度分析
    const activityAnalysis = await User.query(`
      SELECT 
        CASE 
          WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 'daily_active'
          WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'weekly_active'
          WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 'monthly_active'
          ELSE 'inactive'
        END as activity_level,
        COUNT(*) as user_count
      FROM users
      GROUP BY activity_level
    `);

    // 用户留存分析
    const retentionAnalysis = await User.query(`
      SELECT 
        DATE(created_at) as registration_date,
        COUNT(*) as registered_users,
        COUNT(CASE WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as retained_7d,
        COUNT(CASE WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as retained_30d
      FROM users 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY DATE(created_at)
      ORDER BY registration_date DESC
    `, [days]);

    // 用户转化分析
    const conversionAnalysis = await this.getUserConversionAnalysis(days);

    return {
      activity: activityAnalysis,
      retention: retentionAnalysis.map(day => ({
        ...day,
        retention_rate_7d: day.registered_users > 0 ? 
          (day.retained_7d / day.registered_users * 100).toFixed(2) : 0,
        retention_rate_30d: day.registered_users > 0 ? 
          (day.retained_30d / day.registered_users * 100).toFixed(2) : 0
      })),
      conversion: conversionAnalysis
    };
  }

  // 获取热门内容分析
  async getPopularContentAnalysis(timeRange = '7d') {
    const days = this.parseDays(timeRange);
    
    // 热门视频
    const popularVideos = await Video.query(`
      SELECT 
        id, title, view_count, like_count, comment_count,
        (view_count * 0.5 + like_count * 0.3 + comment_count * 0.2) as popularity_score
      FROM videos 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND status = 'published'
      ORDER BY popularity_score DESC
      LIMIT 20
    `, [days]);

    // 热门分类
    const popularCategories = await Video.query(`
      SELECT 
        c.name as category_name,
        COUNT(v.id) as video_count,
        SUM(v.view_count) as total_views,
        AVG(v.view_count) as avg_views
      FROM videos v
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE v.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        AND v.status = 'published'
      GROUP BY c.id
      ORDER BY total_views DESC
      LIMIT 10
    `, [days]);

    // 活跃用户
    const activeUsers = await User.query(`
      SELECT 
        u.id, u.username, u.nickname,
        COUNT(v.id) as videos_uploaded,
        SUM(v.view_count) as total_views,
        COUNT(c.id) as comments_made
      FROM users u
      LEFT JOIN videos v ON u.id = v.user_id AND v.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      LEFT JOIN comments c ON u.id = c.user_id AND c.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      WHERE u.last_login_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY u.id
      ORDER BY (videos_uploaded * 10 + total_views * 0.1 + comments_made * 5) DESC
      LIMIT 20
    `, [days, days, days]);

    return {
      videos: popularVideos,
      categories: popularCategories,
      users: activeUsers
    };
  }

  // 辅助方法
  parseDays(timeRange) {
    const match = timeRange.match(/(\d+)([dwmy])/);
    if (!match) return 7;
    
    const [, num, unit] = match;
    const multipliers = { d: 1, w: 7, m: 30, y: 365 };
    return parseInt(num) * (multipliers[unit] || 1);
  }

  getDateFormat(groupBy) {
    const formats = {
      hour: "DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00')",
      day: "DATE(created_at)",
      week: "DATE_FORMAT(created_at, '%Y-%u')",
      month: "DATE_FORMAT(created_at, '%Y-%m')"
    };
    return formats[groupBy] || formats.day;
  }

  async getInteractionStats(days) {
    const [comments, likes, favorites] = await Promise.all([
      Comment.query(`
        SELECT COUNT(*) as count FROM comments 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND status = 'active'
      `, [days]),
      Like.query(`
        SELECT COUNT(*) as count FROM likes 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      `, [days]),
      Favorite.query(`
        SELECT COUNT(*) as count FROM favorites 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      `, [days])
    ]);

    return {
      comments: comments[0].count,
      likes: likes[0].count,
      favorites: favorites[0].count
    };
  }

  calculateConversionRate(stats) {
    if (!stats.total_memberships || !stats.active_memberships) return 0;
    return (stats.active_memberships / stats.total_memberships * 100).toFixed(2);
  }

  calculateGrowthRates(data, field) {
    return data.map((item, index) => {
      if (index === data.length - 1) {
        return { ...item, growth_rate: 0 };
      }
      
      const current = item[field] || 0;
      const previous = data[index + 1][field] || 0;
      const growthRate = previous > 0 ? ((current - previous) / previous * 100) : 0;
      
      return { ...item, growth_rate: growthRate.toFixed(2) };
    });
  }

  calculateRevenueSummary(data) {
    const totalRevenue = data.reduce((sum, item) => sum + (item.revenue || 0), 0);
    const totalSubscriptions = data.reduce((sum, item) => sum + (item.subscriptions || 0), 0);
    const avgOrderValue = totalSubscriptions > 0 ? totalRevenue / totalSubscriptions : 0;
    
    return {
      totalRevenue: totalRevenue.toFixed(2),
      totalSubscriptions,
      avgOrderValue: avgOrderValue.toFixed(2),
      periods: data.length
    };
  }

  async getUserConversionAnalysis(days) {
    // 简化的转化分析
    const conversionData = await User.query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN id IN (SELECT DISTINCT user_id FROM memberships WHERE status = 'active') THEN 1 END) as converted_users
      FROM users 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    `, [days]);

    const data = conversionData[0];
    const conversionRate = data.total_users > 0 ? 
      (data.converted_users / data.total_users * 100).toFixed(2) : 0;

    return {
      total_users: data.total_users,
      converted_users: data.converted_users,
      conversion_rate: conversionRate
    };
  }
}

module.exports = new StatisticsService();
