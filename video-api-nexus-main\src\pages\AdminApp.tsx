import React from 'react';
import { Outlet } from 'react-router-dom';
import AdminSidebar from '../components/admin/AdminSidebar';
import MobileAdminHeader from '../components/admin/MobileAdminHeader';

const AdminApp = () => {
  return (
    <div className="flex h-screen bg-background">
      {/* 桌面端侧边栏 */}
      <AdminSidebar />
      
      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 移动端顶部导航 */}
        <MobileAdminHeader className="md:hidden" />
        
        {/* 主要内容 */}
        <main className="flex-1 p-4 md:p-6 overflow-auto">
          <div className="h-full">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminApp;
