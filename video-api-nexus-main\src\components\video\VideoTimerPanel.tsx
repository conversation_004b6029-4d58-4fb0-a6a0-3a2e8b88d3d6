import React, { useState } from 'react';
import { Clock, X, Play, Square } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface VideoTimerPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onStartTimer: (minutes: number) => void;
  onStopTimer: () => void;
  isTimerActive: boolean;
  className?: string;
}

const VideoTimerPanel: React.FC<VideoTimerPanelProps> = ({
  isOpen,
  onClose,
  onStartTimer,
  onStopTimer,
  isTimerActive,
  className = '',
}) => {
  const [customMinutes, setCustomMinutes] = useState<string>('');
  const [selectedPreset, setSelectedPreset] = useState<number | null>(null);

  // 预设时间选项（分钟）
  const presetOptions = [
    { label: '5分钟', value: 5 },
    { label: '10分钟', value: 10 },
    { label: '15分钟', value: 15 },
    { label: '30分钟', value: 30 },
    { label: '60分钟', value: 60 },
  ];

  const handlePresetClick = (minutes: number) => {
    setSelectedPreset(minutes);
    setCustomMinutes('');
  };

  const handleCustomInputChange = (value: string) => {
    setCustomMinutes(value);
    setSelectedPreset(null);
  };

  const handleStartTimer = () => {
    let minutes = 0;
    
    if (selectedPreset) {
      minutes = selectedPreset;
    } else if (customMinutes) {
      const customValue = parseInt(customMinutes, 10);
      if (customValue > 0 && customValue <= 999) {
        minutes = customValue;
      } else {
        alert('请输入1-999之间的有效分钟数');
        return;
      }
    } else {
      alert('请选择预设时间或输入自定义时间');
      return;
    }

    onStartTimer(minutes);
    onClose();
  };

  const handleStopTimer = () => {
    onStopTimer();
    setSelectedPreset(null);
    setCustomMinutes('');
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 ${className}`}>
      <Card className="w-full max-w-md bg-background/95 backdrop-blur-sm">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            视频定时器
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 当前状态显示 */}
          {isTimerActive && (
            <div className="p-3 bg-primary/10 rounded-lg border border-primary/20">
              <div className="flex items-center gap-2 text-primary">
                <Clock className="h-4 w-4" />
                <span className="text-sm font-medium">定时器正在运行</span>
              </div>
            </div>
          )}

          {/* 预设时间选项 */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">选择预设时间</Label>
            <div className="grid grid-cols-2 gap-2">
              {presetOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedPreset === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePresetClick(option.value)}
                  className="h-10"
                  disabled={isTimerActive}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* 自定义时间输入 */}
          <div className="space-y-3">
            <Label htmlFor="custom-time" className="text-sm font-medium">
              或输入自定义时间（分钟）
            </Label>
            <Input
              id="custom-time"
              type="number"
              placeholder="输入分钟数 (1-999)"
              value={customMinutes}
              onChange={(e) => handleCustomInputChange(e.target.value)}
              min="1"
              max="999"
              disabled={isTimerActive}
              className="text-center"
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2 pt-4">
            {!isTimerActive ? (
              <>
                <Button
                  onClick={handleStartTimer}
                  className="flex-1"
                  disabled={!selectedPreset && !customMinutes}
                >
                  <Play className="h-4 w-4 mr-2" />
                  开始定时
                </Button>
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1"
                >
                  取消
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="destructive"
                  onClick={handleStopTimer}
                  className="flex-1"
                >
                  <Square className="h-4 w-4 mr-2" />
                  停止定时
                </Button>
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1"
                >
                  关闭
                </Button>
              </>
            )}
          </div>

          {/* 提示信息 */}
          <div className="text-xs text-muted-foreground text-center">
            {!isTimerActive 
              ? '定时器到达设定时间后将自动暂停视频播放'
              : '定时器运行期间可以随时停止或关闭此面板'
            }
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VideoTimerPanel;
