import { useState, useEffect, useCallback } from 'react';
import { MessageCircle, User, Calendar, Eye, Trash2, Ban, Search, Filter } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import { getAdminComments, deleteCommentAsAdmin } from '@/lib/api';
import { useDebounce } from '@/hooks/useDebounce';
import LoadingSpinner from '@/components/LoadingSpinner';

const AdminComments = () => {
  const [comments, setComments] = useState([]);
  const [stats, setStats] = useState({ total: 0, today: 0, pending: 0, flagged: 0 });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const debouncedSearch = useDebounce(searchKeyword, 500);
  const { toast } = useToast();

  const fetchComments = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        search: debouncedSearch,
        status: selectedStatus === 'all' ? '' : selectedStatus,
      };
      const response = await getAdminComments(params);

      // 安全地访问响应数据
      const responseData = response?.data?.data;
      if (responseData) {
        setComments(responseData.comments || []);
        setPagination(prev => ({ ...prev, total: responseData.total || 0 }));
        setStats(responseData.stats || { total: 0, today: 0, pending: 0, flagged: 0 });
        setError(null);
      } else {
        setComments([]);
        setError('数据格式错误，请检查API响应');
      }
    } catch (err: any) {
      console.error('获取评论列表失败:', err);
      if (err.response?.status === 401) {
        setError('认证失败，请重新登录');
      } else if (err.response?.status === 403) {
        setError('权限不足，无法访问评论管理');
      } else if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('网络连接失败，请检查后端服务是否运行');
      } else {
        setError(err.response?.data?.message || '获取评论列表失败，请稍后重试');
      }
      setComments([]);
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.pageSize, debouncedSearch, selectedStatus]);

  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  const handleDeleteComment = async (commentId) => {
    try {
      await deleteCommentAsAdmin(commentId);
      toast({
        title: "删除成功",
        description: `评论 #${commentId} 已被删除`,
      });
      fetchComments(); // Refresh comments
    } catch (error) {
      toast({
        title: "删除失败",
        description: "操作失败，请重试",
        variant: "destructive",
      });
    }
  };

  const handleBanUser = (userName) => {
    toast({
      title: "封禁成功",
      description: `用户 ${userName} 已被封禁`,
    });
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      published: { text: '已发布', className: 'bg-green-100 text-green-800' },
      flagged: { text: '已举报', className: 'bg-red-100 text-red-800' },
      pending: { text: '待审核', className: 'bg-yellow-100 text-yellow-800' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.published;
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
        {config.text}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">评论管理</h1>
        <div className="flex space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="搜索评论内容..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <select 
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="all">所有状态</option>
            <option value="published">已发布</option>
          </select>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageCircle className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">总评论数</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Eye className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">今日新增</p>
                <p className="text-2xl font-bold">{stats.today}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 评论列表 */}
      <Card>
        <CardHeader>
          <CardTitle>评论列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>评论内容</TableHead>
                <TableHead>用户</TableHead>
                <TableHead>视频</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>点赞数</TableHead>
                <TableHead>回复数</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow><TableCell colSpan={9} className="text-center py-12"><LoadingSpinner text="正在加载评论数据..." /></TableCell></TableRow>
              ) : error ? (
                <TableRow><TableCell colSpan={9} className="text-center py-12 text-red-500">{error}</TableCell></TableRow>
              ) : comments.length === 0 ? (
                <TableRow><TableCell colSpan={9} className="text-center py-12 text-muted-foreground">暂无评论数据</TableCell></TableRow>
              ) : comments.map((comment) => (
                <TableRow key={comment.id}>
                  <TableCell>{comment.id}</TableCell>
                  <TableCell className="max-w-xs">
                    <div className="truncate" title={comment.content}>
                      {comment.content}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <span>{comment.username || comment.nickname || '未知用户'}</span>
                    </div>
                  </TableCell>
                  <TableCell className="max-w-xs">
                    <div className="truncate" title={comment.video_title}>
                      {comment.video_title || '未知视频'}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(comment.status)}</TableCell>
                  <TableCell>{comment.like_count}</TableCell>
                  <TableCell>{comment.reply_count}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1 text-sm text-gray-600">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(comment.created_at).toLocaleString()}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => handleDeleteComment(comment.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      <Button
                        onClick={() => handleBanUser(comment.username)}
                      >
                        <Ban className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          第 {pagination.page} 页，共 {Math.ceil(pagination.total / pagination.pageSize)} 页 (总计 {pagination.total} 条评论)
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setPagination(p => ({ ...p, page: p.page - 1 }))}
            className="px-3 py-1 border border-input rounded-md hover:bg-accent disabled:opacity-50"
            disabled={pagination.page <= 1}
          >
            上一页
          </button>
          <button
            onClick={() => setPagination(p => ({ ...p, page: p.page + 1 }))}
            className="px-3 py-1 border border-input rounded-md hover:bg-accent disabled:opacity-50"
            disabled={pagination.page * pagination.pageSize >= pagination.total}
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminComments;
