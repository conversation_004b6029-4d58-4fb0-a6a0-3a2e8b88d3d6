const winston = require('winston');
const path = require('path');
const { combine, timestamp, printf, errors, json, colorize, simple } = winston.format;

// 自定义日志格式
const customFormat = printf(({ level, message, timestamp, stack, ...meta }) => {
  let log = `${timestamp} [${level}]: ${message}`;
  
  // 检查是否有元数据对象，并且它不为空
  if (meta && Object.keys(meta).length > 2) { // 忽略 service 和 an extra field
    // 使用JSON.stringify来美化对象输出
    log += `\n${JSON.stringify(meta, null, 2)}`;
  }

  if (stack) {
    log += `\n${stack}`;
  }
  
  return log;
});

// 创建logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: combine(
    timestamp({ format: 'HH:mm:ss' }),
    errors({ stack: true }),
    json()
  ),
  defaultMeta: { service: 'video-api' },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/error.log'),
      level: 'error'
    }),
    
    // 所有日志文件
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/combined.log')
    })
  ]
});

// 开发环境下同时输出到控制台
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: combine(
      colorize(),
      timestamp({ format: 'HH:mm:ss' }),
      customFormat
    )
  }));
}

// 创建专门的访问日志记录器
const accessLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/access.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 10
    })
  ]
});

// 创建数据库操作日志记录器
const dbLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/database.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
});

// 扩展logger方法
logger.access = (message, meta = {}) => {
  accessLogger.info(message, meta);
};

logger.database = (message, meta = {}) => {
  dbLogger.info(message, meta);
};

// 记录未捕获的异常
logger.exceptions.handle(
  new winston.transports.File({
    filename: path.join(__dirname, '../../logs/exceptions.log')
  })
);

// 记录未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', { reason, promise });
});

module.exports = logger;
