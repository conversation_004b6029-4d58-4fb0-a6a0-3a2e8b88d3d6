const { cache, CACHE_KEYS } = require('../src/utils/cache');
const logger = require('../src/utils/logger');

async function clearAdminCache() {
  try {
    console.log('🧹 开始清除管理员相关缓存...\n');
    
    // 获取所有缓存键
    const allKeys = cache.keys();
    console.log(`📋 当前缓存键总数: ${allKeys.length}`);
    
    if (allKeys.length > 0) {
      console.log('🔍 当前缓存键列表:');
      allKeys.forEach((key, index) => {
        console.log(`   ${index + 1}. ${key}`);
      });
      console.log('');
    }
    
    // 清除用户相关缓存
    const userCachePattern = `${CACHE_KEYS.USER}:.*`;
    console.log(`🗑️  清除用户缓存 (模式: ${userCachePattern})`);
    cache.delPattern(userCachePattern);
    
    // 清除管理员相关缓存
    const adminCachePattern = `${CACHE_KEYS.ADMIN}:.*`;
    console.log(`🗑️  清除管理员缓存 (模式: ${adminCachePattern})`);
    cache.delPattern(adminCachePattern);
    
    // 清除支付相关缓存
    const paymentCachePattern = `${CACHE_KEYS.PAYMENT}:.*`;
    console.log(`🗑️  清除支付缓存 (模式: ${paymentCachePattern})`);
    cache.delPattern(paymentCachePattern);
    
    // 清除会员相关缓存
    const membershipCachePattern = `${CACHE_KEYS.MEMBERSHIP}:.*`;
    console.log(`🗑️  清除会员缓存 (模式: ${membershipCachePattern})`);
    cache.delPattern(membershipCachePattern);
    
    // 清除搜索缓存
    const searchCachePattern = `${CACHE_KEYS.SEARCH}:.*`;
    console.log(`🗑️  清除搜索缓存 (模式: ${searchCachePattern})`);
    cache.delPattern(searchCachePattern);
    
    console.log('\n✅ 缓存清除完成！');
    
    // 验证清除结果
    const remainingKeys = cache.keys();
    console.log(`📊 清除后剩余缓存键数量: ${remainingKeys.length}`);
    
    if (remainingKeys.length > 0) {
      console.log('📋 剩余缓存键:');
      remainingKeys.forEach((key, index) => {
        console.log(`   ${index + 1}. ${key}`);
      });
    }
    
    console.log('\n🎯 建议操作:');
    console.log('   1. 刷新管理员界面的用户管理页面');
    console.log('   2. 刷新支付管理页面');
    console.log('   3. 如果问题仍然存在，请清除浏览器缓存');
    
  } catch (error) {
    console.error('❌ 清除缓存时发生错误:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  clearAdminCache()
    .then(() => {
      console.log('\n🎉 脚本执行完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { clearAdminCache };
