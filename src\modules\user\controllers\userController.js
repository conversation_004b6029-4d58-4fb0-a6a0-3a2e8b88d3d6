const User = require('../../../database/models/User');
const { operationLogger } = require('../../../middleware/requestLogger');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const { AppError, asyncHandler } = require('../../../middleware/errorHandler');
const path = require('path');
const fs = require('fs').promises;
const { toAbsolutePath } = require('../../../utils/pathResolver');
const fileService = require('../../../services/fileService');
const Order = require('../../../database/models/Order');
const { getPagingData, getPagination } = require('../../../utils/pagination');
const { validationResult } = require('express-validator');
const userService = require('../services/userService');

class UserController {
  // 获取用户资料
  getProfile = asyncHandler(async (req, res) => {
    const userId = req.user.id; // 使用当前登录用户的ID

    // 尝试从缓存获取
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
    let userProfile = await cache.get(cacheKey);

    if (!userProfile) {
      // 从数据库获取用户统计信息
      userProfile = await User.getUserStats(userId);
      if (!userProfile) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      // 处理头像URL
      if (userProfile.avatar) {
        userProfile.avatar_url = fileService.getPublicUrl(userProfile.avatar);
      }

      // 缓存用户资料
      await cache.set(cacheKey, userProfile, 300); // 5分钟缓存
    }

    res.json({
      success: true,
      data: {
        user: userProfile
      }
    });
  });
  
  // 更新用户资料
  updateProfile = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const updateData = req.body;
    
    // 更新用户资料
    const updatedUser = await User.updateProfile(userId, updateData);
    
    if (!updatedUser) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    // 清除缓存
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
    await cache.del(cacheKey);
    
    // 记录操作日志
    operationLogger.logUserOperation(
      req, 
      'profile_update', 
      userId, 
      '更新用户资料',
      { updatedFields: Object.keys(updateData) }
    );
    
    res.json({
      success: true,
      message: '资料更新成功',
      data: {
        user: updatedUser
      }
    });
  });
  
  // 上传头像
  uploadAvatar = asyncHandler(async (req, res, next) => {
    const userId = req.user.id;
    
    if (!req.file) {
      return next(new AppError('请上传头像文件', 400));
    }
    
    const file = req.file;
    
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      // 删除上传的文件
      await fs.unlink(file.path).catch(() => {});
      throw new AppError('不支持的图片格式', 400, 'INVALID_FILE_TYPE');
    }
    
    // 验证文件大小 (2MB)
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      await fs.unlink(file.path).catch(() => {});
      throw new AppError('头像文件过大，最大支持2MB', 400, 'FILE_TOO_LARGE');
    }
    
    try {
      // 处理图片（压缩、裁剪等）
      const processedImagePath = await fileService.processAvatar(file.path, userId);
      
      // 生成访问URL
      const avatarUrl = `/uploads/avatars/${path.basename(processedImagePath)}`;
      
      // 获取当前用户信息，删除旧头像
      const currentUser = await User.findById(userId);
      if (currentUser && currentUser.avatar) {
        try {
          const oldAvatarRelativePath = currentUser.avatar.substring(1); // 移除开头的'/'
          const oldAvatarAbsolutePath = toAbsolutePath(oldAvatarRelativePath);
          await fs.unlink(oldAvatarAbsolutePath);
          logger.info(`已删除旧头像: ${oldAvatarAbsolutePath}`);
        } catch (error) {
          logger.warn(`删除旧头像失败: ${oldAvatarAbsolutePath}`);
        }
      }
      
      // 更新用户头像
      const updatedUser = await User.updateProfile(userId, { avatar: avatarUrl });
      
      // 删除原始上传文件
      if (file.path !== processedImagePath) {
        await fs.unlink(file.path).catch(() => {});
      }
      
      // 清除用户缓存
      const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
      await cache.del(cacheKey);
      
      // 记录操作日志
      operationLogger.logUserOperation(
        req, 
        'avatar_upload', 
        userId, 
        '上传头像',
        { 
          fileName: file.originalname,
          fileSize: file.size,
          avatarUrl 
        }
      );
      
      res.json({
        success: true,
        message: '头像上传成功',
        data: {
          avatarUrl,
          user: {
            id: updatedUser.id,
            avatar: updatedUser.avatar
          }
        }
      });
      
    } catch (error) {
      // 清理上传的文件
      await fs.unlink(file.path).catch(() => {});
      throw error;
    }
  });
  
  // 删除头像
  deleteAvatar = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    
    const user = await User.findById(userId);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    if (!user.avatar) {
      throw new AppError('用户未设置头像', 400, 'NO_AVATAR_SET');
    }
    
    // 删除头像文件
    const avatarPath = path.join(process.cwd(), 'uploads', user.avatar.replace('/uploads/', ''));
    await fs.unlink(avatarPath).catch(() => {
      logger.warn(`删除头像文件失败: ${avatarPath}`);
    });
    
    // 更新数据库
    await User.updateProfile(userId, { avatar: null });
    
    // 清除缓存
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
    await cache.del(cacheKey);
    
    // 记录操作日志
    operationLogger.logUserOperation(req, 'avatar_delete', userId, '删除头像');
    
    res.json({
      success: true,
      message: '头像删除成功'
    });
  });
  
  // 获取用户列表（管理员功能）
  getUserList = asyncHandler(async (req, res) => {
    const { page = 1, pageSize = 20, role, status, keyword } = req.query;
    
    // 构建过滤条件
    const filters = {};
    if (role) filters.role = role;
    if (status) filters.status = status;
    
    let result;
    if (keyword) {
      // 搜索用户
      result = await User.searchUsers(keyword, parseInt(page), parseInt(pageSize));
    } else {
      // 获取用户列表
      result = await User.getUserList(parseInt(page), parseInt(pageSize), filters);
    }
    
    res.json({
      success: true,
      data: result
    });
  });
  
  // 获取指定用户信息
  getUserById = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const requesterId = req.user.id;
    const requesterRole = req.user.role;
    
    // 检查权限：只能查看自己的信息，或管理员可以查看所有用户
    if (parseInt(id) !== requesterId && requesterRole !== 'admin') {
      throw new AppError('权限不足', 403, 'INSUFFICIENT_PERMISSIONS');
    }
    
    const user = await User.getUserStats(id);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    res.json({
      success: true,
      data: {
        user
      }
    });
  });
  
  // 禁用用户（管理员功能）
  banUser = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { reason } = req.body;
    const adminId = req.user.id;
    
    // 🔧 使用严格的字符串比较，避免类型转换问题
    if (id.toString() === adminId.toString()) {
      throw new AppError('不能禁用自己的账户', 400, 'CANNOT_BAN_SELF');
    }
    
    const user = await User.findById(id);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    if (user.status === 'banned') {
      throw new AppError('用户已被禁用', 400, 'USER_ALREADY_BANNED');
    }
    
    // 禁用用户
    await User.banUser(id, reason);
    
    // 记录管理员操作
    operationLogger.logAdminOperation(
      req, 
      'user_ban', 
      id, 
      `禁用用户: ${user.username}`,
      { reason, targetUserId: id }
    );
    
    res.json({
      success: true,
      message: '用户已被禁用'
    });
  });
  
  // 启用用户（管理员功能）
  unbanUser = asyncHandler(async (req, res) => {
    const { id } = req.params;
    
    const user = await User.findById(id);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    if (user.status !== 'banned') {
      throw new AppError('用户未被禁用', 400, 'USER_NOT_BANNED');
    }
    
    // 启用用户
    await User.unbanUser(id);
    
    // 记录管理员操作
    operationLogger.logAdminOperation(
      req, 
      'user_unban', 
      id, 
      `启用用户: ${user.username}`,
      { targetUserId: id }
    );
    
    res.json({
      success: true,
      message: '用户已被启用'
    });
  });
  
  // 修改用户角色（管理员功能）
  changeUserRole = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { role } = req.body;
    const adminId = req.user.id;
    
    if (parseInt(id) === adminId) {
      throw new AppError('不能修改自己的角色', 400, 'CANNOT_CHANGE_SELF_ROLE');
    }
    
    const validRoles = ['user', 'member', 'vip', 'admin'];
    if (!validRoles.includes(role)) {
      throw new AppError('无效的用户角色', 400, 'INVALID_ROLE');
    }
    
    const user = await User.findById(id);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    // 更新用户角色
    const updatedUser = await User.update(id, { role });
    
    // 清除用户缓存
    const cacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', id);
    await cache.del(cacheKey);
    
    // 记录管理员操作
    operationLogger.logAdminOperation(
      req, 
      'user_role_change', 
      id, 
      `修改用户角色: ${user.username} (${user.role} -> ${role})`,
      { 
        targetUserId: id,
        oldRole: user.role,
        newRole: role
      }
    );
    
    res.json({
      success: true,
      message: '用户角色修改成功',
      data: {
        user: updatedUser
      }
    });
  });
  
  // 获取用户统计信息
  getUserStats = asyncHandler(async (req, res) => {
    const statsResult = await User.query(`
      SELECT
        COUNT(*) as total_users,
        COUNT(CASE WHEN role = 'user' THEN 1 END) as regular_users,
        COUNT(CASE WHEN role = 'member' THEN 1 END) as members,
        COUNT(CASE WHEN role = 'vip' THEN 1 END) as vip_users,
        COUNT(CASE WHEN role = 'admin' THEN 1 END) as admins,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN status = 'banned' THEN 1 END) as banned_users,
        COUNT(CASE WHEN email_verified = true THEN 1 END) as verified_users,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_30d,
        COUNT(CASE WHEN last_login_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_users_7d
      FROM users
    `);
    const stats = statsResult[0];
    
    res.json({
      success: true,
      data: {
        stats: stats[0]
      }
    });
  });
  
  // 删除用户账户
  deleteAccount = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { password } = req.body;
    
    // 验证密码
    const user = await User.findById(userId);
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    const isValidPassword = await User.verifyPassword(password, user.password);
    if (!isValidPassword) {
      throw new AppError('密码错误', 400, 'INVALID_PASSWORD');
    }
    
    // 软删除用户（更改状态为deleted）
    await User.update(userId, { 
      status: 'deleted',
      deleted_at: new Date()
    });
    
    // 清除所有相关缓存
    const patterns = [
      `${CACHE_KEYS.USER}:*:${userId}`,
      `${CACHE_KEYS.SESSION}:*`
    ];
    
    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
    
    // 记录操作日志
    operationLogger.logUserOperation(
      req, 
      'account_delete', 
      userId, 
      '删除账户',
      { username: user.username }
    );
    
    res.json({
      success: true,
      message: '账户删除成功'
    });
  });

  /**
   * @description 获取当前登录用户的订单列表
   * @route GET /api/user/orders
   * @access Private
   */
  getUserOrders = asyncHandler(async (req, res, next) => {
    const userId = req.user.id;
    const { page, pageSize, type, status } = req.query;

    const options = {
      page: parseInt(page, 10) || 1,
      pageSize: parseInt(pageSize, 10) || 10,
      type: type,
      status: status,
    };

    const result = await Order.getUserOrders(userId, options);

    res.json({ success: true, data: result });
  });

  sendChangeEmailCode = asyncHandler(async (req, res) => {
    const { newEmail } = req.body;
    if (!newEmail) {
      throw new AppError('必须提供新邮箱地址', 400, 'NEW_EMAIL_REQUIRED');
    }
    await userService.sendChangeEmailCode(req.user.id, newEmail);
    res.json({ success: true, message: '验证码已发送' });
  });

  verifyChangeEmail = asyncHandler(async (req, res) => {
    const { newEmail, code } = req.body;
    if (!newEmail || !code) {
      throw new AppError('必须提供新邮箱和验证码', 400, 'EMAIL_AND_CODE_REQUIRED');
    }
    await userService.verifyChangeEmail(req.user.id, newEmail, code);
    res.json({ success: true, message: '邮箱更换成功' });
  });
}

const userControllerInstance = new UserController();

module.exports = userControllerInstance;
