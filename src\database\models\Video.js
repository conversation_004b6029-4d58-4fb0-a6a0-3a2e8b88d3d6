const BaseModel = require('../BaseModel');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');
const connectionManager = require('../ConnectionManager');

class Video extends BaseModel {
  constructor() {
    super('videos');
  }

  // 创建媒体记录（视频或音频）
  async createVideo(videoData) {
    const {
      userId,
      categoryId,
      mediaType = 'video',
      title,
      description,
      tags = [],
      visibility = 'public',
      price = 0,
      originalFilename,
      filePath,
      fileSize,
      duration = 0,
      resolution,
      format,
      // 音频专用字段
      bitrate,
      sampleRate,
      channels
    } = videoData;

    const mediaData = {
      user_id: userId,
      category_id: categoryId,
      media_type: mediaType,
      title,
      description,
      tags: JSON.stringify(tags),
      visibility,
      price,
      original_filename: originalFilename,
      file_path: filePath,
      file_size: fileSize,
      duration,
      format,
      status: 'uploading',
      view_count: 0,
      like_count: 0,
      comment_count: 0,
      download_count: 0
    };

    // 添加视频专用字段
    if (mediaType === 'video' && resolution) {
      mediaData.resolution = resolution;
    }

    // 添加音频专用字段
    if (mediaType === 'audio') {
      if (bitrate) mediaData.bitrate = bitrate;
      if (sampleRate) mediaData.sample_rate = sampleRate;
      if (channels) mediaData.channels = channels;
    }

    const videoId = await this.create(mediaData);
    if (!videoId) {
      throw new AppError('创建媒体记录失败', 500, 'MEDIA_CREATION_FAILED');
    }

    const newVideo = await this.findById(videoId);

    logger.info(`${mediaType === 'audio' ? '音频' : '视频'}记录创建成功: ${newVideo.id}`, { title, userId, mediaType });
    return newVideo;
  }

  // 更新媒体信息
  async updateVideo(videoId, updateData) {
    // 过滤允许更新的字段
    const allowedFields = [
      'title', 'description', 'tags', 'category_id',
      'visibility', 'price', 'thumbnail', 'processed_path',
      'hls_path', 'status', 'published_at', 'duration',
      'resolution', 'bitrate', 'sample_rate', 'channels',
      'file_path', 'url', 'thumbnail_url'
    ];

    const filteredData = {};
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        if (field === 'tags' && Array.isArray(updateData[field])) {
          filteredData[field] = JSON.stringify(updateData[field]);
        } else {
          filteredData[field] = updateData[field];
        }
      }
    }

    if (Object.keys(filteredData).length === 0) {
      throw new AppError('没有可更新的字段', 400, 'NO_UPDATE_FIELDS');
    }

    const updatedVideo = await this.update(videoId, filteredData);
    if (!updatedVideo) {
      throw new AppError('媒体不存在', 404, 'MEDIA_NOT_FOUND');
    }

    return updatedVideo;
  }

  // 获取媒体详情
  async getVideoDetails(videoId, userId = null) {
    const sql = `
      SELECT
        v.*,
        u.username AS uploader_username,
        u.nickname AS uploader_nickname,
        c.name AS category_name,
        ${userId ? `
          (SELECT COUNT(*) FROM likes WHERE target_id = v.id AND target_type = 'video' AND user_id = ?) > 0 AS is_liked,
          (SELECT COUNT(*) FROM favorites WHERE video_id = v.id AND user_id = ?) > 0 AS is_favorited
        ` : `
          0 AS is_liked,
          0 AS is_favorited
        `}
      FROM videos v
      LEFT JOIN users u ON v.user_id = u.id
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE v.id = ? AND v.status != 'deleted'
    `;

    const params = userId ? [userId, userId, videoId] : [videoId];

    const result = await this.query(sql, params);
    return result[0] || null;
  }

  // 获取媒体列表（重构为 getAllVideos）
  async getAllVideos(options = {}) {
    const {
      page = 1,
      limit = 10,
      sortBy = 'created_at',
      order = 'desc',
      filters = {}
    } = options;

    const {
      categoryId,
      userId,
      mediaType,
      visibility,
      status,
      keyword,
      tags
    } = filters;

    const params = [];
    let whereClauses = [];

    // 默认只查询已发布的、未删除的视频
    if (status && status !== 'all') {
      whereClauses.push("v.status = ?");
      params.push(status);
    } else if (!status) {
      whereClauses.push("v.status = ?");
      params.push('published');
    }
    // 始终排除已删除的
    whereClauses.push("v.status != 'deleted'");

    if (categoryId) {
      whereClauses.push('v.category_id = ?');
      params.push(categoryId);
    }

    if (userId) {
      whereClauses.push('v.user_id = ?');
      params.push(userId);
    }

    if (mediaType) {
      whereClauses.push('v.media_type = ?');
      params.push(mediaType);
    }

    if (visibility) {
      whereClauses.push('v.visibility = ?');
      params.push(visibility);
    }

    if (tags && tags.length > 0) {
      const tagConditions = tags.map(() => 'v.tags LIKE ?').join(' OR ');
      whereClauses.push(`(${tagConditions})`);
      tags.forEach(tag => params.push(`%"${tag}"%`));
    }

    if (keyword) {
      whereClauses.push('(v.title LIKE ? OR v.description LIKE ?)');
      const searchTerm = `%${keyword}%`;
      params.push(searchTerm, searchTerm);
    }

    const whereString = whereClauses.join(' AND ');

    // 最终修复：手动管理连接并使用 connection.query() 绕过预编译模式的兼容性问题
    const connection = await connectionManager.getMySQLConnection();
    try {
    let sql = `
      SELECT 
          SQL_CALC_FOUND_ROWS
          v.id, v.title, v.description, v.thumbnail_url, v.url, v.duration, v.status, 
          v.visibility, v.view_count, v.like_count, v.comment_count, 
          v.published_at, v.created_at, v.media_type,
          u.id AS uploader_id, u.nickname AS uploader_nickname,
          c.id AS category_id, c.name AS category_name
      FROM videos v
      LEFT JOIN users u ON v.user_id = u.id
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE ${whereString}
    `;

    const allowedSortFields = ['created_at', 'published_at', 'view_count', 'like_count', 'title', 'duration'];
    const sortField = allowedSortFields.includes(sortBy) ? `v.${sortBy}` : 'v.created_at';
      const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
      sql += ` ORDER BY ${sortField} ${sortOrder}`;

      const offset = (page - 1) * limit;
      sql += ` LIMIT ? OFFSET ?`;
      params.push(Number(limit), Number(offset));

      // 使用 .query() 而不是 .execute()
      const [videos] = await connection.query(sql, params);

      const [totalResult] = await connection.query('SELECT FOUND_ROWS() as total;');
      const total = totalResult[0]?.total || 0;

      // 适配前端，确保 thumbnail 字段存在
      const adaptedVideos = videos.map(video => ({
        ...video,
        thumbnail: video.thumbnail_url
      }));

      return { 
        videos: adaptedVideos, 
        page: Number(page), 
        limit: Number(limit), 
        total, 
        totalPages: Math.ceil(total / limit) 
      };
    } finally {
      if (connection) connection.release();
    }
  }

  // 增加播放次数
  async incrementViewCount(videoId, userId = null) {
    // 防止重复计数，同一用户1小时内只计算一次
    if (userId) {
      const recentView = await this.query(`
        SELECT id FROM watch_history
        WHERE video_id = ? AND user_id = ?
        AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
      `, [videoId, userId]);

      if (recentView.length > 0) {
        return false; // 已经计算过了
      }
    }

    // 增加播放次数
    await this.query('UPDATE videos SET view_count = view_count + 1 WHERE id = ?', [videoId]);

    // 记录播放历史
    if (userId) {
      await this.query(`
        INSERT INTO watch_history (user_id, video_id, created_at)
        VALUES (?, ?, NOW())
        ON DUPLICATE KEY UPDATE updated_at = NOW()
      `, [userId, videoId]);
    }

    return true;
  }

  // 增加点赞数
  async incrementLikeCount(videoId) {
    await this.query('UPDATE videos SET like_count = like_count + 1 WHERE id = ?', [videoId]);
  }

  // 减少点赞数
  async decrementLikeCount(videoId) {
    await this.query('UPDATE videos SET like_count = GREATEST(like_count - 1, 0) WHERE id = ?', [videoId]);
  }

  // 增加评论数
  async incrementCommentCount(videoId) {
    await this.query('UPDATE videos SET comment_count = comment_count + 1 WHERE id = ?', [videoId]);
  }

  // 减少评论数
  async decrementCommentCount(videoId) {
    await this.query('UPDATE videos SET comment_count = GREATEST(comment_count - 1, 0) WHERE id = ?', [videoId]);
  }

  // 获取热门视频
  async getPopularVideos(limit = 10, timeRange = '7d') {
    let timeCondition = '';
    switch (timeRange) {
      case '1d':
        timeCondition = 'AND v.created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)';
        break;
      case '7d':
        timeCondition = 'AND v.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case '30d':
        timeCondition = 'AND v.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      default:
        timeCondition = '';
    }

    const sql = `
      SELECT 
        v.id,
        v.title,
        v.thumbnail,
        v.duration,
        v.view_count,
        v.like_count,
        v.created_at,
        u.username,
        u.nickname,
        c.name as category_name
      FROM videos v
      LEFT JOIN users u ON v.user_id = u.id
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE v.status = 'published' 
        AND v.visibility = 'public'
        ${timeCondition}
      ORDER BY (v.view_count * 0.7 + v.like_count * 0.3) DESC
      LIMIT ?
    `;

    return await this.query(sql, [limit]);
  }

  // 获取推荐视频
  async getRecommendedVideos(userId, limit = 10) {
    // 简单的推荐算法：基于用户观看历史的分类偏好
    const sql = `
      SELECT 
        v.id,
        v.title,
        v.thumbnail,
        v.duration,
        v.view_count,
        v.like_count,
        v.created_at,
        u.username,
        u.nickname,
        c.name as category_name
      FROM videos v
      LEFT JOIN users u ON v.user_id = u.id
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE v.status = 'published' 
        AND v.visibility IN ('public', 'member_only')
        AND v.user_id != ?
        AND v.category_id IN (
          SELECT DISTINCT v2.category_id 
          FROM watch_history wh
          JOIN videos v2 ON wh.video_id = v2.id
          WHERE wh.user_id = ?
          LIMIT 5
        )
      ORDER BY RAND()
      LIMIT ?
    `;

    return await this.query(sql, [userId, userId, limit]);
  }

  // 软删除视频
  async softDeleteVideo(videoId) {
    return await this.update(videoId, {
      status: 'deleted',
      deleted_at: new Date()
    });
  }

  // 硬删除视频（完全删除数据库记录）
  async hardDeleteVideo(videoId) {
    // 直接从连接管理器获取连接，避免上下文问题
    const connection = await connectionManager.getMySQLConnection();
    await connection.beginTransaction();

    try {
      // 先删除相关的评论
      await connection.execute('DELETE FROM comments WHERE video_id = ?', [videoId]);

      // 删除相关的点赞记录
      await connection.execute('DELETE FROM likes WHERE target_type = "video" AND target_id = ?', [videoId]);

      // 删除观看历史
      await connection.execute('DELETE FROM watch_history WHERE video_id = ?', [videoId]);

      // 删除收藏记录
      await connection.execute('DELETE FROM favorites WHERE video_id = ?', [videoId]);

      // 最后删除视频记录
      await connection.execute('DELETE FROM videos WHERE id = ?', [videoId]);

      await connection.commit();

      logger.info(`视频硬删除成功: ${videoId}`);
      return true;

    } catch (error) {
      await connection.rollback();
      logger.error(`视频硬删除失败: ${videoId}`, error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // 获取用户媒体统计
  async getUserVideoStats(userId) {
    const sql = `
      SELECT
        COUNT(*) as total_media,
        COUNT(CASE WHEN media_type = 'video' THEN 1 END) as total_videos,
        COUNT(CASE WHEN media_type = 'audio' THEN 1 END) as total_audios,
        COUNT(CASE WHEN status = 'published' THEN 1 END) as published_media,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_media,
        COUNT(CASE WHEN status = 'private' THEN 1 END) as private_media,
        SUM(view_count) as total_views,
        SUM(like_count) as total_likes,
        SUM(comment_count) as total_comments,
        AVG(view_count) as avg_views
      FROM videos
      WHERE user_id = ? AND status != 'deleted'
    `;

    const result = await this.query(sql, [userId]);
    return result[0];
  }

  // 创建音频记录的便捷方法
  async createAudio(audioData) {
    return await this.createVideo({
      ...audioData,
      mediaType: 'audio'
    });
  }

  // 获取音频列表的便捷方法
  async getAudioList(filters = {}, options = {}) {
    return await this.getAllVideos({
      ...filters,
      mediaType: 'audio'
    }, options);
  }

  // 获取视频列表的便捷方法（保持向后兼容）
  async getVideoOnlyList(filters = {}, options = {}) {
    return await this.getAllVideos({
      ...filters,
      mediaType: 'video'
    }, options);
  }
}

module.exports = new Video();
