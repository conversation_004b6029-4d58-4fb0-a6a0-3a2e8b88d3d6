const Joi = require('joi');
const { validate } = require('../../../middleware/validation');

// 用户资料更新验证规则
const profileUpdateSchema = Joi.object({
  nickname: Joi.string().max(100).optional(),
  phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional(),
  gender: Joi.string().valid('male', 'female', 'other').optional(),
  birthday: Joi.date().max('now').optional(),
  bio: Joi.string().max(500).optional()
});

// 用户偏好设置验证规则
const preferencesSchema = Joi.object({
  language: Joi.string().valid('zh-CN', 'en-US', 'ja-JP').optional(),
  theme: Joi.string().valid('light', 'dark', 'auto').optional(),
  notifications: Joi.object({
    email: Joi.boolean().optional(),
    push: Joi.boolean().optional(),
    comments: Joi.boolean().optional(),
    likes: Joi.boolean().optional(),
    follows: Joi.boolean().optional()
  }).optional(),
  privacy: Joi.object({
    profileVisible: Joi.boolean().optional(),
    showEmail: Joi.boolean().optional(),
    showPhone: Joi.boolean().optional()
  }).optional(),
  video: Joi.object({
    autoplay: Joi.boolean().optional(),
    quality: Joi.string().valid('auto', '360p', '480p', '720p', '1080p').optional(),
    subtitles: Joi.boolean().optional()
  }).optional()
});

// 用户搜索验证规则
const userSearchSchema = Joi.object({
  keyword: Joi.string().min(1).max(100).required(),
  page: Joi.number().integer().min(1).default(1),
  pageSize: Joi.number().integer().min(1).max(50).default(20),
  role: Joi.string().valid('user', 'member', 'vip', 'admin').optional(),
  status: Joi.string().valid('active', 'inactive', 'banned').optional()
});

// 用户角色修改验证规则
const roleChangeSchema = Joi.object({
  role: Joi.string().valid('user', 'member', 'vip', 'admin').required()
});

// 用户禁用验证规则
const banUserSchema = Joi.object({
  reason: Joi.string().max(500).optional()
});

// 账户删除验证规则
const deleteAccountSchema = Joi.object({
  password: Joi.string().required(),
  confirmation: Joi.string().valid('DELETE_MY_ACCOUNT').required()
});

// 关注操作验证规则
const followSchema = Joi.object({
  targetUserId: Joi.number().integer().positive().required()
});

// 验证中间件
const userValidation = {
  validateProfileUpdate: validate(profileUpdateSchema),
  validatePreferences: validate(preferencesSchema),
  validateUserSearch: validate(userSearchSchema, 'query'),
  validateRoleChange: validate(roleChangeSchema),
  validateBanUser: validate(banUserSchema),
  validateDeleteAccount: validate(deleteAccountSchema),
  validateFollow: validate(followSchema)
};

module.exports = userValidation;
