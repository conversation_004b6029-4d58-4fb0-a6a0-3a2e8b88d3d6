const fs = require('fs').promises;
const path = require('path');
const mysql = require('mysql2/promise');
const logger = require('../utils/logger');

// 数据库初始化配置
const initConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  charset: 'utf8mb4',
  multipleStatements: true
};

// 读取SQL文件
async function readSQLFile(filename) {
  try {
    const filePath = path.join(__dirname, filename);
    const content = await fs.readFile(filePath, 'utf8');
    return content;
  } catch (error) {
    logger.error(`读取SQL文件失败: ${filename}`, error);
    throw error;
  }
}

// 执行SQL语句
async function executeSQLStatements(connection, sql) {
  try {
    // 分割SQL语句（以分号分隔）
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        const upperStatement = statement.toUpperCase();

        // 跳过USE语句，因为prepared statement不支持
        if (upperStatement.startsWith('USE ')) {
          logger.info(`跳过USE语句: ${statement.substring(0, 50)}...`);
          continue;
        }

        // 对于某些语句使用query而不是execute
        if (upperStatement.includes('CREATE DATABASE') || upperStatement.includes('DROP DATABASE')) {
          await connection.query(statement);
          logger.info(`执行SQL语句成功(query): ${statement.substring(0, 50)}...`);
        } else {
          await connection.execute(statement);
          logger.info(`执行SQL语句成功(execute): ${statement.substring(0, 50)}...`);
        }
      }
    }
  } catch (error) {
    logger.error('执行SQL语句失败:', error);
    throw error;
  }
}

// 检查数据库是否存在
async function checkDatabaseExists(connection, dbName) {
  try {
    const [rows] = await connection.execute(
      'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?',
      [dbName]
    );
    return rows.length > 0;
  } catch (error) {
    logger.error('检查数据库是否存在失败:', error);
    return false;
  }
}

// 检查表是否存在
async function checkTableExists(connection, dbName, tableName) {
  try {
    const [rows] = await connection.execute(
      'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?',
      [dbName, tableName]
    );
    return rows.length > 0;
  } catch (error) {
    logger.error(`检查表是否存在失败: ${tableName}`, error);
    return false;
  }
}

// 将 runMigrations 移到顶层，使其可被重用
async function runMigrations(connection) {
  try {
    const migrationsPath = path.join(__dirname, 'migrations');
    const files = await fs.readdir(migrationsPath);
    const sqlFiles = files.filter(file => file.endsWith('.sql')).sort();

    if (sqlFiles.length > 0) {
      logger.info(`🔍 发现 ${sqlFiles.length} 个迁移文件，准备按顺序执行...`);
      for (const file of sqlFiles) {
        const filePath = path.join(migrationsPath, file);
        logger.info(`🚀 执行迁移: ${file}`);
        const sql = await fs.readFile(filePath, 'utf8');
        // 使用query执行，因为它更好地支持多语句
        await connection.query(sql);
        logger.info(`✅ 迁移成功: ${file}`);
      }
    } else {
      logger.info('📂 未发现需要执行的数据库迁移文件。');
    }
  } catch (error) {
    logger.error('❌ 执行数据库迁移失败:', error);
    throw error;
  }
}


// 初始化数据库
async function initializeDatabase() {
  let connection;
  
  try {
    logger.info('开始初始化数据库...');
    
    // 创建数据库连接（不指定数据库）
    connection = await mysql.createConnection(initConfig);
    logger.info('数据库连接创建成功');
    
    const dbName = process.env.DB_NAME || 'video_platform';
    
    // 检查数据库是否存在
    const dbExists = await checkDatabaseExists(connection, dbName);
    
    if (dbExists) {
      logger.info(`数据库 ${dbName} 已存在，准备检查其完整性...`);

      // 关闭当前连接，重新创建指定数据库的连接
      await connection.end();
      connection = await mysql.createConnection({
        ...initConfig,
        database: dbName
      });

      // 检查关键表是否存在
      const tablesExist = await checkTableExists(connection, dbName, 'users');
      if (!tablesExist) {
        logger.info('数据库存在但核心表(users)不完整，将重新创建所有表并填充数据...');
        await createSchemaAndSeedData(connection, dbName);
      } else {
        logger.info('核心表(users)已存在，将运行数据库迁移并检查管理员账户...');
        await runMigrations(connection);
        await createDefaultAdmin(connection);
      }
    } else {
      // 数据库不存在，创建数据库、表和种子数据
      logger.info(`数据库 ${dbName} 不存在，开始创建...`);
      await connection.query(`CREATE DATABASE \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
      logger.info(`数据库 ${dbName} 已创建`);

      // 使用新函数创建表和种子数据
      await createSchemaAndSeedData(connection, dbName);
    }
    
    logger.info('✅ 数据库初始化成功');
    return true;
    
  } catch (error) {
    logger.error('数据库初始化失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 创建表
async function createTables(connection) {
  try {
    const schemaSql = await readSQLFile('schema.sql');

    // 使用query执行整个SQL文件，利用 multipleStatements: true 配置
    // 这比手动分割语句更可靠
    await connection.query(schemaSql);

    logger.info('所有数据表已根据 schema.sql 创建成功');

  } catch (error) {
    logger.error('创建表失败:', error);
    throw error;
  }
}

// 插入初始数据
async function insertInitialData(connection) {
  try {
    logger.info('开始插入初始数据...');

    // 注意：连接已经指定了数据库，无需再使用USE语句

    // 插入默认分类
    const defaultCategories = [
      { name: '电影', slug: 'movies', description: '电影视频分类' },
      { name: '电视剧', slug: 'tv-series', description: '电视剧视频分类' },
      { name: '纪录片', slug: 'documentaries', description: '纪录片视频分类' },
      { name: '动漫', slug: 'anime', description: '动漫视频分类' },
      { name: '综艺', slug: 'variety', description: '综艺节目分类' },
      { name: '教育', slug: 'education', description: '教育视频分类' }
    ];
    
    for (const category of defaultCategories) {
      await connection.execute(
        'INSERT IGNORE INTO categories (name, slug, description) VALUES (?, ?, ?)',
        [category.name, category.slug, category.description]
      );
    }
    logger.info('默认分类插入完成');
    
    // 插入系统配置
    const defaultConfigs = [
      { key: 'site_name', value: '视频平台', group: 'site', description: '网站名称' },
      { key: 'site_logo', value: '', group: 'site', description: '网站Logo URL' },
      { key: 'site_favicon', value: '', group: 'site', description: '网站Favicon URL' },
      { key: 'site_contactEmail', value: '<EMAIL>', group: 'site', description: '联系邮箱' },
      { key: 'site_description', value: '一个现代化的视频平台', group: 'site', description: '网站描述' },
      
      { key: 'media_maxUploadSize', value: '500', group: 'media', description: '最大上传大小(MB)' },
      { key: 'media_allowedVideoFormats', value: 'mp4,mov,avi', group: 'media', description: '允许的视频格式' },
      
      { key: 'enableRegistration', value: 'true', group: 'user', description: '是否允许新用户注册' },
      { key: 'enableComments', value: 'true', group: 'user', description: '是否允许评论' },
      { key: 'requireEmailVerification', value: 'false', group: 'user', description: '注册时需要邮箱验证' },

      { key: 'smtpHost', value: '', group: 'email', description: 'SMTP服务器地址' },
      { key: 'smtpPort', value: '587', group: 'email', description: 'SMTP服务器端口' },
      { key: 'smtpUser', value: '', group: 'email', description: 'SMTP用户名' },
      { key: 'smtpPassword', value: '', group: 'email', description: 'SMTP密码' },
      { key: 'fromName', value: '视频平台', group: 'email', description: '发件人名称' },
      { key: 'fromEmail', value: '', group: 'email', description: '发件人邮箱' },
      { key: 'proxyEnabled', value: 'false', group: 'email', description: '是否启用代理' },
      { key: 'proxy', value: '', group: 'email', description: '代理服务器URL' },
      
      { key: 'sessionTimeout', value: '3600', group: 'security', description: '会话超时时间(秒)' },
      { key: 'maxLoginAttempts', value: '5', group: 'security', description: '最大登录尝试次数' },
      { key: 'passwordMinLength', value: '6', group: 'security', description: '最小密码长度' },
      { key: 'enableTwoFactor', value: 'false', group: 'security', description: '启用双因素认证' },
      { key: 'requireStrongPassword', value: 'false', group: 'security', description: '要求强密码' },
    ];

    for (const config of defaultConfigs) {
      await connection.execute(
        'INSERT IGNORE INTO system_configs (config_key, config_value, config_group, description) VALUES (?, ?, ?, ?)',
        [config.key, config.value, config.group, config.description]
      );
    }
    logger.info('系统配置插入完成');

    // 插入默认会员计划
    const defaultPlans = [
      { name: '基础会员', price: 19.90, duration_days: 30, features: '["无广告","1080P画质"]', is_active: true, priority: 10 },
      { name: 'VIP会员', price: 39.90, duration_days: 30, features: '["无广告","4K画质","独家内容"]', is_active: true, priority: 20 },
      { name: '年度VIP', price: 199.00, duration_days: 365, features: '["所有VIP特权","年度优惠"]', is_active: true, priority: 30 }
    ];
    for (const plan of defaultPlans) {
      await connection.execute(
        'INSERT IGNORE INTO membership_plans (name, price, duration_days, features, is_active, priority) VALUES (?, ?, ?, ?, ?, ?)',
        [plan.name, plan.price, plan.duration_days, plan.features, plan.is_active, plan.priority]
      );
    }
    logger.info('默认会员计划插入完成');

    // 创建默认管理员账户
    await createDefaultAdmin(connection);

    // 插入其他种子数据，例如支付网关
    try {
      const gatewaySqlPath = path.resolve(__dirname, '../../scripts/insert-tron-gateway.sql');
      const gatewaySql = await fs.readFile(gatewaySqlPath, 'utf8');
      await connection.query(gatewaySql);
      logger.info('默认支付网关数据插入完成');
    } catch (error) {
      logger.warn('无法插入默认支付网关数据 (可能文件不存在)，跳过此步骤:', error.message);
    }

    logger.info('初始数据插入完成');
    
  } catch (error) {
    logger.error('插入初始数据失败:', error);
    throw error;
  }
}

// 创建默认管理员账户
async function createDefaultAdmin(connection) {
  try {
    const bcrypt = require('bcryptjs');

    // 检查是否已存在管理员账户
    const [existingAdmin] = await connection.execute(
      'SELECT id FROM users WHERE role = ? LIMIT 1',
      ['admin']
    );

    if (existingAdmin.length > 0) {
      logger.info('管理员账户已存在，跳过创建');
      return;
    }

    // 默认管理员信息
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'Admin123456!';
    const adminUsername = process.env.ADMIN_USERNAME || 'admin';

    // 加密密码
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(adminPassword, saltRounds);

    // 插入管理员账户
    await connection.execute(
      `INSERT INTO users (
        email, password, username, nickname, role, status, email_verified, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        adminEmail,
        hashedPassword,
        adminUsername,
        '系统管理员',
        'admin',
        'active',
        true
      ]
    );

    logger.info('✅ 默认管理员账户创建成功');
    logger.info(`📧 管理员邮箱: ${adminEmail}`);
    logger.info(`👤 管理员用户名: ${adminUsername}`);
    logger.info(`🔑 管理员密码: ${adminPassword}`);
    logger.warn('⚠️  请在首次登录后立即修改管理员密码！');

  } catch (error) {
    logger.error('创建默认管理员账户失败:', error);
    // 不抛出错误，避免影响整个初始化过程
  }
}

// 重置数据库（危险操作）
async function resetDatabase() {
  let connection;
  const dbName = process.env.DB_NAME || 'video_platform';
  
  try {
    logger.warn(`开始重置数据库: ${dbName}`);
    connection = await mysql.createConnection(initConfig);
    
    // 删除并重新创建数据库
    await connection.query(`DROP DATABASE IF EXISTS \`${dbName}\``);
    logger.info(`数据库 ${dbName} 已删除`);
    await connection.query(`CREATE DATABASE \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    logger.info(`数据库 ${dbName} 已创建`);

    await connection.changeUser({ database: dbName });
    logger.info(`已切换到数据库: ${dbName}`);

    // 创建表、填充种子数据，然后运行迁移
    await createSchemaAndSeedData(connection, dbName);
    
    logger.info('✅ 数据库重置成功完成');
    return true;

  } catch (error) {
    logger.error(`❌ 数据库重置失败:`, error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info('数据库连接已关闭');
    }
  }
}

/**
 * Helper function to create schema and seed data within a transaction
 */
async function createSchemaAndSeedData(connection, dbName) {
  try {
    await connection.beginTransaction();
    logger.info('事务已开始');

    // 创建表
    await createTables(connection);
    
    // 插入初始数据
    await insertInitialData(connection);
    
    await connection.commit();
    logger.info('事务已成功提交');

  } catch (error) {
    logger.error('Schema创建或数据填充失败，回滚事务', error.message);
    await connection.rollback();
    logger.warn('事务已回滚');
    throw error; // Re-throw the error to be caught by the caller
  }
}

module.exports = {
  initializeDatabase,
  resetDatabase,
  checkDatabaseExists,
};
