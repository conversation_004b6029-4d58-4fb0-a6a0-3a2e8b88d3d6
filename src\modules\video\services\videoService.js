const path = require('path');
const fs = require('fs').promises;
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const Video = require('../../../database/models/Video');
const fileService = require('../../../services/fileService');
const videoProcessingService = require('../../../services/videoProcessingService');
const { toAbsolutePath } = require('../../../utils/pathResolver');
const axios = require('axios');

class VideoService {
  constructor() {
    this.processingQueue = new Map(); // 简单的处理队列
  }

  // 处理视频
  async processVideo(videoId, filePath) {
    try {
      logger.info(`开始处理视频: ${videoId}`);
      const video = await Video.findById(videoId);
      if (!video) {
        logger.error(`视频处理失败: 未找到ID为 ${videoId} 的视频`);
        return;
      }

      const inputPath = path.join(process.cwd(), 'uploads', video.file_path);
      const videoInfo = await this.getVideoInfo(inputPath);

      await Video.updateVideo(videoId, {
        duration: videoInfo.duration,
        resolution: videoInfo.resolution,
        status: 'processing', // 在处理开始时更新状态
      });

      // 模拟视频处理（例如转码、生成缩略图等）
      // 在实际应用中，这里应该是调用 ffmpeg 等工具进行实际处理
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟耗时

      await Video.updateVideo(videoId, {
        status: 'published',
        published_at: new Date(),
      });

      logger.info(`视频处理成功: ${videoId}`);
    } catch (error) {
      logger.error(`视频处理失败: ${videoId}`, error);
      // 更新视频状态为私有（处理失败时）
      await Video.updateVideo(videoId, {
        status: 'private',
        updated_at: new Date(),
      });
    }
  }

  // 获取视频信息（简化版）
  async getVideoInfo(filePath) {
    try {
      const stats = await fs.stat(filePath);
      
      // 这里应该使用FFmpeg获取视频信息
      // 暂时返回模拟数据
      return {
        duration: 120, // 2分钟
        resolution: '1920x1080',
        bitrate: 2000,
        fps: 30,
        size: stats.size
      };
    } catch (error) {
      logger.error('获取视频信息失败:', error);
      return {
        duration: 0,
        resolution: '未知',
        bitrate: 0,
        fps: 0,
        size: 0
      };
    }
  }

  // 生成视频缩略图
  async generateThumbnail(videoId, videoPath) {
    try {
      // 使用文件服务生成缩略图
      const thumbnailPath = await fileService.processVideoThumbnail(videoPath, videoId);
      return thumbnailPath;
    } catch (error) {
      logger.error(`生成缩略图失败: ${videoId}`, error);
      return null;
    }
  }

  // 删除视频文件
  async deleteVideoFiles(video) {
    if (!video) return { deletedCount: 0, errorCount: 0 };

    let deletedCount = 0;
    let errorCount = 0;

    // 优先处理新的、基于ID的目录结构
    try {
      // 从数据库中获取的 file_path 是相对路径，需要解析
      if (video.file_path) {
        const relativeDir = path.dirname(video.file_path);
        const absoluteDir = toAbsolutePath(relativeDir);

        if (require('fs').existsSync(absoluteDir)) {
          await fs.rm(absoluteDir, { recursive: true, force: true });
          logger.info(`成功删除视频目录: ${absoluteDir}`);
          deletedCount++;
          // 既然整个目录都删了，直接返回成功即可
          return { deletedCount, errorCount };
        }
      }
    } catch (dirError) {
      logger.error(`删除视频目录 ${video.id} 失败:`, dirError);
      errorCount++;
    }

    // --- 向下兼容旧的、分散的文件结构 ---
    const legacyFilesToDelete = new Set();
    if (video.file_path) legacyFilesToDelete.add(video.file_path);
    if (video.thumbnail_url) {
      // thumbnail_url 是一个 web url, e.g. /uploads/..., 我们需要移除第一个'/'
      const relativePath = video.thumbnail_url.startsWith('/') ? video.thumbnail_url.substring(1) : video.thumbnail_url;
      legacyFilesToDelete.add(relativePath);
    }
    if (video.processed_path) legacyFilesToDelete.add(video.processed_path);
    if (video.hls_path) legacyFilesToDelete.add(video.hls_path);

    for (const relativeFilePath of legacyFilesToDelete) {
      if (!relativeFilePath) continue;
      try {
        const absoluteFilePath = toAbsolutePath(relativeFilePath);
        await fs.unlink(absoluteFilePath);
        logger.info(`成功删除旧文件: ${absoluteFilePath}`);
        deletedCount++;
      } catch (error) {
        if (error.code !== 'ENOENT') {
          logger.warn(`删除旧文件失败: ${relativeFilePath}`, error);
          errorCount++;
        }
      }
    }

    logger.info(`旧文件删除完成: 成功删除 ${deletedCount} 个文件，${errorCount} 个错误`);
    return { deletedCount, errorCount };
  }

  // 搜索视频
  async searchVideos(searchParams) {
    const {
      keyword,
      page = 1,
      pageSize = 20,
      categoryId,
      sortBy = 'relevance'
    } = searchParams;

    // 构建缓存键
    const cacheKey = cache.generateKey(
      CACHE_KEYS.SEARCH,
      'videos',
      keyword,
      page,
      pageSize,
      categoryId || 'all',
      sortBy
    );

    let result = await cache.get(cacheKey);

    if (!result) {
      const filters = {
        keyword,
        categoryId,
        visibility: 'public',
        status: 'published'
      };

      const options = {
        page,
        pageSize,
        sortBy: this.mapSortBy(sortBy),
        sortOrder: 'DESC'
      };

      result = await Video.getVideoList(filters, options);
      
      // 缓存搜索结果
      await cache.set(cacheKey, result, 300); // 5分钟缓存
    }

    return result;
  }

  // 映射排序字段
  mapSortBy(sortBy) {
    const sortMapping = {
      'relevance': 'view_count',
      'newest': 'created_at',
      'oldest': 'created_at',
      'popular': 'view_count',
      'liked': 'like_count',
      'duration': 'duration'
    };

    return sortMapping[sortBy] || 'created_at';
  }

  // 获取视频分析数据
  async getVideoAnalytics(videoId) {
    const cacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'analytics', videoId);
    let analytics = await cache.get(cacheKey);

    if (!analytics) {
      // 获取基础统计
      const video = await Video.findById(videoId);
      if (!video) {
        throw new Error('视频不存在');
      }

      // 获取观看历史统计
      const viewStats = await this.getViewStats(videoId);
      
      // 获取用户互动统计
      const interactionStats = await this.getInteractionStats(videoId);

      analytics = {
        basic: {
          views: video.view_count,
          likes: video.like_count,
          comments: video.comment_count,
          downloads: video.download_count
        },
        views: viewStats,
        interactions: interactionStats,
        performance: this.calculatePerformanceMetrics(video)
      };

      await cache.set(cacheKey, analytics, 1800); // 30分钟缓存
    }

    return analytics;
  }

  // 获取观看统计
  async getViewStats(videoId) {
    const sql = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as views
      FROM watch_history 
      WHERE video_id = ? 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    const dailyViews = await Video.query(sql, [videoId]);

    return {
      daily: dailyViews,
      total: dailyViews.reduce((sum, day) => sum + day.views, 0)
    };
  }

  // 获取互动统计
  async getInteractionStats(videoId) {
    const sql = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as interactions
      FROM (
        SELECT created_at FROM likes WHERE target_id = ? AND target_type = 'video'
        UNION ALL
        SELECT created_at FROM comments WHERE video_id = ?
        UNION ALL
        SELECT created_at FROM favorites WHERE video_id = ?
      ) as interactions
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;

    const dailyInteractions = await Video.query(sql, [videoId, videoId, videoId]);

    return {
      daily: dailyInteractions,
      total: dailyInteractions.reduce((sum, day) => sum + day.interactions, 0)
    };
  }

  // 计算性能指标
  calculatePerformanceMetrics(video) {
    const daysSincePublished = video.published_at ? 
      Math.floor((Date.now() - new Date(video.published_at).getTime()) / (1000 * 60 * 60 * 24)) : 0;

    const avgViewsPerDay = daysSincePublished > 0 ? video.view_count / daysSincePublished : 0;
    const engagementRate = video.view_count > 0 ? 
      ((video.like_count + video.comment_count) / video.view_count * 100) : 0;

    return {
      avgViewsPerDay: Math.round(avgViewsPerDay * 100) / 100,
      engagementRate: Math.round(engagementRate * 100) / 100,
      likeRatio: video.view_count > 0 ? 
        Math.round((video.like_count / video.view_count * 100) * 100) / 100 : 0
    };
  }

  // 获取相关视频推荐
  async getRelatedVideos(videoId, limit = 5) {
    const cacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'related', videoId, limit);
    let relatedVideos = await cache.get(cacheKey);

    if (!relatedVideos) {
      const video = await Video.findById(videoId);
      if (!video) {
        return [];
      }

      // 基于分类和标签推荐相关视频
      const sql = `
        SELECT 
          v.id,
          v.title,
          v.thumbnail,
          v.duration,
          v.view_count,
          v.like_count,
          u.username,
          u.nickname
        FROM videos v
        LEFT JOIN users u ON v.user_id = u.id
        WHERE v.id != ? 
          AND v.status = 'published'
          AND v.visibility = 'public'
          AND (
            v.category_id = ? 
            OR v.tags LIKE ?
          )
        ORDER BY v.view_count DESC
        LIMIT ?
      `;

      // 简单的标签匹配
      const tagPattern = video.tags ? `%${JSON.parse(video.tags)[0] || ''}%` : '%';
      
      relatedVideos = await Video.query(sql, [
        videoId,
        video.category_id,
        tagPattern,
        limit
      ]);

      await cache.set(cacheKey, relatedVideos, 600); // 10分钟缓存
    }

    return relatedVideos;
  }

  // 获取视频播放URL
  async getVideoPlayUrl(videoId, quality = 'auto') {
    const video = await Video.findById(videoId);
    if (!video) {
      throw new Error('视频不存在');
    }

    // 根据质量返回不同的播放URL
    if (video.hls_path) {
      return {
        type: 'hls',
        url: `/uploads/videos/${path.basename(video.hls_path)}`
      };
    } else if (video.processed_path) {
      return {
        type: 'mp4',
        url: `/uploads/videos/${path.basename(video.processed_path)}`
      };
    } else {
      return {
        type: 'mp4',
        url: `/uploads/videos/${path.basename(video.file_path)}`
      };
    }
  }

  // 批量更新视频状态
  async batchUpdateVideoStatus(videoIds, status) {
    const connection = await Video.beginTransaction();
    
    try {
      for (const videoId of videoIds) {
        await Video.executeInTransaction(connection,
          'UPDATE videos SET status = ?, updated_at = NOW() WHERE id = ?',
          [status, videoId]
        );
      }
      
      await Video.commitTransaction(connection);
      
      // 清除相关缓存
      for (const videoId of videoIds) {
        await this.clearVideoCache(videoId);
      }
      
      logger.info(`批量更新视频状态: ${videoIds.length} 个视频更新为 ${status}`);
      return true;
      
    } catch (error) {
      await Video.rollbackTransaction(connection);
      throw error;
    }
  }

  // 获取视频处理状态
  async getVideoProcessingStatus(videoId) {
    return videoProcessingService.getProcessingStatus(videoId);
  }

  // 获取支持的视频格式
  getSupportedVideoFormats() {
    return videoProcessingService.getSupportedFormats();
  }

  // 验证视频格式
  isValidVideoFormat(filename) {
    return videoProcessingService.isValidVideoFormat(filename);
  }

  // 重新处理视频
  async reprocessVideo(videoId) {
    const video = await Video.findById(videoId);
    if (!video) {
      throw new Error('视频不存在');
    }

    if (!video.file_path) {
      throw new Error('原始视频文件不存在');
    }

    // 重置状态并重新处理
    await Video.updateVideo(videoId, {
      status: 'processing',
      processed_path: null,
      hls_path: null,
      thumbnail: null
    });

    return await this.processVideo(videoId, video.file_path);
  }

  // 清除视频缓存
  async clearVideoCache(videoId) {
    const patterns = [
      `${CACHE_KEYS.VIDEO}:details:${videoId}:*`,
      `${CACHE_KEYS.VIDEO}:analytics:${videoId}`,
      `${CACHE_KEYS.VIDEO}:related:${videoId}:*`
    ];

    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
  }

  async processUploadedVideo(video, finalStatus) {
    if (!finalStatus) {
      // 强制要求必须提供最终状态，防止意外发布
      logger.error('处理视频失败：必须为 processUploadedVideo 提供 finalStatus 参数。');
      // 可以选择抛出错误或将视频设置为一个安全状态，例如 'private'
      if (video && video.id) {
        await Video.updateVideo(video.id, { status: 'private' });
      }
      return; // 提前退出
    }

    if (!video || !video.id || !video.file_path) {
      logger.error('处理视频失败：无效的视频数据提供', { video });
      return;
    }

    const videoId = video.id;
    // tempPath现在是相对路径，例如 'uploads/videos/...'
    const tempPath = video.file_path;
    const absoluteTempPath = toAbsolutePath(tempPath);

    try {
      logger.info(`开始处理新上传的视频: ${videoId}`);
      await Video.updateVideo(videoId, { status: 'processing' });

      const videoInfo = await videoProcessingService.getVideoInfo(absoluteTempPath);
      const { duration, video: videoStream } = videoInfo;
      const resolution = videoStream ? `${videoStream.width}x${videoStream.height}` : '未知';

      // 2. 创建基于 日期/ID 的永久存储目录
      const now = new Date();
      const year = now.getFullYear().toString();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      
      const relativeDir = path.join('uploads', 'media', year, month, day, videoId.toString());
      const absoluteFinalDir = toAbsolutePath(relativeDir);
      await fs.mkdir(absoluteFinalDir, { recursive: true });

      // 3. 移动视频文件到永久目录
      const originalExtension = path.extname(video.original_filename);
      const finalVideoFileName = `source${originalExtension}`;
      
      const relativeVideoPath = path.join(relativeDir, finalVideoFileName);
      const absoluteFinalVideoPath = toAbsolutePath(relativeVideoPath);
      await fs.rename(absoluteTempPath, absoluteFinalVideoPath);

      // 4. 根据媒体类型生成缩略图
      const thumbnailFileName = 'thumbnail.jpg';
      const relativeThumbnailPath = path.join(relativeDir, thumbnailFileName);
      const absoluteThumbnailPath = toAbsolutePath(relativeThumbnailPath);

      if (video.media_type === 'video') {
        // 视频：从视频文件生成缩略图
        await videoProcessingService.generateThumbnail(absoluteFinalVideoPath, absoluteThumbnailPath, {
          size: '640x360'
        });
      } else if (video.media_type === 'audio') {
        // 音频：生成纯黑色的缩略图
        await fileService.generateBlankImage(absoluteThumbnailPath, {
          width: 640,
          height: 360,
          color: '#000000',
          format: 'jpeg'
        });
      }
      
      // 5. 准备要更新到数据库的数据
      const publicVideoUrl = `/${relativeVideoPath.replace(/\\/g, '/')}`;
      const publicThumbnailUrl = `/${relativeThumbnailPath.replace(/\\/g, '/')}`;

      const updateData = {
        duration: Math.round(duration || 0),
        resolution,
        file_path: relativeVideoPath,
        url: publicVideoUrl,
        thumbnail_url: publicThumbnailUrl,
        visibility: video.visibility || 'public',
        status: finalStatus,
        published_at: finalStatus === 'published' ? new Date() : null,
      };

      // 6. 更新数据库
      // 添加调试日志
      logger.info(`准备更新视频 ${videoId} 状态:`, { 
        videoId, 
        finalStatus, 
        updateStatus: updateData.status 
      });

      await Video.updateVideo(videoId, updateData);
      
      // 验证更新后的状态
      const updatedVideo = await Video.findById(videoId);
      logger.info(`视频 ${videoId} 更新后的实际状态: ${updatedVideo?.status}`);

      logger.info(`视频处理成功: ${videoId}`);
      
    } catch (error) {
      logger.error(`处理视频 ${videoId} 时发生严重错误:`, error);
      await Video.updateVideo(videoId, { status: 'private' });
      // 清理临时文件时也使用绝对路径
      await fs.unlink(absoluteTempPath).catch(err => logger.warn(`清理临时文件失败 ${tempPath}:`, err));
    }
  }

  async processVideoFromUrl(video, url) {
    if (!video || !video.id) {
      logger.error('从URL处理视频失败：无效的视频数据', { video });
      return;
    }

    const videoId = video.id;
    const tempDir = toAbsolutePath('uploads/temp');
    await fs.mkdir(tempDir, { recursive: true });
    const tempFileName = `${videoId}_${Date.now()}${path.extname(new URL(url).pathname) || '.mp4'}`;
    const tempPath = path.join(tempDir, tempFileName);
    const writer = require('fs').createWriteStream(tempPath);

    let response;
    try {
      response = await axios({
        method: 'get',
        url: url,
        responseType: 'stream',
      });

      // 验证 Content-Type 和 Content-Length (示例)
      const contentType = response.headers['content-type'];
      const contentLength = parseInt(response.headers['content-length'], 10);

      if (!contentType || (!contentType.startsWith('video/') && !contentType.startsWith('audio/'))) {
        throw new Error(`无效的内容类型: ${contentType}`);
      }

      if (contentLength > 500 * 1024 * 1024) { // 500MB 限制
        throw new Error(`文件大小超出限制: ${contentLength} bytes`);
      }

      response.data.pipe(writer);

      await new Promise((resolve, reject) => {
        writer.on('finish', resolve);
        writer.on('error', reject);
      });

      logger.info(`视频 ${videoId} 从URL下载成功，临时路径: ${tempPath}`);

      // 下载成功后，更新数据库记录中的 filePath
      const relativeTempPath = path.relative(toAbsolutePath('.'), tempPath);
      await Video.updateVideo(videoId, { file_path: relativeTempPath.replace(/\\/g, '/') });

      // 将下载的视频传递给现有的处理流程
      // 注意：需要传递更新后的 video 对象
      const updatedVideo = await Video.findById(videoId);
      await this.processUploadedVideo(updatedVideo, 'pending_review');

    } catch (error) {
      logger.error(`从URL下载或处理视频 ${videoId} 失败:`, error);
      await Video.updateVideo(videoId, { status: 'rejected' }); // 更新状态为失败/拒绝
      // 清理下载失败的临时文件
      await fs.unlink(tempPath).catch(() => {});
    }
  }

  /**
   * 获取媒体列表
   * @param {object} filters - 筛选条件
   * @param {object} options - 分页和排序选项
   * @returns {Promise<object>} - 包含媒体列表和分页信息的对象
   */
  async getVideoList(filters = {}, options = {}) {
    const { page = 1, limit = 20, sortBy = 'created_at', order = 'DESC' } = options;
    const offset = (page - 1) * limit;

    let whereClauses = ['1=1'];
    const params = [];

    if (filters.userId) {
      whereClauses.push('v.user_id = ?');
      params.push(filters.userId);
    }
    if (filters.status) {
      whereClauses.push('v.status = ?');
      params.push(filters.status);
    }
    if (filters.category) {
      whereClauses.push('v.category_id = ?');
      params.push(filters.category);
    }
    // Add other filters as needed...

    const whereSql = whereClauses.join(' AND ');

    const dataSql = `
      SELECT v.*, u.username as uploader_username, u.nickname as uploader_nickname, u.avatar as uploader_avatar,
             c.name as category_name
      FROM videos v
      JOIN users u ON v.user_id = u.id
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE ${whereSql}
      ORDER BY v.${sortBy} ${order}
      LIMIT ? OFFSET ?
    `;
    params.push(limit, offset);

    const countSql = `
      SELECT COUNT(*) as total 
      FROM videos v 
      LEFT JOIN categories c ON v.category_id = c.id 
      WHERE ${whereSql}
    `;
    // Re-create params for count query, without limit and offset
    const countParams = params.slice(0, -2);
    
    const [videos, countResult] = await Promise.all([
      Video.query(dataSql, params),
      Video.query(countSql, countParams)
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      videos,
      pagination: {
        page: parseInt(page),
        pageSize: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      }
    };
  }

  /**
   * 获取单个媒体详情
   * @param {number} videoId - 媒体ID
   * @param {number|null} userId - 当前用户ID (用于检查收藏和点赞状态)
   * @returns {Promise<object|null>} - 媒体详情对象
   */
  async getVideoDetails(videoId, userId = null) {
    try {
      // 生成缓存键
      const cacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'details', videoId, userId || 'guest');

      // 尝试从缓存获取
        const cachedVideo = await cache.get(cacheKey);
        if (cachedVideo) {
          logger.debug(`视频详情缓存命中: ${videoId}`);
          return cachedVideo;
      }

      // 从数据库获取
      const video = await Video.getVideoDetails(videoId, userId);
      if (!video) {
        return null;
      }

      // 缓存结果
        await cache.set(cacheKey, video, 1800); // 30分钟缓存
        logger.debug(`视频详情已缓存: ${videoId}`);

      return video;
    } catch (error) {
      logger.error('获取视频详情失败:', error);
      throw error;
    }
  }
}

module.exports = new VideoService();
