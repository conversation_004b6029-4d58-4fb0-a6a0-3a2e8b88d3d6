const logger = require('./logger');

/**
 * 播放列表工具函数
 */
class PlaylistUtils {
  /**
   * 格式化播放列表数据
   * @param {Object} playlist - 原始播放列表数据
   * @returns {Object} 格式化后的播放列表数据
   */
  static formatPlaylistData(playlist) {
    if (!playlist) return null;

    return {
      id: playlist.id,
      name: playlist.name,
      description: playlist.description,
      isPublic: <PERSON><PERSON><PERSON>(playlist.is_public),
      playMode: playlist.play_mode || 'sequence',
      itemCount: playlist.item_count || (playlist.items ? playlist.items.length : 0),
      createdAt: playlist.created_at,
      updatedAt: playlist.updated_at,
      creator: playlist.creator_name ? {
        name: playlist.creator_name
      } : undefined,
      items: playlist.items ? playlist.items.map(item => PlaylistUtils.formatPlaylistItem(item)) : undefined
    };
  }

  /**
   * 格式化播放列表项目数据
   * @param {Object} item - 原始项目数据
   * @returns {Object} 格式化后的项目数据
   */
  static formatPlaylistItem(item) {
    if (!item) return null;

    return {
      id: item.id,
      videoId: item.video_id,
      position: item.position,
      addedAt: item.added_at,
      video: item.title ? {
        id: item.video_id,
        title: item.title,
        thumbnail: item.thumbnail_url,
        duration: item.duration,
        mediaType: item.media_type || 'video',
        url: item.url
      } : undefined
    };
  }

  /**
   * 格式化播放历史数据
   * @param {Object} history - 原始历史数据
   * @returns {Object} 格式化后的历史数据
   */
  static formatPlayHistoryData(history) {
    if (!history) return null;

    return {
      id: history.id,
      videoId: history.video_id,
      playedAt: history.played_at,
      watchDuration: history.watch_duration,
      videoDuration: history.video_duration,
      completed: Boolean(history.completed),
      completionRate: history.video_duration > 0 
        ? Math.round((history.watch_duration / history.video_duration) * 100) 
        : 0,
      video: history.title ? {
        id: history.video_id,
        title: history.title,
        thumbnail: history.thumbnail_url,
        duration: history.duration,
        mediaType: history.media_type || 'video'
      } : undefined
    };
  }

  /**
   * 验证播放模式
   * @param {string} playMode - 播放模式
   * @returns {boolean} 是否有效
   */
  static isValidPlayMode(playMode) {
    return ['sequence', 'loop', 'random'].includes(playMode);
  }

  /**
   * 获取播放模式描述
   * @param {string} playMode - 播放模式
   * @returns {string} 播放模式描述
   */
  static getPlayModeDescription(playMode) {
    const descriptions = {
      sequence: '顺序播放',
      loop: '循环播放',
      random: '随机播放'
    };
    return descriptions[playMode] || '未知模式';
  }

  /**
   * 计算播放列表总时长
   * @param {Array} items - 播放列表项目
   * @returns {number} 总时长（秒）
   */
  static calculateTotalDuration(items) {
    if (!Array.isArray(items)) return 0;
    
    return items.reduce((total, item) => {
      const duration = item.duration || item.video?.duration || 0;
      return total + duration;
    }, 0);
  }

  /**
   * 格式化时长显示
   * @param {number} seconds - 秒数
   * @returns {string} 格式化的时长字符串
   */
  static formatDuration(seconds) {
    if (!seconds || seconds < 0) return '00:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  }

  /**
   * 生成播放列表分享链接
   * @param {number} playlistId - 播放列表ID
   * @param {boolean} isPublic - 是否公开
   * @returns {string|null} 分享链接
   */
  static generateShareLink(playlistId, isPublic) {
    if (!isPublic) return null;
    
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    return `${baseUrl}/playlist/${playlistId}`;
  }

  /**
   * 验证播放列表权限
   * @param {Object} playlist - 播放列表对象
   * @param {number} userId - 用户ID
   * @returns {Object} 权限信息
   */
  static checkPlaylistPermissions(playlist, userId) {
    if (!playlist) {
      return { canView: false, canEdit: false, canDelete: false };
    }

    const isOwner = playlist.user_id === userId;
    const isPublic = Boolean(playlist.is_public);

    return {
      canView: isOwner || isPublic,
      canEdit: isOwner,
      canDelete: isOwner,
      isOwner,
      isPublic
    };
  }

  /**
   * 生成播放列表统计摘要
   * @param {Array} playlists - 播放列表数组
   * @returns {Object} 统计摘要
   */
  static generatePlaylistSummary(playlists) {
    if (!Array.isArray(playlists)) return null;

    const summary = {
      totalPlaylists: playlists.length,
      totalItems: 0,
      totalDuration: 0,
      publicPlaylists: 0,
      privatePlaylists: 0,
      playModes: {
        sequence: 0,
        loop: 0,
        random: 0
      },
      averageItemsPerPlaylist: 0
    };

    playlists.forEach(playlist => {
      const itemCount = playlist.item_count || (playlist.items ? playlist.items.length : 0);
      summary.totalItems += itemCount;
      
      if (playlist.items) {
        summary.totalDuration += this.calculateTotalDuration(playlist.items);
      }
      
      if (playlist.is_public) {
        summary.publicPlaylists++;
      } else {
        summary.privatePlaylists++;
      }
      
      const playMode = playlist.play_mode || 'sequence';
      if (summary.playModes[playMode] !== undefined) {
        summary.playModes[playMode]++;
      }
    });

    summary.averageItemsPerPlaylist = summary.totalPlaylists > 0 
      ? Math.round(summary.totalItems / summary.totalPlaylists * 100) / 100 
      : 0;

    return summary;
  }

  /**
   * 验证视频是否已在播放列表中
   * @param {Array} items - 播放列表项目
   * @param {number} videoId - 视频ID
   * @returns {boolean} 是否已存在
   */
  static isVideoInPlaylist(items, videoId) {
    if (!Array.isArray(items)) return false;
    return items.some(item => item.video_id === videoId || item.videoId === videoId);
  }

  /**
   * 获取下一个播放项目索引
   * @param {number} currentIndex - 当前索引
   * @param {number} totalItems - 总项目数
   * @param {string} playMode - 播放模式
   * @returns {number|null} 下一个索引，null表示播放结束
   */
  static getNextPlayIndex(currentIndex, totalItems, playMode) {
    if (totalItems === 0) return null;

    switch (playMode) {
      case 'sequence':
        return currentIndex + 1 < totalItems ? currentIndex + 1 : null;
      case 'loop':
        return (currentIndex + 1) % totalItems;
      case 'random':
        return Math.floor(Math.random() * totalItems);
      default:
        return null;
    }
  }

  /**
   * 获取上一个播放项目索引
   * @param {number} currentIndex - 当前索引
   * @param {number} totalItems - 总项目数
   * @param {string} playMode - 播放模式
   * @returns {number|null} 上一个索引
   */
  static getPreviousPlayIndex(currentIndex, totalItems, playMode) {
    if (totalItems === 0) return null;

    switch (playMode) {
      case 'sequence':
        return currentIndex > 0 ? currentIndex - 1 : null;
      case 'loop':
        return currentIndex > 0 ? currentIndex - 1 : totalItems - 1;
      case 'random':
        return Math.floor(Math.random() * totalItems);
      default:
        return null;
    }
  }

  /**
   * 记录操作日志
   * @param {string} action - 操作类型
   * @param {number} userId - 用户ID
   * @param {Object} details - 详细信息
   */
  static logPlaylistAction(action, userId, details = {}) {
    logger.info(`播放列表操作: ${action}`, {
      userId,
      action,
      ...details,
      timestamp: new Date().toISOString()
    });
  }
}

module.exports = PlaylistUtils;
