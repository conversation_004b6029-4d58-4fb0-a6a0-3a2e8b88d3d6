#!/usr/bin/env node

/**
 * 邮箱验证码系统修复测试脚本
 * 测试所有修复的功能点
 */

require('dotenv').config();
const VerificationCodeUtil = require('../src/utils/verificationCode');
const { cache, CACHE_KEYS } = require('../src/utils/cache');
const logger = require('../src/utils/logger');

class EmailVerificationTest {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.failedTests = 0;
  }

  /**
   * 记录测试结果
   */
  recordTest(testName, passed, message = '') {
    const result = {
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    if (passed) {
      this.passedTests++;
      console.log(`✅ ${testName}: ${message || 'PASSED'}`);
    } else {
      this.failedTests++;
      console.log(`❌ ${testName}: ${message || 'FAILED'}`);
    }
  }

  /**
   * 测试数字验证码生成
   */
  testNumericCodeGeneration() {
    try {
      const code = VerificationCodeUtil.generateNumericCode();
      
      // 检查长度
      if (code.length !== 6) {
        this.recordTest('数字验证码长度', false, `期望6位，实际${code.length}位`);
        return;
      }
      
      // 检查是否为纯数字
      if (!/^\d{6}$/.test(code)) {
        this.recordTest('数字验证码格式', false, `包含非数字字符: ${code}`);
        return;
      }
      
      // 检查范围
      const numCode = parseInt(code);
      if (numCode < 100000 || numCode > 999999) {
        this.recordTest('数字验证码范围', false, `超出范围: ${code}`);
        return;
      }
      
      this.recordTest('数字验证码生成', true, `生成验证码: ${code}`);
      
    } catch (error) {
      this.recordTest('数字验证码生成', false, `异常: ${error.message}`);
    }
  }

  /**
   * 测试安全令牌生成
   */
  testSecureTokenGeneration() {
    try {
      const token = VerificationCodeUtil.generateSecureToken(32);
      
      // 检查长度（32字节 = 64位十六进制）
      if (token.length !== 64) {
        this.recordTest('安全令牌长度', false, `期望64位，实际${token.length}位`);
        return;
      }
      
      // 检查是否为十六进制
      if (!/^[a-f0-9]{64}$/.test(token)) {
        this.recordTest('安全令牌格式', false, `非十六进制格式`);
        return;
      }
      
      this.recordTest('安全令牌生成', true, `生成令牌长度: ${token.length}`);
      
    } catch (error) {
      this.recordTest('安全令牌生成', false, `异常: ${error.message}`);
    }
  }

  /**
   * 测试验证码配置生成
   */
  testCodeConfigGeneration() {
    const testCases = [
      { type: 'email_change', expectedType: 'numeric', expectedTtl: 600 },
      { type: 'email_verification', expectedType: 'token', expectedTtl: 86400 },
      { type: 'password_reset', expectedType: 'token', expectedTtl: 3600 }
    ];

    testCases.forEach(testCase => {
      try {
        const config = VerificationCodeUtil.generateCodeConfig(testCase.type);
        
        if (config.type !== testCase.expectedType) {
          this.recordTest(`${testCase.type}配置类型`, false, 
            `期望${testCase.expectedType}，实际${config.type}`);
          return;
        }
        
        if (config.ttl !== testCase.expectedTtl) {
          this.recordTest(`${testCase.type}配置TTL`, false, 
            `期望${testCase.expectedTtl}，实际${config.ttl}`);
          return;
        }
        
        if (testCase.expectedType === 'numeric') {
          if (!/^\d{6}$/.test(config.code)) {
            this.recordTest(`${testCase.type}验证码格式`, false, 
              `期望6位数字，实际${config.code}`);
            return;
          }
        } else {
          if (config.code.length !== 64) {
            this.recordTest(`${testCase.type}令牌长度`, false, 
              `期望64位，实际${config.code.length}位`);
            return;
          }
        }
        
        this.recordTest(`${testCase.type}配置生成`, true, 
          `类型: ${config.type}, TTL: ${config.ttl}s`);
        
      } catch (error) {
        this.recordTest(`${testCase.type}配置生成`, false, `异常: ${error.message}`);
      }
    });
  }

  /**
   * 测试验证码验证功能
   */
  testCodeValidation() {
    // 测试数字验证码验证
    const validNumericCodes = ['123456', '000000', '999999'];
    const invalidNumericCodes = ['12345', '1234567', 'abc123', '12345a', ''];

    validNumericCodes.forEach(code => {
      const isValid = VerificationCodeUtil.isValidNumericCode(code);
      this.recordTest(`数字验证码验证-有效(${code})`, isValid, 
        isValid ? '验证通过' : '验证失败');
    });

    invalidNumericCodes.forEach(code => {
      const isValid = VerificationCodeUtil.isValidNumericCode(code);
      this.recordTest(`数字验证码验证-无效(${code || 'empty'})`, !isValid, 
        !isValid ? '正确拒绝' : '错误接受');
    });

    // 测试十六进制令牌验证
    const validTokens = ['a'.repeat(64), '1234567890abcdef'.repeat(4)];
    const invalidTokens = ['a'.repeat(63), 'g'.repeat(64), ''];

    validTokens.forEach(token => {
      const isValid = VerificationCodeUtil.isValidHexToken(token);
      this.recordTest(`十六进制令牌验证-有效`, isValid, 
        isValid ? '验证通过' : '验证失败');
    });

    invalidTokens.forEach(token => {
      const isValid = VerificationCodeUtil.isValidHexToken(token);
      this.recordTest(`十六进制令牌验证-无效`, !isValid, 
        !isValid ? '正确拒绝' : '错误接受');
    });
  }

  /**
   * 测试验证码比较功能
   */
  testCodeComparison() {
    const testCases = [
      { code1: '123456', code2: '123456', caseSensitive: false, expected: true },
      { code1: '123456', code2: '123457', caseSensitive: false, expected: false },
      { code1: 'abc123', code2: 'ABC123', caseSensitive: false, expected: true },
      { code1: 'abc123', code2: 'ABC123', caseSensitive: true, expected: false },
      { code1: '', code2: '123456', caseSensitive: false, expected: false },
      { code1: '123456', code2: '', caseSensitive: false, expected: false }
    ];

    testCases.forEach((testCase, index) => {
      const result = VerificationCodeUtil.compareCode(
        testCase.code1, 
        testCase.code2, 
        testCase.caseSensitive
      );
      
      const passed = result === testCase.expected;
      this.recordTest(`验证码比较-案例${index + 1}`, passed, 
        `${testCase.code1} vs ${testCase.code2} (区分大小写:${testCase.caseSensitive}) = ${result}`);
    });
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始邮箱验证码系统修复测试...\n');
    
    this.testNumericCodeGeneration();
    this.testSecureTokenGeneration();
    this.testCodeConfigGeneration();
    this.testCodeValidation();
    this.testCodeComparison();
    
    console.log('\n📊 测试结果汇总:');
    console.log(`总测试数: ${this.testResults.length}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.failedTests}`);
    console.log(`成功率: ${((this.passedTests / this.testResults.length) * 100).toFixed(2)}%`);
    
    if (this.failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(result => !result.passed)
        .forEach(result => {
          console.log(`  - ${result.test}: ${result.message}`);
        });
    }
    
    return this.failedTests === 0;
  }
}

// 运行测试
if (require.main === module) {
  const tester = new EmailVerificationTest();
  tester.runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试运行失败:', error);
      process.exit(1);
    });
}

module.exports = EmailVerificationTest;
