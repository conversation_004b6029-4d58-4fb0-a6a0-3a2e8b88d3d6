import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { jwtDecode } from 'jwt-decode';
import { getUserProfile } from '@/services/api';

interface User {
  id: string;
  username: string;
  nickname: string;
  email?: string;
  avatar?: string;
  role: string;
  balance?: number;
  // Add other user properties here
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  login: (newToken: string, triggerPlaylistSync?: () => Promise<void>) => void;
  logout: () => void;
  isLoading: boolean;
  refreshUser: () => Promise<void>;
  // refreshToken: () => Promise<void>; // 未来可用于刷新token
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [token, setToken] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshUser = useCallback(async () => {
    try {
      const response = await getUserProfile();
      const userData = response?.data?.data?.user || response?.data?.user || response?.data || null;
      if (userData) {
        setUser(userData);
      } else {
        // 如果获取失败但有有效token，至少保留token中的基本信息
        const storedToken = localStorage.getItem('token');
        if (storedToken) {
           const decoded = jwtDecode<User>(storedToken);
           setUser(decoded);
        } else {
          throw new Error('无法获取用户资料');
        }
      }
    } catch (error) {
      console.error('刷新用户资料失败:', error);
      // 获取用户资料失败时，可能是token失效，登出处理
      logout();
    }
  }, []);

  useEffect(() => {
    const initializeAuth = async () => {
    try {
      const storedToken = localStorage.getItem('token');
      if (storedToken) {
          const decoded = jwtDecode<{ exp: number }>(storedToken);
        if (decoded.exp * 1000 > Date.now()) {
          setToken(storedToken);
            await refreshUser(); // 使用新函数获取最新用户数据
        } else {
          localStorage.removeItem('token');
        }
      }
    } catch (error) {
      console.error("Failed to initialize auth state:", error);
      localStorage.removeItem('token');
    } finally {
      setIsLoading(false);
    }
    };
    initializeAuth();
  }, [refreshUser]);

  const login = useCallback(async (newToken: string, triggerPlaylistSync?: () => Promise<void>) => {
    try {
        localStorage.setItem('token', newToken);
        setToken(newToken);
        await refreshUser(); // 登录后立即获取最新用户数据

        // 登录成功后自动触发播放列表同步
        if (triggerPlaylistSync) {
          // 增加延迟时间并添加重试机制
          setTimeout(async () => {
            try {
              console.log('开始登录后播放列表同步...');
              await triggerPlaylistSync();
              console.log('登录后播放列表同步成功');
            } catch (error) {
              console.error('登录后播放列表同步失败，尝试重试:', error);
              // 重试一次
              setTimeout(async () => {
                try {
                  await triggerPlaylistSync();
                  console.log('登录后播放列表同步重试成功');
                } catch (retryError) {
                  console.error('登录后播放列表同步重试也失败:', retryError);
                }
              }, 1000);
            }
          }, 300); // 延迟300ms确保状态更新完成
        }
    } catch (error) {
        console.error("Failed to decode token on login:", error);
        // Handle invalid token if necessary
    }
  }, []);

  const logout = useCallback(() => {
    localStorage.removeItem('token');
    setToken(null);
    setUser(null);
  }, []);

  const value = {
    isAuthenticated: !!token,
    user,
    token,
    login,
    logout,
    isLoading,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 