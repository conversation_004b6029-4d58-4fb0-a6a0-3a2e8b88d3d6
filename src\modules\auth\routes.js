const express = require('express');
const router = express.Router();

// 导入控制器和中间件
const authController = require('./controllers/authController');
const { verifyToken, optionalAuth } = require('../../middleware/auth');
const { disabledRateLimiter } = require('../../middleware/rateLimiter');
const {
  validateUserRegister,
  validateUserLogin,
  validatePasswordChange,
  validatePasswordRequirements
} = require('../../middleware/validation');

// --- 公共路由 ---
// 获取公共设置
router.get('/public-settings', authController.getPublicSettings);


// 公开路由（无需认证）
router.post('/send-registration-code',
  disabledRateLimiter, // 可以添加更严格的速率限制
  authController.sendRegistrationCode
);

router.post('/register',
  validateUserRegister,
  validatePasswordRequirements,
  disabledRateLimiter,
  authController.register
);

router.post('/login',
  validateUserLogin,
  disabledRateLimiter,
  authController.login
);

router.post('/refresh-token',
  disabledRateLimiter,
  authController.refreshToken
);

// 获取密码要求配置（公开接口）
router.get('/password-requirements',
  authController.getPasswordRequirements
);

router.post('/forgot-password',
  disabledRateLimiter,
  authController.forgotPassword
);

router.post('/reset-password',
  disabledRateLimiter,
  authController.resetPassword
);

// 需要认证的路由
router.use(verifyToken);

router.post('/logout',
  authController.logout
);

router.get('/me',
  authController.getCurrentUser
);

router.post('/verify-email',
  disabledRateLimiter,
  authController.verifyEmail
);

router.post('/resend-verification',
  disabledRateLimiter,
  authController.resendVerification
);

router.post('/change-password',
  validatePasswordChange,
  validatePasswordRequirements,
  authController.changePassword
);

// 测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '认证模块测试接口',
    module: 'auth',
    user: req.user || null
  });
});

module.exports = router;
