import { useState, useEffect } from 'react';
import { UploadCloud, File, X, Music } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { uploadVideo, getCategories } from '@/lib/api';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useTranslation } from 'react-i18next';

interface Category {
  id: number;
  name: string;
}

export default function UserVideoUpload() {
  const { t } = useTranslation();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [categoryId, setCategoryId] = useState<string>('');
  const [visibility, setVisibility] = useState('public');
  const [price, setPrice] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const response = await getCategories();
        const categoriesData = response?.data?.data?.categories || response?.data?.categories || [];
        setCategories(Array.isArray(categoriesData) ? categoriesData : []);
      } catch (error) {
        console.error('获取分类失败:', error);
        toast({
          title: '获取分类失败',
          description: '无法加载视频分类，请稍后重试',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingCategories(false);
      }
    };
    fetchCategories();
  }, [toast]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file);
      setTitle(file.name.replace(/\.[^/.]+$/, ''));
      setUploadProgress(0);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast({ title: '错误', description: '请先选择一个文件', variant: 'destructive' });
      return;
    }
    if (!title.trim()) {
      toast({ title: '错误', description: '请输入视频标题', variant: 'destructive' });
      return;
    }
    if (visibility === 'paid' && (!price || parseFloat(price) <= 0)) {
      toast({ title: '错误', description: '请输入有效的价格', variant: 'destructive' });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('media', selectedFile);
    formData.append('title', title.trim());
    if (description.trim()) {
      formData.append('description', description.trim());
    }
    if (categoryId) {
      formData.append('categoryId', categoryId);
    }
    // Append new visibility and price data
    formData.append('visibility', visibility);
    if (visibility === 'paid') {
      formData.append('price', price);
    }

    try {
      await uploadVideo(formData, (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / (progressEvent.total ?? 1)
        );
        setUploadProgress(percentCompleted);
      });

      toast({
        title: t('form.uploadSuccess'),
        description: t('form.uploadSuccessMessage')
      });

      resetForm();
    } catch (error) {
      toast({
        title: t('form.uploadFailed'),
        description: t('form.uploadFailedMessage'),
        variant: 'destructive',
      });
      console.error(error);
    } finally {
      setIsUploading(false);
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setTitle('');
    setDescription('');
    setUploadProgress(0);
    setCategoryId('');
    // Reset new states
    setVisibility('public');
    setPrice('');
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">{t('page.upload.title')}</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('page.upload.workInfo')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">{t('form.titleRequired')}</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder={t('form.titlePlaceholder')}
                  disabled={isUploading}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="category">{t('form.categoryLabel')}</Label>
                <Select value={categoryId} onValueChange={setCategoryId} disabled={isUploading || isLoadingCategories}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder={isLoadingCategories ? t('form.categoryLoading') : t('form.categoryPlaceholder')} />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={String(category.id)}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="description">{t('video.description')}</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder={t('page.upload.workDescription')}
                  disabled={isUploading}
                  className="mt-1"
                  rows={4}
                />
              </div>

              {/* --- NEW VISIBILITY AND PRICE FIELDS START --- */}
              <div>
                <Label htmlFor="visibility">{t('page.upload.visibility')}</Label>
                <Select value={visibility} onValueChange={setVisibility} disabled={isUploading}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">{t('page.upload.public')}</SelectItem>
                    <SelectItem value="member_only">{t('page.upload.memberOnly')}</SelectItem>
                    <SelectItem value="paid">{t('page.upload.paid')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {visibility === 'paid' && (
                <div>
                  <Label htmlFor="price">价格 (元)</Label>
                  <Input
                    id="price"
                    type="number"
                    value={price}
                    onChange={(e) => setPrice(e.target.value)}
                    placeholder="例如: 9.9"
                    min="0.01"
                    step="0.01"
                    disabled={isUploading}
                    className="mt-1"
                  />
                </div>
              )}
              {/* --- NEW VISIBILITY AND PRICE FIELDS END --- */}
              
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>{t('page.upload.fileUpload')}</CardTitle>
            </CardHeader>
            <CardContent>
              <input
                type="file"
                id="file-upload"
                className="hidden"
                onChange={handleFileChange}
                accept="video/*,audio/mp3"
                disabled={isUploading}
              />

              {!selectedFile ? (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <UploadCloud className="mx-auto h-16 w-16 text-gray-400" />
                    <h3 className="mt-4 text-lg font-semibold">{t('page.upload.selectFile')}</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      {t('page.upload.maxSize')}
                    </p>
                  </label>
                  <Button
                    onClick={() => document.getElementById('file-upload')?.click()}
                    className="mt-4"
                    disabled={isUploading}
                  >
                    {t('page.upload.chooseFile')}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-4 border rounded-lg">
                    {selectedFile.type.startsWith('audio/') ?
                      <Music className="h-8 w-8 text-blue-500" /> :
                      <File className="h-8 w-8 text-green-500" />
                    }
                    <div className="flex-1">
                      <p className="font-medium">{selectedFile.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <button
                      onClick={() => setSelectedFile(null)}
                      disabled={isUploading}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>

                  {isUploading && (
                    <div className="space-y-2">
                      <Progress value={uploadProgress} />
                      <p className="text-sm text-center">{uploadProgress}%</p>
                    </div>
                  )}

                  <Button
                    onClick={handleUpload}
                    disabled={isUploading || !title.trim()}
                    className="w-full"
                    size="lg"
                  >
                    {isUploading ? t('form.submitting') : t('form.submitForReview')}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 