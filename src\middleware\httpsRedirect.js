/**
 * HTTPS重定向中间件
 * 自动将HTTP请求重定向到HTTPS
 */

const logger = require('../utils/logger');

/**
 * HTTPS重定向中间件
 */
const httpsRedirect = (options = {}) => {
  const {
    enabled = process.env.AUTO_REDIRECT_HTTPS === 'true',
    forceHttps = process.env.FORCE_HTTPS === 'true',
    httpsPort = process.env.HTTPS_PORT || 443,
    excludePaths = ['/health', '/api/payment/webhook'],
    trustProxy = true
  } = options;

  return (req, res, next) => {
    // 如果未启用重定向，直接继续
    if (!enabled && !forceHttps) {
      return next();
    }

    // 检查是否为排除路径
    if (excludePaths.some(path => req.path.startsWith(path))) {
      return next();
    }

    // 检查是否已经是HTTPS
    const isHttps = req.secure || 
                   req.headers['x-forwarded-proto'] === 'https' ||
                   req.headers['x-forwarded-ssl'] === 'on' ||
                   req.connection.encrypted;

    if (isHttps) {
      // 已经是HTTPS，添加安全头
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
      return next();
    }

    // 如果强制HTTPS，拒绝HTTP请求
    if (forceHttps) {
      logger.warn(`拒绝HTTP请求: ${req.method} ${req.url} from ${req.ip}`);
      return res.status(426).json({
        success: false,
        message: '此服务仅支持HTTPS连接',
        code: 'HTTPS_REQUIRED',
        details: {
          upgrade: 'HTTPS',
          httpsUrl: `https://${req.get('host')}${req.url}`
        }
      });
    }

    // 构建HTTPS URL
    let httpsUrl;
    const host = req.get('host');
    
    if (httpsPort === 443 || httpsPort === '443') {
      // 标准HTTPS端口，不需要显示端口号
      httpsUrl = `https://${host.split(':')[0]}${req.url}`;
    } else {
      // 自定义端口
      httpsUrl = `https://${host.split(':')[0]}:${httpsPort}${req.url}`;
    }

    // 记录重定向
    logger.info(`HTTP重定向到HTTPS: ${req.url} -> ${httpsUrl}`);

    // 执行重定向
    res.redirect(301, httpsUrl);
  };
};

/**
 * 安全头中间件
 * 为HTTPS连接添加额外的安全头
 */
const securityHeaders = (req, res, next) => {
  const isHttps = req.secure || 
                 req.headers['x-forwarded-proto'] === 'https' ||
                 req.headers['x-forwarded-ssl'] === 'on' ||
                 req.connection.encrypted;

  if (isHttps) {
    // HTTPS安全头
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
  }

  next();
};

/**
 * 检查HTTPS状态的中间件
 */
const httpsStatus = (req, res, next) => {
  const isHttps = req.secure || 
                 req.headers['x-forwarded-proto'] === 'https' ||
                 req.headers['x-forwarded-ssl'] === 'on' ||
                 req.connection.encrypted;

  // 在请求对象上添加HTTPS状态
  req.isHttps = isHttps;
  req.protocol = isHttps ? 'https' : 'http';
  
  next();
};

/**
 * 混合内容检测中间件
 * 检测并警告混合内容问题
 */
const mixedContentDetection = (req, res, next) => {
  if (req.isHttps) {
    // 检查响应中是否包含HTTP链接
    const originalSend = res.send;
    res.send = function(data) {
      if (typeof data === 'string' && data.includes('http://')) {
        logger.warn(`检测到混合内容: ${req.url} 包含HTTP链接`);
      }
      return originalSend.call(this, data);
    };
  }
  
  next();
};

/**
 * 获取协议信息的工具函数
 */
const getProtocolInfo = (req) => {
  const isHttps = req.secure || 
                 req.headers['x-forwarded-proto'] === 'https' ||
                 req.headers['x-forwarded-ssl'] === 'on' ||
                 req.connection.encrypted;

  return {
    isHttps,
    protocol: isHttps ? 'https' : 'http',
    port: req.socket.localPort,
    host: req.get('host'),
    fullUrl: `${isHttps ? 'https' : 'http'}://${req.get('host')}${req.url}`,
    headers: {
      'x-forwarded-proto': req.headers['x-forwarded-proto'],
      'x-forwarded-ssl': req.headers['x-forwarded-ssl'],
      'x-forwarded-for': req.headers['x-forwarded-for']
    }
  };
};

/**
 * HTTPS配置验证中间件
 * 用于调试和监控HTTPS配置
 */
const httpsDebug = (req, res, next) => {
  if (process.env.NODE_ENV === 'development' && req.path === '/debug/https') {
    const protocolInfo = getProtocolInfo(req);
    const sslStatus = require('../config/ssl').getSSLStatus();
    
    return res.json({
      success: true,
      data: {
        request: protocolInfo,
        ssl: sslStatus,
        environment: {
          ENABLE_HTTPS: process.env.ENABLE_HTTPS,
          FORCE_HTTPS: process.env.FORCE_HTTPS,
          AUTO_REDIRECT_HTTPS: process.env.AUTO_REDIRECT_HTTPS,
          HTTPS_PORT: process.env.HTTPS_PORT,
          NODE_ENV: process.env.NODE_ENV
        }
      }
    });
  }
  
  next();
};

module.exports = {
  httpsRedirect,
  securityHeaders,
  httpsStatus,
  mixedContentDetection,
  httpsDebug,
  getProtocolInfo
};
