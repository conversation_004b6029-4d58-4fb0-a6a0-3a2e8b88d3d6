import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import api from '@/services/api';

interface MembershipInfo {
  id?: number;
  status?: string;
  start_date?: string;
  end_date?: string;
  plan_name?: string;
  plan_id?: number;
  auto_renew?: boolean;
  features?: string[];
  max_video_uploads?: number;
  max_storage_gb?: number;
}

interface MembershipContextType {
  membershipInfo: MembershipInfo | null;
  membershipLevel: string;
  isMember: boolean;
  isVip: boolean;
  isLoading: boolean;
  error: string | null;
  refreshMembership: () => Promise<void>;
  updateMembershipAfterPayment: () => Promise<void>;
}

const MembershipContext = createContext<MembershipContextType | undefined>(undefined);

export const MembershipProvider = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, user } = useAuth();
  const [membershipInfo, setMembershipInfo] = useState<MembershipInfo | null>(null);
  const [membershipLevel, setMembershipLevel] = useState<string>('free');
  const [isMember, setIsMember] = useState<boolean>(false);
  const [isVip, setIsVip] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const refreshMembership = useCallback(async () => {
    if (!isAuthenticated) {
      setMembershipInfo(null);
      setMembershipLevel('free');
      setIsMember(false);
      setIsVip(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.get('/member/my-membership');

      if (response.data.success) {
        const { membership, level, isMember: memberStatus } = response.data.data;

        setMembershipInfo(membership);
        setMembershipLevel(level || 'free');
        setIsMember(memberStatus || false);
        setIsVip(false); // 简化版：不再区分VIP
      } else {
        throw new Error(response.data.message || '获取会员信息失败');
      }
    } catch (err: any) {
      console.error('获取会员信息失败:', err);
      setError(err.response?.data?.message || err.message || '获取会员信息失败');
      
      // 如果是401错误，说明token可能过期，重置状态
      if (err.response?.status === 401) {
        setMembershipInfo(null);
        setMembershipLevel('free');
        setIsMember(false);
        setIsVip(false);
      }
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  const updateMembershipAfterPayment = useCallback(async () => {
    // 支付成功后立即刷新会员状态
    await refreshMembership();
  }, [refreshMembership]);

  // 当用户登录状态变化时，刷新会员信息
  useEffect(() => {
    if (isAuthenticated) {
      refreshMembership();
    } else {
      setMembershipInfo(null);
      setMembershipLevel('free');
      setIsMember(false);
      setIsVip(false);
      setError(null);
    }
  }, [isAuthenticated, refreshMembership]);

  // 基于JWT token中的role进行快速判断（简化版）
  useEffect(() => {
    if (user?.role) {
      const quickIsMember = ['member', 'admin'].includes(user.role);

      // 如果还没有从API获取到详细信息，使用JWT中的role作为初始状态
      if (!membershipInfo && !isLoading) {
        setIsMember(quickIsMember);
        setIsVip(false); // 简化版：不再区分VIP
        setMembershipLevel(user.role === 'admin' ? 'admin' : user.role === 'member' ? 'member' : 'free');
      }
    }
  }, [user?.role, membershipInfo, isLoading]);

  const value = {
    membershipInfo,
    membershipLevel,
    isMember,
    isVip,
    isLoading,
    error,
    refreshMembership,
    updateMembershipAfterPayment,
  };

  return (
    <MembershipContext.Provider value={value}>
      {children}
    </MembershipContext.Provider>
  );
};

export const useMembership = () => {
  const context = useContext(MembershipContext);
  if (context === undefined) {
    throw new Error('useMembership must be used within a MembershipProvider');
  }
  return context;
};
