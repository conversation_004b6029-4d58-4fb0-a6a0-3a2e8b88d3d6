# 数据库更新说明

## 问题描述
Creem webhook 处理时出现数据库字段错误：`Unknown column 'payment_reference' in 'where clause'`

## 解决方案
已更新数据库 schema 和迁移脚本，添加了对 Creem 支付的支持。

## 更新内容

### 1. 更新了 `memberships` 表结构
新增字段：
- `plan_name` VARCHAR(255) - 会员计划名称
- `payment_reference` VARCHAR(255) - 支付参考号（Creem 订单ID等）
- `amount` DECIMAL(10,2) - 支付金额
- `currency` VARCHAR(3) - 货币类型

修改字段：
- `plan_id` - 改为可空，支持第三方支付

### 2. 创建了迁移文件
- `20250725_add_creem_support_to_memberships.sql`

### 3. 更新了主 schema 文件
- `src/database/schema.sql`

## 执行更新

运行以下命令重置数据库（会自动应用所有迁移）：

```bash
npm run db:reset
```

## 验证更新

更新完成后，Creem webhook 应该能正常处理支付完成事件并自动激活用户会员。

## 注意事项

⚠️ **重要**: `npm run db:reset` 会删除所有现有数据，请确保备份重要数据。

如果不想清空数据，可以手动执行迁移SQL：
```sql
-- 执行 src/database/migrations/20250725_add_creem_support_to_memberships.sql 中的内容
```