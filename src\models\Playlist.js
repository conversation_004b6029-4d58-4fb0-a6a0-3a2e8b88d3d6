const { mysql: db } = require('../config/database');

class Playlist {
  // 创建播放列表
  static async create(userId, name, description = null, isPublic = false, playMode = 'sequence') {
    const query = `
      INSERT INTO playlists (user_id, name, description, is_public, play_mode)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    try {
      const [result] = await db.execute(query, [userId, name, description, isPublic, playMode]);
      return {
        id: result.insertId,
        user_id: userId,
        name,
        description,
        is_public: isPublic,
        play_mode: playMode,
        created_at: new Date(),
        updated_at: new Date()
      };
    } catch (error) {
      console.error('创建播放列表失败:', error);
      throw error;
    }
  }

  // 获取用户的所有播放列表
  static async findByUserId(userId, includeItems = false) {
    const query = `
      SELECT p.*, 
             COUNT(pi.id) as item_count
      FROM playlists p
      LEFT JOIN playlist_items pi ON p.id = pi.playlist_id
      WHERE p.user_id = ?
      GROUP BY p.id
      ORDER BY p.updated_at DESC
    `;
    
    try {
      const [playlists] = await db.execute(query, [userId]);
      
      if (includeItems && playlists.length > 0) {
        // 获取每个播放列表的项目
        for (let playlist of playlists) {
          playlist.items = await this.getPlaylistItems(playlist.id);
        }
      }
      
      return playlists;
    } catch (error) {
      console.error('获取用户播放列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取播放列表
  static async findById(playlistId, userId = null) {
    let query = `
      SELECT p.*, u.nickname as creator_name
      FROM playlists p
      LEFT JOIN users u ON p.user_id = u.id
      WHERE p.id = ?
    `;
    const params = [playlistId];
    
    // 如果提供了用户ID，验证权限
    if (userId) {
      query += ` AND (p.user_id = ? OR p.is_public = TRUE)`;
      params.push(userId);
    } else {
      query += ` AND p.is_public = TRUE`;
    }
    
    try {
      const [playlists] = await db.execute(query, params);
      if (playlists.length === 0) {
        return null;
      }
      
      const playlist = playlists[0];
      playlist.items = await this.getPlaylistItems(playlistId);
      
      return playlist;
    } catch (error) {
      console.error('获取播放列表失败:', error);
      throw error;
    }
  }

  // 更新播放列表
  static async update(playlistId, userId, updates) {
    const allowedFields = ['name', 'description', 'is_public', 'play_mode'];
    const updateFields = [];
    const values = [];
    
    for (const [key, value] of Object.entries(updates)) {
      if (allowedFields.includes(key)) {
        updateFields.push(`${key} = ?`);
        values.push(value);
      }
    }
    
    if (updateFields.length === 0) {
      throw new Error('没有有效的更新字段');
    }
    
    values.push(playlistId, userId);
    
    const query = `
      UPDATE playlists 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND user_id = ?
    `;
    
    try {
      const [result] = await db.execute(query, values);
      if (result.affectedRows === 0) {
        throw new Error('播放列表不存在或无权限修改');
      }
      
      return await this.findById(playlistId, userId);
    } catch (error) {
      console.error('更新播放列表失败:', error);
      throw error;
    }
  }

  // 删除播放列表
  static async delete(playlistId, userId) {
    const query = `DELETE FROM playlists WHERE id = ? AND user_id = ?`;
    
    try {
      const [result] = await db.execute(query, [playlistId, userId]);
      if (result.affectedRows === 0) {
        throw new Error('播放列表不存在或无权限删除');
      }
      
      return true;
    } catch (error) {
      console.error('删除播放列表失败:', error);
      throw error;
    }
  }

  // 获取播放列表项目
  static async getPlaylistItems(playlistId) {
    const query = `
      SELECT pi.*, v.title, v.thumbnail_url, v.duration, v.media_type, v.url
      FROM playlist_items pi
      LEFT JOIN videos v ON pi.video_id = v.id
      WHERE pi.playlist_id = ?
      ORDER BY pi.position ASC, pi.added_at ASC
    `;
    
    try {
      const [items] = await db.execute(query, [playlistId]);
      return items;
    } catch (error) {
      console.error('获取播放列表项目失败:', error);
      throw error;
    }
  }

  // 添加项目到播放列表
  static async addItem(playlistId, videoId, userId, position = null) {
    // 验证播放列表权限
    const playlist = await this.findById(playlistId, userId);
    if (!playlist || playlist.user_id !== userId) {
      throw new Error('播放列表不存在或无权限');
    }
    
    // 如果没有指定位置，添加到末尾
    if (position === null) {
      const [maxPos] = await db.execute(
        'SELECT COALESCE(MAX(position), -1) + 1 as next_position FROM playlist_items WHERE playlist_id = ?',
        [playlistId]
      );
      position = maxPos[0].next_position;
    }
    
    const query = `
      INSERT INTO playlist_items (playlist_id, video_id, position)
      VALUES (?, ?, ?)
      ON DUPLICATE KEY UPDATE position = VALUES(position), added_at = CURRENT_TIMESTAMP
    `;
    
    try {
      await db.execute(query, [playlistId, videoId, position]);
      
      // 更新播放列表的更新时间
      await db.execute(
        'UPDATE playlists SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [playlistId]
      );
      
      return true;
    } catch (error) {
      console.error('添加播放列表项目失败:', error);
      throw error;
    }
  }

  // 从播放列表移除项目
  static async removeItem(playlistId, videoId, userId) {
    // 验证播放列表权限
    const playlist = await this.findById(playlistId, userId);
    if (!playlist || playlist.user_id !== userId) {
      throw new Error('播放列表不存在或无权限');
    }
    
    const query = `DELETE FROM playlist_items WHERE playlist_id = ? AND video_id = ?`;
    
    try {
      const [result] = await db.execute(query, [playlistId, videoId]);
      
      if (result.affectedRows > 0) {
        // 更新播放列表的更新时间
        await db.execute(
          'UPDATE playlists SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [playlistId]
        );
      }
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error('移除播放列表项目失败:', error);
      throw error;
    }
  }

  // 重新排序播放列表项目
  static async reorderItems(playlistId, userId, itemOrders) {
    // 验证播放列表权限
    const playlist = await this.findById(playlistId, userId);
    if (!playlist || playlist.user_id !== userId) {
      throw new Error('播放列表不存在或无权限');
    }
    
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 批量更新位置
      for (const { videoId, position } of itemOrders) {
        await connection.execute(
          'UPDATE playlist_items SET position = ? WHERE playlist_id = ? AND video_id = ?',
          [position, playlistId, videoId]
        );
      }
      
      // 更新播放列表的更新时间
      await connection.execute(
        'UPDATE playlists SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [playlistId]
      );
      
      await connection.commit();
      return true;
    } catch (error) {
      await connection.rollback();
      console.error('重新排序播放列表项目失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }
}

module.exports = Playlist;
