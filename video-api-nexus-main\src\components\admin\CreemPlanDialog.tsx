import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { CreemPlan, CreemPlanFormData, TaxCategory } from '@/types/creem';
import { adminApi } from '@/services/adminApi';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";

interface CreemPlanDialogProps {
  plan: CreemPlan | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreemPlanDialog: React.FC<CreemPlanDialogProps> = ({ plan, isOpen, onClose, onSuccess }) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<CreemPlanFormData>({
    name: '',
    description: '',
    price: '',
    currency: 'USD',
    returnUrl: '',
    paymentType: 'one_time',
    billing_period: 'every-month',
    tax_category: 'saas',
  });

  const isEditMode = plan !== null;

  useEffect(() => {
    if (plan) {
      setFormData({
        id: plan.id,
        name: plan.name,
        description: plan.description || '',
        price: String(plan.price),
        currency: plan.currency,
        paymentType: 'one_time', // 默认值
        tax_category: plan.tax_category || 'saas',
        duration_days: plan.duration_days ? String(plan.duration_days) : '',
      });
    } else {
      setFormData({
        name: '',
        description: '',
        price: '',
        currency: 'USD',
        returnUrl: '',
        paymentType: 'one_time',
        billing_period: 'every-month',
        tax_category: 'saas',
        duration_days: '',
      });
    }
  }, [plan, isOpen]);

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSelectChange = (name: keyof CreemPlanFormData, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 如果是订阅模式且更改了billing_period，自动更新duration_days
    if (name === 'billing_period' && formData.paymentType === 'subscription') {
      const newDurationDays = value === 'every-year' ? '365' : '31';
      setFormData(prev => ({ ...prev, duration_days: newDurationDays }));
    }
  };

  const handleTabChange = (value: string) => {
    if (value === 'one_time') {
      // 切换到单次付款，保留之前的值或设置为空
      setFormData(prev => ({ ...prev, paymentType: 'one_time' }));
    } else {
      // 切换到订阅，根据billing_period自动设置duration_days
      setFormData(prev => ({ 
        ...prev, 
        paymentType: 'subscription',
        // 默认为每月订阅，31天
        duration_days: prev.billing_period === 'every-year' ? '365' : '31'
      }));
    }
  };

  const handleSubmit = async () => {
    // 前端价格验证
    const price = parseFloat(formData.price);
    if (isNaN(price) || price < 1.00) {
      toast({
        title: '无效的价格',
        description: '价格必须是一个数字且不能低于1.00。',
        variant: 'destructive',
      });
      return;
    }

    // 会员天数验证
    let durationDays: number | undefined = undefined;
    
    if (formData.paymentType === 'one_time' && formData.duration_days) {
      // 单次付款模式：用户手动输入的天数
      durationDays = parseInt(formData.duration_days, 10);
      if (isNaN(durationDays) || durationDays <= 0) {
        toast({
          title: '无效的会员天数',
          description: '会员天数必须是一个正整数。',
          variant: 'destructive',
        });
        return;
      }
    } else if (formData.paymentType === 'subscription') {
      // 订阅模式：根据billing_period自动设置
      durationDays = formData.billing_period === 'every-year' ? 365 : 31;
    }

    try {
      const dataToSubmit = {
        name: formData.name,
        description: formData.description,
        price: price,
        currency: formData.currency,
        billing_type: formData.paymentType,
        billing_period: formData.paymentType === 'subscription' ? formData.billing_period : undefined,
        tax_category: formData.tax_category,
        default_success_url: formData.returnUrl,
        // 无论是单次付款还是订阅，都包含duration_days字段
        duration_days: durationDays,
      };

      if (isEditMode) {
        await adminApi.updateCreemPlan(plan!.id, dataToSubmit);
        toast({ title: '成功', description: '产品已更新。' });
      } else {
        await adminApi.createCreemPlan(dataToSubmit);
        toast({ title: '成功', description: '新产品已创建。' });
      }
      onSuccess();
      onClose();
    } catch (error: any) {
      toast({
        title: '操作失败',
        description: error.response?.data?.message || error.message || '发生未知错误',
        variant: 'destructive',
      });
    }
  };


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{isEditMode ? '编辑产品' : '创建新产品'}</DialogTitle>
          <DialogDescription>
            创建或修改您的Creem产品信息。
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 py-4">
          {/* 左侧：产品详情 */}
          <div className="space-y-4">
            <h3 className="font-semibold">产品详情</h3>
            <p className="text-sm text-muted-foreground">购买订阅时，最终用户可以看到这些内容</p>
            
            <div className="space-y-2">
              <Label htmlFor="name">名字</Label>
              <Input id="name" name="name" value={formData.name} onChange={handleFormChange} placeholder="测试会员" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="returnUrl">返回URL (自选)</Label>
              <Input id="returnUrl" name="returnUrl" value={formData.returnUrl} onChange={handleFormChange} placeholder="用户付款后将被重定向到的URL" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea id="description" name="description" value={formData.description} onChange={handleFormChange} placeholder="向客户显示的产品描述" rows={5} />
            </div>
          </div>

          {/* 右侧：付款详情 */}
          <div className="space-y-4">
            <h3 className="font-semibold">付款详情</h3>
            <p className="text-sm text-muted-foreground">这些是为您的产品收取的定价详细信息</p>

            <Tabs value={formData.paymentType} onValueChange={handleTabChange} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="one_time">单次付款</TabsTrigger>
                <TabsTrigger value="subscription">订阅</TabsTrigger>
              </TabsList>
              <TabsContent value="one_time" className="pt-4">
                <div className="space-y-2">
                  <Label htmlFor="duration_days">会员天数</Label>
                  <Input 
                    id="duration_days" 
                    name="duration_days" 
                    type="number" 
                    value={formData.duration_days || ''} 
                    onChange={handleFormChange} 
                    placeholder="30" 
                  />
                  <p className="text-xs text-muted-foreground">
                    设置会员有效期天数。此设置仅保存在本地系统，不会发送到Creem。
                  </p>
                </div>
              </TabsContent>
              <TabsContent value="subscription" className="pt-4">
                <div className="space-y-2">
                  <Label htmlFor="billing_period">订阅间隔</Label>
                  <Select name="billing_period" value={formData.billing_period} onValueChange={(value) => handleSelectChange('billing_period', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择间隔" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="every-month">每月</SelectItem>
                      <SelectItem value="every-year">每年</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>
            </Tabs>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="currency">货币</Label>
                <Select name="currency" value={formData.currency} onValueChange={(value) => handleSelectChange('currency', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择货币" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">美元</SelectItem>
                    <SelectItem value="CNY">人民币</SelectItem>
                    <SelectItem value="EUR">欧元</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">定价</Label>
                <Input id="price" name="price" type="number" value={formData.price} onChange={handleFormChange} placeholder="6.00" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tax_category">税类</Label>
              <Select name="tax_category" value={formData.tax_category} onValueChange={(value) => handleSelectChange('tax_category', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择税类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="saas">软件即服务 (SaaS)</SelectItem>
                  <SelectItem value="ebooks">电子书</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="tax_inclusive" className="flex-grow">价格含税</Label>
              <Switch id="tax_inclusive" />
            </div>

            <div className="space-y-2 pt-4">
                <Label>产品图片 (可选)</Label>
                <div className="flex items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-muted">
                    <div className="text-center">
                        <p className="text-sm text-muted-foreground">将您的文件拖放到此处或单击上传</p>
                    </div>
                </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button onClick={handleSubmit}>{isEditMode ? '保存更改' : '创建产品'}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreemPlanDialog; 