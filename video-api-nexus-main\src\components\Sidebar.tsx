
import React from 'react';
import { 
  Shield, 
  Users, 
  Play, 
  MessageCircle, 
  Crown, 
  CreditCard, 
  Settings,
  FileText,
  Code,
  Zap,
  BarChart3,
  Folder
} from 'lucide-react';

const Sidebar = ({ activeSection, setActiveSection }) => {
  const sections = [
    {
      id: 'overview',
      title: '概览',
      icon: FileText,
      subsections: [
        { id: 'intro', title: '介绍' },
        { id: 'authentication', title: '认证说明' },
        { id: 'response-format', title: '响应格式' },
        { id: 'error-codes', title: '错误代码' },
        { id: 'rate-limiting', title: '限流说明' }
      ]
    },
    {
      id: 'auth',
      title: '认证模块',
      icon: Shield,
      count: 11,
      subsections: [
        { id: 'auth-register', title: '用户注册' },
        { id: 'auth-login', title: '用户登录' },
        { id: 'auth-logout', title: '用户登出' },
        { id: 'auth-refresh', title: '刷新Token' },
        { id: 'auth-me', title: '获取当前用户' },
        { id: 'auth-change-password', title: '修改密码' },
        { id: 'auth-forgot-password', title: '忘记密码' },
        { id: 'auth-reset-password', title: '重置密码' },
        { id: 'auth-verify-email', title: '邮箱验证' },
        { id: 'auth-resend-verification', title: '重发验证邮件' },
        { id: 'auth-test', title: '认证测试' }
      ]
    },
    {
      id: 'users',
      title: '用户模块',
      icon: Users,
      count: 12,
      subsections: [
        { id: 'users-profile', title: '获取个人资料' },
        { id: 'users-update-profile', title: '更新个人资料' },
        { id: 'users-avatar', title: '上传头像' },
        { id: 'users-delete-avatar', title: '删除头像' },
        { id: 'users-list', title: '获取用户列表' },
        { id: 'users-detail', title: '获取用户详情' },
        { id: 'users-ban', title: '封禁用户' },
        { id: 'users-unban', title: '解封用户' },
        { id: 'users-role', title: '修改用户角色' },
        { id: 'users-delete', title: '删除账户' },
        { id: 'users-stats', title: '用户统计' },
        { id: 'users-test', title: '用户测试' }
      ]
    },
    {
      id: 'videos',
      title: '视频模块',
      icon: Play,
      count: 22,
      subsections: [
        { id: 'videos-list', title: '获取视频列表' },
        { id: 'videos-upload', title: '上传视频' },
        { id: 'videos-detail', title: '获取视频详情' },
        { id: 'videos-update', title: '更新视频信息' },
        { id: 'videos-delete', title: '删除视频' },
        { id: 'videos-search', title: '搜索视频' },
        { id: 'videos-popular', title: '热门视频' },
        { id: 'videos-recommended', title: '推荐视频' },
        { id: 'videos-user', title: '获取用户视频' },
        { id: 'videos-processing', title: '获取处理状态' },
        { id: 'videos-reprocess', title: '重新处理视频' },
        { id: 'videos-stats', title: '视频统计' },
        { id: 'videos-test', title: '视频测试' }
      ]
    },
    {
      id: 'categories',
      title: '分类管理',
      icon: Folder,
      count: 9,
      subsections: [
        { id: 'categories-tree', title: '获取分类树' },
        { id: 'categories-list', title: '获取分类列表' },
        { id: 'categories-popular', title: '热门分类' },
        { id: 'categories-search', title: '搜索分类' },
        { id: 'categories-detail', title: '获取分类详情' },
        { id: 'categories-videos', title: '获取分类下的视频' },
        { id: 'categories-create', title: '创建分类' },
        { id: 'categories-update', title: '更新分类' },
        { id: 'categories-delete', title: '删除分类' }
      ]
    },
    {
      id: 'interactions',
      title: '互动模块',
      icon: MessageCircle,
      count: 15,
      subsections: [
        { id: 'interactions-comment', title: '发表评论' },
        { id: 'interactions-edit-comment', title: '编辑评论' },
        { id: 'interactions-delete-comment', title: '删除评论' },
        { id: 'interactions-comments', title: '获取视频评论' },
        { id: 'interactions-popular-comments', title: '获取热门评论' },
        { id: 'interactions-replies', title: '获取评论回复' },
        { id: 'interactions-search-comments', title: '搜索评论' },
        { id: 'interactions-stats', title: '获取互动统计' },
        { id: 'interactions-like', title: '点赞/取消点赞' },
        { id: 'interactions-favorite', title: '收藏/取消收藏' },
        { id: 'interactions-user-favorites', title: '获取用户收藏' },
        { id: 'interactions-user-likes', title: '获取用户点赞' },
        { id: 'interactions-user-comments', title: '获取用户评论' },
        { id: 'interactions-batch-check', title: '批量检查互动状态' },
        { id: 'interactions-test', title: '互动测试' }
      ]
    },
    {
      id: 'members',
      title: '会员模块',
      icon: Crown,
      count: 16,
      subsections: [
        { id: 'members-plans', title: '获取会员计划列表' },
        { id: 'members-plan-detail', title: '获取会员计划详情' },
        { id: 'members-compare', title: '比较会员计划' },
        { id: 'members-my-membership', title: '获取我的会员信息' },
        { id: 'members-history', title: '获取会员历史' },
        { id: 'members-subscribe', title: '订阅会员计划' },
        { id: 'members-cancel', title: '取消会员订阅' },
        { id: 'members-auto-renew', title: '设置自动续费' },
        { id: 'members-benefits', title: '获取会员权益' },
        { id: 'members-exclusive', title: '获取独家内容' },
        { id: 'members-admin-stats', title: '获取会员统计' },
        { id: 'members-admin-plans', title: '获取所有计划' },
        { id: 'members-admin-create', title: '创建会员计划' },
        { id: 'members-admin-update', title: '更新会员计划' },
        { id: 'members-admin-delete', title: '删除会员计划' },
        { id: 'members-test', title: '会员测试' }
      ]
    },
    {
      id: 'payment',
      title: '支付模块',
      icon: CreditCard,
      count: 8,
      subsections: [
        { id: 'payment-methods', title: '获取支付方式列表' },
        { id: 'payment-webhook', title: '支付回调' },
        { id: 'payment-create-order', title: '创建支付订单' },
        { id: 'payment-order-status', title: '查询订单状态' },
        { id: 'payment-my-orders', title: '获取我的订单列表' },
        { id: 'payment-refund', title: '申请退款' },
        { id: 'payment-stats', title: '支付统计' },
        { id: 'payment-test', title: '支付测试' }
      ]
    },
    {
      id: 'admin',
      title: '管理模块',
      icon: Settings,
      count: 17,
      subsections: [
        { id: 'admin-dashboard', title: '获取仪表板统计' },
        { id: 'admin-users', title: '用户管理' },
        { id: 'admin-users-batch', title: '批量用户操作' },
        { id: 'admin-videos', title: '视频管理' },
        { id: 'admin-videos-batch', title: '批量视频操作' },
        { id: 'admin-comments', title: '评论管理' },
        { id: 'admin-logs', title: '系统日志' },
        { id: 'admin-config', title: '获取系统配置' },
        { id: 'admin-update-config', title: '更新系统配置' },
        { id: 'admin-clear-cache', title: '清理系统缓存' },
        { id: 'admin-stats-access-overview', title: '获取访问统计概览' },
        { id: 'admin-stats-access-detail', title: '获取访问统计详情' },
        { id: 'admin-stats-revenue-overview', title: '获取收费统计概览' },
        { id: 'admin-stats-revenue-detail', title: '获取收费统计详情' },
        { id: 'admin-stats-users', title: '获取用户行为统计' },
        { id: 'admin-stats-content', title: '获取热门内容统计' },
        { id: 'admin-test', title: '管理测试' }
      ]
    }
  ];

  return (
    <nav className="p-6 space-y-2">
      {sections.map((section) => {
        const IconComponent = section.icon;
        const isActive = activeSection === section.id || section.subsections?.some(sub => sub.id === activeSection);
        
        return (
          <div key={section.id} className="space-y-1">
            <button
              onClick={() => setActiveSection(section.id)}
              className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-md transition-colors ${
                isActive 
                  ? 'bg-primary text-primary-foreground' 
                  : 'hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              <div className="flex items-center space-x-3">
                <IconComponent size={16} />
                <span>{section.title}</span>
              </div>
              {section.count && (
                <span className="bg-muted px-2 py-0.5 text-xs rounded-full">
                  {section.count}
                </span>
              )}
            </button>
            
            {section.subsections && isActive && (
              <div className="ml-4 space-y-1">
                {section.subsections.map((subsection) => (
                  <button
                    key={subsection.id}
                    onClick={() => setActiveSection(subsection.id)}
                    className={`w-full text-left px-3 py-1.5 text-sm rounded-md transition-colors ${
                      activeSection === subsection.id
                        ? 'bg-accent text-accent-foreground'
                        : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
                    }`}
                  >
                    {subsection.title}
                  </button>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </nav>
  );
};

export default Sidebar;
