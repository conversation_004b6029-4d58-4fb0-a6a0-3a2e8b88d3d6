#!/usr/bin/env node

/**
 * 数据库管理工具
 * 用法:
 *   node scripts/db-manager.js init     - 初始化数据库
 *   node scripts/db-manager.js reset    - 重置数据库
 *   node scripts/db-manager.js check    - 检查数据库状态
 */

require('dotenv').config();
const { initializeDatabase, resetDatabase, checkDatabaseExists } = require('../src/database/init');
const mysql = require('mysql2/promise');
const logger = require('../src/utils/logger');
const fs = require('fs');
const path = require('path');

// 命令行参数
const command = process.argv[2];

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || ''
};

// 检查数据库连接
async function checkConnection() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    await connection.ping();
    await connection.end();
    console.log('✅ 数据库连接正常');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
}

// 检查数据库状态
async function checkDatabaseStatus() {
  try {
    console.log('🔍 检查数据库状态...');
    
    let connection = await mysql.createConnection(dbConfig);
    const dbName = process.env.DB_NAME || 'video_platform';
    
    // 检查数据库是否存在
    const dbExists = await checkDatabaseExists(connection, dbName);
    console.log(`📊 数据库 ${dbName}: ${dbExists ? '✅ 存在' : '❌ 不存在'}`);
    
    if (dbExists) {
      // 关闭当前连接，重新创建指定数据库的连接
      await connection.end();
      connection = await mysql.createConnection({
        ...dbConfig,
        database: dbName
      });

      // 检查表
      const [tables] = await connection.execute('SHOW TABLES');
      console.log(`📋 表数量: ${tables.length}`);

      if (tables.length > 0) {
        console.log('📝 表列表:');
        tables.forEach(table => {
          const tableName = Object.values(table)[0];
          console.log(`   - ${tableName}`);
        });

        // 检查用户表记录数
        try {
          const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
          console.log(`👥 用户数量: ${userCount[0].count}`);

          // 检查管理员账户
          const [adminCount] = await connection.execute('SELECT COUNT(*) as count FROM users WHERE role = ?', ['admin']);
          console.log(`👑 管理员数量: ${adminCount[0].count}`);
        } catch (error) {
          console.log('👥 用户数量: 无法获取');
        }
      }
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 检查数据库状态失败:', error.message);
  }
}

// 从文件执行SQL
async function executeSqlFile(filePath) {
  try {
    const fullPath = path.resolve(process.cwd(), filePath);
    console.log(`🚀 开始从文件执行SQL: ${fullPath}`);
    
    if (!fs.existsSync(fullPath)) {
      console.error(`❌ 文件不存在: ${fullPath}`);
      return;
    }
    
    const sql = fs.readFileSync(fullPath, 'utf-8');
    const dbName = process.env.DB_NAME || 'video_platform';
    const connection = await mysql.createConnection({
      ...dbConfig,
      database: dbName,
      multipleStatements: true
    });

    await connection.query(sql);
    
    await connection.end();
    console.log('✅ SQL文件执行成功');
  } catch (error) {
    console.error('❌ SQL文件执行失败:', error.message);
    process.exit(1);
  }
}

// 初始化数据库
async function initDatabase() {
  try {
    console.log('🚀 开始初始化数据库...');
    
    const success = await initializeDatabase();
    if (success) {
      console.log('✅ 数据库初始化成功');
      await checkDatabaseStatus();
    } else {
      console.log('❌ 数据库初始化失败');
    }
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  }
}

// 重置数据库
async function resetDB() {
  try {
    console.log('⚠️  警告: 这将删除所有数据!');
    console.log('🔄 开始重置数据库...');
    
    await resetDatabase();
    console.log('✅ 数据库重置成功');

    // 重置后自动应用所有迁移
    console.log('📂 正在查找并应用数据库迁移...');
    const migrationsDir = path.join(__dirname, '..', 'src', 'database', 'migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // 按文件名（时间戳）排序

    if (migrationFiles.length > 0) {
      console.log(`🔍 发现了 ${migrationFiles.length} 个迁移文件。`);
      for (const file of migrationFiles) {
        const filePath = path.join(migrationsDir, file);
        console.log(`\n▶️  正在应用迁移: ${file}`);
        await executeSqlFile(filePath);
      }
      console.log('\n✅ 所有数据库迁移应用完毕！');
    } else {
      console.log('ℹ️  没有找到需要应用的迁移文件。');
    }

    await checkDatabaseStatus();
  } catch (error) {
    console.error('❌ 数据库重置失败:', error.message);
    process.exit(1);
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
📚 数据库管理工具使用说明

用法: node scripts/db-manager.js <command>

命令:
  init              初始化数据库（创建数据库和表）
  reset             重置数据库（删除并重新创建）
  check             检查数据库状态
  --file <filepath> 从指定的SQL文件执行脚本
  help              显示帮助信息

示例:
  node scripts/db-manager.js init
  node scripts/db-manager.js check
  node scripts/db-manager.js reset
  node scripts/db-manager.js --file src/database/migrations/your-script.sql

环境变量:
  DB_HOST      数据库主机 (默认: localhost)
  DB_PORT      数据库端口 (默认: 3306)
  DB_USER      数据库用户 (默认: root)
  DB_PASSWORD  数据库密码
  DB_NAME      数据库名称 (默认: video_platform)
`);
}

// 主函数
async function main() {
  console.log('🎬 视频平台数据库管理工具\n');
  
  // 首先检查数据库连接
  const connectionOk = await checkConnection();
  if (!connectionOk) {
    console.log('\n💡 请检查以下配置:');
    console.log(`   - 数据库主机: ${dbConfig.host}:${dbConfig.port}`);
    console.log(`   - 数据库用户: ${dbConfig.user}`);
    console.log(`   - 数据库密码: ${dbConfig.password ? '已设置' : '未设置'}`);
    console.log('\n🔧 请确保MySQL服务正在运行，并且配置正确。');
    process.exit(1);
  }
  
  const fileIndex = process.argv.indexOf('--file');
  if (fileIndex > -1 && process.argv[fileIndex + 1]) {
    const filePath = process.argv[fileIndex + 1];
    await executeSqlFile(filePath);
    return;
  }
  
  switch (command) {
    case 'init':
      await initDatabase();
      break;
    case 'reset':
      await resetDB();
      break;
    case 'check':
      await checkDatabaseStatus();
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      console.log('❌ 未知命令:', command);
      showHelp();
      process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  checkConnection,
  checkDatabaseStatus,
  initDatabase,
  resetDB
};
