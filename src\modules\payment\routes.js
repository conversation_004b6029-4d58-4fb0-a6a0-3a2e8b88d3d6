const express = require('express');
const router = express.Router();
const paymentController = require('./controllers/paymentController');
const { verifyToken, requireAdmin } = require('../../middleware/auth');

// 公开路由，获取可用支付方式
router.get('/methods', paymentController.getAvailableMethods);

// 创建支付订单 (需要登录)
router.post('/orders', verifyToken, paymentController.createOrder);

// 查询订单状态 (需要登录)
router.get('/orders/:orderNo', verifyToken, paymentController.queryOrder);

// 为已存在的订单重新生成支付信息 (需要登录)
router.post('/orders/:orderNo/regenerate-payment', verifyToken, paymentController.regeneratePaymentInfo);

// 获取我的订单列表 (需要登录)
router.get('/my-orders', verifyToken, paymentController.getMyOrders);

// 支付回调路由 (无需认证)
router.post('/webhook/:provider', paymentController.handleCallback);

// 申请退款 (需要登录)
router.post('/refunds', verifyToken, paymentController.requestRefund);

// 为购买视频创建订单 (需要登录)
router.post('/orders/video', verifyToken, paymentController.createVideoOrder);

// 使用余额支付订单 (需要登录)
router.post('/orders/:orderId/pay', verifyToken, paymentController.payForOrderWithBalance);

// 测试路由
router.get('/test', verifyToken, paymentController.test);

module.exports = router;
