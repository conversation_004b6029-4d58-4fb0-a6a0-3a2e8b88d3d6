const axios = require('axios');
require('dotenv').config();

async function testAdminUsersAPI() {
  try {
    console.log('🔍 测试管理员用户列表API...\n');
    
    // 首先登录获取管理员token
    console.log('1. 管理员登录...');
    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      email: '<EMAIL>',
      password: 'Admin123456!'
    });
    
    if (!loginResponse.data.success) {
      throw new Error('管理员登录失败');
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ 管理员登录成功');
    console.log(`🔑 Token: ${token.substring(0, 50)}...`);
    
    // 测试用户列表API
    console.log('\n2. 调用用户列表API...');
    const usersResponse = await axios.get('http://localhost:3000/api/admin/users', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params: {
        page: 1,
        pageSize: 10
      }
    });
    
    if (!usersResponse.data.success) {
      throw new Error('获取用户列表失败');
    }
    
    const userData = usersResponse.data.data;
    console.log('✅ API调用成功');
    console.log(`📊 总用户数: ${userData.pagination.total}`);
    console.log(`📄 当前页: ${userData.pagination.page}/${userData.pagination.totalPages}`);
    
    // 查找用户GGG
    console.log('\n3. 查找用户GGG...');
    const gggUser = userData.data.find(user => user.username === 'GGG');
    
    if (gggUser) {
      console.log('👤 找到用户GGG:');
      console.log(`   ID: ${gggUser.id}`);
      console.log(`   用户名: ${gggUser.username}`);
      console.log(`   邮箱: ${gggUser.email}`);
      console.log(`   角色: ${gggUser.role}`);
      console.log(`   状态: ${gggUser.status}`);
      console.log(`   注册时间: ${gggUser.created_at}`);
      console.log(`   最后登录: ${gggUser.last_login || '从未登录'}`);
      
      if (gggUser.role === 'member') {
        console.log('✅ 用户角色显示正确: member');
      } else {
        console.log(`❌ 用户角色显示错误: ${gggUser.role} (应该是 member)`);
      }
    } else {
      console.log('❌ 未找到用户GGG');
      
      // 显示所有用户的基本信息
      console.log('\n📋 当前页面的所有用户:');
      userData.data.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.username} (${user.role}) - ${user.email}`);
      });
    }
    
    // 测试搜索功能
    console.log('\n4. 测试搜索用户GGG...');
    const searchResponse = await axios.get('http://localhost:3000/api/admin/users', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params: {
        page: 1,
        pageSize: 10,
        search: 'GGG'
      }
    });
    
    if (searchResponse.data.success) {
      const searchData = searchResponse.data.data;
      console.log(`🔍 搜索结果: 找到 ${searchData.data.length} 个用户`);
      
      searchData.data.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.username} (${user.role}) - ${user.email}`);
      });
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testAdminUsersAPI()
    .then(() => {
      console.log('\n🎉 测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testAdminUsersAPI };
