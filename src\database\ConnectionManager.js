const { mysql, redis } = require('../config/database');
const logger = require('../utils/logger');
const { cache } = require('../utils/cache');

class ConnectionManager {
  constructor() {
    this.mysql = mysql;
    this.redis = redis;
    this.cache = cache;
    this.transactionConnections = new Map(); // 存储事务连接
  }

  // 获取MySQL连接
  async getMySQLConnection() {
    try {
      const connection = await this.mysql.getConnection();
      logger.debug(`获取MySQL连接: ${connection.threadId}`);
      return connection;
    } catch (error) {
      logger.error('获取MySQL连接失败:', error);
      throw error;
    }
  }

  // 释放MySQL连接
  releaseMySQLConnection(connection) {
    if (connection) {
      connection.release();
      logger.debug(`释放MySQL连接: ${connection.threadId}`);
    }
  }

  // 执行MySQL查询（自动管理连接）
  async executeQuery(sql, params = []) {
    let connection;
    try {
      connection = await this.getMySQLConnection();
      const [rows] = await connection.execute(sql, params);
      logger.debug(`查询执行成功: ${sql.substring(0, 50)}...`);
      return rows;
    } catch (error) {
      logger.error(`查询执行失败: ${sql}`, { params, error: error.message });
      throw error;
    } finally {
      if (connection) {
        this.releaseMySQLConnection(connection);
      }
    }
  }

  // 开始事务
  async beginTransaction(transactionId = null) {
    const txId = transactionId || `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      const connection = await this.getMySQLConnection();
      await connection.beginTransaction();
      
      this.transactionConnections.set(txId, connection);
      logger.info(`事务开始: ${txId}`);
      
      return txId;
    } catch (error) {
      logger.error(`开始事务失败: ${txId}`, error);
      throw error;
    }
  }

  // 提交事务
  async commitTransaction(transactionId) {
    const connection = this.transactionConnections.get(transactionId);
    if (!connection) {
      throw new Error(`事务不存在: ${transactionId}`);
    }

    try {
      await connection.commit();
      this.releaseMySQLConnection(connection);
      this.transactionConnections.delete(transactionId);
      logger.info(`事务提交成功: ${transactionId}`);
    } catch (error) {
      logger.error(`事务提交失败: ${transactionId}`, error);
      await this.rollbackTransaction(transactionId);
      throw error;
    }
  }

  // 回滚事务
  async rollbackTransaction(transactionId) {
    const connection = this.transactionConnections.get(transactionId);
    if (!connection) {
      throw new Error(`事务不存在: ${transactionId}`);
    }

    try {
      await connection.rollback();
      this.releaseMySQLConnection(connection);
      this.transactionConnections.delete(transactionId);
      logger.info(`事务回滚成功: ${transactionId}`);
    } catch (error) {
      logger.error(`事务回滚失败: ${transactionId}`, error);
      // 强制释放连接
      this.releaseMySQLConnection(connection);
      this.transactionConnections.delete(transactionId);
      throw error;
    }
  }

  /**
   * 自动处理事务的辅助函数
   * @param {Function} callback - 在事务中执行的函数，接收一个事务连接作为参数
   */
  async transaction(callback) {
    const connection = await this.getMySQLConnection();
    await connection.beginTransaction();
    logger.info(`事务开始 (自动模式): ${connection.threadId}`);

    try {
      await callback(connection);
      await connection.commit();
      logger.info(`事务提交成功 (自动模式): ${connection.threadId}`);
    } catch (error) {
      await connection.rollback();
      logger.error(`事务回滚 (自动模式): ${connection.threadId}`, error);
      throw error; // 重新抛出错误，以便上层可以捕获
    } finally {
      this.releaseMySQLConnection(connection);
    }
  }

  // 在事务中执行查询
  async executeInTransaction(transactionId, sql, params = []) {
    const connection = this.transactionConnections.get(transactionId);
    if (!connection) {
      throw new Error(`事务不存在: ${transactionId}`);
    }

    try {
      const [rows] = await connection.execute(sql, params);
      logger.debug(`事务查询执行成功: ${transactionId}, ${sql.substring(0, 50)}...`);
      return rows;
    } catch (error) {
      logger.error(`事务查询执行失败: ${transactionId}, ${sql}`, { params, error: error.message });
      throw error;
    }
  }

  // 执行带缓存的查询
  async executeWithCache(sql, params = [], cacheKey = null, ttl = 3600) {
    // 生成缓存键
    if (!cacheKey) {
      const paramStr = params.length > 0 ? JSON.stringify(params) : '';
      cacheKey = `query:${Buffer.from(sql + paramStr).toString('base64').substring(0, 50)}`;
    }

    // 尝试从缓存获取
    const cachedResult = await this.cache.get(cacheKey);
    if (cachedResult !== null) {
      logger.debug(`缓存命中: ${cacheKey}`);
      return cachedResult;
    }

    // 执行查询
    const result = await this.executeQuery(sql, params);
    
    // 存储到缓存
    await this.cache.set(cacheKey, result, ttl);
    logger.debug(`查询结果已缓存: ${cacheKey}`);
    
    return result;
  }

  // 批量执行查询
  async executeBatch(queries) {
    const results = [];
    let connection;

    try {
      connection = await this.getMySQLConnection();
      
      for (const query of queries) {
        const { sql, params = [] } = query;
        const [rows] = await connection.execute(sql, params);
        results.push(rows);
      }
      
      logger.info(`批量查询执行完成，共 ${queries.length} 个查询`);
      return results;
      
    } catch (error) {
      logger.error('批量查询执行失败:', error);
      throw error;
    } finally {
      if (connection) {
        this.releaseMySQLConnection(connection);
      }
    }
  }

  // 执行批量事务
  async executeBatchTransaction(queries) {
    const transactionId = await this.beginTransaction();
    
    try {
      const results = [];
      
      for (const query of queries) {
        const { sql, params = [] } = query;
        const result = await this.executeInTransaction(transactionId, sql, params);
        results.push(result);
      }
      
      await this.commitTransaction(transactionId);
      logger.info(`批量事务执行完成，共 ${queries.length} 个查询`);
      return results;
      
    } catch (error) {
      await this.rollbackTransaction(transactionId);
      logger.error('批量事务执行失败:', error);
      throw error;
    }
  }

  // 获取连接池状态
  getPoolStatus() {
    return {
      mysql: {
        activeTransactions: this.transactionConnections.size
      },
      redis: {
        connected: this.redis ? this.redis.isReady : false
      }
    };
  }

  // 清理过期事务
  cleanupExpiredTransactions(maxAge = 300000) { // 5分钟
    const now = Date.now();
    const expiredTransactions = [];
    
    for (const [txId, connection] of this.transactionConnections) {
      // 从事务ID中提取时间戳
      const timestamp = parseInt(txId.split('_')[1]);
      if (now - timestamp > maxAge) {
        expiredTransactions.push(txId);
      }
    }
    
    // 清理过期事务
    for (const txId of expiredTransactions) {
      logger.warn(`清理过期事务: ${txId}`);
      this.rollbackTransaction(txId).catch(error => {
        logger.error(`清理过期事务失败: ${txId}`, error);
      });
    }
    
    return expiredTransactions.length;
  }

  // 关闭所有连接
  async closeAllConnections() {
    logger.info('开始关闭所有数据库连接...');
    
    // 回滚所有未完成的事务
    for (const txId of this.transactionConnections.keys()) {
      try {
        await this.rollbackTransaction(txId);
      } catch (error) {
        logger.error(`关闭时回滚事务失败: ${txId}`, error);
      }
    }
    
    // 关闭MySQL连接池
    try {
      await this.mysql.end();
      logger.info('MySQL连接池已关闭');
    } catch (error) {
      logger.error('关闭MySQL连接池失败:', error);
    }
    
    // 关闭Redis连接
    try {
      if (this.redis && this.redis.isReady) {
        await this.redis.quit();
        logger.info('Redis连接已关闭');
      }
    } catch (error) {
      logger.error('关闭Redis连接失败:', error);
    }
  }

  // 获取数据库统计信息
  async getDatabaseStats() {
    try {
      const stats = {};
      
      // MySQL统计
      const mysqlStats = await this.executeQuery(`
        SELECT 
          (SELECT COUNT(*) FROM users) as user_count,
          (SELECT COUNT(*) FROM videos) as video_count,
          (SELECT COUNT(*) FROM comments) as comment_count
      `);
      stats.mysql = mysqlStats[0];
      
      // Redis统计
      /*
      if (this.redis && this.redis.isReady) {
        const redisInfo = await this.redis.info('keyspace');
        stats.redis = { info: redisInfo };
      }
      */
      
      return stats;
    } catch (error) {
      logger.error('获取数据库统计信息失败:', error);
      return null;
    }
  }
}

// 创建连接管理器实例
const connectionManager = new ConnectionManager();

// 定期清理过期事务
setInterval(() => {
  connectionManager.cleanupExpiredTransactions();
}, 60000); // 每分钟检查一次

module.exports = connectionManager;
