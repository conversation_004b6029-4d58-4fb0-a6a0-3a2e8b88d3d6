# 跨平台路径配置指南

本指南说明如何在不同操作系统上正确配置项目路径，确保项目在Windows、Linux和macOS上都能正常运行。

## 📋 目录
- [基本原则](#基本原则)
- [环境变量配置](#环境变量配置)
- [常见路径配置](#常见路径配置)
- [部署配置](#部署配置)
- [故障排除](#故障排除)

## 🎯 基本原则

### 1. 使用相对路径
✅ **推荐做法**：
```javascript
// 使用相对路径
const uploadPath = './uploads';
const logPath = './logs/app.log';

// 使用 process.cwd() 和 path.join()
const fullPath = path.join(process.cwd(), 'uploads', 'videos');
```

❌ **避免做法**：
```javascript
// 避免硬编码绝对路径
const uploadPath = '/var/www/uploads';           // Linux专用
const uploadPath = 'C:\\uploads';                // Windows专用
const uploadPath = '/Users/<USER>/uploads';    // macOS专用
```

### 2. 使用环境变量
```bash
# .env 文件示例
UPLOAD_PATH=./uploads
LOG_FILE=./logs/app.log
FFMPEG_PATH=ffmpeg
```

### 3. 使用 Node.js 路径工具
```javascript
const path = require('path');

// 跨平台路径构建
const filePath = path.join(__dirname, '..', 'uploads', 'videos');

// 获取当前工作目录
const projectRoot = process.cwd();
```

## ⚙️ 环境变量配置

### FFmpeg 路径配置
```bash
# 如果 FFmpeg 已添加到系统 PATH
FFMPEG_PATH=ffmpeg

# Windows - 使用相对路径
FFMPEG_PATH=./bin/ffmpeg.exe

# Linux/macOS - 使用相对路径
FFMPEG_PATH=./bin/ffmpeg

# 或者使用绝对路径（不推荐，但有时必要）
# Windows: FFMPEG_PATH=C:\ffmpeg\bin\ffmpeg.exe
# Linux: FFMPEG_PATH=/usr/local/bin/ffmpeg
# macOS: FFMPEG_PATH=/opt/homebrew/bin/ffmpeg
```

### 存储路径配置
```bash
# 推荐：使用相对路径
UPLOAD_PATH=./uploads
VIDEO_PATH=./uploads/videos
AUDIO_PATH=./uploads/audios
AVATAR_PATH=./uploads/avatars
TEMP_PATH=./uploads/temp

# 日志路径
LOG_FILE=./logs/app.log
ERROR_LOG=./logs/error.log
```

## 📁 常见路径配置

### 1. 文件上传目录
```javascript
// 正确的跨平台配置
const uploadConfig = {
  videos: path.join(process.cwd(), 'uploads', 'videos'),
  audios: path.join(process.cwd(), 'uploads', 'audios'),
  avatars: path.join(process.cwd(), 'uploads', 'avatars'),
  thumbnails: path.join(process.cwd(), 'uploads', 'thumbnails')
};
```

### 2. 日志文件路径
```javascript
// 使用相对路径配置日志
const logConfig = {
  error: path.join(__dirname, '../../logs/error.log'),
  combined: path.join(__dirname, '../../logs/combined.log'),
  access: path.join(__dirname, '../../logs/access.log')
};
```

### 3. SSL 证书路径
```javascript
// 灵活的证书路径配置
const sslConfig = {
  certPath: process.env.SSL_CERT_PATH || './certs/certificate.crt',
  keyPath: process.env.SSL_KEY_PATH || './certs/private.key'
};
```

## 🚀 部署配置

### Nginx 配置
```nginx
# 使用变量或注释说明需要调整的路径
server {
    # 静态文件服务
    # 注意：请根据实际项目部署路径调整alias路径
    location /uploads/ {
        alias /path/to/your/project/uploads/;
        expires 1y;
    }
}
```

### PM2 配置
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'video-platform-api',
    script: './app.js',
    // 使用相对路径
    error_file: './logs/error.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log'
  }],
  
  deploy: {
    production: {
      // 使用用户目录相对路径
      path: '~/video-platform-api'
    }
  }
};
```

### Docker 配置
```dockerfile
# 使用工作目录相对路径
WORKDIR /app
COPY . .

# 创建必要目录
RUN mkdir -p uploads/videos uploads/audios logs

# 使用相对路径
ENV UPLOAD_PATH=./uploads
ENV LOG_FILE=./logs/app.log
```

## 🔧 故障排除

### 常见问题

#### 1. 路径分隔符问题
```javascript
// ❌ 错误：硬编码路径分隔符
const filePath = 'uploads/videos/file.mp4';  // 在 Windows 上可能有问题

// ✅ 正确：使用 path.join()
const filePath = path.join('uploads', 'videos', 'file.mp4');
```

#### 2. 权限问题
```bash
# Linux/macOS 权限设置
chmod 755 uploads
chmod 755 logs

# 如果需要 web 服务器访问
sudo chown -R www-data:www-data uploads
sudo chown -R www-data:www-data logs
```

#### 3. 环境变量未生效
```javascript
// 确保在应用启动时加载环境变量
require('dotenv').config();

// 检查环境变量是否正确加载
console.log('Upload path:', process.env.UPLOAD_PATH);
```

### 调试技巧

#### 1. 路径验证
```javascript
const fs = require('fs');
const path = require('path');

// 验证路径是否存在
function validatePath(pathToCheck) {
  const fullPath = path.resolve(pathToCheck);
  console.log(`检查路径: ${fullPath}`);
  
  if (fs.existsSync(fullPath)) {
    console.log('✅ 路径存在');
    return true;
  } else {
    console.log('❌ 路径不存在');
    return false;
  }
}
```

#### 2. 创建必要目录
```javascript
// 自动创建目录的工具函数
async function ensureDirectory(dirPath) {
  try {
    await fs.promises.access(dirPath);
  } catch (error) {
    await fs.promises.mkdir(dirPath, { recursive: true });
    console.log(`创建目录: ${dirPath}`);
  }
}
```

## 📝 最佳实践总结

1. **始终使用相对路径**：避免硬编码绝对路径
2. **使用环境变量**：让路径配置可以灵活调整
3. **使用 Node.js 路径工具**：`path.join()`, `path.resolve()`, `__dirname`, `process.cwd()`
4. **添加路径验证**：在应用启动时检查关键路径是否存在
5. **提供配置说明**：在部署文档中明确说明需要调整的路径
6. **测试多平台**：确保在不同操作系统上都能正常运行

## 🔗 相关文档

- [Node.js Path 模块文档](https://nodejs.org/api/path.html)
- [环境变量配置指南](./ENVIRONMENT.md)
- [部署指南](./DEPLOYMENT.md)

---

遵循这些指南，您的项目将能够在任何操作系统上顺利运行！
