import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ThumbsUp } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';
import { useToast } from "@/components/ui/use-toast";
import { AppError } from '@/lib/error';

interface CommentSectionProps {
  videoId: string;
}

interface User {
  id: string;
  username: string;
  nickname: string;
  avatar?: string;
}

interface Comment {
  id: number;
  content: string;
  created_at: string;
  parent_id: number | null;
  user: User;
  like_count?: number; 
}

const formatDate = (dateString: string) => {
  try {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: zhCN });
  } catch (error) {
    console.error("Invalid date format:", dateString);
    return "日期无效";
  }
};

const fetchComments = async (videoId: string): Promise<Comment[]> => {
  if (!videoId) return [];
  const response = await api.get(`/comment/video/${videoId}`);
  return response.data.data || [];
};

const postComment = async ({ videoId, content }: { videoId: string; content: string }) => {
  const response = await api.post('/comment', { videoId, content });
  return response.data.data;
};

const CommentSection: React.FC<CommentSectionProps> = ({ videoId }) => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [newComment, setNewComment] = useState('');

  const { data: comments = [], isLoading, error } = useQuery<Comment[], Error>({
    queryKey: ['comments', videoId],
    queryFn: () => fetchComments(videoId),
    enabled: !!videoId,
  });

  const mutation = useMutation<Comment, AppError, { videoId: string; content: string }>({
    mutationFn: postComment,
    onSuccess: (newCommentData) => {
      toast({
        title: "评论成功",
        description: "您的评论已成功发布。",
      });
      queryClient.invalidateQueries({ queryKey: ['comments', videoId] });
      setNewComment('');
    },
    onError: (err) => {
      toast({
        title: "评论失败",
        description: err.response?.data?.message || err.message || '发生未知错误，请稍后再试。',
        variant: "destructive",
      });
    },
  });

  const handleCommentSubmit = () => {
    if (!newComment.trim()) return;
    if (!isAuthenticated) {
      toast({
        title: "需要登录",
        description: "请先登录后再发表评论。",
        variant: "destructive",
        action: <Button onClick={() => navigate('/login')}>立即登录</Button>,
      });
      return;
    }
    mutation.mutate({ videoId, content: newComment });
  };

  return (
    <div className="py-6">
      <h2 className="text-xl font-bold mb-4">{comments.length} 条评论</h2>
      
      {isAuthenticated ? (
        <div className="flex flex-col space-y-4 mb-6">
          <Textarea
            placeholder="在此处输入您的评论..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            rows={3}
            disabled={mutation.isPending}
          />
          <div className="flex justify-end">
            <Button onClick={handleCommentSubmit} disabled={mutation.isPending || !newComment.trim()}>
              {mutation.isPending ? '正在发布...' : '发布评论'}
            </Button>
          </div>
        </div>
      ) : (
        <div className="text-center p-4 my-6 border rounded-lg bg-muted/20">
          <p className="text-sm text-muted-foreground">登录后即可发表评论</p>
          <Button size="sm" className="mt-3" onClick={() => navigate('/login')}>
            立即登录
          </Button>
        </div>
      )}

      {isLoading && <div className="text-center py-4">正在加载评论...</div>}
      
      {error && <div className="text-center py-4 text-red-500">加载评论失败: {error.message}</div>}

      {!isLoading && !error && (
        <div className="space-y-6">
          {comments.map((comment) => (
            <div key={comment.id} className="flex items-start space-x-4">
              <Avatar>
                <AvatarImage src={comment.user?.avatar} alt={comment.user?.nickname || comment.user?.username} />
                <AvatarFallback>
                  {(comment.user?.nickname || comment.user?.username || '?').charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="font-semibold text-sm">{comment.user?.nickname || comment.user?.username}</p>
                  <p className="text-xs text-muted-foreground">{formatDate(comment.created_at)}</p>
                </div>
                <p className="mt-1 mb-2 text-sm">{comment.content}</p>
                <div className="flex items-center space-x-4">
                  <button className="flex items-center space-x-1 text-xs text-muted-foreground hover:text-foreground">
                    <ThumbsUp size={14} />
                    <span>{comment.like_count || 0}</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CommentSection; 