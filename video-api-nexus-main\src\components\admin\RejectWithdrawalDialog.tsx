import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { adminApi } from '@/services/adminApi';

const rejectSchema = z.object({
  reason: z.string().min(1, { message: '必须填写拒绝原因。' }),
  notes: z.string().optional(),
});

const RejectWithdrawalDialog = ({ open, onOpenChange, withdrawal, onSuccess }) => {
  const { toast } = useToast();
  const form = useForm<z.infer<typeof rejectSchema>>({
    resolver: zodResolver(rejectSchema),
    defaultValues: {
      reason: '',
      notes: '',
    },
  });

  const mutation = useMutation({
    mutationFn: (values: z.infer<typeof rejectSchema>) => adminApi.rejectWithdrawal(withdrawal.id, values),
    onSuccess: () => {
      toast({ title: '操作成功', description: '提现请求已拒绝。' });
      onSuccess();
      onOpenChange(false);
      form.reset();
    },
    onError: (error: any) => {
      toast({ title: '操作失败', description: error.message, variant: 'destructive' });
    },
  });

  const onSubmit = (values: z.infer<typeof rejectSchema>) => mutation.mutate(values);

  if (!withdrawal) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>拒绝提现请求</DialogTitle>
          <DialogDescription>
            拒绝后，提现金额将退还至用户余额。请务必填写拒绝原因。
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>拒绝原因</FormLabel>
                  <FormControl>
                    <Textarea placeholder="例如：无效的钱包地址" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注 (可选)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="输入内部备注信息..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={mutation.isPending}>取消</Button>
              <Button type="submit" disabled={mutation.isPending} variant="destructive">
                {mutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                确认拒绝
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default RejectWithdrawalDialog; 