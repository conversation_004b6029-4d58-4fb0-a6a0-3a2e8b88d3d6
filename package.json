{"name": "video-api-framework", "version": "1.0.0", "description": "模块化视频网站API框架", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "mocha tests/**/*.test.js --timeout 10000", "test:api": "mocha tests/api.test.js --timeout 10000", "test:performance": "mocha tests/performance.test.js --timeout 30000", "test:coverage": "nyc npm test", "docs:generate": "node scripts/generate-api-docs.js", "db:init": "node scripts/db-manager.js init", "db:reset": "node scripts/db-manager.js reset", "db:check": "node scripts/db-manager.js check", "db:seed": "node database/seed.js", "categories:insert": "node src/scripts/insertCategories.js", "categories:insert-api": "node src/scripts/insertCategoriesViaAPI.js"}, "keywords": ["video", "api", "express", "nodejs", "modular"], "author": "", "license": "MIT", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@faker-js/faker": "^8.4.1", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "^2.1.2", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^5.1.1", "@mui/material": "^7.2.0", "@radix-ui/react-dialog": "^1.1.14", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.83.0", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "bs58": "^5.0.0", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-jwt": "^8.5.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "fluent-ffmpeg": "^2.1.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.516.0", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "node-cache": "^5.1.2", "node-cron": "^4.1.0", "nodemailer": "^6.9.7", "pm2": "^6.0.8", "qrcode": "^1.5.4", "react-router-dom": "^7.6.2", "redis": "^4.6.10", "sequelize": "^6.37.7", "sharp": "^0.32.6", "socks": "^2.8.6", "socks-proxy-agent": "^8.0.5", "speakeasy": "^2.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xml2js": "^0.6.2", "zod": "^3.25.58"}, "devDependencies": {"@vitejs/plugin-react-swc": "^3.11.0", "chai": "^4.3.10", "mocha": "^10.2.0", "nodemon": "^3.0.2", "nyc": "^15.1.0", "supertest": "^6.3.3", "vite": "^7.0.5", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}