#!/usr/bin/env node

/**
 * HTTPS功能测试脚本
 * 测试HTTP和HTTPS服务器的功能
 */

const https = require('https');
const http = require('http');

console.log('🧪 测试HTTPS功能\n');

/**
 * 测试HTTP服务器
 */
function testHTTPServer() {
  return new Promise((resolve) => {
    console.log('🔍 测试HTTP服务器...');
    
    const options = {
      hostname: 'localhost',
      port: process.env.PORT || 3000,
      path: '/health',
      method: 'GET',
      timeout: 5000
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log(`✅ HTTP服务器响应: ${res.statusCode}`);
          console.log(`   状态: ${response.status}`);
          resolve(true);
        } catch (error) {
          console.log(`❌ HTTP服务器响应解析失败: ${error.message}`);
          resolve(false);
        }
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ HTTP服务器连接失败: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ HTTP服务器连接超时');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * 测试HTTPS服务器
 */
function testHTTPSServer() {
  return new Promise((resolve) => {
    console.log('\n🔍 测试HTTPS服务器...');
    
    const options = {
      hostname: 'localhost',
      port: process.env.HTTPS_PORT || 3443,
      path: '/health',
      method: 'GET',
      timeout: 5000,
      rejectUnauthorized: false // 忽略自签名证书错误
    };
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log(`✅ HTTPS服务器响应: ${res.statusCode}`);
          console.log(`   状态: ${response.status}`);
          console.log(`   SSL协议: ${res.socket.getProtocol ? res.socket.getProtocol() : 'N/A'}`);
          resolve(true);
        } catch (error) {
          console.log(`❌ HTTPS服务器响应解析失败: ${error.message}`);
          resolve(false);
        }
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ HTTPS服务器连接失败: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ HTTPS服务器连接超时');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * 测试HTTP到HTTPS重定向
 */
function testHTTPSRedirect() {
  return new Promise((resolve) => {
    console.log('\n🔍 测试HTTP到HTTPS重定向...');
    
    const options = {
      hostname: 'localhost',
      port: process.env.PORT || 3000,
      path: '/api/auth/test',
      method: 'GET',
      timeout: 5000
    };
    
    const req = http.request(options, (res) => {
      if (res.statusCode === 301 || res.statusCode === 302) {
        const location = res.headers.location;
        console.log(`✅ HTTP重定向成功: ${res.statusCode}`);
        console.log(`   重定向到: ${location}`);
        
        if (location && location.startsWith('https://')) {
          console.log('✅ 重定向到HTTPS地址');
          resolve(true);
        } else {
          console.log('❌ 重定向地址不是HTTPS');
          resolve(false);
        }
      } else {
        console.log(`⚠️ 未发生重定向，状态码: ${res.statusCode}`);
        resolve(false);
      }
    });
    
    req.on('error', (error) => {
      console.log(`❌ 重定向测试失败: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ 重定向测试超时');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * 测试HTTPS安全头
 */
function testSecurityHeaders() {
  return new Promise((resolve) => {
    console.log('\n🔍 测试HTTPS安全头...');
    
    const options = {
      hostname: 'localhost',
      port: process.env.HTTPS_PORT || 3443,
      path: '/health',
      method: 'GET',
      timeout: 5000,
      rejectUnauthorized: false
    };
    
    const req = https.request(options, (res) => {
      const securityHeaders = {
        'strict-transport-security': 'HSTS',
        'x-content-type-options': 'Content Type Options',
        'x-frame-options': 'Frame Options',
        'x-xss-protection': 'XSS Protection',
        'referrer-policy': 'Referrer Policy'
      };
      
      let headersFound = 0;
      let totalHeaders = Object.keys(securityHeaders).length;
      
      console.log('   检查安全头:');
      Object.keys(securityHeaders).forEach(header => {
        if (res.headers[header]) {
          console.log(`   ✅ ${securityHeaders[header]}: ${res.headers[header]}`);
          headersFound++;
        } else {
          console.log(`   ❌ ${securityHeaders[header]}: 缺失`);
        }
      });
      
      const success = headersFound >= totalHeaders * 0.8; // 至少80%的安全头存在
      console.log(`   安全头覆盖率: ${headersFound}/${totalHeaders} (${Math.round(headersFound/totalHeaders*100)}%)`);
      
      resolve(success);
    });
    
    req.on('error', (error) => {
      console.log(`❌ 安全头测试失败: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ 安全头测试超时');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * 测试API接口HTTPS访问
 */
function testAPIOverHTTPS() {
  return new Promise((resolve) => {
    console.log('\n🔍 测试API接口HTTPS访问...');
    
    const options = {
      hostname: 'localhost',
      port: process.env.HTTPS_PORT || 3443,
      path: '/debug/https',
      method: 'GET',
      timeout: 5000,
      rejectUnauthorized: false
    };
    
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log(`✅ HTTPS API响应: ${res.statusCode}`);
          
          if (response.success && response.data) {
            console.log(`   请求协议: ${response.data.request.protocol}`);
            console.log(`   HTTPS状态: ${response.data.request.isHttps ? '✅ 是' : '❌ 否'}`);
            console.log(`   SSL配置: ${response.data.ssl.enabled ? '✅ 启用' : '❌ 禁用'}`);
            resolve(response.data.request.isHttps);
          } else {
            console.log('❌ API响应格式异常');
            resolve(false);
          }
        } catch (error) {
          console.log(`❌ API响应解析失败: ${error.message}`);
          resolve(false);
        }
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ HTTPS API测试失败: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ HTTPS API测试超时');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始HTTPS功能测试');
  console.log('='.repeat(50));
  
  // 等待服务器启动
  console.log('⏳ 等待服务器启动...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const httpOK = await testHTTPServer();
  const httpsOK = await testHTTPSServer();
  const redirectOK = await testHTTPSRedirect();
  const headersOK = await testSecurityHeaders();
  const apiOK = await testAPIOverHTTPS();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 HTTPS功能测试结果:');
  console.log('='.repeat(50));
  
  console.log(`HTTP服务器: ${httpOK ? '✅ 正常' : '❌ 异常'}`);
  console.log(`HTTPS服务器: ${httpsOK ? '✅ 正常' : '❌ 异常'}`);
  console.log(`HTTPS重定向: ${redirectOK ? '✅ 正常' : '❌ 异常'}`);
  console.log(`安全头配置: ${headersOK ? '✅ 正常' : '❌ 异常'}`);
  console.log(`HTTPS API: ${apiOK ? '✅ 正常' : '❌ 异常'}`);
  
  const overallSuccess = httpsOK && headersOK;
  const basicSuccess = httpOK;
  
  if (overallSuccess) {
    console.log('\n🎉 HTTPS功能测试全部通过！');
    console.log('\n🔗 访问地址:');
    console.log(`- HTTP API文档: http://localhost:${process.env.PORT || 3000}/docs`);
    console.log(`- HTTPS API文档: https://localhost:${process.env.HTTPS_PORT || 3443}/docs`);
    console.log(`- HTTPS调试接口: https://localhost:${process.env.HTTPS_PORT || 3443}/debug/https`);
    
  } else if (basicSuccess) {
    console.log('\n⚠️ HTTP服务器正常，但HTTPS配置有问题');
    console.log('请检查SSL证书配置和环境变量设置');
    
  } else {
    console.log('\n❌ 服务器未正常启动，请检查配置');
  }
  
  return overallSuccess;
}

// 运行测试
if (require.main === module) {
  main().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('测试过程出错:', error);
    process.exit(1);
  });
}

module.exports = { main, testHTTPServer, testHTTPSServer };
