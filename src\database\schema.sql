-- 视频网站数据库表结构设计
-- 注意：数据库创建和USE语句由初始化脚本处理，此文件仅包含表结构

-- 1. 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    username VARCHAR(100) NOT NULL UNIQUE COMMENT '用户名',
    nickname VARCHAR(100) COMMENT '昵称',
    avatar VARCHAR(500) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    gender ENUM('male', 'female', 'other') DEFAULT 'other' COMMENT '性别',
    birthday DATE COMMENT '生日',
    bio TEXT COMMENT '个人简介',
    role ENUM('user', 'member', 'vip', 'admin') DEFAULT 'user' COMMENT '用户角色',
    balance DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '用户余额',
    status ENUM('active', 'inactive', 'banned', 'deleted') DEFAULT 'active' COMMENT '账户状态',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
    two_factor_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用双因子认证',
    two_factor_secret VARCHAR(255) COMMENT '双因子认证密钥',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    last_activity_at TIMESTAMP NULL COMMENT '最后活动时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户表';

-- 2. 会员计划表
CREATE TABLE membership_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '计划名称',
    description TEXT COMMENT '计划描述',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    original_price DECIMAL(10,2) DEFAULT NULL COMMENT '原价',
    discount_until DATE DEFAULT NULL COMMENT '折扣截止日期',
    duration_days INT NOT NULL COMMENT '持续天数',
    features JSON COMMENT '功能特性',
    max_video_uploads INT COMMENT '最大视频上传数',
    max_storage_gb INT COMMENT '最大存储空间(GB)',
    priority INT DEFAULT 0 COMMENT '优先级',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_name (name),
    INDEX idx_is_active (is_active),
    INDEX idx_priority (priority)
) ENGINE=InnoDB COMMENT='会员计划表';

-- 3. 会员表
CREATE TABLE memberships (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    plan_id INT NULL COMMENT '会员计划ID（本地计划）',
    plan_name VARCHAR(255) NOT NULL COMMENT '会员计划名称',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active' COMMENT '状态',
    auto_renew BOOLEAN DEFAULT FALSE COMMENT '自动续费',
    payment_method VARCHAR(50) COMMENT '支付方式',
    payment_reference VARCHAR(255) COMMENT '支付参考号（订单ID、交易ID等）',
    transaction_id VARCHAR(100) COMMENT '交易ID（保留兼容性）',
    amount DECIMAL(10, 2) COMMENT '支付金额',
    currency VARCHAR(3) DEFAULT 'CNY' COMMENT '货币类型',
    cancelled_at TIMESTAMP NULL COMMENT '取消时间',
    cancel_reason TEXT COMMENT '取消原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES membership_plans(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_plan_id (plan_id),
    INDEX idx_status (status),
    INDEX idx_end_date (end_date),
    INDEX idx_payment_reference (payment_reference)
) ENGINE=InnoDB COMMENT='会员表';

-- 4. 视频分类表
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    slug VARCHAR(100) NOT NULL UNIQUE COMMENT '分类标识',
    description TEXT COMMENT '分类描述',
    parent_id INT DEFAULT NULL COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_parent_id (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB COMMENT='视频分类表';

-- 4. 媒体表 (视频和音频统一)
CREATE TABLE videos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '上传用户ID',
    category_id INT COMMENT '分类ID',
    media_type ENUM('video', 'audio') DEFAULT 'video' COMMENT '媒体类型',
    title VARCHAR(255) NOT NULL COMMENT '媒体标题',
    description TEXT COMMENT '媒体描述',
    tags JSON COMMENT '标签数组',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    url VARCHAR(500) COMMENT '公开访问URL',
    duration INT DEFAULT 0 COMMENT '媒体时长(秒)',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小(字节)',
    original_filename VARCHAR(255) COMMENT '原始文件名',
    file_path VARCHAR(500) COMMENT '文件路径',
    processed_path VARCHAR(500) COMMENT '处理后文件路径',
    hls_path VARCHAR(500) COMMENT 'HLS播放路径',
    resolution VARCHAR(20) COMMENT '分辨率(视频专用)',
    format VARCHAR(20) COMMENT '媒体格式',
    -- 音频专用字段
    bitrate INT COMMENT '音频比特率(kbps)',
    sample_rate INT COMMENT '音频采样率(Hz)',
    channels TINYINT COMMENT '音频声道数',
    -- 通用字段
    status ENUM('uploading', 'downloading', 'processing', 'published', 'private', 'deleted', 'pending_review', 'rejected') DEFAULT 'uploading' COMMENT '状态',
    visibility ENUM('public', 'private', 'member_only', 'vip_only', 'paid') DEFAULT 'public' COMMENT '可见性',
    price DECIMAL(10,2) DEFAULT 0.00 COMMENT '付费价格',
    view_count INT DEFAULT 0 COMMENT '播放次数',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    published_at TIMESTAMP NULL COMMENT '发布时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_category_id (category_id),
    INDEX idx_media_type (media_type),
    INDEX idx_status (status),
    INDEX idx_visibility (visibility),
    INDEX idx_published_at (published_at),
    INDEX idx_view_count (view_count),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_title_description (title, description)
) ENGINE=InnoDB COMMENT='媒体表(视频和音频统一)';

-- 5. 媒体处理任务表
CREATE TABLE video_processing_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    video_id INT NOT NULL COMMENT '媒体ID',
    task_type ENUM('transcode', 'thumbnail', 'hls', 'audio_process', 'waveform') NOT NULL COMMENT '任务类型',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '状态',
    progress INT DEFAULT 0 COMMENT '进度百分比',
    input_path VARCHAR(500) COMMENT '输入文件路径',
    output_path VARCHAR(500) COMMENT '输出文件路径',
    parameters JSON COMMENT '处理参数',
    error_message TEXT COMMENT '错误信息',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    INDEX idx_video_id (video_id),
    INDEX idx_status (status),
    INDEX idx_task_type (task_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='媒体处理任务表';

-- 6. 评论表
CREATE TABLE comments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    video_id INT NOT NULL COMMENT '媒体ID',
    user_id INT NOT NULL COMMENT '用户ID',
    parent_id INT DEFAULT NULL COMMENT '父评论ID(回复)',
    content TEXT NOT NULL COMMENT '评论内容',
    like_count INT DEFAULT 0 COMMENT '点赞数',
    reply_count INT DEFAULT 0 COMMENT '回复数',
    status ENUM('active', 'hidden', 'deleted') DEFAULT 'active' COMMENT '状态',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    INDEX idx_video_id (video_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='评论表';

-- 7. 收藏表
CREATE TABLE favorites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    video_id INT NOT NULL COMMENT '媒体ID',
    folder_name VARCHAR(100) DEFAULT 'default' COMMENT '收藏夹名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_video (user_id, video_id),
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_folder_name (folder_name)
) ENGINE=InnoDB COMMENT='收藏表';

-- 8. 播放记录表
CREATE TABLE watch_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    video_id INT NOT NULL COMMENT '媒体ID',
    watch_duration INT DEFAULT 0 COMMENT '播放时长(秒)',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '播放进度百分比',
    last_position INT DEFAULT 0 COMMENT '最后播放位置(秒)',
    device_type VARCHAR(50) COMMENT '设备类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_video (user_id, video_id),
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='播放记录表';

-- 9. 点赞表
CREATE TABLE likes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    target_type ENUM('video', 'comment') NOT NULL COMMENT '点赞目标类型',
    target_id INT NOT NULL COMMENT '目标ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_target (user_id, target_type, target_id),
    INDEX idx_user_id (user_id),
    INDEX idx_target (target_type, target_id)
) ENGINE=InnoDB COMMENT='点赞表';

-- 10. 订单表
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '订单号',
    type ENUM('membership', 'video', 'recharge', 'BALANCE_RECHARGE') NOT NULL COMMENT '订单类型',
    target_id INT COMMENT '目标ID(会员套餐ID或视频ID)',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    final_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    currency VARCHAR(10) DEFAULT 'CNY' COMMENT '货币类型',
    payment_method VARCHAR(50) COMMENT '支付方式',
    payment_status ENUM('pending', 'paid', 'failed', 'expired', 'refunded', 'cancelled') DEFAULT 'pending' COMMENT '支付状态',
    payment_time TIMESTAMP NULL COMMENT '支付时间',
    transaction_id VARCHAR(100) COMMENT '第三方交易ID',
    description TEXT COMMENT '订单描述',
    metadata JSON COMMENT '订单元数据',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    refund_amount DECIMAL(10,2) DEFAULT 0 COMMENT '退款金额',
    refund_time TIMESTAMP NULL COMMENT '退款时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_order_no (order_no),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='订单表';

-- 11. 邀请关系表
CREATE TABLE invitations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    inviter_id INT NOT NULL COMMENT '邀请人ID',
    invitee_id INT COMMENT '被邀请人ID',
    invitation_code VARCHAR(32) NOT NULL UNIQUE COMMENT '邀请码',
    email VARCHAR(255) COMMENT '邀请邮箱',
    status ENUM('pending', 'accepted', 'expired') DEFAULT 'pending' COMMENT '状态',
    reward_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '奖励金额',
    reward_status ENUM('pending', 'granted', 'cancelled') DEFAULT 'pending' COMMENT '奖励状态',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    accepted_at TIMESTAMP NULL COMMENT '接受时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invitee_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_inviter_id (inviter_id),
    INDEX idx_invitee_id (invitee_id),
    INDEX idx_invitation_code (invitation_code),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='邀请关系表';

-- 12. 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_group VARCHAR(50) DEFAULT NULL COMMENT '配置分组',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '值类型 (string, number, boolean, json)',
    description VARCHAR(255) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_config_group (config_group)
) ENGINE=InnoDB COMMENT='系统配置表';

-- 13. 操作日志表
CREATE TABLE operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '操作用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    target_type VARCHAR(50) COMMENT '目标类型',
    target_id INT COMMENT '目标ID',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_data JSON COMMENT '请求数据',
    response_data JSON COMMENT '响应数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_target (target_type, target_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='操作日志表';

-- 14. 文件上传记录表
CREATE TABLE file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '上传用户ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_type ENUM('video', 'audio', 'image', 'document', 'other') NOT NULL COMMENT '文件类型',
    upload_type ENUM('avatar', 'video', 'audio', 'thumbnail', 'attachment') NOT NULL COMMENT '上传类型',
    status ENUM('uploading', 'completed', 'failed', 'deleted') DEFAULT 'uploading' COMMENT '状态',
    md5_hash VARCHAR(32) COMMENT 'MD5哈希',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_file_type (file_type),
    INDEX idx_upload_type (upload_type),
    INDEX idx_status (status),
    INDEX idx_md5_hash (md5_hash)
) ENGINE=InnoDB COMMENT='文件上传记录表';

--
-- 从 src/database/migrations/20250122_create_playlists_tables.sql 合并而来
--

-- 创建播放列表相关表
-- 执行时间: 2025-01-22

-- 播放列表表
CREATE TABLE IF NOT EXISTS playlists (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    play_mode ENUM('sequence', 'loop', 'random') DEFAULT 'sequence',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 播放列表项目表
CREATE TABLE IF NOT EXISTS playlist_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    playlist_id INT NOT NULL,
    video_id INT NOT NULL,
    position INT NOT NULL DEFAULT 0,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_playlist_id (playlist_id),
    INDEX idx_video_id (video_id),
    INDEX idx_position (position),
    INDEX idx_added_at (added_at),
    
    -- 唯一约束：同一个播放列表中不能有重复的视频
    UNIQUE KEY unique_playlist_video (playlist_id, video_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 播放历史表（可选，用于记录用户播放历史）
CREATE TABLE IF NOT EXISTS play_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    video_id INT NOT NULL,
    played_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    watch_duration INT DEFAULT 0, -- 观看时长（秒）
    video_duration INT DEFAULT 0, -- 视频总时长（秒）
    completed BOOLEAN DEFAULT FALSE, -- 是否看完
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_video_id (video_id),
    INDEX idx_played_at (played_at),
    INDEX idx_completed (completed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加注释
ALTER TABLE playlists COMMENT = '用户播放列表表';
ALTER TABLE playlist_items COMMENT = '播放列表项目表';
ALTER TABLE play_history COMMENT = '播放历史记录表';

-- 支付通道管理
-- 用于存储和管理不同的支付方式及其配置
CREATE TABLE IF NOT EXISTS `payment_gateways` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL COMMENT '支付通道显示名称',
  `key` VARCHAR(50) NOT NULL UNIQUE COMMENT '支付通道唯一标识 (例如 alipay, wechat, paypal)',
  `enabled` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否启用此支付通道',
  `config` JSON NOT NULL COMMENT '支付通道的配置信息 (例如 AppID, Secret Key等)',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '支付通道配置表';

-- 插入默认支持的支付通道
-- 管理员需要在后台管理界面填写具体的 `config` 配置并启用
INSERT INTO `payment_gateways` (`name`, `key`, `config`) VALUES
('支付宝', 'alipay', JSON_OBJECT('appId', '', 'privateKey', '', 'alipayPublicKey', '')),
('微信支付', 'wechat', JSON_OBJECT('appId', '', 'mchId', '', 'apiKey', '')),
('PayPal', 'paypal', JSON_OBJECT('clientId', '', 'clientSecret', '', 'mode', 'sandbox')),
('Epay (USDT)', 'epay-usdt', JSON_OBJECT('partnerId', '', 'key', '', 'apiUrl', '', 'paymentMethod', 'usdt')),
('Epay (TRX)', 'epay-trx', JSON_OBJECT('partnerId', '', 'key', '', 'apiUrl', '', 'paymentMethod', 'trx'));

-- 15. 收益记录表
CREATE TABLE IF NOT EXISTS `earnings` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `order_id` INT NOT NULL COMMENT '关联的订单ID',
  `video_id` INT DEFAULT NULL COMMENT '关联的视频ID',
  `creator_id` INT NOT NULL COMMENT '创作者（收款方）的用户ID',
  `buyer_id` INT NOT NULL COMMENT '购买者（付款方）的用户ID',
  `total_amount` DECIMAL(10, 2) NOT NULL COMMENT '订单总金额',
  `platform_fee` DECIMAL(10, 2) NOT NULL COMMENT '平台抽成金额',
  `creator_earning` DECIMAL(10, 2) NOT NULL COMMENT '创作者实际收益',
  `commission_rate` DECIMAL(5, 4) NOT NULL COMMENT '当时的平台抽成率',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`video_id`) REFERENCES `videos`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`creator_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`buyer_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  INDEX `idx_creator_id` (`creator_id`),
  INDEX `idx_video_id` (`video_id`)
) COMMENT '视频销售收益记录表';

-- 16. 余额变动日志表
CREATE TABLE IF NOT EXISTS `balance_logs` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL COMMENT '关联的用户ID',
  `amount` DECIMAL(10, 2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_after` DECIMAL(10, 2) NOT NULL COMMENT '变动后的账户余额',
  `type` VARCHAR(50) NOT NULL COMMENT '变动类型 (e.g., video_earning, recharge, withdraw, purchase)',
  `description` VARCHAR(255) NOT NULL COMMENT '变动描述',
  `related_id` INT COMMENT '关联对象ID (如收益ID, 订单ID, 提现ID)',
  `related_type` VARCHAR(50) COMMENT '关联对象类型 (e.g., earning, order, withdrawal)',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_type` (`type`)
) COMMENT '用户余额变动日志表';


-- ----------------------------
-- Table structure for notifications
-- ----------------------------
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '接收通知的用户ID',
    type ENUM('system', 'video_review', 'comment', 'reply', 'follow', 'video_upload') DEFAULT 'system' COMMENT '通知类型',
    title VARCHAR(255) NOT NULL COMMENT '通知标题',
    message TEXT NOT NULL COMMENT '通知内容',
    reference_id INT COMMENT '关联对象ID (如视频ID, 评论ID)',
    reference_type VARCHAR(50) COMMENT '关联对象类型',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id_is_read (user_id, is_read)
) COMMENT='用户通知表';

-- ----------------------------
-- Table structure for follows
-- ----------------------------
CREATE TABLE IF NOT EXISTS follows (
    id INT PRIMARY KEY AUTO_INCREMENT,
    follower_id INT NOT NULL COMMENT '关注者用户ID',
    followed_id INT NOT NULL COMMENT '被关注者用户ID',
    status ENUM('active', 'blocked') DEFAULT 'active' COMMENT '关注状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (followed_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 确保同一用户不能重复关注
    UNIQUE KEY uk_follower_followed (follower_id, followed_id),
    
    -- 索引优化
    INDEX idx_follower_id (follower_id),
    INDEX idx_followed_id (followed_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    -- 防止自己关注自己
    CHECK (follower_id != followed_id)
) ENGINE=InnoDB COMMENT='用户关注关系表';

-- ----------------------------
-- Table structure for user_stats
-- ----------------------------
CREATE TABLE IF NOT EXISTS user_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    follower_count INT DEFAULT 0 COMMENT '粉丝数量',
    following_count INT DEFAULT 0 COMMENT '关注数量', 
    video_count INT DEFAULT 0 COMMENT '视频数量',
    total_views INT DEFAULT 0 COMMENT '总播放量',
    total_likes INT DEFAULT 0 COMMENT '总获赞数',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_follower_count (follower_count),
    INDEX idx_following_count (following_count)
) ENGINE=InnoDB COMMENT='用户统计信息表';

-- ----------------------------
-- Initialize user stats for existing users
-- ----------------------------
INSERT IGNORE INTO user_stats (user_id, video_count, total_views, total_likes)
SELECT 
    u.id,
    COALESCE(video_stats.video_count, 0),
    COALESCE(video_stats.total_views, 0),
    COALESCE(video_stats.total_likes, 0)
FROM users u
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) as video_count,
        SUM(view_count) as total_views,
        SUM(like_count) as total_likes
    FROM videos 
    WHERE status != 'deleted'
    GROUP BY user_id
) video_stats ON u.id = video_stats.user_id;

COMMIT;
