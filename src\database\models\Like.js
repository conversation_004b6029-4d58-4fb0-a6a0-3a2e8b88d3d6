const BaseModel = require('../BaseModel');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');

class Like extends BaseModel {
  constructor() {
    super('likes');
  }

  // 点赞/取消点赞
  async toggleLike(userId, targetId, targetType) {
    // 验证目标类型
    const validTypes = ['video', 'comment'];
    if (!validTypes.includes(targetType)) {
      throw new AppError('无效的点赞目标类型', 400, 'INVALID_TARGET_TYPE');
    }

    // 检查目标是否存在
    await this.validateTarget(targetId, targetType);

    // 检查是否已经点赞
    const existingLike = await this.findOne({
      user_id: userId,
      target_id: targetId,
      target_type: targetType
    });

    if (existingLike) {
      // 取消点赞
      await this.delete(existingLike.id);
      await this.decrementLikeCount(targetId, targetType);
      
      logger.info(`取消点赞: ${targetType} ${targetId}`, { userId });
      return { action: 'unliked', liked: false };
    } else {
      // 添加点赞
      const like = await this.create({
        user_id: userId,
        target_id: targetId,
        target_type: targetType
      });
      
      await this.incrementLikeCount(targetId, targetType);
      
      logger.info(`点赞: ${targetType} ${targetId}`, { userId });
      return { action: 'liked', liked: true, like };
    }
  }

  // 验证点赞目标是否存在
  async validateTarget(targetId, targetType) {
    let sql, tableName;
    
    switch (targetType) {
      case 'video':
        sql = 'SELECT id FROM videos WHERE id = ? AND status = "published"';
        tableName = 'videos';
        break;
      case 'comment':
        sql = 'SELECT id FROM comments WHERE id = ? AND status = "active"';
        tableName = 'comments';
        break;
      default:
        throw new AppError('无效的目标类型', 400, 'INVALID_TARGET_TYPE');
    }

    const result = await this.query(sql, [targetId]);
    if (result.length === 0) {
      throw new AppError(`${tableName === 'videos' ? '视频' : '评论'}不存在或不可用`, 404, 'TARGET_NOT_FOUND');
    }
  }

  // 增加点赞数
  async incrementLikeCount(targetId, targetType) {
    let sql;
    
    switch (targetType) {
      case 'video':
        sql = 'UPDATE videos SET like_count = like_count + 1 WHERE id = ?';
        break;
      case 'comment':
        sql = 'UPDATE comments SET like_count = like_count + 1 WHERE id = ?';
        break;
      default:
        return;
    }

    await this.query(sql, [targetId]);
  }

  // 减少点赞数
  async decrementLikeCount(targetId, targetType) {
    let sql;
    
    switch (targetType) {
      case 'video':
        sql = 'UPDATE videos SET like_count = GREATEST(like_count - 1, 0) WHERE id = ?';
        break;
      case 'comment':
        sql = 'UPDATE comments SET like_count = GREATEST(like_count - 1, 0) WHERE id = ?';
        break;
      default:
        return;
    }

    await this.query(sql, [targetId]);
  }

  // 检查用户是否已点赞
  async isLiked(userId, targetId, targetType) {
    const like = await this.findOne({
      user_id: userId,
      target_id: targetId,
      target_type: targetType
    });

    return !!like;
  }

  // 获取用户点赞列表
  async getUserLikes(userId, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      targetType = null
    } = options;

    let sql = `
      SELECT 
        l.*,
        CASE 
          WHEN l.target_type = 'video' THEN v.title
          WHEN l.target_type = 'comment' THEN c.content
        END as target_title,
        CASE 
          WHEN l.target_type = 'video' THEN v.thumbnail
          ELSE NULL
        END as target_thumbnail
      FROM likes l
      LEFT JOIN videos v ON l.target_type = 'video' AND l.target_id = v.id
      LEFT JOIN comments c ON l.target_type = 'comment' AND l.target_id = c.id
      WHERE l.user_id = ?
    `;

    const params = [userId];

    if (targetType) {
      sql += ' AND l.target_type = ?';
      params.push(targetType);
    }

    sql += ' ORDER BY l.created_at DESC';

    const offset = (page - 1) * pageSize;
    sql += ' LIMIT ? OFFSET ?';
    params.push(pageSize, offset);

    const likes = await this.query(sql, params);

    // 获取总数
    let countSql = 'SELECT COUNT(*) as total FROM likes WHERE user_id = ?';
    const countParams = [userId];

    if (targetType) {
      countSql += ' AND target_type = ?';
      countParams.push(targetType);
    }

    const countResult = await this.query(countSql, countParams);
    const total = countResult[0].total;

    return {
      data: likes,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 获取目标的点赞用户列表
  async getTargetLikes(targetId, targetType, options = {}) {
    const {
      page = 1,
      pageSize = 20
    } = options;

    const sql = `
      SELECT 
        l.*,
        u.username,
        u.nickname,
        u.avatar as user_avatar
      FROM likes l
      LEFT JOIN users u ON l.user_id = u.id
      WHERE l.target_id = ? AND l.target_type = ?
      ORDER BY l.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const offset = (page - 1) * pageSize;
    const likes = await this.query(sql, [targetId, targetType, pageSize, offset]);

    // 获取总数
    const countResult = await this.query(
      'SELECT COUNT(*) as total FROM likes WHERE target_id = ? AND target_type = ?',
      [targetId, targetType]
    );
    const total = countResult[0].total;

    return {
      data: likes,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 批量检查点赞状态
  async batchCheckLiked(userId, targets) {
    if (!targets || targets.length === 0) {
      return {};
    }

    const conditions = targets.map(() => '(target_id = ? AND target_type = ?)').join(' OR ');
    const params = [userId];
    targets.forEach(target => {
      params.push(target.id, target.type);
    });

    const sql = `
      SELECT target_id, target_type
      FROM likes
      WHERE user_id = ? AND (${conditions})
    `;

    const likes = await this.query(sql, params);
    
    const likedMap = {};
    likes.forEach(like => {
      const key = `${like.target_type}_${like.target_id}`;
      likedMap[key] = true;
    });

    return likedMap;
  }

  // 获取热门点赞内容
  async getPopularLikedContent(targetType, options = {}) {
    const {
      limit = 10,
      timeRange = '7d'
    } = options;

    let timeCondition = '';
    switch (timeRange) {
      case '1d':
        timeCondition = 'AND l.created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)';
        break;
      case '7d':
        timeCondition = 'AND l.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case '30d':
        timeCondition = 'AND l.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      default:
        timeCondition = '';
    }

    let sql;
    if (targetType === 'video') {
      sql = `
        SELECT 
          v.id,
          v.title,
          v.thumbnail,
          v.view_count,
          v.like_count,
          COUNT(l.id) as recent_likes,
          u.username,
          u.nickname
        FROM videos v
        LEFT JOIN likes l ON v.id = l.target_id AND l.target_type = 'video' ${timeCondition}
        LEFT JOIN users u ON v.user_id = u.id
        WHERE v.status = 'published' AND v.visibility = 'public'
        GROUP BY v.id
        ORDER BY recent_likes DESC, v.like_count DESC
        LIMIT ?
      `;
    } else if (targetType === 'comment') {
      sql = `
        SELECT 
          c.id,
          c.content,
          c.like_count,
          COUNT(l.id) as recent_likes,
          u.username,
          u.nickname,
          v.title as video_title
        FROM comments c
        LEFT JOIN likes l ON c.id = l.target_id AND l.target_type = 'comment' ${timeCondition}
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN videos v ON c.video_id = v.id
        WHERE c.status = 'active'
        GROUP BY c.id
        ORDER BY recent_likes DESC, c.like_count DESC
        LIMIT ?
      `;
    }

    return await this.query(sql, [limit]);
  }

  // 获取点赞统计
  async getLikeStats(targetId = null, targetType = null) {
    let sql = `
      SELECT 
        target_type,
        COUNT(*) as total_likes,
        COUNT(DISTINCT user_id) as unique_users,
        DATE(created_at) as date,
        COUNT(*) as daily_likes
      FROM likes
      WHERE 1=1
    `;

    const params = [];

    if (targetId && targetType) {
      sql += ' AND target_id = ? AND target_type = ?';
      params.push(targetId, targetType);
    } else if (targetType) {
      sql += ' AND target_type = ?';
      params.push(targetType);
    }

    sql += ' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
    sql += ' GROUP BY target_type, DATE(created_at)';
    sql += ' ORDER BY date DESC';

    return await this.query(sql, params);
  }

  // 清理用户所有点赞
  async clearUserLikes(userId) {
    const likes = await this.findAll({ user_id: userId });
    
    for (const like of likes) {
      await this.decrementLikeCount(like.target_id, like.target_type);
      await this.delete(like.id);
    }

    logger.info(`清理用户所有点赞: ${userId}`, { count: likes.length });
    return likes.length;
  }
}

module.exports = new Like();
