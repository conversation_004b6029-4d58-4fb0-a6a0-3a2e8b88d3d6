const BaseModel = require('../BaseModel');
const { AppError } = require('../../middleware/errorHandler');
const crypto = require('crypto');

class AuthToken extends BaseModel {
  constructor() {
    super('auth_tokens');
  }

  // 创建认证令牌记录
  async createToken(tokenData) {
    const {
      userId,
      tokenType, // 'access', 'refresh', 'reset', 'verification'
      token,
      expiresAt,
      deviceInfo = null,
      ipAddress = null
    } = tokenData;

    // 哈希存储token（安全考虑）
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

    const tokenRecord = await this.create({
      user_id: userId,
      token_type: tokenType,
      token_hash: hashedToken,
      expires_at: expiresAt,
      device_info: deviceInfo ? JSON.stringify(deviceInfo) : null,
      ip_address: ipAddress,
      is_revoked: false,
      created_at: new Date(),
      last_used_at: null
    });

    return tokenRecord;
  }

  // 验证令牌
  async validateToken(token, tokenType) {
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
    
    const tokenRecord = await this.findOne({
      token_hash: hashedToken,
      token_type: tokenType,
      is_revoked: false
    });

    if (!tokenRecord) {
      throw new AppError('令牌无效', 401, 'INVALID_TOKEN');
    }

    // 检查是否过期
    if (new Date() > new Date(tokenRecord.expires_at)) {
      // 自动清理过期令牌
      await this.revokeToken(hashedToken);
      throw new AppError('令牌已过期', 401, 'TOKEN_EXPIRED');
    }

    // 更新最后使用时间
    await this.update(tokenRecord.id, {
      last_used_at: new Date()
    });

    return tokenRecord;
  }

  // 撤销令牌
  async revokeToken(tokenOrHash) {
    const hashedToken = tokenOrHash.length === 64 ? 
      tokenOrHash : 
      crypto.createHash('sha256').update(tokenOrHash).digest('hex');

    const result = await this.update(
      { token_hash: hashedToken },
      { 
        is_revoked: true,
        revoked_at: new Date()
      }
    );

    return result;
  }

  // 撤销用户所有令牌
  async revokeAllUserTokens(userId, tokenType = null) {
    const conditions = { user_id: userId };
    if (tokenType) {
      conditions.token_type = tokenType;
    }

    const result = await this.query(
      `UPDATE ${this.tableName} SET is_revoked = true, revoked_at = NOW() WHERE user_id = ? ${tokenType ? 'AND token_type = ?' : ''}`,
      tokenType ? [userId, tokenType] : [userId]
    );

    return result;
  }

  // 清理过期令牌
  async cleanupExpiredTokens() {
    const result = await this.query(
      `DELETE FROM ${this.tableName} WHERE expires_at < NOW() OR is_revoked = true`
    );

    return result;
  }

  // 获取用户活跃令牌
  async getUserActiveTokens(userId, tokenType = null) {
    let sql = `
      SELECT id, token_type, expires_at, device_info, ip_address, created_at, last_used_at
      FROM ${this.tableName} 
      WHERE user_id = ? AND is_revoked = false AND expires_at > NOW()
    `;
    
    const params = [userId];
    
    if (tokenType) {
      sql += ' AND token_type = ?';
      params.push(tokenType);
    }
    
    sql += ' ORDER BY created_at DESC';
    
    return await this.query(sql, params);
  }

  // 获取令牌统计信息
  async getTokenStats(userId = null) {
    let sql = `
      SELECT 
        token_type,
        COUNT(*) as total_count,
        COUNT(CASE WHEN is_revoked = false AND expires_at > NOW() THEN 1 END) as active_count,
        COUNT(CASE WHEN is_revoked = true THEN 1 END) as revoked_count,
        COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as expired_count
      FROM ${this.tableName}
    `;
    
    const params = [];
    
    if (userId) {
      sql += ' WHERE user_id = ?';
      params.push(userId);
    }
    
    sql += ' GROUP BY token_type';
    
    return await this.query(sql, params);
  }

  // 检查令牌使用频率
  async checkTokenUsageFrequency(userId, tokenType, timeWindow = 3600) {
    const sql = `
      SELECT COUNT(*) as usage_count
      FROM ${this.tableName}
      WHERE user_id = ? 
        AND token_type = ? 
        AND last_used_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
        AND is_revoked = false
    `;
    
    const result = await this.query(sql, [userId, tokenType, timeWindow]);
    return result[0]?.usage_count || 0;
  }

  // 获取可疑令牌活动
  async getSuspiciousTokenActivity(timeWindow = 24) {
    const sql = `
      SELECT 
        user_id,
        token_type,
        ip_address,
        COUNT(*) as token_count,
        COUNT(DISTINCT ip_address) as unique_ips,
        MIN(created_at) as first_created,
        MAX(created_at) as last_created
      FROM ${this.tableName}
      WHERE created_at > DATE_SUB(NOW(), INTERVAL ? HOUR)
        AND is_revoked = false
      GROUP BY user_id, token_type
      HAVING token_count > 10 OR unique_ips > 5
      ORDER BY token_count DESC, unique_ips DESC
    `;
    
    return await this.query(sql, [timeWindow]);
  }

  // 按设备类型统计令牌
  async getTokensByDevice(userId = null) {
    let sql = `
      SELECT 
        JSON_EXTRACT(device_info, '$.type') as device_type,
        JSON_EXTRACT(device_info, '$.os') as device_os,
        COUNT(*) as token_count,
        MAX(last_used_at) as last_used
      FROM ${this.tableName}
      WHERE device_info IS NOT NULL 
        AND is_revoked = false 
        AND expires_at > NOW()
    `;
    
    const params = [];
    
    if (userId) {
      sql += ' AND user_id = ?';
      params.push(userId);
    }
    
    sql += ' GROUP BY device_type, device_os ORDER BY token_count DESC';
    
    return await this.query(sql, params);
  }

  // 令牌安全检查
  async performSecurityCheck(userId) {
    const checks = {
      activeTokens: 0,
      suspiciousIPs: 0,
      oldTokens: 0,
      multipleDevices: 0
    };

    // 检查活跃令牌数量
    const activeTokens = await this.getUserActiveTokens(userId);
    checks.activeTokens = activeTokens.length;

    // 检查可疑IP
    const ipStats = await this.query(`
      SELECT ip_address, COUNT(*) as count
      FROM ${this.tableName}
      WHERE user_id = ? 
        AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND is_revoked = false
      GROUP BY ip_address
      HAVING count > 5
    `, [userId]);
    checks.suspiciousIPs = ipStats.length;

    // 检查长期未使用的令牌
    const oldTokens = await this.query(`
      SELECT COUNT(*) as count
      FROM ${this.tableName}
      WHERE user_id = ? 
        AND is_revoked = false
        AND expires_at > NOW()
        AND (last_used_at IS NULL OR last_used_at < DATE_SUB(NOW(), INTERVAL 30 DAY))
    `, [userId]);
    checks.oldTokens = oldTokens[0]?.count || 0;

    // 检查多设备登录
    const deviceStats = await this.getTokensByDevice(userId);
    checks.multipleDevices = deviceStats.length;

    return checks;
  }
}

module.exports = new AuthToken();
