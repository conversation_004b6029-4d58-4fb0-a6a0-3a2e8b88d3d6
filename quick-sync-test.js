/**
 * 快速跨设备同步测试
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'aaaabbbb123'
};

async function quickSyncTest() {
  console.log('🚀 快速跨设备同步测试\n');

  try {
    // 设备A登录
    console.log('📱 设备A登录...');
    const loginA = await axios.post(`${API_BASE_URL}/auth/login`, TEST_USER);
    const tokenA = loginA.data.data.tokens.accessToken;
    
    // 设备A获取播放列表
    console.log('📋 设备A获取播放列表...');
    const playlistsA = await axios.get(`${API_BASE_URL}/playlists`, {
      headers: { Authorization: `Bearer ${tokenA}` }
    });
    console.log(`✅ 设备A播放列表数量: ${playlistsA.data.data.playlists.length}`);
    
    // 设备A登出
    await axios.post(`${API_BASE_URL}/auth/logout`, {}, {
      headers: { Authorization: `Bearer ${tokenA}` }
    });
    
    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 设备B登录（同一用户）
    console.log('\n📱 设备B登录（同一账户）...');
    const loginB = await axios.post(`${API_BASE_URL}/auth/login`, TEST_USER);
    const tokenB = loginB.data.data.tokens.accessToken;
    
    // 设备B获取播放列表
    console.log('📋 设备B获取播放列表...');
    const playlistsB = await axios.get(`${API_BASE_URL}/playlists`, {
      headers: { Authorization: `Bearer ${tokenB}` }
    });
    console.log(`✅ 设备B播放列表数量: ${playlistsB.data.data.playlists.length}`);
    
    // 比较结果
    const countA = playlistsA.data.data.playlists.length;
    const countB = playlistsB.data.data.playlists.length;
    
    if (countA === countB) {
      console.log('\n🎉 成功！跨设备播放列表数量一致');
      console.log('📊 播放列表详情:');
      playlistsB.data.data.playlists.forEach(p => {
        console.log(`  - ${p.name} (${p.itemCount} 项)`);
      });
    } else {
      console.log('\n⚠️  警告：跨设备播放列表数量不一致');
      console.log(`设备A: ${countA} 个，设备B: ${countB} 个`);
    }
    
    // 设备B登出
    await axios.post(`${API_BASE_URL}/auth/logout`, {}, {
      headers: { Authorization: `Bearer ${tokenB}` }
    });
    
    console.log('\n✅ 测试完成');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.response?.data?.message || error.message);
  }
}

quickSyncTest();
