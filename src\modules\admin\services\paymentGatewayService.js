const PaymentGateway = require('../../../database/models/PaymentGateway');
const { AppError } = require('../../../middleware/errorHandler');

class PaymentGatewayService {
  constructor() {
    this.paymentGatewayModel = new PaymentGateway();
  }

  /**
   * 获取所有支付通道
   * @returns {Promise<Array>}
   */
  async getAllGateways() {
    const gateways = await this.paymentGatewayModel.findAll({}, { orderBy: 'id', order: 'ASC' });
    // 解析JSON字符串
    return gateways.map(gateway => ({
      ...gateway,
      config: typeof gateway.config === 'string' ? JSON.parse(gateway.config) : gateway.config
    }));
  }

  /**
   * 更新支付通道
   * @param {number} id
   * @param {object} data
   * @returns {Promise<Object>}
   */
  async updateGateway(id, data) {
    const gateway = await this.paymentGatewayModel.findById(id);
    if (!gateway) {
      throw new AppError('支付通道不存在', 404);
    }

    const updatedGateway = await this.paymentGatewayModel.updateGateway(id, data);
    
    // 解析JSON字符串
    return {
      ...updatedGateway,
      config: typeof updatedGateway.config === 'string' ? JSON.parse(updatedGateway.config) : updatedGateway.config
    };
  }
}

module.exports = PaymentGatewayService; 