const BaseModel = require('../BaseModel');

class Earning extends BaseModel {
  constructor() {
    super('earnings');
  }

  async create(earningData, connection = null) {
    const fields = [
      'order_id', 'video_id', 'creator_id', 'buyer_id',
      'total_amount', 'platform_fee', 'creator_earning', 'commission_rate'
    ];
    const placeholders = fields.map(() => '?').join(', ');
    const values = fields.map(field => earningData[field]);

    const query = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
    
    const executor = connection || this;
    const [result] = await executor.query(query, values);
    return result.insertId;
  }
}

module.exports = new Earning(); 