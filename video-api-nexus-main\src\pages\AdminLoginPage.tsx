import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { login as apiLogin } from '@/lib/api';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from '@/hooks/useAuth';
import { usePlaylistSync } from '@/hooks/usePlaylistSync';
import { useToast } from "@/components/ui/use-toast";

const AdminLoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();
  const { toast } = useToast();

  // 获取播放列表同步功能
  const { syncServerToLocalOnLogin } = usePlaylistSync({
    autoSync: false, // 管理员登录页面不需要自动同步
  });

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');
    try {
      const response = await apiLogin({ email, password, isAdminLogin: true });
      const accessToken = response.data.data.tokens.accessToken;

      if (accessToken) {
        // 管理员登录时也传入同步函数
        login(accessToken, syncServerToLocalOnLogin);

        toast({
          title: "登录成功",
          description: "欢迎进入管理后台！",
        });

        navigate('/admin/dashboard');
      } else {
        throw new Error("登录失败，未从服务器收到Token");
      }
    } catch (err) {
      console.error('Admin login error:', err);
      const errorMessage = err.response?.data?.message || '登录时发生错误，请重试';
      setError(errorMessage);
      toast({
        title: "登录失败",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <Card className="mx-auto max-w-sm">
        <CardHeader>
          <CardTitle className="text-2xl">Admin Login</CardTitle>
          <CardDescription>Enter your credentials to access the admin panel</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            {error && <p className="text-red-500 text-sm">{error}</p>}
            <Button type="submit" className="w-full">
              Login to Admin Panel
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminLoginPage; 