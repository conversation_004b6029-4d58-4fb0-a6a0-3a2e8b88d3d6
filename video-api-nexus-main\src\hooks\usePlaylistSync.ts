import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { usePlaylist } from '@/hooks/usePlaylist';
import { 
  useUserPlaylists, 
  useCreatePlaylist, 
  useUpdatePlaylist, 
  useAddVideoToPlaylist,
  useRemoveVideoFromPlaylist,
  useRecordPlayHistory 
} from '@/hooks/usePlaylistApi';
import { PlaylistItem } from '@/types/playlist';
import { toast } from '@/hooks/use-toast';

export interface UsePlaylistSyncOptions {
  autoSync?: boolean;
  syncInterval?: number; // 同步间隔（毫秒）
  onSyncSuccess?: () => void;
  onSyncError?: (error: any) => void;
}

export const usePlaylistSync = (options: UsePlaylistSyncOptions = {}) => {
  const {
    autoSync = false, // 改为默认关闭自动同步，避免频繁请求
    syncInterval = 30 * 60 * 1000, // 延长为30分钟，减少服务器压力
    onSyncSuccess,
    onSyncError,
  } = options;

  const { isAuthenticated } = useAuth();
  const localPlaylist = usePlaylist();
  
  const { 
    data: serverPlaylistsData = { playlists: [] }, 
    isLoading: isLoadingServer,
    refetch: refetchServerPlaylists 
  } = useUserPlaylists(true, { enabled: isAuthenticated }) as { 
    data: { playlists: any[] } | undefined; 
    isLoading: boolean; 
    refetch: () => Promise<{ data: { playlists: any[] } | undefined }>;
  };

  const createServerPlaylist = useCreatePlaylist();
  const updateServerPlaylist = useUpdatePlaylist();
  const addVideoToServerPlaylist = useAddVideoToPlaylist();
  const removeVideoFromServerPlaylist = useRemoveVideoFromPlaylist();
  const recordPlayHistory = useRecordPlayHistory();

  const [syncStatus, setSyncStatus] = useState<{
    isLoading: boolean;
    lastSyncTime: Date | null;
    error: string | null;
    pendingOperations: number;
  }>({
    isLoading: false,
    lastSyncTime: null,
    error: null,
    pendingOperations: 0,
  });

  const initialSyncPerformed = useRef(false);

  const syncServerToLocalOnLogin = useCallback(async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('跳过同步：未找到认证token');
      return;
    }

    try {
      setSyncStatus(prev => ({ ...prev, isLoading: true, error: null }));
      const serverData = await refetchServerPlaylists();
      const playlists = serverData.data?.playlists || [];

      if (playlists.length > 0) {
        const convertedPlaylists = playlists.map((serverPl: any) => ({
          id: `server_${serverPl.id}`,
          name: serverPl.name,
          items: (serverPl.items || []).map((item: any) => ({
            id: `item_${item.id}`,
            videoId: item.video_id || item.videoId,
            title: item.video?.title || '未知标题',
            duration: item.video?.duration || 0,
            thumbnail: item.video?.thumbnail_url || item.video?.thumbnail,
            mediaType: (item.video?.mediaType || 'video') as 'video' | 'audio',
            addedAt: new Date(item.added_at || item.addedAt),
            url: item.video?.url,
          })),
          currentIndex: 0,
          isPlaying: false,
          playMode: (serverPl.play_mode || serverPl.playMode || 'sequence') as 'sequence' | 'loop' | 'random',
          createdAt: new Date(serverPl.created_at || serverPl.createdAt),
          updatedAt: new Date(serverPl.updated_at || serverPl.updatedAt),
          isTemporary: false,
        }));

        localPlaylist.setSavedPlaylists(convertedPlaylists);

        const tempPlaylist = convertedPlaylists.find((pl: any) => pl.name === '临时播放列表');
        if (tempPlaylist) {
          localPlaylist.setCurrentPlaylist({
            ...tempPlaylist,
            currentIndex: tempPlaylist.items.length > 0 ? 0 : -1,
            isPlaying: false,
          });
        }
      }

      setSyncStatus({ isLoading: false, lastSyncTime: new Date(), error: null, pendingOperations: 0 });
      if (onSyncSuccess) {
        onSyncSuccess();
      }
    } catch (error: any) {
      setSyncStatus({ isLoading: false, lastSyncTime: null, error: error.message || '同步失败', pendingOperations: 0 });
      if (onSyncError) {
        onSyncError(error);
      }
    }
  }, [refetchServerPlaylists, localPlaylist, onSyncSuccess, onSyncError]);

  useEffect(() => {
    if (isAuthenticated && !initialSyncPerformed.current) {
      initialSyncPerformed.current = true;
      syncServerToLocalOnLogin();
    } else if (!isAuthenticated) {
      initialSyncPerformed.current = false;
    }
  }, [isAuthenticated, syncServerToLocalOnLogin]);
  
  const manualSync = useCallback(async () => {
    await syncServerToLocalOnLogin();
    toast({ title: '同步完成', description: '播放列表已手动同步。' });
  }, [syncServerToLocalOnLogin]);

  const addToPlaylistSync = useCallback(async (item: PlaylistItem) => {
    if (isAuthenticated && item.videoId) {
        const serverPlaylists = serverPlaylistsData?.playlists || [];
        let targetPlaylist = serverPlaylists.find((p: any) => p.name === '临时播放列表');

        if (!targetPlaylist) {
            targetPlaylist = await createServerPlaylist.mutateAsync({
                name: '临时播放列表',
                description: '自动创建的播放列表',
                isPublic: false,
            });
        }
        
        if (targetPlaylist) {
            await addVideoToServerPlaylist.mutateAsync({
                playlistId: targetPlaylist.id,
                data: { videoId: Number(item.videoId) }
            });
            await refetchServerPlaylists();
        }
    }
    
    localPlaylist.addToCurrentPlaylist(item);

    if (localPlaylist.currentPlaylist) {
        const pl = localPlaylist.currentPlaylist;
        if (pl.items.length === 1 && pl.currentIndex === -1) {
            console.log('PlaylistSync Fix: 第一个项目已添加，将currentIndex设置为0');
            localPlaylist.setCurrentIndex(0);
        } else if (pl.items.length > 0 && pl.currentIndex === -1) {
            console.log('PlaylistSync Fix: 列表不为空但无选中项，将currentIndex设置为0');
            localPlaylist.setCurrentIndex(0);
        }
    }

  }, [isAuthenticated, localPlaylist, serverPlaylistsData, createServerPlaylist, addVideoToServerPlaylist, refetchServerPlaylists]);

  const removeFromPlaylistSync = useCallback(async (itemId: string) => {
    const itemToRemove = localPlaylist.currentPlaylist?.items.find(i => i.id === itemId);
    if (isAuthenticated && itemToRemove?.videoId) {
        const serverPlaylists = serverPlaylistsData?.playlists || [];
        const targetPlaylist = serverPlaylists.find((p: any) => p.name === localPlaylist.currentPlaylist?.name);

        if (targetPlaylist) {
            await removeVideoFromServerPlaylist.mutateAsync({
                playlistId: targetPlaylist.id,
                videoId: Number(itemToRemove.videoId)
            });
            await refetchServerPlaylists();
        }
    }
    localPlaylist.removeFromPlaylist(itemId);
  }, [isAuthenticated, localPlaylist, serverPlaylistsData, removeVideoFromServerPlaylist, refetchServerPlaylists]);

  const recordPlayHistoryToServer = useCallback(async (videoId: number, watchDuration: number, videoDuration: number) => {
    if (!isAuthenticated) return;
    try {
        await recordPlayHistory.mutateAsync({ 
            videoId, 
            watchDuration,
            videoDuration,
            completed: watchDuration >= videoDuration 
        });
    } catch (error) {
        console.error("记录播放历史失败:", error);
    }
  }, [isAuthenticated, recordPlayHistory]);
  
  return {
    ...localPlaylist,
    serverPlaylists: serverPlaylistsData?.playlists || [],
    isLoadingServer,
    syncStatus,
    manualSync,
    addToPlaylistSync,
    removeFromPlaylistSync,
    recordPlayHistoryToServer,
    isAuthenticated,
    isSyncing: syncStatus.isLoading,
  };
}; 