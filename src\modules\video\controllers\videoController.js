const { AppError, asyncHandler } = require('../../../middleware/errorHandler');
const { operationLogger } = require('../../../middleware/requestLogger');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const Video = require('../../../database/models/Video');
const Category = require('../../../database/models/Category');
const Order = require('../../../database/models/Order');
const fileService = require('../../../services/fileService');
const videoService = require('../services/videoService');
const path = require('path');
const fs = require('fs').promises;

class VideoController {
  // 上传媒体文件（视频或音频）
  uploadVideo = asyncHandler(async (req, res) => {
    const userId = req.user.id;

    if (!req.file) {
      throw new AppError('请选择媒体文件', 400, 'NO_FILE_UPLOADED');
    }

    const file = req.file;
    const { title, description, categoryId, tags, visibility = 'public', price = 0 } = req.body;

    // 自动检测媒体类型
    const mediaType = this.detectMediaType(file);

    try {
      // 验证分类
      if (categoryId) {
        const category = await Category.findById(categoryId);
        if (!category) {
          throw new AppError('分类不存在', 404, 'CATEGORY_NOT_FOUND');
        }
      }

      // 解析标签
      let parsedTags = [];
      if (tags) {
        try {
          parsedTags = typeof tags === 'string' ? JSON.parse(tags) : tags;
        } catch (error) {
          parsedTags = [];
        }
      }

      // 创建媒体记录
      const mediaData = {
        userId,
        categoryId: categoryId || null,
        mediaType,
        title,
        description: description || '',
        tags: parsedTags,
        visibility,
        price: parseFloat(price),
        originalFilename: file.originalname,
        filePath: file.path,
        fileSize: file.size,
        format: path.extname(file.originalname).substring(1),
        duration: 0,
        resolution: '未知'
      };

      // 添加视频特有字段
      if (mediaType === 'video') {
        mediaData.resolution = '未知';
      }

      const video = await Video.createVideo(mediaData);

      // 所有的视频上传后都需要审核
      const initialStatus = 'pending_review';

      // 异步处理媒体文件，不阻塞响应
      videoService.processUploadedVideo(video, initialStatus)
        .catch(processingError => {
          // 异步处理中的错误只记录日志，不影响主流程响应
          logger.error(`后台处理视频 ${video.id} 时发生严重错误:`, processingError);
          // 可以在这里添加额外的错误处理逻辑，例如更新视频状态为 'failed'
        });

      // 记录操作日志
      operationLogger.logUserOperation(
        req,
        `${mediaType}_upload`,
        video.id,
        `上传${mediaType === 'audio' ? '音频' : '视频'}`,
        {
          title,
          fileName: file.originalname,
          fileSize: file.size,
          mediaType
        }
      );

      const successMessage = `${mediaType === 'audio' ? '音频' : '视频'}提交成功，等待管理员审核`;

      res.status(202).json({
        success: true,
        message: successMessage,
        data: {
          media: {
            id: video.id,
            title: video.title,
            mediaType: video.media_type,
            status: 'processing', // 客户端可以显示为“处理中”或“审核中”
            created_at: video.created_at
          }
        }
      });

    } catch (error) {
      // Cleanup the uploaded file on error
      if (file && file.path) {
        await fs.unlink(file.path).catch(() => {});
      }
      throw error;
    }
  });

  // 从URL上传视频（仅限管理员）
  uploadVideoFromUrl = asyncHandler(async (req, res) => {
    const { url, title, description, categoryId, tags, visibility, price } = req.body;
    const userId = req.user.id;

    // 1. 基本验证
    if (!url || !title) {
      throw new AppError('视频URL和标题不能为空', 400, 'MISSING_URL_OR_TITLE');
    }

    try {
      // 2. 直接构建符合数据库 schema 的对象，绕过 createVideo 方法
      const mediaData = {
        user_id: userId,
        title,
        description: description || '',
        category_id: categoryId || null,
        tags: JSON.stringify(tags || []),
        visibility: visibility || 'public',
        price: price || 0,
        status: 'downloading', // 初始状态为下载中
        original_filename: path.basename(new URL(url).pathname),
        media_type: 'video',
        file_path: null,
        file_size: 0,
        url: null,
        thumbnail_url: null,
        duration: 0,
        resolution: '未知',
        processed_path: null,
        hls_path: null,
        view_count: 0,
        like_count: 0,
        comment_count: 0,
        download_count: 0
      };
      
      const newVideoId = await Video.create(mediaData);
      const video = await Video.findById(newVideoId);

      if (!video) {
        throw new AppError('创建视频记录后无法找到该记录', 500, 'VIDEO_CREATION_FETCH_FAILED');
      }

      // 3. 异步处理下载和后续流程
      videoService.processVideoFromUrl(video, url)
        .catch(processingError => {
          logger.error(`从URL处理视频 ${video.id} 时发生严重错误:`, processingError);
        });
      
      // 4. 立即返回成功响应
      res.status(202).json({
        success: true,
        message: '视频链接已提交，后台将开始下载和处理',
        data: {
          media: {
            id: video.id,
            title: video.title,
            status: 'downloading'
          }
        }
      });
    } catch (error) {
      logger.error('从URL上传视频失败:', error);
      // 这里不需要删除文件，因为文件还未下载
      throw error;
    }
  });

  // 检测媒体类型
  detectMediaType(file) {
    const audioExtensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', '.wma'];
    const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv'];

    const ext = path.extname(file.originalname).toLowerCase();
    const mimeType = file.mimetype.toLowerCase();

    // 优先根据MIME类型判断
    if (mimeType.startsWith('audio/')) {
      return 'audio';
    } else if (mimeType.startsWith('video/')) {
      return 'video';
    }

    // 备用：根据文件扩展名判断
    if (audioExtensions.includes(ext)) {
      return 'audio';
    } else if (videoExtensions.includes(ext)) {
      return 'video';
    }

    // 默认为视频（向后兼容）
    return 'video';
  }

  // 专门的音频上传方法
  uploadAudio = asyncHandler(async (req, res) => {
    // 强制设置媒体类型为音频
    req.body.mediaType = 'audio';
    return await this.uploadVideo(req, res);
  });

  // 获取音频列表
  getAudioList = asyncHandler(async (req, res) => {
    // 强制设置媒体类型筛选为音频
    req.query.mediaType = 'audio';
    return await this.getVideoList(req, res);
  });

  // 获取视频列表（仅视频）
  getVideoOnlyList = asyncHandler(async (req, res) => {
    // 强制设置媒体类型筛选为视频
    req.query.mediaType = 'video';
    return await this.getVideoList(req, res);
  });

  // 获取媒体详情
  getVideoDetails = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user?.id;

    // --- 关键修复：先检查权限，再处理缓存 ---

    // 1. 直接从数据库获取视频的基本信息用于权限判断（包含用户信息）
    // 注意：这里我们不希望被任何可能存在的、不安全的缓存影响
    const videoQuery = `
      SELECT v.*, u.username as uploader_username, u.nickname as uploader_nickname, u.avatar as uploader_avatar,
             c.name as category_name
      FROM videos v
      LEFT JOIN users u ON v.user_id = u.id
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE v.id = ?
    `;
    const videoResults = await Video.query(videoQuery, [id]);
    const videoForAccessCheck = videoResults[0];

    if (!videoForAccessCheck) {
      throw new AppError('视频不存在', 404, 'VIDEO_NOT_FOUND');
    }

    // 2. 执行权限检查
    const accessResult = await this.checkVideoAccess(videoForAccessCheck, req.user);
    if (!accessResult.access) {
      switch (accessResult.reason) {
        case 'NEEDS_PURCHASE':
          throw new AppError('此视频需要付费购买才能观看', 403, 'NEEDS_PURCHASE', { price: accessResult.details.price });
        case 'NEEDS_MEMBERSHIP':
          throw new AppError('此内容是会员专属，请先开通会员', 403, 'NEEDS_MEMBERSHIP');
        case 'NEEDS_LOGIN':
          throw new AppError('请登录后访问', 401, 'UNAUTHORIZED');
        case 'NOT_PUBLISHED':
          throw new AppError('该视频尚未发布或正在审核中', 403, 'NOT_AVAILABLE');
        default:
      throw new AppError('无权访问此视频', 403, 'ACCESS_DENIED');
    }
    }

    // 3. 如果有权访问，则准备并返回视频数据
    const video = { ...videoForAccessCheck };

      // --- 数据重塑 ---
      if (video.uploader_username || video.uploader_nickname) {
        video.uploader = {
          id: video.user_id,
          username: video.uploader_username,
          nickname: video.uploader_nickname,
          avatar: video.uploader_avatar
        };
        // 保留这些字段供前端兼容性使用
        video.uploader_username = video.uploader_username;
        video.uploader_nickname = video.uploader_nickname;
        // 只删除avatar字段，因为已经移到uploader对象中
        delete video.uploader_avatar;
      }
      if (video.category_name) {
        video.category = {
          id: video.category_id,
          name: video.category_name
        };
        delete video.category_id;
        delete video.category_name;
      }
      // --- 修改结束 ---

      // 缓存视频详情
    const cacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'details', id, userId || 'guest');
      await cache.set(cacheKey, video, 300); // 5分钟缓存

    // 增加观看次数
    if (video.status === 'published') {
      await Video.incrementViewCount(id, userId);

      // 🔧 更新缓存中的观看次数，保持数据一致性
      try {
        const updatedCacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'details', id, userId || 'guest');
        const cachedVideo = await cache.get(updatedCacheKey);
        if (cachedVideo) {
          cachedVideo.view_count = (cachedVideo.view_count || 0) + 1;
          await cache.set(updatedCacheKey, cachedVideo, 300);
        }

        // 同时更新不带用户ID的通用缓存
        const generalCacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'details', id, 'guest');
        const generalCachedVideo = await cache.get(generalCacheKey);
        if (generalCachedVideo) {
          generalCachedVideo.view_count = (generalCachedVideo.view_count || 0) + 1;
          await cache.set(generalCacheKey, generalCachedVideo, 300);
        }
      } catch (cacheError) {
        logger.warn('更新视频观看次数缓存失败:', cacheError);
        // 缓存更新失败不影响主流程
      }
    }

    res.json({
      success: true,
      data: {
        video
      }
    });
  });

  // 检查视频访问权限
  checkVideoAccess = async (video, user) => {
    // 1. 管理员和视频作者拥有无条件访问权
    if (user?.role === 'admin' || (user && video.user_id === user.id)) {
      return { access: true, reason: 'owner_or_admin' };
    }

    // 2. 检查视频是否已发布
    if (video.status !== 'published') {
      return { access: false, reason: 'NOT_PUBLISHED' };
    }

    // 3. 根据视频的可见性进行分层检查
    switch (video.visibility) {
      case 'public':
        return { access: true, reason: 'public_video' };

      case 'paid':
    if (!user) {
          return { access: false, reason: 'NEEDS_LOGIN' };
    }
        const hasPurchased = await Order.hasUserPurchasedVideo(user.id, video.id);
        if (hasPurchased) {
          return { access: true, reason: 'purchased' };
        } else {
          return { access: false, reason: 'NEEDS_PURCHASE', details: { price: video.price } };
        }

      case 'member_only':
        if (!user) {
          return { access: false, reason: 'NEEDS_LOGIN' };
        }
        const isMember = ['member', 'vip'].includes(user.role);
        if (isMember) {
          return { access: true, reason: 'is_member' };
        } else {
          return { access: false, reason: 'NEEDS_MEMBERSHIP' };
        }
        
      default:
        // 对于任何其他情况（包括旧的 private, vip_only 等），默认拒绝访问
        return { access: false, reason: 'GENERIC_DENIAL' };
    }
  }

  // 获取媒体列表
  getVideoList = asyncHandler(async (req, res) => {
    const {
      page = 1,
      limit = 12,
      sortBy = 'created_at', 
      order = 'desc',
      categoryId,
      mediaType,
      keyword
    } = req.query;

    const options = {
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      sortBy,
      order,
      filters: {}
    };

    if (categoryId) options.filters.categoryId = categoryId;
    if (mediaType) options.filters.mediaType = mediaType;
    if (keyword) options.filters.keyword = keyword;

    // 关键修复：直接调用 Video 模型，绕过 service 层
    const result = await Video.getAllVideos(options);

    // 数据重塑，以匹配前端期望的格式
    const reshapedVideos = result.videos.map(video => {
      const newVideo = { ...video };

      // 重塑 uploader 信息
      if (newVideo.uploader_id || newVideo.uploader_nickname) {
        newVideo.uploader = {
          id: newVideo.uploader_id,
          nickname: newVideo.uploader_nickname,
        };
        delete newVideo.uploader_id;
        delete newVideo.uploader_nickname;
      }

      // 重塑 category 信息
      if (newVideo.category_id || newVideo.category_name) {
        newVideo.category = {
          id: newVideo.category_id,
          name: newVideo.category_name,
        };
        delete newVideo.category_id;
        delete newVideo.category_name;
      }

      return newVideo;
    });

    res.json({
      success: true,
      data: {
        videos: reshapedVideos,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          totalPages: result.totalPages
        }
      }
    });
  });

  // 获取用户视频列表
  getUserVideos = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { page = 1, pageSize = 20 } = req.query;

    const result = await videoService.getVideoList(
      { userId: id },
      { page: parseInt(page), limit: parseInt(pageSize) }
    );

    res.json({
      success: true,
      data: result,
    });
  });

  // 更新视频信息
  updateVideo = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;
    const updateData = req.body;

    // 获取视频信息
    const video = await Video.findById(id);
    if (!video) {
      throw new AppError('视频不存在', 404, 'VIDEO_NOT_FOUND');
    }

    // 检查权限
    if (video.user_id !== userId && req.user.role !== 'admin') {
      throw new AppError('无权修改此视频', 403, 'ACCESS_DENIED');
    }

    // 验证分类
    if (updateData.categoryId) {
      const category = await Category.findById(updateData.categoryId);
      if (!category) {
        throw new AppError('分类不存在', 404, 'CATEGORY_NOT_FOUND');
      }
    }

    // 更新视频
    const updatedVideo = await Video.updateVideo(id, updateData);

    // 清除相关缓存
    await this.clearVideoCache(id);

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      'video_update',
      id,
      '更新视频信息',
      { updatedFields: Object.keys(updateData) }
    );

    res.json({
      success: true,
      message: '视频信息更新成功',
      data: {
        video: updatedVideo
      }
    });
  });

  // 删除视频
  deleteVideo = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;

    // 获取视频信息
    const video = await Video.findById(id);
    if (!video) {
      throw new AppError('视频不存在', 404, 'VIDEO_NOT_FOUND');
    }

    // 检查权限
    if (video.user_id !== userId && req.user.role !== 'admin') {
      throw new AppError('无权删除此视频', 403, 'ACCESS_DENIED');
    }

    // 软删除视频
    await Video.softDeleteVideo(id);

    // 异步删除文件
    setImmediate(async () => {
      try {
        await videoService.deleteVideoFiles(video);
      } catch (error) {
        logger.error(`删除视频文件失败: ${id}`, error);
      }
    });

    // 清除相关缓存
    await this.clearVideoCache(id);

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      'video_delete',
      id,
      '删除视频',
      { title: video.title }
    );

    res.json({
      success: true,
      message: '视频删除成功'
    });
  });

  // 获取热门视频
  getPopularVideos = asyncHandler(async (req, res) => {
    const { timeRange = '7d', limit = 10 } = req.query;

    const cacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'popular', timeRange, limit);
    let videos = await cache.get(cacheKey);

    if (!videos) {
      videos = await Video.getPopularVideos(parseInt(limit), timeRange);
      await cache.set(cacheKey, videos, 600); // 10分钟缓存
    }

    res.json({
      success: true,
      data: {
        videos
      }
    });
  });

  // 获取推荐视频
  getRecommendedVideos = asyncHandler(async (req, res) => {
    const userId = req.user?.id;
    const { limit = 10 } = req.query;

    if (!userId) {
      // 未登录用户返回热门视频
      return this.getPopularVideos(req, res);
    }

    const cacheKey = cache.generateKey(CACHE_KEYS.VIDEO, 'recommended', userId, limit);
    let videos = await cache.get(cacheKey);

    if (!videos) {
      videos = await Video.getRecommendedVideos(userId, parseInt(limit));
      await cache.set(cacheKey, videos, 300); // 5分钟缓存
    }

    res.json({
      success: true,
      data: {
        videos
      }
    });
  });

  // 获取当前登录用户的视频列表
  getMyVideos = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    req.params.id = userId; // 将当前用户ID赋给params.id以复用getUserVideos
    return await this.getUserVideos(req, res);
  });

  // 搜索视频
  searchVideos = asyncHandler(async (req, res) => {
    const {
      keyword,
      page = 1,
      pageSize = 20,
      categoryId,
      sortBy = 'relevance'
    } = req.query;

    if (!keyword || keyword.trim().length === 0) {
      throw new AppError('搜索关键词不能为空', 400, 'KEYWORD_REQUIRED');
    }

    const result = await videoService.searchVideos({
      keyword: keyword.trim(),
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      categoryId: categoryId ? parseInt(categoryId) : null,
      sortBy
    });

    res.json({
      success: true,
      data: result
    });
  });

  // 获取视频统计信息
  getVideoStats = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;

    // 获取视频信息
    const video = await Video.findById(id);
    if (!video) {
      throw new AppError('视频不存在', 404, 'VIDEO_NOT_FOUND');
    }

    // 检查权限
    if (video.user_id !== userId && req.user.role !== 'admin') {
      throw new AppError('无权查看此视频统计', 403, 'ACCESS_DENIED');
    }

    const stats = await videoService.getVideoAnalytics(id);

    res.json({
      success: true,
      data: {
        stats
      }
    });
  });

  // 获取视频处理状态
  getVideoProcessingStatus = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;

    // 获取视频信息
    const video = await Video.findById(id);
    if (!video) {
      throw new AppError('视频不存在', 404, 'VIDEO_NOT_FOUND');
    }

    // 检查权限
    if (video.user_id !== userId && req.user.role !== 'admin') {
      throw new AppError('无权查看此视频处理状态', 403, 'ACCESS_DENIED');
    }

    const processingStatus = await videoService.getVideoProcessingStatus(id);

    res.json({
      success: true,
      data: {
        status: processingStatus,
        video: {
          id: video.id,
          title: video.title,
          status: video.status,
          created_at: video.created_at
        }
      }
    });
  });

  // 重新处理视频
  reprocessVideo = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;

    // 获取视频信息
    const video = await Video.findById(id);
    if (!video) {
      throw new AppError('视频不存在', 404, 'VIDEO_NOT_FOUND');
    }

    // 检查权限
    if (video.user_id !== userId && req.user.role !== 'admin') {
      throw new AppError('无权重新处理此视频', 403, 'ACCESS_DENIED');
    }

    // 开始重新处理
    setImmediate(async () => {
      try {
        await videoService.reprocessVideo(id);
      } catch (error) {
        logger.error(`视频重新处理失败: ${id}`, error);
      }
    });

    // 记录操作日志
    operationLogger.logUserOperation(
      req,
      'video_reprocess',
      id,
      '重新处理视频',
      { title: video.title }
    );

    res.json({
      success: true,
      message: '视频重新处理已开始'
    });
  });

  // 清除视频缓存
  async clearVideoCache(videoId) {
    const patterns = [
      `${CACHE_KEYS.VIDEO}:details:${videoId}:*`,
      `${CACHE_KEYS.VIDEO}:list:*`,
      `${CACHE_KEYS.VIDEO}:popular:*`,
      `${CACHE_KEYS.VIDEO}:recommended:*`
    ];

    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
  }
}

// 分类控制器
class CategoryController {
  // 获取分类树
  getCategoryTree = asyncHandler(async (req, res) => {
    const cacheKey = cache.generateKey(CACHE_KEYS.CATEGORY, 'tree');
    let categories = await cache.get(cacheKey);

    if (!categories) {
      categories = await Category.getCategoryTree();
      await cache.set(cacheKey, categories, 1800); // 30分钟缓存，减少缓存时间
    }

    res.json({
      success: true,
      data: {
        categories
      }
    });
  });

  // 获取分类列表
  getCategoryList = asyncHandler(async (req, res) => {
    const { withStats = false } = req.query;

    let categories;
    if (withStats === 'true') {
      const cacheKey = cache.generateKey(CACHE_KEYS.CATEGORY, 'stats');
      categories = await cache.get(cacheKey);

      if (!categories) {
        categories = await Category.getCategoryStats();
        await cache.set(cacheKey, categories, 1800); // 30分钟缓存
      }
    } else {
      categories = await Category.findAll(
        { status: 'active' },
        { orderBy: 'sort_order', order: 'ASC' }
      );
    }

    res.json({
      success: true,
      data: {
        categories
      }
    });
  });

  // 获取分类详情
  getCategoryDetails = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const category = await Category.findById(id);
    if (!category) {
      throw new AppError('分类不存在', 404, 'CATEGORY_NOT_FOUND');
    }

    // 获取分类路径
    const path = await Category.getCategoryPath(id);

    // 获取分类统计
    const stats = await Category.getCategoryStats(id);

    res.json({
      success: true,
      data: {
        category: {
          ...category,
          path,
          stats: stats[0] || {}
        }
      }
    });
  });

  // 获取分类下的视频
  getCategoryVideos = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    const category = await Category.findById(id);
    if (!category) {
      throw new AppError('分类不存在', 404, 'CATEGORY_NOT_FOUND');
    }

    // 获取分类及其子分类的视频
    const categoryIds = await Category.getCategoryWithChildren(id);

    const filters = {
      categoryIds,
      visibility: 'public',
      status: 'published'
    };

    const options = {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      sortBy,
      sortOrder
    };

    const result = await Video.getVideoList(filters, options);

    res.json({
      success: true,
      data: result
    });
  });

  // 创建分类（管理员功能）
  createCategory = asyncHandler(async (req, res) => {
    const { name, slug, description, parentId, sortOrder } = req.body;

    const category = await Category.createCategory({
      name,
      slug,
      description,
      parentId,
      sortOrder
    });

    // 清除分类缓存
    await this.clearCategoryCache();

    // 记录管理员操作
    operationLogger.logAdminOperation(
      req,
      'category_create',
      category.id,
      `创建分类: ${name}`,
      { name, slug }
    );

    res.status(201).json({
      success: true,
      message: '分类创建成功',
      data: {
        category
      }
    });
  });

  // 更新分类（管理员功能）
  updateCategory = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;

    const updatedCategory = await Category.updateCategory(id, updateData);

    // 清除分类缓存
    await this.clearCategoryCache();

    // 记录管理员操作
    operationLogger.logAdminOperation(
      req,
      'category_update',
      id,
      `更新分类: ${updatedCategory.name}`,
      { updatedFields: Object.keys(updateData) }
    );

    res.json({
      success: true,
      message: '分类更新成功',
      data: {
        category: updatedCategory
      }
    });
  });

  // 删除分类（管理员功能）
  deleteCategory = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const category = await Category.findById(id);
    if (!category) {
      throw new AppError('分类不存在', 404, 'CATEGORY_NOT_FOUND');
    }

    await Category.deleteCategory(id);

    // 清除分类缓存
    await this.clearCategoryCache();

    // 记录管理员操作
    operationLogger.logAdminOperation(
      req,
      'category_delete',
      id,
      `删除分类: ${category.name}`
    );

    res.json({
      success: true,
      message: '分类删除成功'
    });
  });

  // 获取热门分类
  getPopularCategories = asyncHandler(async (req, res) => {
    const { limit = 10 } = req.query;

    const cacheKey = cache.generateKey(CACHE_KEYS.CATEGORY, 'popular', limit);
    let categories = await cache.get(cacheKey);

    if (!categories) {
      categories = await Category.getPopularCategories(parseInt(limit));
      await cache.set(cacheKey, categories, 1800); // 30分钟缓存
    }

    res.json({
      success: true,
      data: {
        categories
      }
    });
  });

  // 搜索分类
  searchCategories = asyncHandler(async (req, res) => {
    const { keyword, limit = 20 } = req.query;

    if (!keyword || keyword.trim().length === 0) {
      throw new AppError('搜索关键词不能为空', 400, 'KEYWORD_REQUIRED');
    }

    const categories = await Category.searchCategories(keyword.trim(), parseInt(limit));

    res.json({
      success: true,
      data: {
        categories
      }
    });
  });

  // 清除分类缓存
  async clearCategoryCache() {
    const patterns = [
      `${CACHE_KEYS.CATEGORY}:*`
    ];

    for (const pattern of patterns) {
      await cache.delPattern(pattern);
    }
  }
}

// 导出控制器实例
module.exports = {
  VideoController: new VideoController(),
  CategoryController: new CategoryController()
};
