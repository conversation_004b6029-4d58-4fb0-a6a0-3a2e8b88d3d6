# 上下文
文件名：[task.md]
创建于：[2024-07-30]
创建者：[AI]
关联协议：RIPER-5 + Multidimensional + Agent Protocol (Conditional Interactive Step Review Enhanced)

# 任务描述
请检查当前项目的开通会员生成订单的逻辑。
请确保不会把旧的收款记录生成订单和记录。
还有检查开通会员生成订单的逻辑是否正确。

应该是生成订单用户选择支付方式，才付款。不是查找收款记录才生成订单

# 项目概述
一个包含会员系统、视频点播和多种支付方式（包括TRON USDT）的web应用。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
- 系统中存在两种会员购买流程：标准的网页支付流程和TRON USDT支付流程。
- 标准流程（如支付宝）是正确的：用户下单 -> 生成pending订单 -> 用户付款 -> 回调 -> 订单置为paid，激活会员。
- TRON USDT流程是错误的，由`tronTransactionScannerService.js`实现。该服务通过定时任务扫描区块链地址，发现收款后，直接在数据库中创建一个`paid`状态的`BALANCE_RECHARGE`订单，但该订单无法关联到具体用户（`user_id`为null）。
- 这个错误流程完全符合用户描述的“查找收款记录才生成订单”的问题，是问题的根源。它导致用户体验中断、资金无法自动入账，且与用户购买会员的初衷不符。

# 提议的解决方案 (由 INNOVATE 模式填充)
强烈推荐采纳方案二，因为它更可靠、自动化程度更高，能从根本上解决问题。

**方案一：优化现有模式 - “静态地址 + 订单号备注(Memo)” (快速但不推荐)**
1.  **停用旧扫描服务**: 禁用`tronTransactionScannerService.js`。
2.  **改造下单流程**: 用户下单时创建`pending`订单，并要求用户转账时在`Memo`中填写订单号。
3.  **创建新扫描服务**: 新服务通过`Memo`中的订单号来匹配`pending`订单，完成后续操作。
4.  **缺点**: 严重依赖用户操作的准确性，用户填错或忘记填写`Memo`将导致支付失败，需要大量人工客服介入。

**方案二：业界标准方案 - “一单一地址 (动态地址)” (推荐)**
1.  **停用旧扫描服务**: 禁用`tronTransactionScannerService.js`。
2.  **集成HD钱包**: 集成一个分层确定性钱包库，用于为每笔订单动态生成唯一的收款地址。
3.  **改造下单流程**: 用户下单时，系统为其生成一个唯一的、仅供本次支付使用的TRON地址，并与`pending`订单关联。用户无需填写`Memo`。
4.  **创建新扫描服务**: 新服务会遍历所有`pending`状态的TRON订单，检查其专属地址的余额。一旦余额足够，则确认支付成功。
5.  **优点**: 流程全自动，可靠性极高，用户体验好，安全性更高。
6.  **缺点**: 实现复杂度稍高，需要管理HD钱包和处理资金归集。

# 实施计划 (由 PLAN 模式生成)
**全新实施检查清单:**

1.  **【环境与配置】**
    a.  `[卸载`tron-wallet-hd`依赖, review:true]`：执行 `npm uninstall tron-wallet-hd`，移除上一方案中引入的、现已不再需要的依赖库。
    b.  `[确认静态钱包配置, review:true]`：检查并确保在 `payment_gateways` 数据库表中，`tron_usdt` 支付方式的配置包含了正确的、固定的收款地址（`walletAddress`）和API密钥（`apiKey`）。

2.  **【后端 - 支付逻辑重构】**
    a.  `[修改`paymentController.js`, review:true]`：重构控制器中处理TRON支付的部分。在创建订单时，必须实现以下逻辑：
        1.  先在数据库中创建一条基础的、`pending`状态的订单，并获取到该订单的唯一ID。
        2.  根据订单的基础价格和这个唯一ID，计算出一个带有微小、唯一“标签”的最终支付金额 `final_amount` (例如: `基础价格 + 订单ID * 0.000001`)。
        3.  用这个计算出的 `final_amount` 更新刚刚创建的订单记录。
        4.  将这个**精确到小数点后多位的最终金额**和**固定的收款地址**返回给前端。

3.  **【后端 - 扫描服务重构】**
    a.  `[重构`tronTransactionScannerService.js`, review:true]`：将旧的扫描服务文件彻底重写，以实现新的识别逻辑：
        1.  从`trongrid.io` API获取固定收款地址上的所有最新交易。
        2.  对于每一笔交易，获取其精确的收款金额。
        3.  使用这个金额，在 `orders` 表中查找是否存在一个`pending`状态的订单，其`final_amount`与交易金额**完全相等**。
        4.  如果找到唯一匹配的订单，就调用系统中通用的 `BasePaymentService.handleSuccess` 方法，触发后续的订单状态更新和会员激活流程。

4.  **【前端 - 适配新流程】**
    a.  `[修改`UpgradeDialog.tsx`, review:true]`：更新前端支付对话框。当用户选择TRON支付时，UI必须清晰、醒目地向用户展示由后端返回的那个**带有多个小数位的精确金额**，并用提示文字强调“**必须转账与此完全相同的金额，否则无法自动到账**”。

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "2.a [修改`paymentController.js`, review:true]" (审查需求: true, 状态: 初步实施中)

# 任务进度 (由 EXECUTE 模式在每步完成后，以及在交互式审查迭代中追加)
*   [2024-07-30]
    *   步骤：[1.b 确认静态钱包配置 (审查需求: review:true, 状态：用户最终确认 - 成功)]
    *   修改：[无文件修改]
    *   更改摘要：[已通过代码分析确认，系统从`payment_gateways`数据库表的`tron_usdt`条目中加载静态钱包地址`walletAddress`和`apiKey`。用户在审查中再次确认了“唯一金额”+“固定地址”方案。]
    *   原因：[执行新计划步骤 1.b]
    *   阻碍：[无]
    *   用户确认状态：[成功]
    *   (若适用)交互式审查脚本退出信息: [--- REVIEW GATE: 用户通过 '继续' 结束了对【本步骤】的审查 ---]
*   [2024-07-30]
    *   步骤：[1.a 卸载`tron-wallet-hd`依赖 (审查需求: review:true, 状态：用户最终确认 - 成功)]
    *   修改：[`package.json`, `package-lock.json`]
    *   更改摘要：[通过`npm uninstall tron-wallet-hd`命令成功卸载了不再需要的依赖库。]
    *   原因：[执行新计划步骤 1.a 的初步实施]
    *   阻碍：[无]
    *   用户确认状态：[待确认]
    *   (若适用)交互式审查脚本退出信息: [不适用]
*   [2024-07-30]
    *   步骤：[1.a 安装`tron-wallet-hd`依赖 (审查需求: review:true, 状态：用户最终确认 - 成功，但方案被否决)]
    *   修改：[无文件修改]
    *   更改摘要：[通过`npm install tron-wallet-hd`命令成功安装了依赖库。]
    *   原因：[执行计划步骤 1.a 的初步实施]
    *   阻碍：[无]
    *   用户确认状态：[成功但有小问题]
    *   (若适用)交互式审查脚本退出信息: [--- REVIEW GATE: 用户通过 '继续' 结束了对【本步骤】的审查 ---. 用户反馈指示需要采用“唯一金额”方案，而非“动态地址”方案。原计划被否决。]

# 最终审查 (由 REVIEW 模式填充)
[待填充] 