#!/usr/bin/env node

/**
 * 快速管理员密码重置工具
 * 用于紧急情况下快速重置管理员密码
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

// 快速重置第一个管理员密码为默认密码
async function quickResetAdmin() {
  console.log('🔄 快速重置管理员密码工具');
  console.log('================================\n');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    // 查找第一个管理员账户
    const [admins] = await connection.execute(
      'SELECT id, email, username, nickname FROM users WHERE role = ? ORDER BY id ASC LIMIT 1',
      ['admin']
    );
    
    if (admins.length === 0) {
      console.log('❌ 没有找到管理员账户');
      console.log('💡 请先运行: node scripts/admin-manager.js create');
      await connection.end();
      return;
    }
    
    const admin = admins[0];
    const newPassword = 'Admin123456!'; // 默认密码
    
    // 加密新密码
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // 更新密码
    await connection.execute(
      'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?',
      [hashedPassword, admin.id]
    );
    
    console.log('✅ 管理员密码重置成功！');
    console.log('');
    console.log('📋 管理员账户信息:');
    console.log(`   📧 邮箱: ${admin.email}`);
    console.log(`   👤 用户名: ${admin.username}`);
    console.log(`   🔑 新密码: ${newPassword}`);
    console.log('');
    console.log('⚠️  安全提醒:');
    console.log('   1. 请立即登录并修改密码');
    console.log('   2. 不要在生产环境使用默认密码');
    console.log('   3. 确保密码包含大小写字母、数字和特殊字符');
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 重置密码失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 请检查数据库连接配置和服务状态');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 请检查数据库用户名和密码');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 请检查数据库名称是否正确');
    }
  }
}

// 创建紧急管理员账户（如果不存在）
async function createEmergencyAdmin() {
  console.log('🚨 创建紧急管理员账户');
  console.log('========================\n');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    // 检查是否已存在管理员
    const [existingAdmins] = await connection.execute(
      'SELECT COUNT(*) as count FROM users WHERE role = ?',
      ['admin']
    );
    
    if (existingAdmins[0].count > 0) {
      console.log('ℹ️  管理员账户已存在，使用重置功能');
      await connection.end();
      await quickResetAdmin();
      return;
    }
    
    // 创建紧急管理员账户
    const adminData = {
      email: '<EMAIL>',
      username: 'emergency_admin',
      password: 'Emergency123456!',
      nickname: '紧急管理员'
    };
    
    // 检查邮箱是否被占用
    const [emailCheck] = await connection.execute(
      'SELECT id FROM users WHERE email = ?',
      [adminData.email]
    );
    
    if (emailCheck.length > 0) {
      console.log('❌ 紧急管理员邮箱已被占用');
      await connection.end();
      return;
    }
    
    // 检查用户名是否被占用
    const [usernameCheck] = await connection.execute(
      'SELECT id FROM users WHERE username = ?',
      [adminData.username]
    );
    
    if (usernameCheck.length > 0) {
      console.log('❌ 紧急管理员用户名已被占用');
      await connection.end();
      return;
    }
    
    // 加密密码
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(adminData.password, saltRounds);
    
    // 插入紧急管理员账户
    const [result] = await connection.execute(
      `INSERT INTO users (
        email, password, username, nickname, role, status, email_verified, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        adminData.email,
        hashedPassword,
        adminData.username,
        adminData.nickname,
        'admin',
        'active',
        true
      ]
    );
    
    console.log('✅ 紧急管理员账户创建成功！');
    console.log('');
    console.log('📋 紧急管理员信息:');
    console.log(`   🆔 ID: ${result.insertId}`);
    console.log(`   📧 邮箱: ${adminData.email}`);
    console.log(`   👤 用户名: ${adminData.username}`);
    console.log(`   🔑 密码: ${adminData.password}`);
    console.log(`   💡 昵称: ${adminData.nickname}`);
    console.log('');
    console.log('⚠️  安全提醒:');
    console.log('   1. 这是临时紧急账户，请立即登录并修改密码');
    console.log('   2. 建议创建正式的管理员账户后删除此紧急账户');
    console.log('   3. 不要在生产环境长期使用此账户');
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 创建紧急管理员失败:', error.message);
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
🔧 快速管理员重置工具

使用方法:
  node scripts/quick-admin-reset.js [命令]

可用命令:
  reset       重置第一个管理员密码为默认密码 (默认)
  emergency   创建紧急管理员账户
  help        显示此帮助信息

示例:
  node scripts/quick-admin-reset.js
  node scripts/quick-admin-reset.js reset
  node scripts/quick-admin-reset.js emergency

默认密码: Admin123456!

注意事项:
- 此工具仅用于紧急情况
- 重置后请立即修改密码
- 生产环境请谨慎使用
`);
}

// 主函数
async function main() {
  const command = process.argv[2] || 'reset';
  
  switch (command) {
    case 'reset':
      await quickResetAdmin();
      break;
    case 'emergency':
      await createEmergencyAdmin();
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      console.log('❌ 未知命令，使用 help 查看可用命令');
      showHelp();
      break;
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  quickResetAdmin,
  createEmergencyAdmin
};
