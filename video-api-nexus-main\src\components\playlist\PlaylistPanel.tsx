import React, { useState } from 'react';
import { 
  X, 
  <PERSON>, 
  Pause, 
  Ski<PERSON><PERSON>or<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Shuffle, 
  Repeat, 
  Repeat1,
  Plus,
  Trash2,
  GripVertical
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Playlist, PlaylistItem } from '@/types/playlist';

interface PlaylistPanelProps {
  isOpen: boolean;
  onClose: () => void;
  playlist: Playlist | null;
  currentItem: PlaylistItem | null;
  onPlayItem: (itemId: string) => void;
  onPlayNext: () => void;
  onPlayPrevious: () => void;
  onSetPlayMode: (mode: 'sequence' | 'loop' | 'random') => void;
  onRemoveItem: (itemId: string) => void;
  onAddToPlaylist?: () => void;
  className?: string;
}

const PlaylistPanel: React.FC<PlaylistPanelProps> = ({
  isOpen,
  onClose,
  playlist,
  currentItem,
  onPlayItem,
  onPlayNext,
  onPlayPrevious,
  onSetPlayMode,
  onRemoveItem,
  onAddToPlaylist,
  className = '',
}) => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  if (!isOpen) return null;

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getPlayModeIcon = () => {
    if (!playlist) return <Repeat1 className="h-4 w-4" />;

    switch (playlist.playMode) {
      case 'loop':
        return <Repeat className="h-4 w-4 text-blue-500" />;
      case 'random':
        return <Shuffle className="h-4 w-4 text-orange-500" />;
      case 'sequence':
      default:
        return <Repeat1 className="h-4 w-4 text-green-500" />;
    }
  };

  const getPlayModeText = () => {
    if (!playlist) return '顺序播放';
    
    switch (playlist.playMode) {
      case 'loop':
        return '循环播放';
      case 'random':
        return '随机播放';
      case 'sequence':
      default:
        return '顺序播放';
    }
  };

  const handlePlayModeToggle = () => {
    if (!playlist) return;
    
    const modes: Array<'sequence' | 'loop' | 'random'> = ['sequence', 'loop', 'random'];
    const currentIndex = modes.indexOf(playlist.playMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    onSetPlayMode(nextMode);
  };

  return (
    <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4 ${className}`}>
      <Card className="w-full max-w-2xl max-h-[80vh] bg-background/95 backdrop-blur-sm">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-lg font-semibold">
            {playlist?.name || '播放列表'}
          </CardTitle>
          <div className="flex items-center gap-2">
            {playlist?.isTemporary && (
              <Badge variant="secondary" className="text-xs">
                临时列表
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* 播放控制区域 */}
          <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={onPlayPrevious}
                disabled={!playlist || playlist.items.length === 0}
                className="h-8 w-8 p-0"
              >
                <SkipBack className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onPlayNext}
                disabled={!playlist || playlist.items.length === 0}
                className="h-8 w-8 p-0"
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePlayModeToggle}
                className="flex items-center gap-1 text-xs border-2"
                title={`当前模式: ${getPlayModeText()}，点击切换`}
              >
                {getPlayModeIcon()}
                <span className="font-medium">{getPlayModeText()}</span>
              </Button>
            </div>

            {onAddToPlaylist && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onAddToPlaylist}
                className="flex items-center gap-1 text-xs"
              >
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">添加</span>
              </Button>
            )}
          </div>

          <Separator />

          {/* 播放列表内容 */}
          {!playlist || playlist.items.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <div className="mb-2">播放列表为空</div>
              <div className="text-sm">添加一些视频或音频开始播放</div>
            </div>
          ) : (
            <ScrollArea className="h-[400px]">
              <div className="space-y-2">
                {playlist.items.map((item, index) => {
                  const isCurrentItem = index === playlist.currentIndex;
                  const isPlaying = isCurrentItem && playlist.isPlaying;

                  return (
                    <div
                      key={item.id}
                      className={`flex items-center gap-3 p-3 rounded-lg border transition-colors cursor-pointer hover:bg-muted/50 ${
                        isCurrentItem ? 'bg-primary/10 border-primary/20' : 'border-border'
                      }`}
                      onClick={() => onPlayItem(item.id)}
                    >
                      {/* 拖拽手柄 */}
                      <div className="cursor-grab text-muted-foreground hover:text-foreground">
                        <GripVertical className="h-4 w-4" />
                      </div>

                      {/* 播放状态指示器 */}
                      <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                        {isPlaying ? (
                          <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                        ) : isCurrentItem ? (
                          <Pause className="h-3 w-3 text-primary" />
                        ) : (
                          <span className="text-xs text-muted-foreground">{index + 1}</span>
                        )}
                      </div>

                      {/* 缩略图 */}
                      {item.thumbnail && (
                        <div className="flex-shrink-0 w-12 h-8 bg-muted rounded overflow-hidden">
                          <img 
                            src={item.thumbnail} 
                            alt={item.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}

                      {/* 内容信息 */}
                      <div className="flex-1 min-w-0">
                        <div className={`font-medium truncate ${
                          isCurrentItem ? 'text-primary' : 'text-foreground'
                        }`}>
                          {item.title}
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <Badge variant="outline" className="text-xs">
                            {item.mediaType === 'audio' ? '音频' : '视频'}
                          </Badge>
                          <span>{formatDuration(item.duration)}</span>
                        </div>
                      </div>

                      {/* 删除按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onRemoveItem(item.id);
                        }}
                        className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          )}

          {/* 播放列表统计 */}
          {playlist && playlist.items.length > 0 && (
            <div className="text-xs text-muted-foreground text-center pt-2 border-t">
              共 {playlist.items.length} 项 • 总时长 {formatDuration(
                playlist.items.reduce((total, item) => total + item.duration, 0)
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PlaylistPanel;
