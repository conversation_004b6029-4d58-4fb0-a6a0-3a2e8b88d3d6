# 开发指南

本文档为开发者提供详细的开发环境搭建和开发流程指导。

## 🛠️ 开发环境搭建

### 系统要求
- **Node.js**: 16.0+ (推荐18.0+)
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Git**: 2.0+
- **IDE**: VS Code (推荐)

### 1. 克隆项目
```bash
git clone <repository-url>
cd video-platform-api
```

### 2. 安装依赖
```bash
# 安装项目依赖
npm install

# 安装开发工具 (可选)
npm install -g nodemon pm2
```

### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

**开发环境配置示例**:
```env
# 开发环境
NODE_ENV=development
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=video_platform_dev

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=dev_jwt_secret_key
JWT_REFRESH_SECRET=dev_refresh_secret_key
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# 邮件配置 (开发环境可使用测试邮箱)
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=your_mailtrap_user
SMTP_PASS=your_mailtrap_pass

# 文件上传
MAX_FILE_SIZE=100MB
UPLOAD_PATH=./uploads

# 日志配置
LOG_LEVEL=debug
```

### 4. 数据库初始化
```bash
# 创建开发数据库
mysql -u root -p -e "CREATE DATABASE video_platform_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入表结构
mysql -u root -p video_platform_dev < database/schema.sql

# 插入测试数据 (可选)
mysql -u root -p video_platform_dev < database/test_data.sql
```

### 5. 启动开发服务器
```bash
# 使用npm scripts
npm run dev

# 或直接使用nodemon
nodemon app.js

# 查看日志
tail -f logs/app.log
```

## 📁 项目架构

### 目录结构说明
```
src/
├── database/              # 数据库相关
│   ├── models/           # 数据模型
│   │   ├── BaseModel.js  # 基础模型类
│   │   ├── User.js       # 用户模型
│   │   ├── Video.js      # 视频模型
│   │   └── ...
│   ├── migrations/       # 数据库迁移文件
│   └── ConnectionManager.js # 数据库连接管理
├── middleware/           # 中间件
│   ├── auth.js          # 认证中间件
│   ├── validation.js    # 数据验证中间件
│   ├── rateLimiter.js   # 限流中间件
│   ├── security.js      # 安全中间件
│   └── errorHandler.js  # 错误处理中间件
├── modules/             # 功能模块
│   ├── auth/           # 认证模块
│   │   ├── controllers/ # 控制器
│   │   ├── services/   # 业务服务
│   │   ├── middleware/ # 模块中间件
│   │   └── routes.js   # 路由定义
│   ├── user/           # 用户模块
│   ├── video/          # 视频模块
│   ├── interaction/    # 互动模块
│   └── admin/          # 管理模块
├── services/           # 通用服务
│   ├── emailService.js # 邮件服务
│   ├── fileService.js  # 文件服务
│   └── ...
├── utils/              # 工具类
│   ├── logger.js       # 日志工具
│   ├── cache.js        # 缓存工具
│   ├── validator.js    # 验证工具
│   └── ...
└── config/             # 配置文件
    ├── database.js     # 数据库配置
    ├── redis.js        # Redis配置
    └── ...
```

### 架构设计原则

1. **模块化**: 按功能划分模块，每个模块独立
2. **分层架构**: Controller -> Service -> Model
3. **依赖注入**: 通过构造函数注入依赖
4. **错误处理**: 统一的错误处理机制
5. **日志记录**: 详细的日志记录和监控

## 🔧 开发规范

### 代码风格
```javascript
// 使用ES6+语法
const { AppError } = require('../middleware/errorHandler');

// 类定义
class UserService {
  constructor() {
    this.model = require('../models/User');
  }

  // 异步方法使用async/await
  async createUser(userData) {
    try {
      // 数据验证
      if (!userData.email) {
        throw new AppError('邮箱不能为空', 400, 'EMAIL_REQUIRED');
      }

      // 业务逻辑
      const user = await this.model.create(userData);
      return user;
    } catch (error) {
      // 错误处理
      throw error;
    }
  }
}

module.exports = new UserService();
```

### 命名规范
- **文件名**: 使用camelCase (userService.js)
- **类名**: 使用PascalCase (UserService)
- **变量名**: 使用camelCase (userData)
- **常量**: 使用UPPER_SNAKE_CASE (MAX_FILE_SIZE)
- **数据库字段**: 使用snake_case (user_id)

### 注释规范
```javascript
/**
 * 创建新用户
 * @param {Object} userData - 用户数据
 * @param {string} userData.email - 用户邮箱
 * @param {string} userData.password - 用户密码
 * @returns {Promise<Object>} 创建的用户对象
 * @throws {AppError} 当邮箱已存在时抛出错误
 */
async createUser(userData) {
  // 实现代码
}
```

### Git提交规范
```bash
# 提交格式
<type>(<scope>): <subject>

# 类型说明
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例
feat(user): 添加用户头像上传功能
fix(video): 修复视频上传失败的问题
docs(api): 更新API文档
```

## 🧪 测试

### 单元测试
```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- --grep "User"

# 生成测试覆盖率报告
npm run test:coverage
```

### 测试示例
```javascript
// tests/user.test.js
const request = require('supertest');
const app = require('../app');

describe('User API', () => {
  describe('POST /api/auth/register', () => {
    it('should register a new user', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
    });
  });
});
```

## 🔍 调试

### VS Code调试配置
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug App",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/app.js",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "runtimeExecutable": "nodemon",
      "skipFiles": ["<node_internals>/**"]
    }
  ]
}
```

### 日志调试
```javascript
const logger = require('../utils/logger');

// 不同级别的日志
logger.debug('调试信息', { userId: 1 });
logger.info('信息日志', { action: 'login' });
logger.warn('警告信息', { error: 'deprecated' });
logger.error('错误信息', { error: error.message });
```

## 📦 依赖管理

### 添加新依赖
```bash
# 生产依赖
npm install package-name

# 开发依赖
npm install --save-dev package-name

# 全局依赖
npm install -g package-name
```

### 依赖更新
```bash
# 检查过期依赖
npm outdated

# 更新依赖
npm update

# 安全审计
npm audit
npm audit fix
```

## 🚀 部署流程

### 开发环境部署
```bash
# 启动开发服务器
npm run dev

# 使用PM2启动
pm2 start ecosystem.config.js --env development
```

### 生产环境部署
```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 使用PM2启动
pm2 start ecosystem.config.js --env production
```

## 🔧 常用开发工具

### 推荐VS Code插件
- **ES6 String HTML**: HTML语法高亮
- **REST Client**: API测试
- **GitLens**: Git增强
- **Prettier**: 代码格式化
- **ESLint**: 代码检查
- **Thunder Client**: API测试客户端

### 开发脚本
```json
// package.json scripts
{
  "scripts": {
    "dev": "nodemon app.js",
    "start": "node app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix",
    "format": "prettier --write src/",
    "db:migrate": "node scripts/migrate.js",
    "db:seed": "node scripts/seed.js"
  }
}
```

## 🐛 常见问题

### 1. 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查连接参数
mysql -h localhost -u root -p

# 查看错误日志
tail -f logs/error.log
```

### 2. Redis连接失败
```bash
# 检查Redis服务状态
sudo systemctl status redis

# 测试Redis连接
redis-cli ping

# 检查Redis配置
cat /etc/redis/redis.conf
```

### 3. 文件上传失败
```bash
# 检查上传目录权限
ls -la uploads/

# 创建上传目录
mkdir -p uploads/{videos,thumbnails,avatars}

# 设置权限
chmod 755 uploads/
```

### 4. 端口占用
```bash
# 查看端口占用
netstat -tlnp | grep 3000

# 杀死占用进程
kill -9 <PID>
```

## 📚 学习资源

### 官方文档
- [Node.js官方文档](https://nodejs.org/docs/)
- [Express.js官方文档](https://expressjs.com/)
- [MySQL官方文档](https://dev.mysql.com/doc/)
- [Redis官方文档](https://redis.io/documentation)

### 推荐教程
- [Node.js最佳实践](https://github.com/goldbergyoni/nodebestpractices)
- [Express.js安全指南](https://expressjs.com/en/advanced/best-practice-security.html)
- [JWT认证指南](https://jwt.io/introduction/)

---

**提示**: 开发过程中遇到问题，请先查看日志文件，然后参考本文档的常见问题部分。如果问题仍未解决，请提交Issue或联系开发团队。
