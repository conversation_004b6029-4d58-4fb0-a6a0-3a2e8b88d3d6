#!/usr/bin/env node

/**
 * 测试会员用户查询
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const Membership = require('../src/database/models/Membership');
const logger = require('../src/utils/logger');

async function testMemberQuery() {
  try {
    logger.info('测试会员用户查询...');
    
    const sql = `
      SELECT 
        u.id,
        u.username,
        u.email,
        u.nickname,
        u.role,
        u.status as user_status,
        m.id as membership_id,
        m.status as membership_status,
        m.start_date as membership_start_date,
        m.end_date as membership_end_date,
        m.auto_renew,
        m.created_at as membership_created_at,
        mp.id as plan_id,
        mp.name as plan_name,
        mp.price as plan_price,
        mp.duration_days as plan_duration
      FROM users u
      LEFT JOIN memberships m ON u.id = m.user_id 
        AND m.status IN ('active', 'expired', 'cancelled')
        AND m.id = (
          SELECT id FROM memberships m2 
          WHERE m2.user_id = u.id 
          ORDER BY m2.created_at DESC 
          LIMIT 1
        )
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.id IS NOT NULL
      ORDER BY m.created_at DESC
      LIMIT 10
    `;

    const users = await Membership.query(sql);
    
    logger.info(`找到 ${users.length} 个会员用户:`);
    users.forEach(user => {
      logger.info(`用户: ${user.username}, 计划: ${user.plan_name}, 状态: ${user.membership_status}, 开始: ${user.membership_start_date}, 结束: ${user.membership_end_date}`);
    });
    
  } catch (error) {
    logger.error('测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testMemberQuery()
    .then(() => {
      logger.info('测试完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testMemberQuery };
