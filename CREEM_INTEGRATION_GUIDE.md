# Creem.io 支付集成配置指南

## 配置步骤

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# Creem.io 支付配置
CREEM_API_KEY=your_creem_api_key
CREEM_WEBHOOK_SECRET=your_creem_webhook_secret
FRONTEND_URL=http://localhost:8081  # 你的前端URL
```

### 2. Creem 后台配置

登录 [Creem.io](https://creem.io) 后台，进行以下配置：

#### 2.1 获取 API Key
1. 进入 **Developers** > **API Keys**
2. 复制你的 API Key 到环境变量 `CREEM_API_KEY`

#### 2.2 配置 Webhook
1. 进入 **Developers** > **Webhooks**
2. 点击 **Add Webhook**
3. 配置如下信息：
   - **Name**: Video Platform Webhook
   - **URL**: `https://your-domain.com/api/payment/creem/webhook`
   - **Events**: 选择 `checkout.completed`, `subscription.created`, `subscription.cancelled`
4. 保存后，复制 **Webhook Secret** 到环境变量 `CREEM_WEBHOOK_SECRET`

#### 2.3 配置产品重定向URL (可选)
在创建产品时，可以设置：
- **Success URL**: `https://your-domain.com/membership?status=success`
- **Cancel URL**: `https://your-domain.com/membership?status=cancelled`

### 3. 测试配置

#### 3.1 使用 ngrok 进行本地测试
```bash
# 安装 ngrok
npm install -g ngrok

# 启动隧道
ngrok http 3000

# 在 Creem 配置中使用生成的 URL
# 例如: https://abc123.ngrok.io/api/payment/creem/webhook
```

#### 3.2 测试支付流程
1. 用户选择会员计划
2. 点击"信用卡"支付
3. 跳转到 Creem 支付页面
4. 完成支付
5. Creem 发送 webhook 到你的服务器
6. 系统自动激活用户会员

## 支付流程说明

### 用户支付流程：
1. **发起支付** → 前端调用 `/api/payment/creem/create-order`
2. **跳转支付** → 重定向到 Creem 支付页面
3. **完成支付** → 用户在 Creem 页面完成支付
4. **Webhook 通知** → Creem 发送 `checkout.completed` 事件
5. **激活会员** → 系统自动处理会员激活
6. **用户返回** → 重定向到成功页面

### 技术实现：
- **Webhook 验证**: 使用 HMAC-SHA256 验证签名
- **幂等性**: 防止重复处理同一支付
- **会员管理**: 自动计算到期时间和状态
- **错误处理**: 完善的日志记录和错误处理

## Webhook 事件处理

### checkout.completed
- 一次性支付完成
- 激活用户会员权限
- 记录支付信息

### subscription.created
- 订阅创建成功
- 处理订阅类型会员

### subscription.cancelled
- 订阅取消
- 更新会员状态为已取消

## 故障排查

### 1. Webhook 未收到
- 检查 URL 是否可访问
- 验证 Creem 后台 Webhook 配置
- 查看服务器日志

### 2. 签名验证失败
- 确认 `CREEM_WEBHOOK_SECRET` 配置正确
- 检查请求头 `creem-signature`

### 3. 用户未激活会员
- 检查用户邮箱是否匹配
- 确认产品 ID 在数据库中存在
- 查看 webhook 处理日志

## 安全注意事项

1. **Webhook 安全**:
   - 始终验证签名
   - 使用 HTTPS
   - 实现重试机制

2. **数据安全**:
   - 不存储敏感支付信息
   - 记录必要的审计日志

3. **环境隔离**:
   - 测试环境使用测试密钥
   - 生产环境使用生产密钥