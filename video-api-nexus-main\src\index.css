@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* 霓虹主题 */
  .neon {
    --background: 0 0% 5%;
    --foreground: 300 100% 90%;

    --card: 0 0% 8%;
    --card-foreground: 300 100% 90%;

    --popover: 0 0% 8%;
    --popover-foreground: 300 100% 90%;

    --primary: 300 100% 50%;
    --primary-foreground: 0 0% 5%;

    --secondary: 240 100% 15%;
    --secondary-foreground: 300 100% 90%;

    --muted: 240 100% 10%;
    --muted-foreground: 300 50% 70%;

    --accent: 180 100% 50%;
    --accent-foreground: 0 0% 5%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 300 50% 20%;
    --input: 300 50% 15%;
    --ring: 300 100% 50%;
    --sidebar-background: 0 0% 3%;
    --sidebar-foreground: 300 100% 90%;
    --sidebar-primary: 300 100% 50%;
    --sidebar-primary-foreground: 0 0% 5%;
    --sidebar-accent: 240 100% 15%;
    --sidebar-accent-foreground: 300 100% 90%;
    --sidebar-border: 300 50% 20%;
    --sidebar-ring: 300 100% 50%;
  }

  /* 深海主题 */
  .ocean {
    --background: 220 50% 8%;
    --foreground: 180 100% 85%;

    --card: 220 50% 12%;
    --card-foreground: 180 100% 85%;

    --popover: 220 50% 12%;
    --popover-foreground: 180 100% 85%;

    --primary: 200 100% 40%;
    --primary-foreground: 220 50% 8%;

    --secondary: 210 50% 20%;
    --secondary-foreground: 180 100% 85%;

    --muted: 210 50% 15%;
    --muted-foreground: 180 50% 65%;

    --accent: 180 100% 30%;
    --accent-foreground: 220 50% 8%;

    --destructive: 0 80% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 50% 25%;
    --input: 210 50% 20%;
    --ring: 200 100% 40%;
    --sidebar-background: 220 50% 5%;
    --sidebar-foreground: 180 100% 85%;
    --sidebar-primary: 200 100% 40%;
    --sidebar-primary-foreground: 220 50% 8%;
    --sidebar-accent: 210 50% 20%;
    --sidebar-accent-foreground: 180 100% 85%;
    --sidebar-border: 210 50% 25%;
    --sidebar-ring: 200 100% 40%;
  }

  /* 森林主题 */
  .forest {
    --background: 120 30% 8%;
    --foreground: 120 50% 85%;

    --card: 120 30% 12%;
    --card-foreground: 120 50% 85%;

    --popover: 120 30% 12%;
    --popover-foreground: 120 50% 85%;

    --primary: 120 60% 40%;
    --primary-foreground: 120 30% 8%;

    --secondary: 110 40% 20%;
    --secondary-foreground: 120 50% 85%;

    --muted: 110 40% 15%;
    --muted-foreground: 120 30% 65%;

    --accent: 80 70% 45%;
    --accent-foreground: 120 30% 8%;

    --destructive: 0 80% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 110 40% 25%;
    --input: 110 40% 20%;
    --ring: 120 60% 40%;
    --sidebar-background: 120 30% 5%;
    --sidebar-foreground: 120 50% 85%;
    --sidebar-primary: 120 60% 40%;
    --sidebar-primary-foreground: 120 30% 8%;
    --sidebar-accent: 110 40% 20%;
    --sidebar-accent-foreground: 120 50% 85%;
    --sidebar-border: 110 40% 25%;
    --sidebar-ring: 120 60% 40%;
  }

  /* 日落主题 */
  .sunset {
    --background: 20 40% 8%;
    --foreground: 40 80% 90%;

    --card: 20 40% 12%;
    --card-foreground: 40 80% 90%;

    --popover: 20 40% 12%;
    --popover-foreground: 40 80% 90%;

    --primary: 30 100% 50%;
    --primary-foreground: 20 40% 8%;

    --secondary: 15 60% 20%;
    --secondary-foreground: 40 80% 90%;

    --muted: 15 60% 15%;
    --muted-foreground: 40 50% 70%;

    --accent: 350 100% 60%;
    --accent-foreground: 20 40% 8%;

    --destructive: 0 80% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 15 60% 25%;
    --input: 15 60% 20%;
    --ring: 30 100% 50%;
    --sidebar-background: 20 40% 5%;
    --sidebar-foreground: 40 80% 90%;
    --sidebar-primary: 30 100% 50%;
    --sidebar-primary-foreground: 20 40% 8%;
    --sidebar-accent: 15 60% 20%;
    --sidebar-accent-foreground: 40 80% 90%;
    --sidebar-border: 15 60% 25%;
    --sidebar-ring: 30 100% 50%;
  }

  /* 彩虹主题 */
  .rainbow {
    --background: 0 0% 6%;
    --foreground: 0 0% 95%;

    --card: 0 0% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 95%;

    --primary: 270 100% 60%;
    --primary-foreground: 0 0% 6%;

    --secondary: 240 50% 20%;
    --secondary-foreground: 0 0% 95%;

    --muted: 240 50% 15%;
    --muted-foreground: 0 0% 70%;

    --accent: 180 100% 50%;
    --accent-foreground: 0 0% 6%;

    --destructive: 0 100% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 240 50% 25%;
    --input: 240 50% 20%;
    --ring: 270 100% 60%;
    --sidebar-background: 0 0% 3%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 270 100% 60%;
    --sidebar-primary-foreground: 0 0% 6%;
    --sidebar-accent: 240 50% 20%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 240 50% 25%;
    --sidebar-ring: 270 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply bg-background;
  }

  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  #root {
    @apply bg-background min-h-screen;
  }

  /* 主题切换动画 */
  .theme-transition * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
  }

  /* 霓虹效果 */
  .neon .glow {
    box-shadow: 0 0 5px hsl(var(--primary)), 0 0 10px hsl(var(--primary)), 0 0 15px hsl(var(--primary));
  }

  .neon .text-glow {
    text-shadow: 0 0 5px hsl(var(--primary)), 0 0 10px hsl(var(--primary));
  }

  /* 彩虹渐变效果 */
  .rainbow .rainbow-border {
    background: linear-gradient(45deg,
      hsl(0, 100%, 50%),
      hsl(60, 100%, 50%),
      hsl(120, 100%, 50%),
      hsl(180, 100%, 50%),
      hsl(240, 100%, 50%),
      hsl(300, 100%, 50%),
      hsl(360, 100%, 50%)
    );
    background-size: 400% 400%;
    animation: rainbow-shift 3s ease infinite;
  }

  @keyframes rainbow-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* 波浪效果（深海主题） */
  .ocean .wave-effect {
    background: linear-gradient(45deg,
      hsl(var(--primary)) 0%,
      hsl(var(--accent)) 50%,
      hsl(var(--primary)) 100%
    );
    background-size: 200% 200%;
    animation: wave-motion 4s ease-in-out infinite;
  }

  @keyframes wave-motion {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* 主题特定的增强效果 */
  .neon {
    /* 为霓虹主题添加微妙的背景纹理 */
    background-image: radial-gradient(circle at 25% 25%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
                      radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
  }

  .ocean {
    /* 为深海主题添加波浪纹理 */
    background-image: radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
                      radial-gradient(circle at 80% 20%, rgba(8, 145, 178, 0.1) 0%, transparent 50%);
  }

  .forest {
    /* 为森林主题添加自然纹理 */
    background-image: radial-gradient(circle at 30% 70%, rgba(34, 197, 94, 0.08) 0%, transparent 50%),
                      radial-gradient(circle at 70% 30%, rgba(234, 179, 8, 0.08) 0%, transparent 50%);
  }

  .sunset {
    /* 为日落主题添加温暖渐变 */
    background-image: radial-gradient(circle at 50% 100%, rgba(249, 115, 22, 0.1) 0%, transparent 70%),
                      radial-gradient(circle at 100% 50%, rgba(220, 38, 38, 0.08) 0%, transparent 50%);
  }

  .rainbow {
    /* 为彩虹主题添加多彩光效 */
    background-image: radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 40%),
                      radial-gradient(circle at 80% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 40%),
                      radial-gradient(circle at 50% 50%, rgba(34, 197, 94, 0.05) 0%, transparent 60%);
  }

  /* 登录/注册页面特殊效果 */
  .neon .auth-glow {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3), 0 0 40px rgba(168, 85, 247, 0.2);
  }

  .ocean .auth-wave {
    background: linear-gradient(135deg,
      rgba(14, 165, 233, 0.1) 0%,
      rgba(8, 145, 178, 0.1) 50%,
      rgba(6, 182, 212, 0.1) 100%
    );
  }

  .forest .auth-nature {
    background: linear-gradient(135deg,
      rgba(34, 197, 94, 0.08) 0%,
      rgba(22, 163, 74, 0.08) 50%,
      rgba(234, 179, 8, 0.08) 100%
    );
  }

  .sunset .auth-warm {
    background: linear-gradient(135deg,
      rgba(249, 115, 22, 0.1) 0%,
      rgba(251, 146, 60, 0.1) 50%,
      rgba(220, 38, 38, 0.08) 100%
    );
  }

  .rainbow .auth-rainbow {
    background: linear-gradient(135deg,
      rgba(139, 92, 246, 0.1) 0%,
      rgba(6, 182, 212, 0.1) 25%,
      rgba(34, 197, 94, 0.1) 50%,
      rgba(234, 179, 8, 0.1) 75%,
      rgba(239, 68, 68, 0.1) 100%
    );
    animation: rainbow-shift 8s ease infinite;
  }

  /* 文本截断样式 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 视频卡片主题特效 */
  .neon .video-card-glow:hover {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4), 0 0 40px rgba(168, 85, 247, 0.2);
    border-color: rgba(168, 85, 247, 0.5);
  }

  .ocean .video-card-wave:hover {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.3), 0 0 40px rgba(14, 165, 233, 0.1);
    border-color: rgba(14, 165, 233, 0.5);
  }

  .forest .video-card-nature:hover {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3), 0 0 40px rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.5);
  }

  .sunset .video-card-warm:hover {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.3), 0 0 40px rgba(249, 115, 22, 0.1);
    border-color: rgba(249, 115, 22, 0.5);
  }

  .rainbow .video-card-rainbow:hover {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3), 0 0 40px rgba(6, 182, 212, 0.2);
    border-color: rgba(139, 92, 246, 0.5);
    animation: rainbow-border 3s ease infinite;
  }

  @keyframes rainbow-border {
    0%, 100% { border-color: rgba(139, 92, 246, 0.5); }
    25% { border-color: rgba(6, 182, 212, 0.5); }
    50% { border-color: rgba(34, 197, 94, 0.5); }
    75% { border-color: rgba(234, 179, 8, 0.5); }
  }
}

/* 使用utilities层确保按钮主题样式优先级 */
@layer utilities {
  /* 增强按钮在不同主题下的可见性 */
  .neon .bg-primary {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    box-shadow: 0 0 10px hsla(var(--primary), 0.3) !important;
  }

  .ocean .bg-primary {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    box-shadow: 0 2px 8px hsla(var(--primary), 0.3) !important;
  }

  .forest .bg-primary {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    box-shadow: 0 2px 8px hsla(var(--primary), 0.3) !important;
  }

  .sunset .bg-primary {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    box-shadow: 0 2px 8px hsla(var(--primary), 0.3) !important;
  }

  .rainbow .bg-primary {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    box-shadow: 0 0 10px hsla(var(--primary), 0.4) !important;
  }

  /* 增强按钮hover效果 */
  .neon .bg-primary:hover {
    background-color: hsl(var(--primary) / 0.9) !important;
    box-shadow: 0 0 15px hsla(var(--primary), 0.5) !important;
  }

  .ocean .bg-primary:hover {
    background-color: hsl(var(--primary) / 0.9) !important;
    box-shadow: 0 4px 12px hsla(var(--primary), 0.4) !important;
  }

  .forest .bg-primary:hover {
    background-color: hsl(var(--primary) / 0.9) !important;
    box-shadow: 0 4px 12px hsla(var(--primary), 0.4) !important;
  }

  .sunset .bg-primary:hover {
    background-color: hsl(var(--primary) / 0.9) !important;
    box-shadow: 0 4px 12px hsla(var(--primary), 0.4) !important;
  }

  .rainbow .bg-primary:hover {
    background-color: hsl(var(--primary) / 0.9) !important;
    box-shadow: 0 0 20px hsla(var(--primary), 0.6) !important;
  }

  /* 确保dark和light主题也有正确的按钮样式 */
  .dark .bg-primary {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
  }

  .light .bg-primary {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
  }

  .dark .bg-primary:hover {
    background-color: hsl(var(--primary) / 0.9) !important;
  }

  .light .bg-primary:hover {
    background-color: hsl(var(--primary) / 0.9) !important;
  }

  /* 优化ghost按钮在不同主题下的显示效果 */
  .neon .hover\\:bg-accent:hover {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
    box-shadow: 0 0 8px hsla(var(--accent), 0.3) !important;
  }

  .ocean .hover\\:bg-accent:hover {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
    box-shadow: 0 2px 6px hsla(var(--accent), 0.2) !important;
  }

  .forest .hover\\:bg-accent:hover {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
    box-shadow: 0 2px 6px hsla(var(--accent), 0.2) !important;
  }

  .sunset .hover\\:bg-accent:hover {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
    box-shadow: 0 2px 6px hsla(var(--accent), 0.2) !important;
  }

  .rainbow .hover\\:bg-accent:hover {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
    box-shadow: 0 0 8px hsla(var(--accent), 0.3) !important;
  }

  /* 移动端优化样式 */
  @media (max-width: 640px) {
    /* 确保移动端字体自动缩小 */
    html {
      font-size: 14px;
    }

    /* 移动端容器优化 */
    .container {
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }

    /* 移动端按钮优化 */
    .btn-mobile {
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
    }

    /* 移动端卡片优化 */
    .mobile-card {
      padding: 0.75rem;
    }

    /* 移动端间距优化 */
    .mobile-spacing {
      gap: 0.5rem;
    }

    /* 移动端文字优化 */
    .mobile-text-sm {
      font-size: 0.75rem;
    }

    .mobile-text-base {
      font-size: 0.875rem;
    }

    .mobile-text-lg {
      font-size: 1rem;
    }
  }

  /* 超小屏幕优化 */
  @media (max-width: 480px) {
    html {
      font-size: 13px;
    }

    .container {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
    }

    /* 超小屏幕按钮进一步缩小 */
    .btn-xs {
      padding: 0.375rem 0.5rem;
      font-size: 0.75rem;
    }
  }

  /* 添加xs断点支持 */
  @media (min-width: 475px) {
    .xs\\:block {
      display: block !important;
    }

    .xs\\:hidden {
      display: none !important;
    }

    .xs\\:flex {
      display: flex !important;
    }
  }

  /* 移动端触摸优化 */
  @media (max-width: 640px) {
    /* 增加触摸目标大小 */
    button, a, [role="button"] {
      min-height: 44px;
      min-width: 44px;
    }

    /* 优化滚动性能 */
    .overflow-x-auto {
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    .overflow-x-auto::-webkit-scrollbar {
      display: none;
    }

    /* 移动端卡片悬停效果调整 */
    .group:hover .group-hover\\:scale-\\[1\\.02\\] {
      transform: none;
    }

    /* 移动端输入框优化 */
    input[type="search"], input[type="text"], input[type="email"], input[type="password"] {
      font-size: 16px; /* 防止iOS缩放 */
    }

    /* 移动端下拉菜单优化 */
    [data-radix-popper-content-wrapper] {
      max-width: calc(100vw - 2rem) !important;
    }
  }

  /* 移动端专用工具类 */
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .mobile-header-height {
    height: 3.5rem; /* 56px */
  }

  @media (min-width: 640px) {
    .mobile-header-height {
      height: 4rem; /* 64px */
    }
  }
}