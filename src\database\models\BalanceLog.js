const BaseModel = require('../BaseModel');

class BalanceLog extends BaseModel {
  constructor() {
    super('balance_logs');
  }

  async create(logData, connection = null) {
    const fields = [
      'user_id', 'amount', 'balance_after', 'type',
      'description', 'related_id', 'related_type'
    ];
    const placeholders = fields.map(() => '?').join(', ');
    const values = fields.map(field => logData[field]);

    const query = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
    
    const executor = connection || this;
    const [result] = await executor.query(query, values);
    return result.insertId;
  }
}

module.exports = new BalanceLog(); 