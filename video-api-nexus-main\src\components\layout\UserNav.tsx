import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { useFollowContext } from '@/contexts/FollowContext';
import { followApi } from '@/services/followApi';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  CreditCard,
  LogOut,
  User,
  Heart,
  Video,
  ListOrdered,
  DollarSign,
  Loader2,
  Wallet,
  Users,
  UserPlus,
} from "lucide-react"

export function UserNav() {
  const { t } = useTranslation();
  const { isAuthenticated, user, logout, isLoading } = useAuth();
  const { refreshCounter } = useFollowContext();
  const navigate = useNavigate();
  const [followStats, setFollowStats] = useState({ follower_count: 0, following_count: 0 });

  // 获取关注统计数据
  useEffect(() => {
    const fetchFollowStats = async () => {
      if (user?.id) {
        try {
          const response = await followApi.getFollowStats(user.id);
          console.log('Follow stats response:', response); // 调试日志
          // 安全地提取数据
          const stats = response?.data?.data || response?.data || {};
          setFollowStats({
            follower_count: stats.follower_count || 0,
            following_count: stats.following_count || 0
          });
        } catch (error) {
          console.warn('获取关注统计失败:', error);
          setFollowStats({ follower_count: 0, following_count: 0 });
        }
      }
    };

    if (isAuthenticated && user?.id) {
      fetchFollowStats();
    }
  }, [isAuthenticated, user?.id, refreshCounter]);

  const handleLogout = () => {
    logout();
    navigate('/'); 
  };
  
  if (isLoading) {
    return <Loader2 className="mr-2 h-4 w-4 animate-spin" />;
  }

  if (!isAuthenticated) {
    return (
      <Button asChild>
        <Link to="/login">{t('nav.login')}</Link>
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={user?.avatar ? (user.avatar.startsWith('http') ? user.avatar : user.avatar) : "/placeholder.svg"}
              alt={user?.username || 'User'}
            />
            <AvatarFallback>{user?.username ? user.username.charAt(0).toUpperCase() : 'U'}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user?.username}</p>
            {user?.email && <p className="text-xs leading-none text-muted-foreground">{user.email}</p>}
            
            {/* 关注和粉丝统计 */}
            <div className="flex items-center space-x-4 pt-2">
              <Link 
                to={`/users/${user?.id}/followers?tab=followers`}
                className="flex items-center space-x-1 text-xs text-muted-foreground hover:text-foreground transition-colors"
              >
                <Users className="h-3 w-3" />
                <span>{followStats.follower_count} {t('user.followers')}</span>
              </Link>
              <Link
                to={`/users/${user?.id}/following?tab=following`}
                className="flex items-center space-x-1 text-xs text-muted-foreground hover:text-foreground transition-colors"
              >
                <UserPlus className="h-3 w-3" />
                <span>{followStats.following_count} {t('user.following')}</span>
              </Link>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem disabled className="cursor-default">
          <Wallet className="mr-2 h-4 w-4" />
          <span>{t('user.balance')}: ¥{user?.balance ? Number(user.balance).toFixed(2) : '0.00'}</span>
        </DropdownMenuItem>
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link to="/profile">
              <User className="mr-2 h-4 w-4" />
              <span>{t('user.profile')}</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to="/my-works">
              <Video className="mr-2 h-4 w-4" />
              <span>{t('user.myWorks')}</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to="/my-favorites">
              <Heart className="mr-2 h-4 w-4" />
              <span>{t('user.favorites')}</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to="/my-orders">
              <ListOrdered className="mr-2 h-4 w-4" />
              <span>{t('user.orders')}</span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link to="/my-earnings">
              <DollarSign className="mr-2 h-4 w-4" />
              <span>{t('user.earnings')}</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>{t('nav.logout')}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 