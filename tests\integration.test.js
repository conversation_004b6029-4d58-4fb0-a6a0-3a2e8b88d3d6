const request = require('supertest');
const app = require('../app');
const { expect } = require('chai');

describe('集成测试套件', () => {
  let userToken;
  let adminToken;
  let testUserId;
  let testVideoId;
  let testCommentId;

  // 测试用户数据
  const testUser = {
    username: 'integrationtest',
    email: '<EMAIL>',
    password: 'password123',
    nickname: '集成测试用户'
  };

  describe('完整用户流程测试', () => {
    it('用户注册 -> 登录 -> 更新资料 -> 查看资料', async () => {
      // 1. 注册
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(testUser)
        .expect(201);

      expect(registerResponse.body.success).to.be.true;
      userToken = registerResponse.body.data.tokens.accessToken;
      testUserId = registerResponse.body.data.user.id;

      // 2. 登录验证
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200);

      expect(loginResponse.body.success).to.be.true;

      // 3. 更新资料
      const updateData = {
        nickname: '更新后的昵称',
        bio: '这是更新后的个人简介'
      };

      const updateResponse = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(200);

      expect(updateResponse.body.data.user.nickname).to.equal(updateData.nickname);

      // 4. 查看资料
      const profileResponse = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(profileResponse.body.data.user.nickname).to.equal(updateData.nickname);
      expect(profileResponse.body.data.user.bio).to.equal(updateData.bio);
    });
  });

  describe('视频和互动流程测试', () => {
    it('查看视频 -> 发表评论 -> 点赞视频 -> 收藏视频', async () => {
      // 1. 获取视频列表
      const videoListResponse = await request(app)
        .get('/api/videos/list?page=1&pageSize=5')
        .expect(200);

      expect(videoListResponse.body.success).to.be.true;
      
      // 如果有视频，使用第一个视频进行测试
      if (videoListResponse.body.data.data.length > 0) {
        testVideoId = videoListResponse.body.data.data[0].id;

        // 2. 查看视频详情
        const videoDetailResponse = await request(app)
          .get(`/api/videos/${testVideoId}`)
          .set('Authorization', `Bearer ${userToken}`)
          .expect(200);

        expect(videoDetailResponse.body.success).to.be.true;

        // 3. 发表评论
        const commentResponse = await request(app)
          .post('/api/interactions/comments')
          .set('Authorization', `Bearer ${userToken}`)
          .send({
            videoId: testVideoId,
            content: '这是一个集成测试评论'
          })
          .expect(201);

        expect(commentResponse.body.success).to.be.true;
        testCommentId = commentResponse.body.data.comment.id;

        // 4. 点赞视频
        const likeResponse = await request(app)
          .post('/api/interactions/likes')
          .set('Authorization', `Bearer ${userToken}`)
          .send({
            targetId: testVideoId,
            targetType: 'video'
          })
          .expect(200);

        expect(likeResponse.body.success).to.be.true;
        expect(likeResponse.body.data.liked).to.be.true;

        // 5. 收藏视频
        const favoriteResponse = await request(app)
          .post('/api/interactions/favorites')
          .set('Authorization', `Bearer ${userToken}`)
          .send({
            videoId: testVideoId
          })
          .expect(200);

        expect(favoriteResponse.body.success).to.be.true;
        expect(favoriteResponse.body.data.favorited).to.be.true;

        // 6. 验证互动状态
        const batchCheckResponse = await request(app)
          .post('/api/interactions/batch-check')
          .set('Authorization', `Bearer ${userToken}`)
          .send({
            videoIds: [testVideoId]
          })
          .expect(200);

        expect(batchCheckResponse.body.success).to.be.true;
        expect(batchCheckResponse.body.data.likes[`video_${testVideoId}`]).to.be.true;
        expect(batchCheckResponse.body.data.favorites[testVideoId]).to.be.true;
      }
    });
  });

  describe('会员系统流程测试', () => {
    it('查看计划 -> 获取会员信息 -> 查看权益', async () => {
      // 1. 获取会员计划
      const plansResponse = await request(app)
        .get('/api/members/plans')
        .expect(200);

      expect(plansResponse.body.success).to.be.true;
      expect(plansResponse.body.data.plans).to.be.an('array');

      // 2. 获取我的会员信息
      const membershipResponse = await request(app)
        .get('/api/members/my-membership')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(membershipResponse.body.success).to.be.true;
      expect(membershipResponse.body.data.level).to.exist;

      // 3. 如果有计划，测试计划详情
      if (plansResponse.body.data.plans.length > 0) {
        const planId = plansResponse.body.data.plans[0].id;
        
        const planDetailResponse = await request(app)
          .get(`/api/members/plans/${planId}`)
          .expect(200);

        expect(planDetailResponse.body.success).to.be.true;
        expect(planDetailResponse.body.data.plan.id).to.equal(planId);
      }
    });
  });

  describe('搜索功能测试', () => {
    it('视频搜索 -> 评论搜索', async () => {
      // 1. 视频搜索
      const videoSearchResponse = await request(app)
        .get('/api/videos/search?keyword=测试&page=1&pageSize=5')
        .expect(200);

      expect(videoSearchResponse.body.success).to.be.true;
      expect(videoSearchResponse.body.data.data).to.be.an('array');

      // 2. 评论搜索
      const commentSearchResponse = await request(app)
        .get('/api/interactions/comments/search?keyword=测试&page=1&pageSize=5')
        .expect(200);

      expect(commentSearchResponse.body.success).to.be.true;
      expect(commentSearchResponse.body.data.data).to.be.an('array');
    });
  });

  describe('权限控制测试', () => {
    it('测试不同权限级别的访问控制', async () => {
      // 1. 未认证用户访问需要认证的接口
      const unauthorizedResponse = await request(app)
        .get('/api/users/profile')
        .expect(401);

      expect(unauthorizedResponse.body.success).to.be.false;

      // 2. 普通用户访问管理员接口
      const forbiddenResponse = await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(forbiddenResponse.body.success).to.be.false;

      // 3. 普通用户访问自己的资源
      const allowedResponse = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(allowedResponse.body.success).to.be.true;
    });
  });

  describe('数据一致性测试', () => {
    it('测试操作后数据的一致性', async () => {
      if (!testVideoId || !testCommentId) {
        console.log('跳过数据一致性测试：缺少测试数据');
        return;
      }

      // 1. 获取视频评论列表，验证我们的评论存在
      const commentsResponse = await request(app)
        .get(`/api/interactions/videos/${testVideoId}/comments`)
        .expect(200);

      expect(commentsResponse.body.success).to.be.true;
      
      const ourComment = commentsResponse.body.data.data.find(
        comment => comment.id === testCommentId
      );
      expect(ourComment).to.exist;
      expect(ourComment.content).to.equal('这是一个集成测试评论');

      // 2. 获取用户收藏列表，验证收藏的视频存在
      const favoritesResponse = await request(app)
        .get(`/api/interactions/users/${testUserId}/favorites`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(favoritesResponse.body.success).to.be.true;
      
      const favoriteVideo = favoritesResponse.body.data.data.find(
        fav => fav.video_id === testVideoId
      );
      expect(favoriteVideo).to.exist;
    });
  });

  describe('错误恢复测试', () => {
    it('测试系统对错误输入的处理', async () => {
      // 1. 无效的JSON数据
      const invalidJsonResponse = await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);

      expect(invalidJsonResponse.body.success).to.be.false;

      // 2. 缺少必需字段
      const missingFieldResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>'
          // 缺少password
        })
        .expect(400);

      expect(missingFieldResponse.body.success).to.be.false;

      // 3. 无效的ID格式
      const invalidIdResponse = await request(app)
        .get('/api/videos/invalid-id')
        .expect(400);

      expect(invalidIdResponse.body.success).to.be.false;

      // 4. 不存在的资源
      const notFoundResponse = await request(app)
        .get('/api/videos/999999')
        .expect(404);

      expect(notFoundResponse.body.success).to.be.false;
    });
  });

  // 清理测试数据
  after(async () => {
    console.log('清理集成测试数据...');
    
    // 这里可以添加清理逻辑
    // 例如：删除测试用户、测试评论等
    
    if (testCommentId && userToken) {
      try {
        await request(app)
          .delete(`/api/interactions/comments/${testCommentId}`)
          .set('Authorization', `Bearer ${userToken}`);
      } catch (error) {
        console.log('清理测试评论失败:', error.message);
      }
    }
  });
});
