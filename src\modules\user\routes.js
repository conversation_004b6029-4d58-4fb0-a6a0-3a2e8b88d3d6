const express = require('express');
const router = express.Router();

// 导入控制器和中间件
const userController = require('./controllers/userController');
const { verifyToken, requireAdmin, requireOwnership, auth } = require('../../middleware/auth');
const { disabledRateLimiter } = require('../../middleware/rateLimiter');
const { uploadMiddlewares, fileCleanup, extractFileInfo } = require('../../middleware/upload');
const {
  validateUserUpdate,
  validateUserList,
  validateId,
  validatePagination
} = require('../../middleware/validation');

// 所有用户路由都需要认证
router.use(verifyToken);

// 用户个人资料相关路由
router.get('/profile', userController.getProfile);

router.put('/profile',
  validateUserUpdate,
  userController.updateProfile
);

router.post('/avatar',
  disabledRateLimiter,
  uploadMiddlewares.avatar,
  fileCleanup,
  extractFileInfo,
  userController.uploadAvatar
);

router.delete('/avatar',
  userController.deleteAvatar
);

router.delete('/account',
  userController.deleteAccount
);

// 发送更换邮箱验证码
router.post('/send-change-email-code', auth, userController.sendChangeEmailCode);

// 验证并更换邮箱
router.post('/verify-change-email', auth, userController.verifyChangeEmail);

// 用户订单查询路由
router.get('/orders',
  validatePagination,
  userController.getUserOrders
);

// 用户查询路由
router.get('/list',
  requireAdmin,
  validateUserList,
  userController.getUserList
);

router.get('/stats',
  requireAdmin,
  userController.getUserStats
);

router.get('/:id',
  validateId,
  userController.getUserById
);

// 管理员操作路由
router.put('/:id/ban',
  requireAdmin,
  validateId,
  userController.banUser
);

router.put('/:id/unban',
  requireAdmin,
  validateId,
  userController.unbanUser
);

router.put('/:id/role',
  requireAdmin,
  validateId,
  userController.changeUserRole
);

// 测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '用户模块测试接口',
    module: 'user',
    user: req.user || null
  });
});

module.exports = router;
