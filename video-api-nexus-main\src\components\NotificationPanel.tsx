import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '@/lib/api';
import { Bell, CheckCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import LoadingSpinner from './LoadingSpinner';
import { ScrollArea } from './ui/scroll-area';
import { useTranslation } from 'react-i18next';
import { translateNotificationContent } from '@/utils/notificationTranslator';

export default function NotificationPanel({ unreadCount, isOpen, onOpenChange }) {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { data, isLoading, refetch } = useQuery({
    queryKey: ['notifications'],
    queryFn: () => getNotifications({ pageSize: 10 }),
    enabled: false, // 初始禁用，等待手动触发
  });

  const markAsReadMutation = useMutation({
    mutationFn: markNotificationAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['notificationStatus'] });
    },
  });

  const markAllAsReadMutation = useMutation({
    mutationFn: markAllNotificationsAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      queryClient.invalidateQueries({ queryKey: ['notificationStatus'] });
    },
  });

  const handleOpenChange = (open) => {
    onOpenChange(open);
    if (open) {
      refetch(); // 直接调用 useQuery 返回的 refetch 函数
    }
  };

  const notifications = data?.data?.data?.notifications || [];
  // “全部已读”按钮的状态由内部实时计算，保证交互即时性
  const localUnreadCount = notifications.filter(n => !n.is_read).length;



  return (
    <DropdownMenu onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <Bell className="h-5 w-5" />
          {/* 红点由外部轮询的 unreadCount 驱动，保证自动提示 */}
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 flex h-2 w-2">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end">
        <DropdownMenuLabel className="flex justify-between items-center">
          <span>{t('notification.title')}</span>
          <Button variant="ghost" size="sm" onClick={() => markAllAsReadMutation.mutate()} disabled={localUnreadCount === 0 || markAllAsReadMutation.isPending}>
            <CheckCheck className="mr-2 h-4 w-4" />
            {t('notification.markAllRead')}
          </Button>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <ScrollArea className="h-96">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <LoadingSpinner />
            </div>
          ) : notifications.length === 0 ? (
            <p className="text-center text-sm text-muted-foreground py-4">{t('notification.noNotifications')}</p>
          ) : (
            notifications.map((notif: any) => {
              const translatedContent = translateNotificationContent(notif, t);
              return (
                <DropdownMenuItem
                  key={notif.id}
                  className={`flex flex-col items-start gap-2 whitespace-normal ${!notif.is_read ? 'bg-accent' : ''}`}
                  onClick={() => !notif.is_read && markAsReadMutation.mutate(notif.id)}
                  disabled={markAsReadMutation.isPending}
                >
                  <p className="font-bold">{translatedContent.title}</p>
                  <p className="text-sm text-muted-foreground">{translatedContent.message}</p>
                  <p className="text-xs text-muted-foreground self-end">{new Date(notif.created_at).toLocaleString()}</p>
                </DropdownMenuItem>
              );
            })
          )}
        </ScrollArea>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 