import React, { useState } from 'react';
import { Heart } from 'lucide-react';
import { toggleFavorite } from '@/lib/api';
import clsx from 'clsx';

interface FavoriteButtonProps {
  videoId: string | number;
  isFavorited: boolean;
  onFavoriteChange: (isFavorited: boolean) => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showText?: boolean;
  requireAuthAction?: (action: () => void) => void;
}

const FavoriteButton: React.FC<FavoriteButtonProps> = ({
  videoId,
  isFavorited,
  onFavoriteChange,
  size = 'md',
  className = '',
  showText = true,
  requireAuthAction
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 图标大小映射
  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 20
  };

  // 按钮样式映射
  const buttonStyles = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const handleFavorite = async (e: React.MouseEvent) => {
    e.stopPropagation(); // 防止触发父元素的点击事件
    
    const favoriteAction = async () => {
    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await toggleFavorite(videoId);
      const newFavoritedState = response.data.data.favorited;
      
        onFavoriteChange(newFavoritedState);

      // 显示成功提示（可选）
      if (newFavoritedState) {
        console.log('收藏成功');
      } else {
        console.log('取消收藏成功');
      }
    } catch (error: any) {
      console.error('收藏操作失败:', error);
      setError('操作失败，请稍后重试');
      
      // 可以在这里添加错误提示的UI反馈
      setTimeout(() => setError(null), 3000);
      } finally {
        setIsLoading(false);
      }
    };

    if (requireAuthAction) {
      requireAuthAction(favoriteAction);
    } else {
      favoriteAction();
    }
  };

  return (
    <div className="relative">
      <button
        onClick={handleFavorite}
        disabled={isLoading}
        className={clsx(
          'flex items-center space-x-1 rounded-md transition-all duration-200',
          buttonStyles[size],
          isFavorited
            ? 'bg-red-50 text-red-600 hover:bg-red-100 border border-red-200'
            : 'bg-gray-50 text-gray-600 hover:bg-gray-100 border border-gray-200',
          isLoading && 'opacity-50 cursor-not-allowed',
          'hover:scale-105 active:scale-95',
          className
        )}
        title={isFavorited ? '取消收藏' : '收藏'}
      >
        <Heart
          size={iconSizes[size]}
          className={clsx(
            'transition-all duration-200',
            isFavorited ? 'fill-current text-red-500' : 'text-gray-500',
            isLoading && 'animate-pulse'
          )}
        />
        {showText && (
          <span className={clsx(
            'transition-colors duration-200',
            isFavorited ? 'text-red-600' : 'text-gray-600'
          )}>
            {isLoading ? '...' : (isFavorited ? '已收藏' : '收藏')}
          </span>
        )}
      </button>
      
      {/* 错误提示 */}
      {error && (
        <div className="absolute top-full left-0 mt-1 px-2 py-1 bg-red-100 text-red-600 text-xs rounded shadow-lg whitespace-nowrap z-10">
          {error}
        </div>
      )}
    </div>
  );
};

export default FavoriteButton;
