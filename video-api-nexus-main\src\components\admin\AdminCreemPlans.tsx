import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '@/services/adminApi';
import { CreemPlan } from '@/types/creem';
import { Button } from '@/components/ui/button';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { DataTable } from '@/components/ui/data-table.tsx'; 
import { ColumnDef } from '@tanstack/react-table';
import { Switch } from '@/components/ui/switch';
import CreemPlanDialog from './CreemPlanDialog';

const AdminCreemPlans: React.FC = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<CreemPlan | null>(null);

  // 获取数据
  const { data: plans, isLoading, isError, error } = useQuery<CreemPlan[], Error>({
    queryKey: ['creemPlans'],
    queryFn: async () => {
        const response = await adminApi.getCreemPlans();
        // 修正：从 response.data 中获取数据
        if (response.data.success) {
            return response.data.data;
        }
        // 修正：从 response.data 中获取错误信息
        throw new Error(response.data.message || '获取Creem产品列表失败');
    }
  });

  // 删除操作
  const deleteMutation = useMutation({
    mutationFn: (id: number) => adminApi.deleteCreemPlan(id),
    onSuccess: () => {
      toast({ title: '成功', description: '产品已删除。' });
      queryClient.invalidateQueries({ queryKey: ['creemPlans'] });
    },
    onError: (err: any) => {
      toast({
        title: '删除失败',
        description: err.response?.data?.message || err.message,
        variant: 'destructive',
      });
    },
  });

  // 更新状态操作
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, isActive }: { id: number, isActive: boolean }) => 
        adminApi.updateCreemPlan(id, { is_active: isActive }),
    onSuccess: () => {
        toast({ title: '成功', description: '产品状态已更新。' });
        queryClient.invalidateQueries({ queryKey: ['creemPlans'] });
    },
    onError: (err: any) => {
        toast({
            title: '状态更新失败',
            description: err.response?.data?.message || err.message,
            variant: 'destructive',
        });
    },
  });

  const handleCreate = () => {
    setSelectedPlan(null);
    setIsDialogOpen(true);
  };

  const handleEdit = (plan: CreemPlan) => {
    setSelectedPlan(plan);
    setIsDialogOpen(true);
  };

  const handleDelete = (id: number) => {
    if (window.confirm('您确定要删除这个产品吗？此操作不可逆。')) {
      deleteMutation.mutate(id);
    }
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setSelectedPlan(null);
  };
  
  const handleSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ['creemPlans'] });
  };
  
  const columns: ColumnDef<CreemPlan>[] = [
    { accessorKey: 'id', header: 'ID' },
    { accessorKey: 'name', header: '名称' },
    { accessorKey: 'price', header: '价格', cell: ({ row }) => `${row.original.price} ${row.original.currency}` },
    { accessorKey: 'creem_product_id', header: 'Creem产品ID' },
    { 
      accessorKey: 'is_active', 
      header: '状态',
      cell: ({ row }) => (
        <Switch
          checked={row.original.is_active}
          onCheckedChange={(isActive) => {
            updateStatusMutation.mutate({ id: row.original.id, isActive });
          }}
        />
      )
    },
    { accessorKey: 'created_at', header: '创建时间', cell: ({ row }) => new Date(row.original.created_at).toLocaleString() },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <Button variant="ghost" size="icon" onClick={() => handleEdit(row.original)}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={() => handleDelete(row.original.id)}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];


  if (isLoading) return <div>正在加载产品列表...</div>;
  if (isError) return <div className="text-red-500">加载错误: {error.message}</div>;

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Creem 产品管理</h1>
        <Button onClick={handleCreate}>
          <Plus className="mr-2 h-4 w-4" /> 创建新产品
        </Button>
      </div>
      <DataTable columns={columns} data={plans || []} />
      <CreemPlanDialog
        plan={selectedPlan}
        isOpen={isDialogOpen}
        onClose={handleDialogClose}
        onSuccess={handleSuccess}
      />
    </div>
  );
};

export default AdminCreemPlans; 