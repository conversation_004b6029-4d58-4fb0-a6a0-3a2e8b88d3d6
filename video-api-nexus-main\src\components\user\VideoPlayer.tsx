import React, { useState, useEffect } from 'react';
import { ArrowLeft, Heart, Share, Download, Eye } from 'lucide-react';
import { batchCheckInteractions } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import CommentSection from './CommentSection';
import FavoriteButton from '@/components/common/FavoriteButton';
import { useTranslation } from 'react-i18next';

interface Video {
  id: string | number;
  title: string;
  description?: string;
  username: string;
  view_count: number;
  like_count: number;
  comment_count: number;
}

interface VideoPlayerProps {
  video: Video;
  onBack: () => void;
  onFavoriteChange?: () => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ video, onBack, onFavoriteChange }) => {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { t } = useTranslation();
  const [isLiked, setIsLiked] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [interactionLoading, setInteractionLoading] = useState(true);

  // 优化上传者显示逻辑
  const isSystemAdmin = video.username === t('videoInfo.systemAdmin');
  const displayUsername = video.username || t('videoInfo.anonymousAuthor');

  // 获取收藏状态
  useEffect(() => {
    const fetchInteractionStatus = async () => {
      // 如果用户未认证或认证状态加载中，设置默认状态
      if (!isAuthenticated || authLoading) {
        setIsFavorited(false);
        setIsLiked(false);
        setInteractionLoading(false);
        return;
      }

      try {
        setInteractionLoading(true);
        const response = await batchCheckInteractions([video.id]);
        const favorites = response.data.data.favorites;
        setIsFavorited(favorites[video.id] || false);
      } catch (err) {
        console.error('获取收藏状态失败:', err);
        // 不显示错误，只是默认为未收藏状态
        setIsFavorited(false);
        setIsLiked(false);
      } finally {
        setInteractionLoading(false);
      }
    };

    fetchInteractionStatus();
  }, [video.id, isAuthenticated, authLoading]);
  
  return (
    <div className="space-y-6">
      {/* Back Button */}
      <button 
        onClick={onBack}
        className="flex items-center space-x-2 text-muted-foreground hover:text-foreground"
      >
        <ArrowLeft size={20} />
        <span>返回</span>
      </button>

      {/* Video Player */}
      <div className="bg-black rounded-lg overflow-hidden aspect-video flex items-center justify-center">
        <div className="text-white text-center">
          <div className="w-20 h-20 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-3xl text-primary">▶</span>
          </div>
          <p>视频播放器</p>
          <p className="text-sm text-white/70 mt-1">点击播放 {video.title}</p>
        </div>
      </div>

      {/* Video Info */}
      <div className="space-y-4">
        <div>
          <h1 className="text-2xl font-bold mb-2">{video.title}</h1>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Eye size={16} />
                <span>{video.view_count.toLocaleString()} {t('videoInfo.views')}</span>
              </div>
              <span>•</span>
              <span>发布于 2024年12月15日</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsLiked(!isLiked)}
                className={`flex items-center space-x-1 px-3 py-2 rounded-md transition-colors ${
                  isLiked
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary hover:bg-secondary/80'
                }`}
              >
                <Heart size={16} className={isLiked ? 'fill-current' : ''} />
                <span>{video.like_count + (isLiked ? 1 : 0)}</span>
              </button>

              {/* 收藏按钮 */}
              {!interactionLoading && (
                <FavoriteButton
                  videoId={video.id}
                  initialFavorited={isFavorited}
                  onFavoriteChange={(newState) => {
                    setIsFavorited(newState);
                    // 通知父组件收藏状态变化
                    if (onFavoriteChange) {
                      onFavoriteChange();
                    }
                  }}
                  size="md"
                  showText={true}
                  className="bg-secondary hover:bg-secondary/80"
                />
              )}

              <button className="flex items-center space-x-1 px-3 py-2 bg-secondary hover:bg-secondary/80 rounded-md">
                <Share size={16} />
                <span>分享</span>
              </button>

              <button className="flex items-center space-x-1 px-3 py-2 bg-secondary hover:bg-secondary/80 rounded-md">
                <Download size={16} />
                <span>下载</span>
              </button>
            </div>
          </div>
        </div>

        {/* Channel Info */}
        {!isSystemAdmin && (
          <div className="flex items-center justify-between p-4 bg-card rounded-lg border">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground font-semibold">
                  {displayUsername[0]}
                </span>
              </div>
              <div>
                <p className="font-semibold">{displayUsername}</p>
                <p className="text-sm text-muted-foreground">1.2万 订阅者</p>
              </div>
            </div>
            <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
              订阅
            </button>
          </div>
        )}

        {/* Description */}
        <div className="p-4 bg-card rounded-lg border">
          <p className="text-sm">{video.description}</p>
        </div>
      </div>

      {/* Comments Section */}
      <CommentSection initialCommentCount={video.comment_count} />
    </div>
  );
};

export default VideoPlayer;
