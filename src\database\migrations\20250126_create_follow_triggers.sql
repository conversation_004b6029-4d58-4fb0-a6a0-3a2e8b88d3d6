-- 用户统计触发器迁移（简化版）
-- 执行时间: 2025-01-26

-- 注意：如果数据库没有SUPER权限，触发器创建可能失败
-- 但关注功能的基本操作仍然可以正常工作

-- 这里暂时注释掉触发器，后续可以手动执行或通过其他方式创建
-- 应用程序会通过代码逻辑来维护统计数据

/*
-- 当用户发布新视频时自动更新统计
DROP TRIGGER IF EXISTS update_user_stats_on_video_insert;
CREATE TRIGGER update_user_stats_on_video_insert
AFTER INSERT ON videos
FOR EACH ROW
INSERT INTO user_stats (user_id, video_count) 
VALUES (NEW.user_id, 1)
ON DUPLICATE KEY UPDATE 
    video_count = video_count + 1,
    updated_at = CURRENT_TIMESTAMP;
*/

-- 插入测试数据确保迁移执行成功
INSERT IGNORE INTO user_stats (user_id, follower_count, following_count, video_count, total_views, total_likes)
SELECT id, 0, 0, 0, 0, 0 FROM users WHERE id = 1;