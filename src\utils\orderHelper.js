function generateOrderNo() {
  const date = new Date();
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  const randomPart = Math.random().toString().substr(2, 6);
  
  return `ORD-${year}${month}${day}${hours}${minutes}${seconds}-${randomPart}`;
}

module.exports = {
  generateOrderNo,
}; 