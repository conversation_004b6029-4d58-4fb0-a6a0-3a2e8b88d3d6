const helmet = require('helmet');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');
const { operationLogger } = require('./requestLogger');

// 基础安全配置
const securityConfig = {
  // 内容安全策略
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      mediaSrc: ["'self'", "blob:"],
      connectSrc: ["'self'"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"]
    }
  },
  
  // HSTS配置
  hsts: {
    maxAge: 31536000, // 1年
    includeSubDomains: true,
    preload: true
  },
  
  // 其他安全头
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
};

// 创建安全中间件
const createSecurityMiddleware = (customConfig = {}) => {
  const config = { ...securityConfig, ...customConfig };
  return helmet(config);
};

// IP白名单中间件
const createIPWhitelist = (whitelist = []) => {
  return (req, res, next) => {
    const clientIP = req.ip;
    
    if (whitelist.length === 0) {
      return next(); // 没有白名单限制
    }
    
    const isAllowed = whitelist.some(ip => {
      if (ip.includes('/')) {
        // CIDR格式支持（简化版）
        const [network, prefix] = ip.split('/');
        return clientIP.startsWith(network.split('.').slice(0, Math.floor(parseInt(prefix) / 8)).join('.'));
      }
      return clientIP === ip;
    });
    
    if (!isAllowed) {
      operationLogger.logSecurityEvent(req, 'ip_blocked', 'high', `IP ${clientIP} 不在白名单中`);
      throw new AppError('访问被拒绝', 403, 'IP_NOT_ALLOWED');
    }
    
    next();
  };
};

// 用户代理检查中间件
const userAgentFilter = (options = {}) => {
  const {
    blockedPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i, /java/i
    ],
    allowedBots = ['googlebot', 'bingbot'],
    blockEmpty = true
  } = options;
  
  return (req, res, next) => {
    const userAgent = req.get('User-Agent') || '';
    
    // 检查空用户代理
    if (blockEmpty && !userAgent.trim()) {
      operationLogger.logSecurityEvent(req, 'empty_user_agent', 'medium', '空用户代理被阻止');
      throw new AppError('无效的用户代理', 403, 'INVALID_USER_AGENT');
    }
    
    // 检查是否为允许的机器人
    const isAllowedBot = allowedBots.some(bot => 
      userAgent.toLowerCase().includes(bot.toLowerCase())
    );
    
    if (isAllowedBot) {
      return next();
    }
    
    // 检查被阻止的模式
    const isBlocked = blockedPatterns.some(pattern => pattern.test(userAgent));
    
    if (isBlocked) {
      operationLogger.logSecurityEvent(req, 'blocked_user_agent', 'medium', `用户代理被阻止: ${userAgent}`);
      throw new AppError('访问被拒绝', 403, 'USER_AGENT_BLOCKED');
    }
    
    next();
  };
};

// SQL注入检测中间件
const sqlInjectionDetector = (req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
    /(\'|\"|;|--|\*|\/\*|\*\/)/,
    /(\bOR\b|\bAND\b).*(\=|\<|\>)/i,
    /(UNION.*SELECT|SELECT.*FROM|INSERT.*INTO|UPDATE.*SET|DELETE.*FROM)/i
  ];
  
  const checkForSQLInjection = (obj, path = '') => {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;
      
      if (typeof value === 'string') {
        const isSuspicious = sqlPatterns.some(pattern => pattern.test(value));
        if (isSuspicious) {
          operationLogger.logSecurityEvent(
            req, 
            'sql_injection_attempt', 
            'high', 
            `检测到SQL注入尝试: ${currentPath} = ${value}`
          );
          throw new AppError('检测到恶意输入', 400, 'MALICIOUS_INPUT');
        }
      } else if (typeof value === 'object' && value !== null) {
        checkForSQLInjection(value, currentPath);
      }
    }
  };
  
  // 检查查询参数、请求体和路径参数
  if (req.query && Object.keys(req.query).length > 0) {
    checkForSQLInjection(req.query);
  }
  
  if (req.body && Object.keys(req.body).length > 0) {
    checkForSQLInjection(req.body);
  }
  
  if (req.params && Object.keys(req.params).length > 0) {
    checkForSQLInjection(req.params);
  }
  
  next();
};

// XSS检测中间件
const xssDetector = (req, res, next) => {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]+src[^>]*>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi
  ];
  
  const checkForXSS = (obj, path = '') => {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;
      
      if (typeof value === 'string') {
        const isSuspicious = xssPatterns.some(pattern => pattern.test(value));
        if (isSuspicious) {
          operationLogger.logSecurityEvent(
            req, 
            'xss_attempt', 
            'high', 
            `检测到XSS尝试: ${currentPath} = ${value}`
          );
          throw new AppError('检测到恶意脚本', 400, 'MALICIOUS_SCRIPT');
        }
      } else if (typeof value === 'object' && value !== null) {
        checkForXSS(value, currentPath);
      }
    }
  };
  
  // 检查请求数据
  if (req.body && Object.keys(req.body).length > 0) {
    checkForXSS(req.body);
  }
  
  if (req.query && Object.keys(req.query).length > 0) {
    checkForXSS(req.query);
  }
  
  next();
};

// 请求大小限制中间件
const requestSizeLimit = (options = {}) => {
  const {
    maxBodySize = '10mb',
    maxUrlLength = 2048,
    maxHeaderSize = 8192
  } = options;
  
  return (req, res, next) => {
    // 检查URL长度
    if (req.url.length > maxUrlLength) {
      operationLogger.logSecurityEvent(req, 'url_too_long', 'medium', `URL长度超限: ${req.url.length}`);
      throw new AppError('请求URL过长', 414, 'URL_TOO_LONG');
    }
    
    // 检查请求头大小
    const headerSize = JSON.stringify(req.headers).length;
    if (headerSize > maxHeaderSize) {
      operationLogger.logSecurityEvent(req, 'headers_too_large', 'medium', `请求头过大: ${headerSize}`);
      throw new AppError('请求头过大', 431, 'HEADERS_TOO_LARGE');
    }
    
    next();
  };
};

// 敏感路径保护中间件
const sensitivePathProtection = (sensitivePaths = []) => {
  const defaultSensitivePaths = [
    '/admin', '/api/admin', '/config', '/logs',
    '/.env', '/package.json', '/node_modules'
  ];
  
  const allSensitivePaths = [...defaultSensitivePaths, ...sensitivePaths];
  
  return (req, res, next) => {
    const requestPath = req.path.toLowerCase();
    
    const isSensitive = allSensitivePaths.some(path => 
      requestPath.startsWith(path.toLowerCase())
    );
    
    if (isSensitive) {
      // 检查是否有适当的权限
      if (!req.user || req.user.role !== 'admin') {
        operationLogger.logSecurityEvent(
          req, 
          'sensitive_path_access', 
          'high', 
          `未授权访问敏感路径: ${req.path}`
        );
        throw new AppError('访问被拒绝', 403, 'ACCESS_DENIED');
      }
    }
    
    next();
  };
};

// 暴力破解保护中间件
const bruteForcePrevention = (options = {}) => {
  const {
    maxAttempts = 5,
    windowMs = 15 * 60 * 1000, // 15分钟
    blockDuration = 60 * 60 * 1000 // 1小时
  } = options;
  
  const attempts = new Map();
  const blocked = new Map();
  
  return (req, res, next) => {
    const identifier = req.ip;
    const now = Date.now();
    
    // 检查是否被阻止
    if (blocked.has(identifier)) {
      const blockTime = blocked.get(identifier);
      if (now - blockTime < blockDuration) {
        operationLogger.logSecurityEvent(req, 'brute_force_blocked', 'high', `IP被阻止: ${identifier}`);
        throw new AppError('访问被暂时阻止', 429, 'TEMPORARILY_BLOCKED');
      } else {
        blocked.delete(identifier);
        attempts.delete(identifier);
      }
    }
    
    // 保存原始的res.end方法
    const originalEnd = res.end;
    
    res.end = function(chunk, encoding) {
      // 检查是否为失败的认证尝试
      if (res.statusCode === 401 || res.statusCode === 403) {
        const userAttempts = attempts.get(identifier) || [];
        const recentAttempts = userAttempts.filter(time => now - time < windowMs);
        recentAttempts.push(now);
        
        attempts.set(identifier, recentAttempts);
        
        if (recentAttempts.length >= maxAttempts) {
          blocked.set(identifier, now);
          operationLogger.logSecurityEvent(
            req, 
            'brute_force_detected', 
            'critical', 
            `检测到暴力破解，IP已被阻止: ${identifier}`
          );
        }
      } else if (res.statusCode < 400) {
        // 成功请求，清除尝试记录
        attempts.delete(identifier);
      }
      
      originalEnd.call(this, chunk, encoding);
    };
    
    next();
  };
};

// 综合安全中间件
const comprehensiveSecurity = (options = {}) => {
  const middlewares = [
    createSecurityMiddleware(options.helmet),
    requestSizeLimit(options.requestSize),
    sqlInjectionDetector,
    xssDetector
  ];
  
  if (options.userAgent !== false) {
    middlewares.push(userAgentFilter(options.userAgent));
  }
  
  if (options.ipWhitelist && options.ipWhitelist.length > 0) {
    middlewares.push(createIPWhitelist(options.ipWhitelist));
  }
  
  if (options.sensitivePaths) {
    middlewares.push(sensitivePathProtection(options.sensitivePaths));
  }
  
  if (options.bruteForce !== false) {
    middlewares.push(bruteForcePrevention(options.bruteForce));
  }
  
  return middlewares;
};

module.exports = {
  createSecurityMiddleware,
  createIPWhitelist,
  userAgentFilter,
  sqlInjectionDetector,
  xssDetector,
  requestSizeLimit,
  sensitivePathProtection,
  bruteForcePrevention,
  comprehensiveSecurity
};
