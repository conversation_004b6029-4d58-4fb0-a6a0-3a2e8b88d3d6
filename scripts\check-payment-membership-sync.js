const mysql = require('mysql2/promise');
const logger = require('../src/utils/logger');

// 加载环境变量
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'video_user',
  password: process.env.DB_PASSWORD || 'secure_password',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

async function checkPaymentMembershipSync() {
  let connection;
  
  try {
    console.log('🔍 开始检查支付订单与会员状态同步问题...\n');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功\n');
    
    // 1. 查找已支付的会员订单
    console.log('📋 查询已支付的会员订单...');
    const [paidOrders] = await connection.execute(`
      SELECT 
        o.id,
        o.user_id,
        o.order_no,
        o.type,
        o.target_id,
        o.payment_status,
        o.payment_time,
        o.final_amount,
        o.created_at,
        u.username,
        u.email,
        u.role as user_role,
        mp.name as plan_name,
        mp.duration_days
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN membership_plans mp ON o.target_id = mp.id
      WHERE o.type = 'membership' 
        AND o.payment_status = 'paid'
      ORDER BY o.payment_time DESC
    `);
    
    console.log(`找到 ${paidOrders.length} 个已支付的会员订单\n`);
    
    if (paidOrders.length === 0) {
      console.log('❌ 没有找到已支付的会员订单');
      return;
    }
    
    // 2. 检查每个已支付订单对应的会员状态
    console.log('🔍 检查会员状态同步情况...\n');
    
    const issues = [];
    
    for (const order of paidOrders) {
      console.log(`检查订单: ${order.order_no} (用户: ${order.username || order.email})`);
      
      // 查询用户当前会员状态
      const [memberships] = await connection.execute(`
        SELECT 
          m.*,
          mp.name as plan_name,
          mp.duration_days
        FROM memberships m
        LEFT JOIN membership_plans mp ON m.plan_id = mp.id
        WHERE m.user_id = ?
        ORDER BY m.created_at DESC
      `, [order.user_id]);
      
      // 查询用户当前有效会员
      const [activeMemberships] = await connection.execute(`
        SELECT 
          m.*,
          mp.name as plan_name
        FROM memberships m
        LEFT JOIN membership_plans mp ON m.plan_id = mp.id
        WHERE m.user_id = ? 
          AND m.status = 'active'
          AND m.end_date > NOW()
        ORDER BY m.end_date DESC
        LIMIT 1
      `, [order.user_id]);
      
      const issue = {
        order,
        memberships,
        activeMembership: activeMemberships[0] || null,
        problems: []
      };
      
      // 检查问题
      if (memberships.length === 0) {
        issue.problems.push('❌ 已支付订单但没有任何会员记录');
      } else {
        // 检查是否有对应的会员记录
        const matchingMembership = memberships.find(m => 
          m.plan_id === order.target_id && 
          m.transaction_id === order.transaction_id
        );
        
        if (!matchingMembership) {
          issue.problems.push('❌ 没有找到与订单匹配的会员记录');
        }
        
        if (!activeMemberships[0]) {
          issue.problems.push('⚠️ 用户当前没有有效会员');
        }
        
        // 检查用户角色
        if (order.user_role !== 'admin' && order.user_role !== 'member') {
          issue.problems.push(`⚠️ 用户角色异常: ${order.user_role} (应该是 member)`);
        }
      }
      
      if (issue.problems.length > 0) {
        issues.push(issue);
        console.log(`  ❌ 发现问题: ${issue.problems.join(', ')}`);
      } else {
        console.log(`  ✅ 状态正常`);
      }
      
      console.log('');
    }
    
    // 3. 输出问题汇总
    console.log('\n📊 问题汇总:');
    console.log(`总订单数: ${paidOrders.length}`);
    console.log(`有问题的订单数: ${issues.length}`);
    console.log(`正常订单数: ${paidOrders.length - issues.length}\n`);
    
    if (issues.length > 0) {
      console.log('🚨 详细问题列表:\n');
      
      issues.forEach((issue, index) => {
        console.log(`${index + 1}. 订单号: ${issue.order.order_no}`);
        console.log(`   用户: ${issue.order.username || issue.order.email} (ID: ${issue.order.user_id})`);
        console.log(`   计划: ${issue.order.plan_name}`);
        console.log(`   支付时间: ${issue.order.payment_time}`);
        console.log(`   金额: ¥${issue.order.final_amount}`);
        console.log(`   用户角色: ${issue.order.user_role}`);
        console.log(`   问题:`);
        issue.problems.forEach(problem => {
          console.log(`     ${problem}`);
        });
        
        if (issue.memberships.length > 0) {
          console.log(`   会员记录 (${issue.memberships.length}条):`);
          issue.memberships.forEach((m, i) => {
            console.log(`     ${i + 1}. 状态: ${m.status}, 计划: ${m.plan_name}, 开始: ${m.start_date}, 结束: ${m.end_date}`);
          });
        }
        
        if (issue.activeMembership) {
          console.log(`   当前有效会员: ${issue.activeMembership.plan_name} (结束: ${issue.activeMembership.end_date})`);
        }
        
        console.log('');
      });
      
      // 4. 提供修复建议
      console.log('🔧 修复建议:\n');
      
      const noMembershipRecords = issues.filter(i => i.memberships.length === 0);
      const wrongRole = issues.filter(i => i.order.user_role !== 'admin' && i.order.user_role !== 'member');
      const noActiveMembership = issues.filter(i => !i.activeMembership);
      
      if (noMembershipRecords.length > 0) {
        console.log(`1. ${noMembershipRecords.length} 个订单缺少会员记录，需要手动创建会员记录`);
      }
      
      if (wrongRole.length > 0) {
        console.log(`2. ${wrongRole.length} 个用户角色需要更新为 'member'`);
      }
      
      if (noActiveMembership.length > 0) {
        console.log(`3. ${noActiveMembership.length} 个用户没有有效会员，可能需要检查会员过期时间`);
      }
      
    } else {
      console.log('✅ 所有已支付订单的会员状态都正常！');
    }
    
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
    logger.error('检查支付会员同步失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkPaymentMembershipSync()
    .then(() => {
      console.log('\n✅ 检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { checkPaymentMembershipSync };
