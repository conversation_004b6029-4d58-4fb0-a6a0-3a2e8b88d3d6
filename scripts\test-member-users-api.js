#!/usr/bin/env node

/**
 * 测试会员用户API
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const memberController = require('../src/modules/member/controllers/memberController');
const logger = require('../src/utils/logger');

async function testMemberUsersAPI() {
  try {
    logger.info('测试会员用户API...');
    
    // 模拟请求对象
    const req = {
      query: {
        page: 1,
        pageSize: 10,
        search: '',
        status: 'all'
      }
    };
    
    // 模拟响应对象
    let responseData = null;
    const res = {
      json: (data) => {
        responseData = data;
        logger.info('API响应:');
        logger.info(JSON.stringify(data, null, 2));
      }
    };

    // 调用API方法
    await memberController.getAdminMemberUsers(req, res);

    if (!responseData) {
      logger.warn('没有收到API响应');
    }
    
  } catch (error) {
    logger.error('测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testMemberUsersAPI()
    .then(() => {
      logger.info('测试完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testMemberUsersAPI };
