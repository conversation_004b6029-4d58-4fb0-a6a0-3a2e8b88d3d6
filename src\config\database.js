const mysql = require('mysql2/promise');
const redis = require('redis');
const logger = require('../utils/logger');

// MySQL连接配置
const mysqlConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4',
  timezone: 'Z',
  dateStrings: false,
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
  queueLimit: 0,
  // MySQL2支持的配置选项
  waitForConnections: true,
  maxIdle: 10, // 最大空闲连接数
  idleTimeout: 300000, // 空闲连接超时时间（毫秒）
  // 移除了 acquireTimeout 和 timeout，这些选项在 MySQL2 中不被支持
  enableKeepAlive: true,
  keepAliveInitialDelay: 0
};

// 创建MySQL连接池
const mysqlPool = mysql.createPool(mysqlConfig);

// MySQL连接池事件监听
mysqlPool.on('connection', (connection) => {
  logger.debug(`MySQL新连接建立: ${connection.threadId}`);
});

mysqlPool.on('acquire', (connection) => {
  logger.debug(`MySQL连接获取: ${connection.threadId}`);
});

mysqlPool.on('release', (connection) => {
  logger.debug(`MySQL连接释放: ${connection.threadId}`);
});

mysqlPool.on('enqueue', () => {
  logger.debug('MySQL连接请求排队');
});

// Redis连接配置
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB) || 0,
  connectTimeout: 10000,
  lazyConnect: true,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: 3,
  retryDelayOnClusterDown: 300,
  enableOfflineQueue: false,
  family: 4, // 4 (IPv4) or 6 (IPv6)
  keepAlive: true,
  dropBufferSupport: false
};

// 创建Redis客户端
const redisClient = redis.createClient(redisConfig);

// Redis连接事件处理
redisClient.on('connect', () => {
  logger.info('Redis连接成功');
});

redisClient.on('error', (err) => {
  logger.error('Redis连接错误:', err);
});

redisClient.on('ready', () => {
  logger.info('Redis准备就绪');
});

redisClient.on('end', () => {
  logger.warn('Redis连接断开');
});

// 初始化Redis连接
const initRedis = async () => {
  try {
    await redisClient.connect();
    logger.info('Redis初始化完成');
  } catch (error) {
    logger.error('Redis初始化失败:', error);
  }
};

// 测试MySQL连接
const testMysqlConnection = async () => {
  try {
    const connection = await mysqlPool.getConnection();
    await connection.ping();
    connection.release();
    logger.info('MySQL连接测试成功');
    return true;
  } catch (error) {
    logger.error('MySQL连接测试失败:', error);
    return false;
  }
};

// 测试Redis连接
const testRedisConnection = async () => {
  try {
    await redisClient.ping();
    logger.info('Redis连接测试成功');
    return true;
  } catch (error) {
    logger.error('Redis连接测试失败:', error);
    return false;
  }
};

// 数据库初始化
const initDatabase = async () => {
  logger.info('开始初始化数据库连接...');

  // 测试MySQL连接
  const mysqlOk = await testMysqlConnection();
  if (!mysqlOk) {
    throw new Error('MySQL连接失败');
  }

  // 初始化Redis
  await initRedis();

  // 测试Redis连接
  const redisOk = await testRedisConnection();
  if (!redisOk) {
    logger.warn('Redis连接失败，将在无缓存模式下运行');
  }

  // 启动健康监控
  try {
    const { dbHealthMonitor } = require('../utils/dbHealth');
    dbHealthMonitor.startPeriodicHealthCheck();
    logger.info('数据库健康监控已启动');
  } catch (error) {
    logger.warn('启动数据库健康监控失败:', error.message);
  }

  logger.info('数据库初始化完成');
};

// 优雅关闭数据库连接
const closeDatabase = async () => {
  try {
    await mysqlPool.end();
    await redisClient.quit();
    logger.info('数据库连接已关闭');
  } catch (error) {
    logger.error('关闭数据库连接时出错:', error);
  }
};

module.exports = {
  mysql: mysqlPool,
  redis: redisClient,
  initDatabase,
  closeDatabase,
  testMysqlConnection,
  testRedisConnection
};
