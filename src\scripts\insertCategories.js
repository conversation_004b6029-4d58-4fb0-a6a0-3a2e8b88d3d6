// 加载环境变量
require('dotenv').config();

const mysql = require('mysql2/promise');
const logger = require('../utils/logger');

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'video_platform',
  charset: 'utf8mb4'
};

// 调试信息
console.log('数据库配置:');
console.log(`  主机: ${dbConfig.host}`);
console.log(`  端口: ${dbConfig.port}`);
console.log(`  用户: ${dbConfig.user}`);
console.log(`  密码: ${dbConfig.password ? '***已设置***' : '***未设置***'}`);
console.log(`  数据库: ${dbConfig.database}`);

// 默认分类数据
const defaultCategories = [
  { name: '电影', slug: 'movies', description: '电影视频分类' },
  { name: '电视剧', slug: 'tv-series', description: '电视剧视频分类' },
  { name: '纪录片', slug: 'documentaries', description: '纪录片视频分类' },
  { name: '动漫', slug: 'anime', description: '动漫视频分类' },
  { name: '综艺', slug: 'variety', description: '综艺节目分类' },
  { name: '教育', slug: 'education', description: '教育视频分类' },
  { name: '音乐', slug: 'music', description: '音乐视频分类' },
  { name: '游戏', slug: 'gaming', description: '游戏视频分类' },
  { name: '科技', slug: 'technology', description: '科技视频分类' },
  { name: '生活', slug: 'lifestyle', description: '生活视频分类' }
];

async function insertCategories() {
  let connection;
  
  try {
    logger.info('开始插入默认分类...');
    
    // 创建数据库连接
    connection = await mysql.createConnection(dbConfig);
    logger.info('数据库连接成功');
    
    // 检查现有分类
    const [existingCategories] = await connection.execute(
      'SELECT id, name, slug FROM categories WHERE status = "active"'
    );
    
    logger.info(`当前数据库中有 ${existingCategories.length} 个分类`);
    
    if (existingCategories.length > 0) {
      logger.info('现有分类:');
      existingCategories.forEach(cat => {
        logger.info(`  - ${cat.name} (${cat.slug})`);
      });
    }
    
    // 插入新分类
    let insertedCount = 0;
    let skippedCount = 0;
    
    for (const category of defaultCategories) {
      try {
        // 检查分类是否已存在
        const [existing] = await connection.execute(
          'SELECT id FROM categories WHERE slug = ? OR name = ?',
          [category.slug, category.name]
        );
        
        if (existing.length > 0) {
          logger.info(`分类 "${category.name}" 已存在，跳过`);
          skippedCount++;
          continue;
        }
        
        // 插入新分类
        const [result] = await connection.execute(
          'INSERT INTO categories (name, slug, description, status, sort_order) VALUES (?, ?, ?, ?, ?)',
          [category.name, category.slug, category.description, 'active', insertedCount]
        );
        
        logger.info(`✅ 成功插入分类: ${category.name} (ID: ${result.insertId})`);
        insertedCount++;
        
      } catch (error) {
        logger.error(`插入分类 "${category.name}" 失败:`, error.message);
      }
    }
    
    // 最终检查
    const [finalCategories] = await connection.execute(
      'SELECT id, name, slug, status FROM categories ORDER BY sort_order, name'
    );
    
    logger.info('\n=== 分类插入完成 ===');
    logger.info(`新插入: ${insertedCount} 个分类`);
    logger.info(`跳过: ${skippedCount} 个分类`);
    logger.info(`总计: ${finalCategories.length} 个分类`);
    
    logger.info('\n当前所有分类:');
    finalCategories.forEach((cat, index) => {
      logger.info(`  ${index + 1}. ${cat.name} (${cat.slug}) - ${cat.status}`);
    });
    
    return true;
    
  } catch (error) {
    logger.error('插入分类失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      logger.info('数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  insertCategories()
    .then(() => {
      logger.info('分类插入脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('分类插入脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { insertCategories };
