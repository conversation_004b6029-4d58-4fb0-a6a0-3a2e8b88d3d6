const express = require('express');
const router = express.Router();

// 导入控制器
const PlaylistController = require('../controllers/playlistController');
const PlayHistoryController = require('../controllers/playHistoryController');

// 导入中间件
const authMiddleware = require('../middleware/auth');
const { 
  validateCreatePlaylist,
  validateUpdatePlaylist,
  validateAddVideo,
  validateAddMultipleVideos,
  validateReorderItems,
  validateDuplicatePlaylist,
  validateRecordPlayHistory,
  validateQueryParams,
  validatePlaylistId,
  validateVideoId,
  validatePlaylistAndVideo
} = require('../middleware/playlistValidation');
const { asyncHandler } = require('../middleware/errorHandler');

// 播放列表路由

/**
 * @route GET /api/playlists
 * @desc 获取用户的所有播放列表
 * @access Private
 */
router.get('/', 
  authMiddleware.verifyToken,
  validateQueryParams,
  asyncHandler(PlaylistController.getUserPlaylists)
);

/**
 * @route GET /api/playlists/stats
 * @desc 获取播放列表统计信息
 * @access Private
 */
router.get('/stats',
  authMiddleware.verifyToken,
  asyncHandler(PlaylistController.getPlaylistStats)
);

/**
 * @route GET /api/playlists/:playlistId
 * @desc 获取播放列表详情
 * @access Public (公开播放列表) / Private (私有播放列表)
 */
router.get('/:playlistId',
  authMiddleware.optionalAuth, // 可选认证，支持游客访问公开播放列表
  validatePlaylistId,
  asyncHandler(PlaylistController.getPlaylistById)
);

/**
 * @route POST /api/playlists
 * @desc 创建新播放列表
 * @access Private
 */
router.post('/',
  authMiddleware.verifyToken,
  validateCreatePlaylist,
  asyncHandler(PlaylistController.createPlaylist)
);

/**
 * @route PUT /api/playlists/:playlistId
 * @desc 更新播放列表信息
 * @access Private
 */
router.put('/:playlistId',
  authMiddleware.verifyToken,
  validatePlaylistId,
  validateUpdatePlaylist,
  asyncHandler(PlaylistController.updatePlaylist)
);

/**
 * @route DELETE /api/playlists/:playlistId
 * @desc 删除播放列表
 * @access Private
 */
router.delete('/:playlistId',
  authMiddleware.verifyToken,
  validatePlaylistId,
  asyncHandler(PlaylistController.deletePlaylist)
);

/**
 * @route POST /api/playlists/:playlistId/items
 * @desc 添加视频到播放列表
 * @access Private
 */
router.post('/:playlistId/items',
  authMiddleware.verifyToken,
  validatePlaylistId,
  validateAddVideo,
  asyncHandler(PlaylistController.addVideoToPlaylist)
);

/**
 * @route DELETE /api/playlists/:playlistId/items/:videoId
 * @desc 从播放列表移除视频
 * @access Private
 */
router.delete('/:playlistId/items/:videoId',
  authMiddleware.verifyToken,
  validatePlaylistAndVideo,
  asyncHandler(PlaylistController.removeVideoFromPlaylist)
);

/**
 * @route POST /api/playlists/:playlistId/items/batch
 * @desc 批量添加视频到播放列表
 * @access Private
 */
router.post('/:playlistId/items/batch',
  authMiddleware.verifyToken,
  validatePlaylistId,
  validateAddMultipleVideos,
  asyncHandler(PlaylistController.addMultipleVideos)
);

/**
 * @route PUT /api/playlists/:playlistId/items/reorder
 * @desc 重新排序播放列表项目
 * @access Private
 */
router.put('/:playlistId/items/reorder',
  authMiddleware.verifyToken,
  validatePlaylistId,
  validateReorderItems,
  asyncHandler(PlaylistController.reorderPlaylistItems)
);

/**
 * @route POST /api/playlists/:playlistId/duplicate
 * @desc 复制播放列表
 * @access Private
 */
router.post('/:playlistId/duplicate',
  authMiddleware.verifyToken,
  validatePlaylistId,
  validateDuplicatePlaylist,
  asyncHandler(PlaylistController.duplicatePlaylist)
);

// 播放历史路由

/**
 * @route POST /api/playlists/history/record
 * @desc 记录播放历史
 * @access Private
 */
router.post('/history/record',
  authMiddleware.verifyToken,
  validateRecordPlayHistory,
  asyncHandler(PlayHistoryController.recordPlayHistory)
);

/**
 * @route GET /api/playlists/history
 * @desc 获取播放历史
 * @access Private
 */
router.get('/history',
  authMiddleware.verifyToken,
  validateQueryParams,
  asyncHandler(PlayHistoryController.getPlayHistory)
);

/**
 * @route GET /api/playlists/history/recent
 * @desc 获取最近播放的视频
 * @access Private
 */
router.get('/history/recent',
  authMiddleware.verifyToken,
  validateQueryParams,
  asyncHandler(PlayHistoryController.getRecentlyPlayed)
);

/**
 * @route GET /api/playlists/history/stats
 * @desc 获取播放统计信息
 * @access Private
 */
router.get('/history/stats',
  authMiddleware.verifyToken,
  asyncHandler(PlayHistoryController.getPlayStats)
);

/**
 * @route GET /api/playlists/history/trends
 * @desc 获取播放趋势数据
 * @access Private
 */
router.get('/history/trends',
  authMiddleware.verifyToken,
  validateQueryParams,
  asyncHandler(PlayHistoryController.getPlayTrends)
);

/**
 * @route GET /api/playlists/history/progress/:videoId
 * @desc 获取视频观看进度（断点续播）
 * @access Private
 */
router.get('/history/progress/:videoId',
  authMiddleware.verifyToken,
  validateVideoId,
  asyncHandler(PlayHistoryController.getWatchProgress)
);

/**
 * @route DELETE /api/playlists/history
 * @desc 清除播放历史
 * @access Private
 */
router.delete('/history',
  authMiddleware.verifyToken,
  asyncHandler(PlayHistoryController.clearPlayHistory)
);

module.exports = router;
