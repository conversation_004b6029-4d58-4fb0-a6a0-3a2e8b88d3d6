import React from 'react';
import { 
  Play, 
  Pause, 
  Ski<PERSON><PERSON>or<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  List,
  Shuffle,
  Repeat,
  Repeat1
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Playlist, PlaylistItem } from '@/types/playlist';

interface PlaylistDisplayProps {
  playlist: Playlist | null;
  currentItem: PlaylistItem | null;
  onPlayPause: () => void;
  onPlayNext: () => void;
  onPlayPrevious: () => void;
  onOpenPlaylist: () => void;
  onSetPlayMode: (mode: 'sequence' | 'loop' | 'random') => void;
  className?: string;
}

const PlaylistDisplay: React.FC<PlaylistDisplayProps> = ({
  playlist,
  currentItem,
  onPlayPause,
  onPlayNext,
  onPlayPrevious,
  onOpenPlaylist,
  onSetPlayMode,
  className = '',
}) => {
  // 只要播放列表存在且有项目就显示，不强制要求currentItem
  if (!playlist || !playlist.items || playlist.items.length === 0) return null;

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getPlayModeIcon = () => {
    switch (playlist.playMode) {
      case 'loop':
        return <Repeat className="h-4 w-4 text-blue-500" />;
      case 'random':
        return <Shuffle className="h-4 w-4 text-orange-500" />;
      case 'sequence':
      default:
        return <Repeat1 className="h-4 w-4 text-green-500" />;
    }
  };

  const getPlayModeText = () => {
    switch (playlist.playMode) {
      case 'loop':
        return '循环播放';
      case 'random':
        return '随机播放';
      case 'sequence':
      default:
        return '顺序播放';
    }
  };

  const handlePlayModeToggle = () => {
    const modes: Array<'sequence' | 'loop' | 'random'> = ['sequence', 'loop', 'random'];
    const currentIndex = modes.indexOf(playlist.playMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    onSetPlayMode(nextMode);
  };

  const currentProgress = playlist.items.length > 0 
    ? ((playlist.currentIndex + 1) / playlist.items.length) * 100 
    : 0;

  return (
    <Card className={`p-4 bg-background/95 backdrop-blur-sm border-primary/20 ${className}`}>
      <div className="space-y-3">
        {/* 当前播放信息 */}
        <div className="flex items-center gap-3">
          {/* 缩略图 - 检查整个currentItem对象 */}
          {currentItem && (
            <div className="flex-shrink-0 w-12 h-8 bg-muted rounded overflow-hidden">
              <img 
                src={currentItem.thumbnail} 
                alt={currentItem.title}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* 播放信息 - 使用可选链 ?. */}
          <div className="flex-1 min-w-0">
            <div className="font-medium truncate text-primary">
              {currentItem?.title || playlist.items[0]?.title || '播放列表'}
            </div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Badge variant="outline" className="text-xs">
                {(currentItem?.mediaType || playlist.items[0]?.mediaType) === 'audio' ? '音频' : '视频'}
              </Badge>
              <span>{formatDuration(currentItem?.duration || playlist.items[0]?.duration || 0)}</span>
              <span>•</span>
              <span>{playlist.currentIndex + 1} / {playlist.items.length}</span>
            </div>
          </div>

          {/* 播放列表按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onOpenPlaylist}
            className="h-8 w-8 p-0"
            title="打开播放列表"
          >
            <List className="h-4 w-4" />
          </Button>
        </div>

        {/* 进度条 */}
        <div className="space-y-1">
          <Progress value={currentProgress} className="h-1" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>播放进度</span>
            <span>{Math.round(currentProgress)}%</span>
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onPlayPrevious}
              disabled={playlist.items.length === 0}
              className="h-8 w-8 p-0"
              title="上一个"
            >
              <SkipBack className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onPlayPause}
              disabled={playlist.items.length === 0}
              className="h-8 w-8 p-0"
              title={playlist.isPlaying ? "暂停" : "播放"}
            >
              {playlist.isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onPlayNext}
              disabled={playlist.items.length === 0}
              className="h-8 w-8 p-0"
              title="下一个"
            >
              <SkipForward className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePlayModeToggle}
              className="flex items-center gap-1 text-xs h-8 border"
              title={`当前模式: ${getPlayModeText()}，点击切换`}
            >
              {getPlayModeIcon()}
              <span className="font-medium">{getPlayModeText()}</span>
            </Button>

            {playlist.isTemporary && (
              <Badge variant="secondary" className="text-xs">
                临时
              </Badge>
            )}
          </div>
        </div>

        {/* 播放列表名称 */}
        <div className="text-center">
          <div className="text-sm font-medium text-muted-foreground">
            {playlist.name}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default PlaylistDisplay;
