import React, { useState, useEffect, useCallback } from 'react';
import { CreditCard, DollarSign, TrendingUp, Users, Eye, Download, Search, ChevronLeft, ChevronRight, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { adminApi } from '@/services/adminApi';
import { useDebounce } from '@/hooks/useDebounce';

// Define a generic API response type to help TypeScript
interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
}

const AdminPayments = () => {
  const [stats, setStats] = useState({
    todayRevenue: 0,
    monthlyRevenue: 0,
    totalOrders: 0,
    paidUsers: 0
  });
  const [orders, setOrders] = useState([]);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0, totalPages: 1 });
  const [loading, setLoading] = useState(true);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [orderDetail, setOrderDetail] = useState(null);
  const [actionType, setActionType] = useState(''); // 'complete' or 'cancel'
  const [actionReason, setActionReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const { toast } = useToast();

  const debouncedKeyword = useDebounce(searchKeyword, 500);

  const fetchPaymentData = useCallback(async (page = 1, keyword = '', status = 'all') => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize: pagination.pageSize,
        keyword: keyword || undefined,
        status: status === 'all' ? undefined : status,
      };
      
      const response = await adminApi.getPayments(params);
      const apiResponse = response.data as ApiResponse;

      if (apiResponse && apiResponse.success) {
        setStats(apiResponse.data?.stats || { todayRevenue: 0, monthlyRevenue: 0, totalOrders: 0, paidUsers: 0 });
        setOrders(apiResponse.data?.orders || []);
        setPagination(apiResponse.data?.pagination || { page: 1, pageSize: 10, total: 0, totalPages: 1 });
      } else {
        toast({ title: "获取数据失败", description: apiResponse?.message || "未知错误", variant: "destructive" });
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || "无法连接到服务器";
      toast({ title: "请求错误", description: errorMessage, variant: "destructive" });
    } finally {
      setLoading(false);
    }
  }, [pagination.pageSize, toast]);

  useEffect(() => {
    fetchPaymentData(1, debouncedKeyword, selectedStatus);
  }, [debouncedKeyword, selectedStatus, fetchPaymentData]);
  
  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= pagination.totalPages) {
      fetchPaymentData(newPage, debouncedKeyword, selectedStatus);
    }
  };

  const handleExportData = () => {
    toast({
      title: "导出成功",
      description: "支付数据已导出到Excel文件",
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: { [key: string]: { text: string; className: string } } = {
      paid: { text: '已支付', className: 'bg-green-100 text-green-800' },
      pending: { text: '待支付', className: 'bg-yellow-100 text-yellow-800' },
      failed: { text: '支付失败', className: 'bg-red-100 text-red-800' },
      refunded: { text: '已退款', className: 'bg-gray-100 text-gray-800' },
      expired: { text: '已过期', className: 'bg-gray-100 text-gray-800' },
      cancelled: { text: '已取消', className: 'bg-gray-200 text-gray-500' }
    };
    const config = statusConfig[status] || statusConfig.pending;
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
        {config.text}
      </span>
    );
  };

  const getTypeText = (type) => {
    const typeMap = {
      membership: '会员订阅',
      video: '视频购买',
      recharge: '账户充值',
      BALANCE_RECHARGE: '余额充值申请'
    };
    return typeMap[type] || type || '未知类型';
  };

  // 处理订单操作
  const handleOrderAction = (order: any, action: string) => {
    setSelectedOrder(order);
    setActionType(action);
    setActionReason('');
    setShowConfirmDialog(true);
  };

  // 确认订单操作
  const confirmOrderAction = async () => {
    if (!selectedOrder || !actionType) return;

    setActionLoading(true);
    try {
      const status = actionType === 'complete' ? 'paid' : 'cancelled';
      const response = await adminApi.updateOrderStatus((selectedOrder as any).order_no, status, actionReason);

      // axios 返回的数据在 response.data 中
      if (response.data.success) {
        toast({ title: "操作成功", description: `订单已${actionType === 'complete' ? '完成' : '关闭'}` });

        // 刷新数据
        fetchPaymentData(pagination.page, debouncedKeyword, selectedStatus);
        setShowConfirmDialog(false);
      } else {
        toast({ title: "操作失败", description: response.data.message || "未知错误", variant: "destructive" });
      }
    } catch (error: any) {
      toast({ title: "操作失败", description: error.response?.data?.message || error.message || "网络错误", variant: "destructive" });
    } finally {
      setActionLoading(false);
    }
  };

  // 查看订单详情
  const handleViewDetail = async (order: any) => {
    try {
      const response = await adminApi.getOrderDetail(order.order_no);
      if (response.data.success && response.data.data && response.data.data.order) {
        // 确保订单数据完整性
        const orderData = response.data.data.order;

        // 对关键字段进行安全处理
        const safeOrderDetail = {
          ...orderData,
          order_no: orderData.order_no || order.order_no || '-',
          type: orderData.type || 'unknown',
          payment_status: orderData.payment_status || 'pending',
          amount: orderData.amount || 0,
          final_amount: orderData.final_amount || 0,
          payment_method: orderData.payment_method || '-',
          username: orderData.username || '-',
          nickname: orderData.nickname || '-',
          email: orderData.email || '-',
          phone: orderData.phone || '-',
          target_name: orderData.target_name || '-',
          description: orderData.description || '-'
        };

        setOrderDetail(safeOrderDetail);
        setShowDetailDialog(true);
      } else {
        toast({ title: "获取详情失败", description: response.data.message || "订单数据不完整", variant: "destructive" });
      }
    } catch (error: any) {
      toast({ title: "获取详情失败", description: error.response?.data?.message || error.message || "网络错误", variant: "destructive" });
    }
  };

  // 判断订单是否可以操作
  const canOperateOrder = (order: any) => {
    return order.payment_status === 'pending' || order.payment_status === 'failed';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">订单管理</h1>
        <div className="flex space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="搜索订单号或用户..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <select 
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border rounded-md bg-white dark:bg-gray-800"
          >
            <option value="all">所有状态</option>
            <option value="paid">已支付</option>
            <option value="pending">待支付</option>
            <option value="failed">支付失败</option>
            <option value="refunded">已退款</option>
            <option value="expired">已过期</option>
            <option value="cancelled">已取消</option>
          </select>
          <Button onClick={handleExportData}>
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">今日收入</p>
                <p className="text-2xl font-bold">${Number(stats.todayRevenue || 0).toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">本月收入</p>
                <p className="text-2xl font-bold">${Number(stats.monthlyRevenue || 0).toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-8 w-8 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">总订单数</p>
                <p className="text-2xl font-bold">{stats.totalOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">付费用户</p>
                <p className="text-2xl font-bold">{stats.paidUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 订单列表 */}
      <Card>
        <CardHeader>
          <CardTitle>订单列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>订单号</TableHead>
                <TableHead>用户</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>支付方式</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>支付时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow><TableCell colSpan={9} className="text-center">加载中...</TableCell></TableRow>
              ) : orders.length > 0 ? (
                orders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-mono">{order.order_no}</TableCell>
                    <TableCell>{order.username || 'N/A'}</TableCell>
                    <TableCell>{getTypeText(order.type)}</TableCell>
                  <TableCell className="font-bold text-green-600">
                      ${Number(order.amount || 0).toFixed(2)}
                  </TableCell>
                    <TableCell>{getStatusBadge(order.payment_status)}</TableCell>
                    <TableCell>{order.payment_method || '-'}</TableCell>
                  <TableCell className="text-sm text-gray-600">
                      {order.created_at ? new Date(order.created_at).toLocaleString() : '-'}
                  </TableCell>
                  <TableCell className="text-sm text-gray-600">
                      {order.payment_time ? new Date(order.payment_time).toLocaleString() : '-'}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetail(order)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {canOperateOrder(order) && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-green-600 hover:text-green-700"
                            onClick={() => handleOrderAction(order, 'complete')}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => handleOrderAction(order, 'cancel')}
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
                ))
              ) : (
                <TableRow><TableCell colSpan={9} className="text-center">没有找到订单</TableCell></TableRow>
              )}
            </TableBody>
          </Table>
          <div className="flex items-center justify-end space-x-2 py-4">
            <span className="text-sm text-gray-600">
              第 {pagination.page} / {pagination.totalPages} 页 (共 {pagination.total} 条)
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.totalPages}
            >
              下一页
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 确认操作对话框 */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {actionType === 'complete' ? '确认完成订单' : '确认关闭订单'}
            </DialogTitle>
            <DialogDescription>
              {actionType === 'complete'
                ? '此操作将标记订单为已支付状态，请确认订单确实已收到款项。'
                : '此操作将关闭订单，用户将无法继续支付。'
              }
            </DialogDescription>
          </DialogHeader>

          {selectedOrder && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-3 rounded">
                <p><strong>订单号：</strong>{selectedOrder.order_no || '-'}</p>
                <p><strong>用户：</strong>{selectedOrder.username || '-'}</p>
                <p><strong>金额：</strong>¥{Number(selectedOrder.amount || 0).toFixed(2)}</p>
                <p><strong>类型：</strong>{getTypeText(selectedOrder.type)}</p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  操作原因 {actionType === 'complete' ? '(可选)' : '(必填)'}
                </label>
                <Textarea
                  value={actionReason}
                  onChange={(e) => setActionReason(e.target.value)}
                  placeholder={actionType === 'complete'
                    ? '请输入完成订单的原因...'
                    : '请输入关闭订单的原因...'
                  }
                  rows={3}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={actionLoading}
            >
              取消
            </Button>
            <Button
              onClick={confirmOrderAction}
              disabled={actionLoading || (actionType === 'cancel' && !actionReason.trim())}
              className={actionType === 'complete' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
            >
              {actionLoading ? '处理中...' : (actionType === 'complete' ? '确认完成' : '确认关闭')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 订单详情对话框 */}
      <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>订单详情</DialogTitle>
          </DialogHeader>

          {orderDetail && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">基本信息</h4>
                  <div className="space-y-1 text-sm">
                    <p><strong>订单号：</strong>{orderDetail.order_no || '-'}</p>
                    <p><strong>类型：</strong>{getTypeText(orderDetail.type || 'unknown')}</p>
                    <div className="flex items-center gap-2">
                      <strong>状态：</strong>
                      {getStatusBadge(orderDetail.payment_status || 'pending')}
                    </div>
                    <p><strong>金额：</strong>¥{(() => {
                      try {
                        return Number(orderDetail.amount || 0).toFixed(2);
                      } catch (error) {
                        return '0.00';
                      }
                    })()}</p>
                    <p><strong>实付金额：</strong>¥{(() => {
                      try {
                        return Number(orderDetail.final_amount || 0).toFixed(2);
                      } catch (error) {
                        return '0.00';
                      }
                    })()}</p>
                    <p><strong>支付方式：</strong>{orderDetail.payment_method || '-'}</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">用户信息</h4>
                  <div className="space-y-1 text-sm">
                    <p><strong>用户名：</strong>{orderDetail.username || '-'}</p>
                    <p><strong>昵称：</strong>{orderDetail.nickname || '-'}</p>
                    <p><strong>邮箱：</strong>{orderDetail.email || '-'}</p>
                    <p><strong>手机：</strong>{orderDetail.phone || '-'}</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">时间信息</h4>
                <div className="space-y-1 text-sm">
                  <p><strong>创建时间：</strong>{(() => {
                    try {
                      return orderDetail.created_at ? new Date(orderDetail.created_at).toLocaleString() : '-';
                    } catch (error) {
                      return orderDetail.created_at || '-';
                    }
                  })()}</p>
                  <p><strong>支付时间：</strong>{(() => {
                    try {
                      return orderDetail.payment_time ? new Date(orderDetail.payment_time).toLocaleString() : '-';
                    } catch (error) {
                      return orderDetail.payment_time || '-';
                    }
                  })()}</p>
                  <p><strong>过期时间：</strong>{(() => {
                    try {
                      return orderDetail.expires_at ? new Date(orderDetail.expires_at).toLocaleString() : '-';
                    } catch (error) {
                      return orderDetail.expires_at || '-';
                    }
                  })()}</p>
                </div>
              </div>

              {orderDetail.target_name && (
                <div>
                  <h4 className="font-medium mb-2">购买内容</h4>
                  <p className="text-sm">{orderDetail.target_name}</p>
                </div>
              )}

              {orderDetail.description && (
                <div>
                  <h4 className="font-medium mb-2">订单描述</h4>
                  <p className="text-sm">{orderDetail.description}</p>
                </div>
              )}


            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setShowDetailDialog(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminPayments;
