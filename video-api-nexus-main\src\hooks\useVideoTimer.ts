import { useState, useEffect, useCallback, useRef } from 'react';

export interface VideoTimerState {
  isActive: boolean;
  isPaused: boolean;
  timeRemaining: number; // 剩余时间（秒）
  totalTime: number; // 总时间（秒）
  progress: number; // 进度百分比 (0-100)
}

export interface VideoTimerActions {
  startTimer: (minutes: number) => void;
  pauseTimer: () => void;
  resumeTimer: () => void;
  stopTimer: () => void;
  resetTimer: () => void;
}

export interface UseVideoTimerOptions {
  onTimerEnd?: () => void;
  onTimerTick?: (timeRemaining: number) => void;
  playlistMode?: boolean; // 是否为播放列表模式
  onPlaylistTimerEnd?: () => void; // 播放列表模式下定时器结束的回调
}

export const useVideoTimer = (options: UseVideoTimerOptions = {}) => {
  const { onTimerEnd, onTimerTick, playlistMode = false, onPlaylistTimerEnd } = options;
  
  const [state, setState] = useState<VideoTimerState>({
    isActive: false,
    isPaused: false,
    timeRemaining: 0,
    totalTime: 0,
    progress: 0,
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  const clearTimer = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // 开始定时器
  const startTimer = useCallback((minutes: number) => {
    const totalSeconds = minutes * 60;
    
    setState({
      isActive: true,
      isPaused: false,
      timeRemaining: totalSeconds,
      totalTime: totalSeconds,
      progress: 100,
    });

    clearTimer();
    
    intervalRef.current = setInterval(() => {
      setState(prevState => {
        const newTimeRemaining = prevState.timeRemaining - 1;
        const newProgress = prevState.totalTime > 0 
          ? (newTimeRemaining / prevState.totalTime) * 100 
          : 0;

        // 时间到达时触发回调
        if (newTimeRemaining <= 0) {
          clearInterval(intervalRef.current!);
          intervalRef.current = null;

          // 根据模式触发不同的回调
          if (playlistMode && onPlaylistTimerEnd) {
            onPlaylistTimerEnd();
          } else if (onTimerEnd) {
            onTimerEnd();
          }

          return {
            ...prevState,
            isActive: false,
            isPaused: false,
            timeRemaining: 0,
            progress: 0,
          };
        }

        // 每秒触发回调
        if (onTimerTick) {
          onTimerTick(newTimeRemaining);
        }

        return {
          ...prevState,
          timeRemaining: newTimeRemaining,
          progress: Math.max(0, newProgress),
        };
      });
    }, 1000);
  }, [clearTimer, onTimerEnd, onTimerTick]);

  // 暂停定时器
  const pauseTimer = useCallback(() => {
    clearTimer();
    setState(prevState => ({
      ...prevState,
      isPaused: true,
    }));
  }, [clearTimer]);

  // 恢复定时器
  const resumeTimer = useCallback(() => {
    if (!state.isActive || !state.isPaused || state.timeRemaining <= 0) {
      return;
    }

    setState(prevState => ({
      ...prevState,
      isPaused: false,
    }));

    intervalRef.current = setInterval(() => {
      setState(prevState => {
        const newTimeRemaining = prevState.timeRemaining - 1;
        const newProgress = prevState.totalTime > 0 
          ? (newTimeRemaining / prevState.totalTime) * 100 
          : 0;

        if (newTimeRemaining <= 0) {
          clearInterval(intervalRef.current!);
          intervalRef.current = null;

          // 根据模式触发不同的回调
          if (playlistMode && onPlaylistTimerEnd) {
            onPlaylistTimerEnd();
          } else if (onTimerEnd) {
            onTimerEnd();
          }

          return {
            ...prevState,
            isActive: false,
            isPaused: false,
            timeRemaining: 0,
            progress: 0,
          };
        }

        if (onTimerTick) {
          onTimerTick(newTimeRemaining);
        }

        return {
          ...prevState,
          timeRemaining: newTimeRemaining,
          progress: Math.max(0, newProgress),
        };
      });
    }, 1000);
  }, [state.isActive, state.isPaused, state.timeRemaining, onTimerEnd, onTimerTick]);

  // 停止定时器
  const stopTimer = useCallback(() => {
    clearTimer();
    setState({
      isActive: false,
      isPaused: false,
      timeRemaining: 0,
      totalTime: 0,
      progress: 0,
    });
  }, [clearTimer]);

  // 重置定时器
  const resetTimer = useCallback(() => {
    clearTimer();
    setState(prevState => ({
      ...prevState,
      isActive: false,
      isPaused: false,
      timeRemaining: prevState.totalTime,
      progress: 100,
    }));
  }, [clearTimer]);

  // 格式化时间显示
  const formatTime = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // 清理副作用
  useEffect(() => {
    return () => {
      clearTimer();
    };
  }, [clearTimer]);

  const actions: VideoTimerActions = {
    startTimer,
    pauseTimer,
    resumeTimer,
    stopTimer,
    resetTimer,
  };

  return {
    ...state,
    ...actions,
    formatTime,
  };
};
