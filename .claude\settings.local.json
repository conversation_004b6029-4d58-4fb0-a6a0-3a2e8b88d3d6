{"permissions": {"allow": ["<PERSON><PERSON>(curl -H \"Authorization: <PERSON><PERSON> YOUR_TOKEN\" http://localhost:3000/api/admin/creem-plans)", "Bash(cd \"D:\\beifen\\kaifa\\md\")", "Bash(curl -X POST http://localhost:3000/api/payment/orders -H \"Content-Type: application/json\" -H \"Authorization: Bearer test\" -d '{\"\"type\"\":\"\"membership\"\",\"\"targetId\"\":1,\"\"paymentMethod\"\":\"\"balance\"\",\"\"description\"\":\"\"test\"\"}')", "Bash(curl -X POST http://localhost:3000/api/payment/creem/create-order -H \"Content-Type: application/json\" -H \"Authorization: Bearer test\" -d '{\"\"planId\"\":\"\"test\"\"}')", "Bash(curl -X GET http://localhost:3000/api/creem/plans)", "Bash(curl -X POST http://localhost:3000/api/payment/creem/webhook -H \"Content-Type: application/json\" -d '{\"\"test\"\": \"\"webhook\"\"}')", "Bash(curl -X POST http://localhost:3000/api/payment/creem/webhook -H \"Content-Type: application/json\" -d '{\"\"type\"\": \"\"test\"\", \"\"id\"\": \"\"test_123\"\", \"\"data\"\": {}}')", "Bash(curl -X POST http://localhost:3000/api/payment/creem/webhook -H \"Content-Type: application/json\" -d '{\"\"test\"\": \"\"data\"\"}')", "Bash(curl -X POST http://localhost:3000/api/payment/creem/webhook -H \"Content-Type: application/json\" -d '{\n  \"\"id\"\": \"\"evt_6plzPkPb7lZ2hfEbD6Sk4g\"\",\n  \"\"eventType\"\": \"\"checkout.completed\"\",\n  \"\"created_at\"\": 1753464869659,\n  \"\"object\"\": {\n    \"\"id\"\": \"\"ch_3QIwYtNrrV1INcUHEJhb8x\"\",\n    \"\"object\"\": \"\"checkout\"\",\n    \"\"order\"\": {\n      \"\"object\"\": \"\"order\"\",\n      \"\"id\"\": \"\"ord_7LrhSleHiyxI8e0WrOeC82\"\",\n      \"\"customer\"\": \"\"cust_45ktbCJEOEyi5Rdr4b1wwu\"\",\n      \"\"product\"\": \"\"prod_3ldL8MJAUqA2jl9VxnduIr\"\",\n      \"\"amount\"\": 1800,\n      \"\"currency\"\": \"\"USD\"\",\n      \"\"status\"\": \"\"paid\"\"\n    },\n    \"\"product\"\": {\n      \"\"id\"\": \"\"prod_3ldL8MJAUqA2jl9VxnduIr\"\",\n      \"\"name\"\": \"\"普通会员sssssssssd\"\"\n    },\n    \"\"customer\"\": {\n      \"\"email\"\": \"\"<EMAIL>\"\"\n    },\n    \"\"metadata\"\": {\n      \"\"user_id\"\": \"\"2\"\",\n      \"\"plan_name\"\": \"\"普通会员sssssssssd\"\"\n    }\n  }\n}')", "Bash(where ngrok)", "Bash(npm run typecheck)", "Bash(rm \"D:\\beifen\\kaifa\\md\\src\\database\\migrations\\20250725_fix_system_configs.sql\")", "Bash(node -e \"\nconst Video = require(''./src/database/models/Video'');\nasync function checkVideoStatus() {\n  try {\n    const videos = await Video.query(''SELECT id, title, status, user_id, created_at FROM videos ORDER BY id DESC LIMIT 5'');\n    console.log(''最近的5个视频状态:'');\n    console.table(videos);\n  } catch (error) {\n    console.error(''查询失败:'', error);\n  }\n  process.exit(0);\n}\ncheckVideoStatus();\n\")", "Bash(npm start)", "Bash(node -e \"\nconst connectionManager = require(''./src/database/ConnectionManager'');\n\nasync function testWithdrawals() {\n  try {\n    console.log(''=== 检查提现表是否存在 ==='');\n    const tables = await connectionManager.executeQuery(''SHOW TABLES LIKE \"\"withdrawals\"\"'');\n    console.log(''提现表查询结果:'', tables);\n    \n    if (tables.length > 0) {\n      console.log(''\\n=== 检查提现记录数量 ==='');\n      const count = await connectionManager.executeQuery(''SELECT COUNT(*) as total FROM withdrawals'');\n      console.log(''提现记录总数:'', count[0].total);\n      \n      console.log(''\\n=== 查看最近的提现记录 ==='');\n      const recent = await connectionManager.executeQuery(`\n        SELECT w.*, u.username, u.email \n        FROM withdrawals w \n        LEFT JOIN users u ON w.user_id = u.id \n        ORDER BY w.requested_at DESC \n        LIMIT 5\n      `);\n      console.log(''最近的提现记录:'', JSON.stringify(recent, null, 2));\n      \n      console.log(''\\n=== 检查提现记录按状态分组 ==='');\n      const statusCount = await connectionManager.executeQuery(''SELECT status, COUNT(*) as count FROM withdrawals GROUP BY status'');\n      console.log(''按状态分组:'', statusCount);\n    } else {\n      console.log(''提现表不存在'');\n    }\n    \n  } catch (error) {\n    console.error(''查询错误:'', error);\n  } finally {\n    process.exit(0);\n  }\n}\n\ntestWithdrawals();\n\")", "Bash(mkdir -p \"D:\\beifen\\kaifa\\md\\src\\modules\\follow\\controllers\" \"D:\\beifen\\kaifa\\md\\src\\modules\\follow\\services\" \"D:\\beifen\\kaifa\\md\\src\\modules\\follow\\models\")", "Bash(npm run db:reset)", "Bash(npm run db:check)", "Bash(node test-follow-functionality.js)", "Bash(find . -name \"*.tsx\" -exec grep -l \"VideoPlayerWithFeatures\\|video.*播放\\|视频播放\" {} ;)", "Bash(grep -A 10 -B 5 \"GET /api/video/1\" 对话文件.md)", "Bash(find . -name \"*.tsx\" -exec grep -l \"余额\\|balance\\|DropdownMenu\" {} ;)", "Bash(node test-follow-system.js)"], "deny": []}}