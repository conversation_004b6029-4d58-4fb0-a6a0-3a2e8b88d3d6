const PlaylistService = require('../services/playlistService');
const PlaylistUtils = require('../utils/playlistUtils');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

class PlayHistoryController {
  // 记录播放历史
  static async recordPlayHistory(req, res, next) {
    try {
      const userId = req.user.id;
      const { videoId, watchDuration, videoDuration, completed } = req.body;

      // 验证视频ID
      if (!videoId || isNaN(videoId)) {
        throw new AppError('无效的视频ID', 400, 'INVALID_VIDEO_ID');
      }

      // 验证时长数据
      if (watchDuration < 0 || videoDuration < 0) {
        throw new AppError('时长不能为负数', 400, 'INVALID_DURATION');
      }

      if (watchDuration > videoDuration && videoDuration > 0) {
        throw new AppError('观看时长不能超过视频总时长', 400, 'INVALID_WATCH_DURATION');
      }

      await PlaylistService.recordPlayHistory(userId, videoId, watchDuration, videoDuration, completed);

      logger.info(`用户 ${userId} 记录播放历史: 视频 ${videoId}, 观看时长 ${watchDuration}s`);

      res.json({
        success: true,
        message: '记录播放历史成功',
        data: {
          videoId,
          watchDuration,
          videoDuration,
          completed,
          completionRate: videoDuration > 0 ? Math.round((watchDuration / videoDuration) * 100) : 0
        }
      });
    } catch (error) {
      logger.error('记录播放历史失败:', error);
      next(error);
    }
  }

  // 获取播放历史
  static async getPlayHistory(req, res, next) {
    try {
      const userId = req.user.id;
      const { limit = 50, offset = 0, videoId } = req.query;

      // 如果指定了videoId，获取特定视频的播放历史
      let history;
      if (videoId) {
        // 这里可以扩展为获取特定视频的播放历史
        history = await PlaylistService.getPlayHistory(userId, limit, offset);
        history = history.filter(item => item.video_id == videoId);
      } else {
        history = await PlaylistService.getPlayHistory(userId, limit, offset);
      }

      const formattedHistory = history.map(PlaylistUtils.formatPlayHistoryData);

      res.json({
        success: true,
        message: '获取播放历史成功',
        data: {
          history: formattedHistory,
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset),
            total: formattedHistory.length,
            hasMore: formattedHistory.length === parseInt(limit)
          }
        }
      });
    } catch (error) {
      logger.error('获取播放历史失败:', error);
      next(error);
    }
  }

  // 获取最近播放的视频
  static async getRecentlyPlayed(req, res, next) {
    try {
      const userId = req.user.id;
      const { limit = 10 } = req.query;

      const recentVideos = await PlaylistService.getRecentlyPlayed(userId, parseInt(limit));
      
      const formattedVideos = recentVideos.map(video => ({
        videoId: video.video_id,
        title: video.title,
        thumbnail: video.thumbnail_url,
        duration: video.duration,
        durationFormatted: PlaylistUtils.formatDuration(video.duration),
        mediaType: video.media_type || 'video',
        playedAt: video.played_at,
        playedAtFormatted: new Date(video.played_at).toLocaleString('zh-CN')
      }));

      res.json({
        success: true,
        message: '获取最近播放成功',
        data: {
          videos: formattedVideos,
          total: formattedVideos.length
        }
      });
    } catch (error) {
      logger.error('获取最近播放失败:', error);
      next(error);
    }
  }

  // 清除播放历史
  static async clearPlayHistory(req, res, next) {
    try {
      const userId = req.user.id;
      const { videoId, clearAll } = req.body;

      let result;
      if (clearAll) {
        // 清除所有播放历史
        result = await PlaylistService.clearPlayHistory(userId);
        logger.info(`用户 ${userId} 清除所有播放历史`);
      } else if (videoId) {
        // 清除特定视频的播放历史
        result = await PlaylistService.clearPlayHistory(userId, videoId);
        logger.info(`用户 ${userId} 清除视频 ${videoId} 的播放历史`);
      } else {
        throw new AppError('请指定要清除的范围', 400, 'INVALID_CLEAR_SCOPE');
      }

      res.json({
        success: true,
        message: clearAll ? '清除所有播放历史成功' : '清除指定视频播放历史成功',
        data: {
          deletedCount: result.deletedCount,
          videoId: videoId || null,
          clearAll: Boolean(clearAll)
        }
      });
    } catch (error) {
      logger.error('清除播放历史失败:', error);
      next(error);
    }
  }

  // 获取播放统计信息
  static async getPlayStats(req, res, next) {
    try {
      const userId = req.user.id;

      const stats = await PlaylistService.getPlayStats(userId);

      // 格式化统计数据
      const formattedStats = {
        totalPlays: stats.total_plays || 0,
        uniqueVideos: stats.unique_videos || 0,
        totalWatchTime: stats.total_watch_time || 0,
        totalWatchTimeFormatted: PlaylistUtils.formatDuration(stats.total_watch_time || 0),
        completedVideos: stats.completed_videos || 0,
        avgWatchTime: stats.avg_watch_time || 0,
        avgWatchTimeFormatted: PlaylistUtils.formatDuration(stats.avg_watch_time || 0),
        completionRate: stats.total_plays > 0 
          ? Math.round((stats.completed_videos / stats.total_plays) * 100) 
          : 0
      };

      res.json({
        success: true,
        message: '获取播放统计成功',
        data: {
          stats: formattedStats
        }
      });
    } catch (error) {
      logger.error('获取播放统计失败:', error);
      next(error);
    }
  }

  // 获取播放趋势数据（按日期统计）
  static async getPlayTrends(req, res, next) {
    try {
      const userId = req.user.id;
      const { days = 7 } = req.query;

      // 这里可以扩展为更复杂的趋势分析
      // 目前先返回基础的播放历史数据
      const history = await PlaylistService.getPlayHistory(userId, 1000, 0);
      
      // 按日期分组统计
      const trends = {};
      const now = new Date();
      
      // 初始化最近N天的数据
      for (let i = 0; i < parseInt(days); i++) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const dateKey = date.toISOString().split('T')[0];
        trends[dateKey] = {
          date: dateKey,
          plays: 0,
          watchTime: 0,
          uniqueVideos: new Set()
        };
      }

      // 统计历史数据
      history.forEach(item => {
        const dateKey = new Date(item.played_at).toISOString().split('T')[0];
        if (trends[dateKey]) {
          trends[dateKey].plays++;
          trends[dateKey].watchTime += item.watch_duration || 0;
          trends[dateKey].uniqueVideos.add(item.video_id);
        }
      });

      // 转换为数组并格式化
      const trendData = Object.values(trends)
        .map(trend => ({
          date: trend.date,
          plays: trend.plays,
          watchTime: trend.watchTime,
          watchTimeFormatted: PlaylistUtils.formatDuration(trend.watchTime),
          uniqueVideos: trend.uniqueVideos.size
        }))
        .sort((a, b) => new Date(a.date) - new Date(b.date));

      res.json({
        success: true,
        message: '获取播放趋势成功',
        data: {
          trends: trendData,
          period: {
            days: parseInt(days),
            startDate: trendData[0]?.date,
            endDate: trendData[trendData.length - 1]?.date
          }
        }
      });
    } catch (error) {
      logger.error('获取播放趋势失败:', error);
      next(error);
    }
  }

  // 获取观看进度（用于断点续播）
  static async getWatchProgress(req, res, next) {
    try {
      const userId = req.user.id;
      const { videoId } = req.params;

      if (!videoId || isNaN(videoId)) {
        throw new AppError('无效的视频ID', 400, 'INVALID_VIDEO_ID');
      }

      // 获取该视频的最新播放记录
      const history = await PlaylistService.getPlayHistory(userId, 1, 0);
      const videoHistory = history.find(item => item.video_id == videoId);

      if (!videoHistory) {
        return res.json({
          success: true,
          message: '未找到观看记录',
          data: {
            videoId: parseInt(videoId),
            hasProgress: false,
            watchDuration: 0,
            videoDuration: 0,
            completionRate: 0,
            completed: false
          }
        });
      }

      const formattedProgress = {
        videoId: parseInt(videoId),
        hasProgress: true,
        watchDuration: videoHistory.watch_duration || 0,
        videoDuration: videoHistory.video_duration || 0,
        completionRate: videoHistory.video_duration > 0 
          ? Math.round((videoHistory.watch_duration / videoHistory.video_duration) * 100) 
          : 0,
        completed: Boolean(videoHistory.completed),
        lastPlayedAt: videoHistory.played_at,
        canResume: !videoHistory.completed && videoHistory.watch_duration > 30 // 30秒以上才显示续播
      };

      res.json({
        success: true,
        message: '获取观看进度成功',
        data: formattedProgress
      });
    } catch (error) {
      logger.error('获取观看进度失败:', error);
      next(error);
    }
  }
}

module.exports = PlayHistoryController;
