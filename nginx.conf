events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 基本设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 500M;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 上游服务器
    upstream video_platform_api {
        server app:3000;
        keepalive 32;
    }

    # HTTP服务器 - 重定向到HTTPS
    server {
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS服务器
    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/certificate.crt;
        ssl_certificate_key /etc/nginx/ssl/private.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin";

        # API代理
        location /api/ {
            proxy_pass http://video_platform_api;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            proxy_send_timeout 300s;
        }

        # 健康检查
        location /health {
            proxy_pass http://video_platform_api;
            access_log off;
        }

        # 静态文件服务
        # 注意：请根据实际项目部署路径调整alias路径
        # 示例：如果项目在 /home/<USER>/video-platform，则使用 /home/<USER>/video-platform/uploads/
        location /uploads/ {
            alias /path/to/your/project/uploads/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";

            # 视频和音频文件特殊处理
            location ~* \.(mp4|webm|ogg|avi|mov|mkv|mp3|wav|flac|aac|m4a)$ {
                add_header Accept-Ranges bytes;
                add_header Cache-Control "public, max-age=31536000";

                # 支持视频流
                mp4;
                mp4_buffer_size 1m;
                mp4_max_buffer_size 5m;
            }

            # 图片文件
            location ~* \.(jpg|jpeg|png|gif|webp|svg)$ {
                add_header Cache-Control "public, max-age=2592000";
            }
        }

        # 文档页面
        location /docs {
            proxy_pass http://video_platform_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 默认页面
        location / {
            return 200 '{"message":"Video Platform API","status":"running","timestamp":"$time_iso8601"}';
            add_header Content-Type application/json;
        }

        # 错误页面
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            return 404 '{"error":"Not Found","code":404}';
            add_header Content-Type application/json;
        }
        
        location = /50x.html {
            return 500 '{"error":"Internal Server Error","code":500}';
            add_header Content-Type application/json;
        }
    }

    # 开发环境HTTP服务器
    server {
        listen 8080;
        server_name localhost;

        # API代理
        location /api/ {
            proxy_pass http://video_platform_api;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # 静态文件（开发环境）
        # 注意：请根据实际项目部署路径调整alias路径
        location /uploads/ {
            alias /path/to/your/project/uploads/;
            expires 1h;
        }

        # 健康检查
        location /health {
            proxy_pass http://video_platform_api;
            access_log off;
        }

        # 默认页面
        location / {
            return 200 '{"message":"Video Platform API - Development","status":"running"}';
            add_header Content-Type application/json;
        }
    }
}
