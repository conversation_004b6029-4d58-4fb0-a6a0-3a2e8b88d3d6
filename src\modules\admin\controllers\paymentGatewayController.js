const { asyncHandler, AppError } = require('../../../middleware/errorHandler');
const PaymentGatewayService = require('../services/paymentGatewayService');

class PaymentGatewayController {
  constructor() {
    this.paymentGatewayService = new PaymentGatewayService();
  }

  /**
   * @desc    获取所有支付通道
   * @route   GET /api/admin/payment-gateways
   * @access  Private (Admin)
   */
  getGateways = asyncHandler(async (req, res) => {
    const gateways = await this.paymentGatewayService.getAllGateways();
    res.json({ success: true, data: gateways });
  });

  /**
   * @desc    更新支付通道
   * @route   PUT /api/admin/payment-gateways/:id
   * @access  Private (Admin)
   */
  updateGateway = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { enabled, config } = req.body;

    // 数据验证
    if (enabled === undefined && !config) {
      throw new AppError('至少需要提供 enabled 或 config 字段进行更新', 400);
    }
    
    if (config && typeof config !== 'object') {
      throw new AppError('config 字段必须是一个对象', 400);
    }

    const updatedGateway = await this.paymentGatewayService.updateGateway(id, { enabled, config });

    res.json({ success: true, message: '支付通道更新成功', data: updatedGateway });
  });
}

module.exports = new PaymentGatewayController(); 