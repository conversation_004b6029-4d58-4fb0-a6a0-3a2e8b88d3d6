const { logUtils } = require('../utils/advancedLogger');
const errorMonitor = require('../services/errorMonitorService');

// 性能监控中间件
const performanceMonitor = (req, res, next) => {
  const startTime = Date.now();
  const startHrTime = process.hrtime();
  
  // 记录请求开始
  req.startTime = startTime;
  req.startHrTime = startHrTime;
  
  // 监听响应结束
  res.on('finish', () => {
    const endTime = Date.now();
    const diff = process.hrtime(startHrTime);
    const responseTime = diff[0] * 1000 + diff[1] * 1e-6; // 转换为毫秒
    
    // 记录访问日志
    logUtils.logAccess(req, res, responseTime);
    
    // 记录性能指标
    const performanceData = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      userId: req.user?.id || null
    };
    
    logUtils.logPerformance('api_request', responseTime, performanceData);
    
    // 检查慢请求
    if (responseTime > 5000) { // 5秒
      logUtils.logSystem('slow_request', 'warn', {
        ...performanceData,
        threshold: '5000ms'
      });
    }
    
    // 检查错误响应
    if (res.statusCode >= 400) {
      const errorData = {
        ...performanceData,
        errorType: res.statusCode >= 500 ? 'server_error' : 'client_error'
      };
      
      if (res.statusCode >= 500) {
        // 服务器错误，记录到错误监控
        const error = new Error(`HTTP ${res.statusCode}: ${req.method} ${req.originalUrl}`);
        error.code = `HTTP_${res.statusCode}`;
        errorMonitor.recordError(error, errorData);
      }
      
      logUtils.logSystem('error_response', 'warn', errorData);
    }
  });
  
  next();
};

// 数据库性能监控
const databaseMonitor = {
  // 监控数据库查询
  monitorQuery: (operation, table) => {
    const startTime = Date.now();
    
    return {
      end: (error = null) => {
        const duration = Date.now() - startTime;
        logUtils.logDatabase(operation, table, duration, error);
        
        // 检查慢查询
        if (duration > 1000) { // 1秒
          logUtils.logSystem('slow_query', 'warn', {
            operation,
            table,
            duration: `${duration}ms`,
            threshold: '1000ms'
          });
        }
        
        return duration;
      }
    };
  }
};

// 缓存性能监控
const cacheMonitor = {
  // 监控缓存操作
  monitorCache: (operation, key) => {
    const startTime = Date.now();
    
    return {
      end: (hit = null) => {
        const duration = Date.now() - startTime;
        logUtils.logCache(operation, key, hit, duration);
        
        // 记录缓存命中率
        if (operation === 'get') {
          logUtils.logPerformance('cache_operation', duration, {
            operation,
            key,
            hit: hit ? 'hit' : 'miss'
          });
        }
        
        return duration;
      }
    };
  }
};

// 内存监控
const memoryMonitor = {
  // 获取内存使用情况
  getMemoryUsage: () => {
    const usage = process.memoryUsage();
    return {
      rss: Math.round(usage.rss / 1024 / 1024), // MB
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024),
      heapUsagePercent: Math.round((usage.heapUsed / usage.heapTotal) * 100)
    };
  },
  
  // 检查内存使用
  checkMemoryUsage: () => {
    const usage = memoryMonitor.getMemoryUsage();
    
    logUtils.logPerformance('memory_check', 0, usage);
    
    // 内存使用率超过90%时告警
    if (usage.heapUsagePercent > 90) {
      logUtils.logSystem('high_memory_usage', 'warn', {
        ...usage,
        threshold: '90%'
      });
    }
    
    return usage;
  }
};

// CPU监控
const cpuMonitor = {
  // 获取CPU使用情况
  getCpuUsage: () => {
    const usage = process.cpuUsage();
    return {
      user: usage.user,
      system: usage.system,
      total: usage.user + usage.system
    };
  },
  
  // 监控CPU使用
  monitorCpuUsage: () => {
    const startUsage = process.cpuUsage();
    const startTime = Date.now();
    
    return {
      end: () => {
        const endUsage = process.cpuUsage(startUsage);
        const duration = Date.now() - startTime;
        
        const cpuData = {
          user: endUsage.user,
          system: endUsage.system,
          total: endUsage.user + endUsage.system,
          duration
        };
        
        logUtils.logPerformance('cpu_usage', duration, cpuData);
        
        return cpuData;
      }
    };
  }
};

// 请求统计
const requestStats = {
  stats: {
    total: 0,
    success: 0,
    error: 0,
    avgResponseTime: 0,
    responseTimeSum: 0
  },
  
  // 记录请求
  recordRequest: (responseTime, isError = false) => {
    requestStats.stats.total++;
    requestStats.stats.responseTimeSum += responseTime;
    requestStats.stats.avgResponseTime = requestStats.stats.responseTimeSum / requestStats.stats.total;
    
    if (isError) {
      requestStats.stats.error++;
    } else {
      requestStats.stats.success++;
    }
  },
  
  // 获取统计信息
  getStats: () => {
    const stats = requestStats.stats;
    return {
      ...stats,
      successRate: stats.total > 0 ? (stats.success / stats.total * 100).toFixed(2) : '0.00',
      errorRate: stats.total > 0 ? (stats.error / stats.total * 100).toFixed(2) : '0.00'
    };
  },
  
  // 重置统计
  reset: () => {
    requestStats.stats = {
      total: 0,
      success: 0,
      error: 0,
      avgResponseTime: 0,
      responseTimeSum: 0
    };
  }
};

// 系统健康检查
const healthCheck = {
  // 执行健康检查
  check: async () => {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: memoryMonitor.getMemoryUsage(),
      cpu: cpuMonitor.getCpuUsage(),
      requests: requestStats.getStats(),
      errors: errorMonitor.getErrorStats()
    };
    
    // 判断健康状态
    if (health.memory.heapUsagePercent > 90) {
      health.status = 'degraded';
      health.issues = health.issues || [];
      health.issues.push('High memory usage');
    }
    
    if (health.requests.errorRate > 10) {
      health.status = 'degraded';
      health.issues = health.issues || [];
      health.issues.push('High error rate');
    }
    
    if (health.requests.avgResponseTime > 5000) {
      health.status = 'degraded';
      health.issues = health.issues || [];
      health.issues.push('Slow response time');
    }
    
    logUtils.logSystem('health_check', 'info', health);
    
    return health;
  }
};

// 定期监控任务
const startPeriodicMonitoring = () => {
  // 每分钟检查内存使用
  setInterval(() => {
    memoryMonitor.checkMemoryUsage();
  }, 60000);
  
  // 每5分钟执行健康检查
  setInterval(async () => {
    await healthCheck.check();
  }, 300000);
  
  // 每小时重置请求统计
  setInterval(() => {
    const stats = requestStats.getStats();
    logUtils.logSystem('hourly_stats', 'info', stats);
    requestStats.reset();
  }, 3600000);
};

module.exports = {
  performanceMonitor,
  databaseMonitor,
  cacheMonitor,
  memoryMonitor,
  cpuMonitor,
  requestStats,
  healthCheck,
  startPeriodicMonitoring
};
