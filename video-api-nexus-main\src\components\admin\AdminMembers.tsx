import React, { useState, useEffect, useCallback } from 'react';
import { Crown, Users, Calendar, DollarSign, Plus, Edit2, Trash2, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { adminApi } from '@/services/adminApi'; // 导入API服务
import { getMembershipOverview } from '@/lib/api'; // 导入统计API
import { Switch } from '@/components/ui/switch'; // 导入Switch组件
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

// 定义API响应的基础接口
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 定义数据类型
interface Plan {
  id: number;
  name: string;
  price: number;
  original_price?: number;
  discount_until?: string;
  duration_days: number;
  features: string[];
  is_active: boolean;
  total_subscriptions?: number;
  active_subscriptions?: number;
}

interface Member {
  id: number;
  username: string;
  membership_plan: { name: string } | null; // 匹配后端数据结构
  membership_status: string;
  membership_start_date: string | null;
  membership_end_date: string | null;
  auto_renew: boolean;
}

// 为表单创建一个单独的数据接口
interface PlanFormData {
  id?: number;
  name: string;
  price: string;
  original_price?: string;
  discount_until?: Date | null;
  duration_days: string;
  features: string;
  is_active?: boolean;
}

// 统计数据接口
interface MembershipOverview {
  totalMembers: number;
  activeMembers: number;
  monthlyRevenue: string;
  renewalRate: number;
}

const AdminMembers = () => {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 统计数据状态
  const [overview, setOverview] = useState<MembershipOverview>({
    totalMembers: 0,
    activeMembers: 0,
    monthlyRevenue: '0.00',
    renewalRate: 0
  });

  const [searchKeyword, setSearchKeyword] = useState('');
  const [showCreatePlan, setShowCreatePlan] = useState(false);

  // 新增一个状态来独立控制日历 Popover
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // 使用新的、类型安全的状态来处理表单数据
  const [planFormData, setPlanFormData] = useState<PlanFormData | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  const { toast } = useToast();

  const fetchPlans = async () => {
    try {
      const response = await adminApi.getAllPlans();
      const apiResponse = response.data as ApiResponse<{ plans: Plan[] }>;
      
      if (apiResponse.success && Array.isArray(apiResponse.data.plans)) {
        setPlans(apiResponse.data.plans);
      } else {
        throw new Error(apiResponse.message || '获取会员计划失败，或数据格式不正确');
      }
    } catch (err: any) {
       const errorMessage = err.response?.data?.message || err.message || '加载计划数据时发生错误';
       setError(errorMessage);
       toast({
         title: "计划数据加载失败",
         description: errorMessage,
         variant: "destructive"
       });
    }
  };

  const fetchOverview = useCallback(async () => {
    try {
      const response = await getMembershipOverview();
      if (response.data.success) {
        setOverview(response.data.data);
      } else {
        throw new Error(response.data.message || '获取统计数据失败');
      }
    } catch (err: any) {
      console.error('获取统计数据失败:', err);
      toast({
        title: "统计数据加载失败",
        description: err.message,
        variant: "destructive"
      });
    }
  }, []);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        await Promise.all([
          fetchPlans(),
          fetchOverview(),
          (async () => {
            const membersResponse = await adminApi.getAllMemberUsers({ role: 'member' }) as unknown as ApiResponse<{ users: Member[] }>;
            if (membersResponse.success) {
              setMembers(membersResponse.data.users);
            } else {
              throw new Error(membersResponse.message || '获取会员用户失败');
            }
          })()
        ]);
      } catch (err: any) {
        // setError和toast已在各自的fetch函数中处理
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast]);


  const handleOpenCreateModal = () => {
    setIsEditMode(false);
    // 初始化空的表单数据
    setPlanFormData({ name: '', price: '0', original_price: '', discount_until: null, duration_days: '0', features: '' });
    setShowCreatePlan(true);
  };

  const handleOpenEditModal = (plan: Plan) => {
    setIsEditMode(true);
    // 将Plan模型转换为PlanFormData模型
    setPlanFormData({
      ...plan,
      price: String(plan.price),
      original_price: plan.original_price ? String(plan.original_price) : '',
      discount_until: plan.discount_until ? new Date(plan.discount_until) : null,
      duration_days: String(plan.duration_days),
      features: Array.isArray(plan.features) ? plan.features.join('\\n') : '',
    });
    setShowCreatePlan(true);
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setPlanFormData(prev => prev ? { ...prev, [name]: value } : null);
  };


  const handleFormSubmit = async () => {
    if (!planFormData) return;

    // 1. 验证输入
    if (!planFormData.name || !planFormData.price || !planFormData.duration_days) {
      toast({ title: "输入错误", description: "请填写所有必填字段。", variant: "destructive" });
      return;
    }

    // 2. 准备提交的数据，从字符串转换回正确的数字/数组类型
    const price = parseFloat(planFormData.price);
    const duration_days = parseInt(planFormData.duration_days, 10);
    const original_price = planFormData.original_price ? parseFloat(planFormData.original_price) : undefined;

    if (isNaN(price) || isNaN(duration_days)) {
      toast({ title: "输入错误", description: "价格和时长必须是有效的数字。", variant: "destructive" });
      return;
    }
    
    const planData = {
      name: planFormData.name,
      price: price,
      originalPrice: original_price,
      discountUntil: planFormData.discount_until,
      durationDays: duration_days,
      features: planFormData.features.split('\\n').filter(f => f.trim() !== ''),
    };
    
    try {
      let response;
      if (isEditMode) {
        // 更新逻辑
        response = await adminApi.updatePlan(planFormData.id!, planData) as unknown as ApiResponse<{ plan: Plan }>;
      } else {
        // 创建逻辑
        response = await adminApi.createPlan(planData) as unknown as ApiResponse<{ plan: Plan }>;
      }

      if (response.success) {
        toast({
          title: isEditMode ? "更新成功" : "创建成功",
          description: `会员计划 "${response.data.plan.name}" 已成功${isEditMode ? '更新' : '创建'}。`
        });

        await Promise.all([fetchPlans(), fetchOverview()]); // 重新获取计划列表和统计数据
        setShowCreatePlan(false); // 关闭弹窗
        setPlanFormData(null); // 重置表单状态
      } else {
        throw new Error(response.message || '操作失败');
      }
    } catch (err: any) {
    toast({
        title: isEditMode ? "更新失败" : "创建失败",
        description: err.message || '一个未知错误发生了',
        variant: "destructive",
    });
    }
  };

  const handleDeletePlan = async (planId: number) => {
    try {
      const response = await adminApi.deletePlan(planId) as unknown as ApiResponse<null>;
      if (response.success) {
        setPlans(prevPlans => prevPlans.filter(p => p.id !== planId));
    toast({
      title: "删除成功",
      description: `会员计划 #${planId} 已被删除`,
    });
      } else {
        throw new Error(response.message || '删除失败');
      }
    } catch (err: any) {
      toast({
        title: "删除失败",
        description: err.message,
        variant: "destructive"
      });
    }
  };

  // 新增：处理计划状态切换的函数
  const handleTogglePlanStatus = async (plan: Plan) => {
    const newStatus = !plan.is_active;
    try {
      const response = await adminApi.updatePlan(plan.id, { is_active: newStatus }) as unknown as ApiResponse<{ plan: Plan }>;
      if (response.success) {
        toast({
          title: "状态更新成功",
          description: `计划 "${plan.name}" 已被${newStatus ? '启用' : '禁用'}。`,
        });
        // 更新前端状态以立即反映变化
        setPlans(prevPlans => 
          prevPlans.map(p => 
            p.id === plan.id ? { ...p, is_active: newStatus } : p
          )
        );
      } else {
        throw new Error(response.message || '状态更新失败');
      }
    } catch (err: any) {
      toast({
        title: "操作失败",
        description: err.message || '更新计划状态时发生错误。',
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string | null) => {
    if (!status) {
      return null;
    }
    const statusConfig = {
      active: { text: '有效', className: 'bg-green-100 text-green-800' },
      expired: { text: '已过期', className: 'bg-red-100 text-red-800' },
      canceled: { text: '已取消', className: 'bg-gray-100 text-gray-800' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { text: '未知', className: 'bg-gray-100 text-gray-800' };
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
        {config.text}
      </span>
    );
  };

  if (loading) return <div>正在加载...</div>;
  if (error) return <div className="text-red-500">错误: {error}</div>;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">会员管理</h1>
        {/*
          将 Popover 移到 Dialog 外部，作为其兄弟节点。
          这样 Popover 的内容就不会被 Dialog 的堆叠上下文所限制。
        */}
        <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
          <Dialog open={showCreatePlan} onOpenChange={setShowCreatePlan}>
            <DialogTrigger asChild>
              <Button onClick={handleOpenCreateModal}>
                <Plus className="h-4 w-4 mr-2" />
                创建会员计划
              </Button>
            </DialogTrigger>
            <DialogContent 
              className="sm:max-w-[425px]"
              onInteractOutside={(e) => {
                const target = e.target as HTMLElement;
                // 当日历打开时，如果点击的目标在弹出的日历内容中，则阻止Dialog关闭
                if (isCalendarOpen && target.closest('[data-radix-popper-content-wrapper]')) {
                  e.preventDefault();
                }
              }}
            >
              <DialogHeader>
                <DialogTitle>{planFormData?.id ? '编辑会员计划' : '创建新的会员计划'}</DialogTitle>
              </DialogHeader>
              {planFormData && (
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="name" className="text-right">名称</Label>
                    <Input id="name" name="name" value={planFormData.name} onChange={handleFormChange} className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="original_price" className="text-right">原价</Label>
                    <Input id="original_price" name="original_price" value={planFormData.original_price || ''} onChange={handleFormChange} className="col-span-3" placeholder="例如: 99.00 (选填)"/>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="price" className="text-right">{planFormData.original_price ? '折扣价' : '价格'}</Label>
                    <Input id="price" name="price" value={planFormData.price} onChange={handleFormChange} className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="discount_until" className="text-right">折扣截止</Label>
                    {/* PopoverTrigger 保持在原位，但它现在控制外部的 Popover */}
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={`w-full col-span-3 justify-start text-left font-normal ${!planFormData.discount_until && "text-muted-foreground"}`}
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        {planFormData.discount_until ? format(planFormData.discount_until, "PPP") : <span>选择日期</span>}
                      </Button>
                    </PopoverTrigger>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="duration_days" className="text-right">持续天数</Label>
                    <Input id="duration_days" name="duration_days" value={planFormData.duration_days} onChange={handleFormChange} className="col-span-3" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="features" className="text-right">功能特权</Label>
                    <Textarea id="features" name="features" value={planFormData.features} onChange={handleFormChange} className="col-span-3" />
                  </div>
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => { setShowCreatePlan(false); setPlanFormData(null); }}>取消</Button>
                <Button onClick={handleFormSubmit}>{planFormData?.id ? '保存更改' : '创建计划'}</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          {/* PopoverContent 现在是 Dialog 的兄弟节点，拥有自己的堆叠上下文 */}
          <PopoverContent className="w-auto p-0">
            <CalendarComponent
              mode="single"
              selected={planFormData?.discount_until || undefined}
              onSelect={(date: Date | undefined) => {
                setPlanFormData(prev => prev ? { ...prev, discount_until: date || null } : null);
                setIsCalendarOpen(false); // 选择后关闭日历
              }}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Crown className="h-8 w-8 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">总会员数</p>
                <p className="text-2xl font-bold">{overview.totalMembers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">活跃会员</p>
                <p className="text-2xl font-bold">{overview.activeMembers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">月收入</p>
                <p className="text-2xl font-bold">¥{overview.monthlyRevenue}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">续费率</p>
                <p className="text-2xl font-bold">{overview.renewalRate}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 会员计划管理 */}
      <Card>
        <CardHeader>
          <CardTitle>会员计划</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>计划名称</TableHead>
                <TableHead>价格 (元)</TableHead>
                <TableHead>原价 (元)</TableHead>
                <TableHead>折扣价 (元)</TableHead>
                <TableHead>折扣截止</TableHead>
                <TableHead>时长 (天)</TableHead>
                <TableHead>功能特权</TableHead>
                <TableHead>订阅人数</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {plans.map((plan) => (
                <TableRow key={plan.id}>
                  <TableCell>{plan.id}</TableCell>
                  <TableCell className="font-medium">{plan.name}</TableCell>
                  <TableCell>
                    {plan.original_price && plan.original_price > plan.price ? (
                      <div>
                        <span className="line-through text-gray-500">¥{Number(plan.original_price).toFixed(2)}</span>
                        <span className="text-red-600 font-bold ml-2">¥{Number(plan.price).toFixed(2)}</span>
                      </div>
                    ) : (
                      `¥${Number(plan.price).toFixed(2)}`
                    )}
                  </TableCell>
                  <TableCell>{plan.original_price ? `¥${Number(plan.original_price).toFixed(2)}` : '无'}</TableCell>
                  <TableCell>{plan.discount_until ? format(new Date(plan.discount_until), "yyyy-MM-dd") : '无'}</TableCell>
                  <TableCell>{plan.duration_days}</TableCell>
                  <TableCell>
                    <ul className="list-disc list-inside text-sm">
                      {Array.isArray(plan.features) && plan.features.map((feature, index) => (
                        <li key={index}>{feature}</li>
                      ))}
                    </ul>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>总计: {plan.total_subscriptions || 0}</div>
                      <div className="text-green-600">活跃: {plan.active_subscriptions || 0}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={plan.is_active}
                      onCheckedChange={() => handleTogglePlanStatus(plan)}
                      aria-label={`Toggle status for ${plan.name}`}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleOpenEditModal(plan)}>
                        <Edit2 className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDeletePlan(plan.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 会员用户管理 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            会员用户
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索用户..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>用户名</TableHead>
                <TableHead>会员计划</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>开始时间</TableHead>
                <TableHead>结束时间</TableHead>
                <TableHead>自动续费</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {members.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>{member.id}</TableCell>
                  <TableCell>{member.username}</TableCell>
                  <TableCell>{member.membership_plan?.name || '无'}</TableCell>
                  <TableCell>{getStatusBadge(member.membership_status)}</TableCell>
                  <TableCell>{member.membership_start_date ? new Date(member.membership_start_date).toLocaleDateString() : 'N/A'}</TableCell>
                  <TableCell>{member.membership_end_date ? new Date(member.membership_end_date).toLocaleDateString() : 'N/A'}</TableCell>
                  <TableCell>{member.auto_renew ? '是' : '否'}</TableCell>
                  <TableCell>
                    <Button variant="outline" size="sm">
                      管理
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminMembers;
