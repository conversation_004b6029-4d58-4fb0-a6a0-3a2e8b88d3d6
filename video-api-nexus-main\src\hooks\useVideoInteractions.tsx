import { useBatchCheckInteractions } from './queries/useInteractions';

interface VideoInteractionState {
  [videoId: string]: {
    isLiked: boolean;
    isFavorited: boolean;
  };
}

interface UseVideoInteractionsReturn {
  interactionStates: VideoInteractionState;
  loading: boolean;
  error: string | null;
  updateInteractionState: (videoId: string | number, type: 'like' | 'favorite', newState: boolean) => void;
  refreshInteractions: (videoIds: (string | number)[]) => Promise<void>;
}

export const useVideoInteractions = (videoIds: (string | number)[] = []): UseVideoInteractionsReturn => {
  // 使用React Query的批量检查交互状态
  const { data: interactionStates = {}, isLoading: loading, error, refetch } = useBatchCheckInteractions(videoIds);

  // 更新单个视频的交互状态（这个功能现在由React Query的乐观更新处理）
  const updateInteractionState = (videoId: string | number, type: 'like' | 'favorite', newState: boolean) => {
    // 这个函数现在主要用于向后兼容，实际的状态更新由React Query mutations处理
    console.log(`交互状态更新: ${videoId} ${type} ${newState}`);
  };

  // 刷新交互状态
  const refreshInteractions = async (ids: (string | number)[]) => {
    await refetch();
  };

  return {
    interactionStates,
    loading,
    error: error ? '获取交互状态失败' : null,
    updateInteractionState,
    refreshInteractions,
  };
};
