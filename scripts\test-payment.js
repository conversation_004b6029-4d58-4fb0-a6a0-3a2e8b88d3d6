#!/usr/bin/env node

/**
 * 支付模块测试脚本
 * 用于测试支付功能是否正常工作
 */

const axios = require('axios');

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// 测试用户凭据
let authToken = null;

/**
 * 登录获取token
 */
async function login() {
  try {
    console.log('🔐 正在登录...');
    
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (response.data.success) {
      authToken = response.data.data.accessToken;
      console.log('✅ 登录成功');
      return true;
    } else {
      console.log('❌ 登录失败:', response.data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ 登录错误:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * 测试获取支付方式
 */
async function testGetPaymentMethods() {
  try {
    console.log('\n📋 测试获取支付方式...');
    
    const response = await axios.get(`${API_BASE_URL}/api/payment/methods`);
    
    if (response.data.success) {
      console.log('✅ 获取支付方式成功');
      console.log('可用支付方式:', response.data.data.methods);
      return response.data.data.methods;
    } else {
      console.log('❌ 获取支付方式失败:', response.data.message);
      return [];
    }
  } catch (error) {
    console.log('❌ 获取支付方式错误:', error.response?.data?.message || error.message);
    return [];
  }
}

/**
 * 测试创建支付订单
 */
async function testCreatePaymentOrder() {
  try {
    console.log('\n💳 测试创建支付订单...');
    
    const orderData = {
      type: 'recharge',
      amount: 10.00,
      paymentMethod: 'epay',
      description: '测试充值订单'
    };

    const response = await axios.post(`${API_BASE_URL}/api/payment/create-order`, orderData, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.data.success) {
      console.log('✅ 创建支付订单成功');
      console.log('订单信息:', {
        orderNo: response.data.data.order.orderNo,
        amount: response.data.data.order.amount,
        paymentUrl: response.data.data.order.paymentUrl
      });
      return response.data.data.order;
    } else {
      console.log('❌ 创建支付订单失败:', response.data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ 创建支付订单错误:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试查询订单状态
 */
async function testQueryOrderStatus(orderNo) {
  try {
    console.log('\n🔍 测试查询订单状态...');
    
    const response = await axios.get(`${API_BASE_URL}/api/payment/orders/${orderNo}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.data.success) {
      console.log('✅ 查询订单状态成功');
      console.log('订单状态:', {
        orderNo: response.data.data.order.orderNo,
        paymentStatus: response.data.data.order.paymentStatus,
        amount: response.data.data.order.amount
      });
      return response.data.data.order;
    } else {
      console.log('❌ 查询订单状态失败:', response.data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ 查询订单状态错误:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试获取我的订单列表
 */
async function testGetMyOrders() {
  try {
    console.log('\n📜 测试获取我的订单列表...');
    
    const response = await axios.get(`${API_BASE_URL}/api/payment/my-orders?page=1&pageSize=5`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.data.success) {
      console.log('✅ 获取订单列表成功');
      console.log(`订单数量: ${response.data.data.data.length}`);
      console.log('分页信息:', response.data.data.pagination);
      return response.data.data;
    } else {
      console.log('❌ 获取订单列表失败:', response.data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ 获取订单列表错误:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试支付模块状态
 */
async function testPaymentModuleStatus() {
  try {
    console.log('\n🔧 测试支付模块状态...');
    
    const response = await axios.get(`${API_BASE_URL}/api/payment/test`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.data.success) {
      console.log('✅ 支付模块状态正常');
      console.log('模块信息:', {
        module: response.data.data.module,
        availableServices: response.data.data.status.availableServices.length,
        totalServices: response.data.data.status.totalServices
      });
      return response.data.data;
    } else {
      console.log('❌ 支付模块状态异常:', response.data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ 支付模块状态错误:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试支付统计（管理员功能）
 */
async function testPaymentStats() {
  try {
    console.log('\n📊 测试支付统计...');
    
    const response = await axios.get(`${API_BASE_URL}/api/payment/stats`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.data.success) {
      console.log('✅ 获取支付统计成功');
      console.log('统计信息:', response.data.data.stats);
      return response.data.data;
    } else {
      console.log('❌ 获取支付统计失败:', response.data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ 获取支付统计错误:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始支付模块测试\n');
  console.log('='.repeat(50));

  // 1. 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ 登录失败，无法继续测试');
    return;
  }

  // 2. 测试获取支付方式
  const paymentMethods = await testGetPaymentMethods();

  // 3. 测试支付模块状态
  await testPaymentModuleStatus();

  // 4. 测试创建支付订单
  const order = await testCreatePaymentOrder();

  // 5. 如果订单创建成功，测试查询订单状态
  if (order) {
    await testQueryOrderStatus(order.orderNo);
  }

  // 6. 测试获取我的订单列表
  await testGetMyOrders();

  // 7. 测试支付统计（管理员功能）
  await testPaymentStats();

  console.log('\n' + '='.repeat(50));
  console.log('🎉 支付模块测试完成');
  
  // 输出测试总结
  console.log('\n📋 测试总结:');
  console.log(`- 可用支付方式: ${paymentMethods.length}种`);
  console.log(`- 订单创建: ${order ? '成功' : '失败'}`);
  console.log('- 建议: 请检查支付配置和服务状态');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testGetPaymentMethods,
  testCreatePaymentOrder,
  testQueryOrderStatus
};
