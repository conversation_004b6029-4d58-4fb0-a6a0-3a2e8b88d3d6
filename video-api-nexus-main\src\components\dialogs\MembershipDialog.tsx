import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Crown, Calendar, Gift, Settings, X } from 'lucide-react';
import { useMembership } from '@/hooks/useMembership';
import { UpgradeDialog } from '@/components/ui/UpgradeDialog';
import { useTranslation } from 'react-i18next';

interface MembershipDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const MembershipDialog: React.FC<MembershipDialogProps> = ({ open, onOpenChange }) => {
  const { membershipInfo, isMember, membershipLevel } = useMembership();
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false);
  const { t } = useTranslation();

  const handleUpgrade = () => {
    onOpenChange(false);
    setUpgradeDialogOpen(true);
  };

  const formatDate = (dateString: string) => {
    const locale = t('common.locale', { defaultValue: 'zh-CN' });
    return new Date(dateString).toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getRemainingDays = () => {
    if (!membershipInfo?.end_date) return 0;
    const endDate = new Date(membershipInfo.end_date);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const memberBenefits = [
    { icon: '🚫', text: t('membership.adFreeViewing') },
    { icon: '🎬', text: t('membership.hdVideo') },
    { icon: '⬇️', text: t('membership.videoDownload') },
    { icon: '👑', text: t('membership.exclusiveContent') },
    { icon: '🎯', text: t('membership.prioritySupport') }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-blue-500" />
            {t('membership.info')}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* 会员状态卡片 */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Crown className="h-6 w-6 text-blue-500" />
                  <span className="text-lg font-semibold">{t('membership.memberUser')}</span>
                </div>
                <Badge variant="default" className="bg-blue-500">
                  {membershipLevel === 'member' ? t('membership.enabled') : t('membership.freeUser')}
                </Badge>
              </div>
              
              {isMember && membershipInfo && (
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{t('membership.expiryTime')}: {formatDate(membershipInfo.end_date)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Gift className="h-4 w-4" />
                    <span>{t('membership.remainingDays')}: {getRemainingDays()} {t('membership.days')}</span>
                  </div>
                  {membershipInfo.plan_name && (
                    <div className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      <span>{t('membership.currentPlan')}: {membershipInfo.plan_name}</span>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 会员权益 */}
          <Card>
            <CardContent className="pt-6">
              <h4 className="font-medium mb-3">{t('membership.benefits')}</h4>
              <div className="grid grid-cols-1 gap-2">
                {memberBenefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3 text-sm">
                    <span className="text-lg">{benefit.icon}</span>
                    <span className={isMember ? 'text-green-600' : 'text-muted-foreground'}>
                      {benefit.text}
                    </span>
                    {isMember && <span className="text-green-500 text-xs">✓</span>}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => onOpenChange(false)}
            >
              <X className="h-4 w-4 mr-2" />
              {t('common.close')}
            </Button>
            {!isMember && (
              <Button
                className="flex-1 bg-orange-500 hover:bg-orange-600"
                onClick={handleUpgrade}
              >
                <Crown className="h-4 w-4 mr-2" />
                {t('membership.upgradeNow')}
              </Button>
            )}
            {isMember && (
              <Button
                className="flex-1 bg-blue-500 hover:bg-blue-600"
                onClick={handleUpgrade}
              >
                <Crown className="h-4 w-4 mr-2" />
                {t('membership.renewMembership')}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>

      {/* 升级/续费对话框 */}
      <UpgradeDialog
        open={upgradeDialogOpen}
        onOpenChange={setUpgradeDialogOpen}
        isMember={isMember}
      />
    </Dialog>
  );
};

export default MembershipDialog;
