import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Mail, Loader2, CheckCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { sendChangeEmailCode, verifyChangeEmail } from '@/services/api';

interface EmailChangeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentEmail?: string;
}

const EmailChangeDialog: React.FC<EmailChangeDialogProps> = ({ 
  open, 
  onOpenChange, 
  currentEmail = '' 
}) => {
  const [step, setStep] = useState<'input' | 'verify'>('input');
  const [formData, setFormData] = useState({
    newEmail: '',
    verificationCode: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.newEmail) {
      setErrors({ newEmail: '请输入新邮箱地址' });
      return;
    }

    if (!validateEmail(formData.newEmail)) {
      setErrors({ newEmail: '请输入有效的邮箱地址' });
      return;
    }

    if (formData.newEmail === currentEmail) {
      setErrors({ newEmail: '新邮箱不能与当前邮箱相同' });
      return;
    }

    setIsSubmitting(true);
    try {
      await sendChangeEmailCode(formData.newEmail);
      
      toast({
        title: "验证码已发送",
        description: `验证码已发送到 ${formData.newEmail}，请查收邮件`,
        className: "bg-blue-500 text-white",
      });

      setStep('verify');
      setErrors({});
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || '发送验证码失败，请重试';
      toast({
        title: "发送失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.verificationCode) {
      setErrors({ verificationCode: '请输入验证码' });
      return;
    }

    if (formData.verificationCode.length !== 6) {
      setErrors({ verificationCode: '验证码应为6位数字' });
      return;
    }

    // 验证是否为纯数字
    if (!/^\d{6}$/.test(formData.verificationCode)) {
      setErrors({ verificationCode: '验证码只能包含数字' });
      return;
    }

    setIsSubmitting(true);
    try {
      await verifyChangeEmail(formData.newEmail, formData.verificationCode);
      
      toast({
        title: "邮箱更换成功",
        description: `您的邮箱已成功更换为 ${formData.newEmail}`,
        className: "bg-green-500 text-white",
      });

      handleCancel();
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || '验证失败，请检查验证码';
      toast({
        title: "验证失败",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      newEmail: '',
      verificationCode: ''
    });
    setErrors({});
    setStep('input');
    onOpenChange(false);
  };

  const handleResendCode = async () => {
    setIsSubmitting(true);
    try {
      await sendChangeEmailCode(formData.newEmail);
      
      toast({
        title: "验证码已重新发送",
        description: "请查收邮件",
        className: "bg-blue-500 text-white",
      });
    } catch (error) {
      toast({
        title: "发送失败",
        description: "重新发送验证码失败，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-blue-500" />
            {step === 'input' ? '更换邮箱' : '验证新邮箱'}
          </DialogTitle>
        </DialogHeader>
        
        {step === 'input' ? (
          <form onSubmit={handleEmailSubmit} className="space-y-4">
            <div>
              <Label htmlFor="currentEmail">当前邮箱</Label>
              <Input
                id="currentEmail"
                value={currentEmail}
                disabled
                className="bg-muted"
              />
            </div>

            <div>
              <Label htmlFor="newEmail">新邮箱地址</Label>
              <Input
                id="newEmail"
                type="email"
                value={formData.newEmail}
                onChange={(e) => handleInputChange('newEmail', e.target.value)}
                placeholder="输入新的邮箱地址"
                className={errors.newEmail ? 'border-destructive' : ''}
              />
              {errors.newEmail && (
                <p className="text-sm text-destructive mt-1">{errors.newEmail}</p>
              )}
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="flex-1"
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    发送中...
                  </>
                ) : (
                  '发送验证码'
                )}
              </Button>
            </div>
          </form>
        ) : (
          <form onSubmit={handleVerificationSubmit} className="space-y-4">
            <div className="text-center py-4">
              <CheckCircle className="h-12 w-12 text-blue-500 mx-auto mb-3" />
              <p className="text-muted-foreground text-sm">
                验证码已发送到
              </p>
              <p className="font-medium">
                {formData.newEmail}
              </p>
              <p className="text-muted-foreground text-xs mt-2">
                请查收邮件并输入6位数字验证码（有效期10分钟）
              </p>
            </div>

            <div>
              <Label htmlFor="verificationCode">验证码</Label>
              <Input
                id="verificationCode"
                value={formData.verificationCode}
                onChange={(e) => {
                  // 只允许输入数字
                  const value = e.target.value.replace(/\D/g, '');
                  handleInputChange('verificationCode', value);
                }}
                placeholder="输入6位数字验证码"
                maxLength={6}
                className={`text-center text-lg tracking-widest ${
                  errors.verificationCode ? 'border-destructive' : ''
                }`}
              />
              {errors.verificationCode && (
                <p className="text-sm text-destructive mt-1">{errors.verificationCode}</p>
              )}
            </div>

            <div className="text-center">
              <Button
                type="button"
                variant="ghost"
                onClick={handleResendCode}
                disabled={isSubmitting}
                className="text-blue-500 hover:text-blue-600 text-sm"
              >
                没收到验证码？重新发送
              </Button>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setStep('input')}
                disabled={isSubmitting}
                className="flex-1"
              >
                返回
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    验证中...
                  </>
                ) : (
                  '确认更换'
                )}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EmailChangeDialog;
