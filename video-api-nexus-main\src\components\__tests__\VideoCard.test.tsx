import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import VideoCard from '../VideoCard';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'next-themes';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'videoInfo.systemAdmin': '系统管理员',
        'videoInfo.anonymousAuthor': '匿名作者',
        'videoInfo.uncategorized': '未分类',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock hooks
vi.mock('@/hooks/queries/useInteractions', () => ({
  useLikeMutation: () => ({
    mutate: vi.fn(),
    isPending: false,
  }),
  useFavoriteMutation: () => ({
    mutate: vi.fn(),
    isPending: false,
  }),
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ThemeProvider attribute="class" defaultTheme="light">
          {children}
        </ThemeProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('VideoCard 上传者显示优化', () => {
  const baseVideo = {
    id: '1',
    title: '测试视频',
    thumbnail_url: '/test.jpg',
    duration: 120,
    view_count: 100,
    like_count: 10,
    comment_count: 5,
    category: { name: '测试分类' },
    is_liked: false,
    is_favorited: false,
    media_type: 'video' as const,
    url: '/test-video',
    visibility: 'public' as const,
  };

  it('应该隐藏系统管理员上传的视频的上传者信息', () => {
    const systemAdminVideo = {
      ...baseVideo,
      uploader: { nickname: '系统管理员' },
    };

    render(
      <TestWrapper>
        <VideoCard video={systemAdminVideo} />
      </TestWrapper>
    );

    // 应该不显示上传者信息
    expect(screen.queryByText('系统管理员')).not.toBeInTheDocument();
  });

  it('应该显示普通用户上传的视频的用户名', () => {
    const userVideo = {
      ...baseVideo,
      uploader: { nickname: '普通用户' },
    };

    render(
      <TestWrapper>
        <VideoCard video={userVideo} />
      </TestWrapper>
    );

    // 应该显示用户名
    expect(screen.getByText('普通用户')).toBeInTheDocument();
  });

  it('应该显示匿名作者当没有上传者信息时', () => {
    const anonymousVideo = {
      ...baseVideo,
      uploader: undefined,
    };

    render(
      <TestWrapper>
        <VideoCard video={anonymousVideo} />
      </TestWrapper>
    );

    // 应该显示匿名作者
    expect(screen.getByText('匿名作者')).toBeInTheDocument();
  });

  it('应该显示视频标题和其他信息', () => {
    const normalVideo = {
      ...baseVideo,
      uploader: { nickname: '测试用户' },
    };

    render(
      <TestWrapper>
        <VideoCard video={normalVideo} />
      </TestWrapper>
    );

    // 应该显示视频标题
    expect(screen.getByText('测试视频')).toBeInTheDocument();
    // 应该显示用户名
    expect(screen.getByText('测试用户')).toBeInTheDocument();
    // 应该显示观看次数
    expect(screen.getByText('100')).toBeInTheDocument();
  });
});
