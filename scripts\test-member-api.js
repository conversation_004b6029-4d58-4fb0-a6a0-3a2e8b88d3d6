const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testMemberAPI() {
  try {
    console.log('=== 测试会员管理API ===\n');

    // 1. 管理员登录
    console.log('1. 管理员登录...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123456!',
      isAdminLogin: true
    });

    if (!loginResponse.data.success) {
      console.error('❌ 管理员登录失败:', loginResponse.data.message);
      return;
    }

    const adminToken = loginResponse.data.data.tokens.accessToken;
    console.log('✓ 管理员登录成功');
    console.log('Token:', adminToken.substring(0, 50) + '...\n');

    // 2. 获取会员统计概览
    console.log('2. 获取会员统计概览...');
    try {
      const overviewResponse = await axios.get(`${API_BASE}/member/admin/overview`, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });

      if (overviewResponse.data.success) {
        console.log('✓ 会员统计概览:');
        console.log(JSON.stringify(overviewResponse.data.data, null, 2));
      } else {
        console.log('❌ 获取统计概览失败:', overviewResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 获取统计概览出错:', error.response?.data?.message || error.message);
    }
    console.log('');

    // 3. 获取会员用户列表
    console.log('3. 获取会员用户列表...');
    try {
      const usersResponse = await axios.get(`${API_BASE}/member/admin/users`, {
        headers: { Authorization: `Bearer ${adminToken}` },
        params: { page: 1, pageSize: 10 }
      });

      if (usersResponse.data.success) {
        console.log('✓ 会员用户列表:');
        console.log(`总数: ${usersResponse.data.data.pagination.total}`);
        console.log('用户列表:');
        usersResponse.data.data.users.forEach((user, index) => {
          console.log(`${index + 1}. 用户: ${user.username}(${user.id}), 角色: ${user.role}, 会员状态: ${user.membership_status || '无'}, 计划: ${user.membership_plan?.name || '无'}`);
        });
      } else {
        console.log('❌ 获取会员用户列表失败:', usersResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 获取会员用户列表出错:', error.response?.data?.message || error.message);
    }
    console.log('');

    // 4. 获取会员计划列表
    console.log('4. 获取会员计划列表...');
    try {
      const plansResponse = await axios.get(`${API_BASE}/member/admin/plans`, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });

      if (plansResponse.data.success) {
        console.log('✓ 会员计划列表:');
        plansResponse.data.data.plans.forEach((plan, index) => {
          console.log(`${index + 1}. 计划: ${plan.name}, 价格: ¥${plan.price}, 天数: ${plan.duration_days}, 状态: ${plan.is_active ? '启用' : '禁用'}`);
        });
      } else {
        console.log('❌ 获取会员计划列表失败:', plansResponse.data.message);
      }
    } catch (error) {
      console.log('❌ 获取会员计划列表出错:', error.response?.data?.message || error.message);
    }
    console.log('');

    // 5. 测试不同的查询参数
    console.log('5. 测试不同查询参数的会员用户列表...');
    
    // 5.1 查询所有用户（不限制角色）
    try {
      const allUsersResponse = await axios.get(`${API_BASE}/member/admin/users`, {
        headers: { Authorization: `Bearer ${adminToken}` },
        params: { page: 1, pageSize: 10, role: 'all' }
      });

      if (allUsersResponse.data.success) {
        console.log('✓ 所有用户列表:');
        console.log(`总数: ${allUsersResponse.data.data.pagination.total}`);
        const roleStats = {};
        allUsersResponse.data.data.users.forEach(user => {
          roleStats[user.role] = (roleStats[user.role] || 0) + 1;
        });
        console.log('角色分布:', roleStats);
      }
    } catch (error) {
      console.log('❌ 获取所有用户列表出错:', error.response?.data?.message || error.message);
    }

    // 5.2 只查询member角色用户
    try {
      const memberUsersResponse = await axios.get(`${API_BASE}/member/admin/users`, {
        headers: { Authorization: `Bearer ${adminToken}` },
        params: { page: 1, pageSize: 10, role: 'member' }
      });

      if (memberUsersResponse.data.success) {
        console.log('✓ Member角色用户列表:');
        console.log(`总数: ${memberUsersResponse.data.data.pagination.total}`);
        memberUsersResponse.data.data.users.forEach((user, index) => {
          console.log(`${index + 1}. 用户: ${user.username}(${user.id}), 角色: ${user.role}, 会员状态: ${user.membership_status || '无'}`);
        });
      }
    } catch (error) {
      console.log('❌ 获取Member用户列表出错:', error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error.response?.data || error.message);
  }
}

// 运行测试
testMemberAPI().then(() => {
  console.log('\n=== 测试完成 ===');
  process.exit(0);
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
