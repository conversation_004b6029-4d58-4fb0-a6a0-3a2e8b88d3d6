require('dotenv').config();
const { mysql } = require('../src/config/database');
const Membership = require('../src/database/models/Membership');

async function runTest() {
  console.log('正在测试MySQL数据库连接和查询...');

  try {
    console.log('尝试获取一个数据库连接...');
    const connection = await mysql.getConnection();
    console.log('✅ 连接获取成功。');
    
    console.log('正在 Ping 数据库...');
    await connection.ping();
    console.log('✅ Ping 成功。');

    const query = "SELECT COUNT(DISTINCT user_id) as count FROM memberships WHERE status = 'active'";
    console.log(`正在执行查询: ${query}`);
    
    const [rows] = await connection.execute(query);
    
    console.log('✅ 查询执行成功！');
    console.log('查询结果:', rows);
    
    connection.release();
    
    console.log('\n结论: 数据库连接和基本查询功能均正常。');
    console.log('如果应用仍然报错，问题可能出在：');
    console.log('1. 应用启动时的异步初始化顺序问题 (竞态条件)。');
    console.log('2. 其他更复杂的查询失败 (例如 `orders` 表相关查询)。');
    console.log('3. Redis 连接问题 (在 `initDatabase` 中被调用)。');

    process.exit(0);
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    
    console.log('\n请检查:');
    console.log('- 数据库服务是否正在运行?');
    console.log('- `.env` 文件或 `database.js` 中的配置是否正确?');
    console.log(`- 数据库 \`${process.env.DB_NAME || 'video_platform'}\` 中是否存在 \`memberships\` 表?`);
    console.log('- 用户 \`' + (process.env.DB_USER || 'root') + '\` 是否有权限访问该表?');
    process.exit(1);
  }
}

runTest(); 