const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testProfileFeatures() {
  console.log('🧪 测试个人资料功能...\n');

  try {
    // 1. 登录获取token
    console.log('1. 登录管理员账户...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'Admin123456!'
    });

    const token = loginResponse.data.data.tokens.accessToken;
    console.log('✅ 登录成功');

    // 设置请求头
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. 获取用户资料
    console.log('\n2. 获取用户资料...');
    const profileResponse = await axios.get(`${API_BASE}/auth/me`, { headers });
    console.log('✅ 用户资料获取成功:');
    console.log('   用户名:', profileResponse.data.data.user.username);
    console.log('   邮箱:', profileResponse.data.data.user.email);
    console.log('   角色:', profileResponse.data.data.user.role);

    // 3. 更新用户资料
    console.log('\n3. 更新用户资料...');
    const updateData = {
      username: 'admin',
      phone: '13800138000',
      gender: 'other',
      bio: '这是一个测试的个人简介，用于验证个人资料更新功能。'
    };

    const updateResponse = await axios.put(`${API_BASE}/user/profile`, updateData, { headers });
    console.log('✅ 用户资料更新成功:');
    console.log('   更新后用户名:', updateResponse.data.data.user.username);
    console.log('   手机号:', updateResponse.data.data.user.phone);
    console.log('   性别:', updateResponse.data.data.user.gender);
    console.log('   个人简介:', updateResponse.data.data.user.bio);

    // 4. 再次获取用户资料验证更新
    console.log('\n4. 验证资料更新...');
    const verifyResponse = await axios.get(`${API_BASE}/auth/me`, { headers });
    const updatedProfile = verifyResponse.data.data.user;
    console.log('✅ 资料更新验证成功:');
    console.log('   用户名:', updatedProfile.username);
    console.log('   手机号:', updatedProfile.phone);
    console.log('   性别:', updatedProfile.gender);
    console.log('   个人简介:', updatedProfile.bio);

    console.log('\n🎉 所有个人资料功能测试通过！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.error('错误详情:', error.response.data);
    }
  }
}

// 运行测试
testProfileFeatures();
