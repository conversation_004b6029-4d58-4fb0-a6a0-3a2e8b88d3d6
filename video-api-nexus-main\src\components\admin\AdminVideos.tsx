import { useState, useEffect, useCallback } from 'react';
import { Search, Filter, MoreHorizontal, Play, Eye, Ban, Trash2, CheckCircle, X, Database } from 'lucide-react';
import { getAdminVideos, batchVideoAction } from '@/lib/api';
import { useCategories } from '@/hooks/queries/useCategories';
import { useDebounce } from '@/hooks/useDebounce';
import LoadingSpinner from '@/components/LoadingSpinner';
import { Checkbox } from "@/components/ui/checkbox"
import { useTranslation } from 'react-i18next';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const AdminVideos = () => {
  const { t } = useTranslation();
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 9, total: 0 });
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({ status: 'all', category: 'all' });
  const [selectedVideoIds, setSelectedVideoIds] = useState<number[]>([]);

  // 获取分类数据
  const { data: categories = [], isLoading: categoriesLoading, error: categoriesError } = useCategories();

  // 调试日志
  useEffect(() => {
    console.log('Categories data:', categories);
    console.log('Categories loading:', categoriesLoading);
    console.log('Categories error:', categoriesError);
  }, [categories, categoriesLoading, categoriesError]);

  const debouncedSearch = useDebounce(searchQuery, 500);

  const fetchVideos = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        search: debouncedSearch,
        status: filters.status === 'all' ? '' : filters.status,
        categoryId: filters.category === 'all' ? '' : filters.category,
      };
      const response = await getAdminVideos(params);

      // 安全地访问响应数据
      const responseData = response?.data?.data;
      if (responseData) {
        // 后端返回的数据结构：{ videos: [...], pagination: {...} }
        setVideos(responseData.videos || []);
        setPagination(prev => ({
          ...prev,
          total: responseData.pagination?.total || 0,
          page: responseData.pagination?.page || 1,
          pageSize: responseData.pagination?.pageSize || 9,
        }));
        setError(null);
      } else {
        setVideos([]);
        setError('数据格式错误，请检查API响应');
      }
    } catch (err: any) {
      console.error('获取视频列表失败:', err);
      if (err.response?.status === 401) {
        setError('认证失败，请重新登录');
      } else if (err.response?.status === 403) {
        setError('权限不足，无法访问视频管理');
      } else if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('网络连接失败，请检查后端服务是否运行');
      } else {
        setError(err.response?.data?.message || '获取视频列表失败，请稍后重试');
      }
      setVideos([]);
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.pageSize, debouncedSearch, filters]);

  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  const handleSelectVideo = (videoId: number, checked: boolean) => {
    setSelectedVideoIds(prev => {
      if (checked) {
        return [...prev, videoId];
      } else {
        return prev.filter(id => id !== videoId);
      }
    });
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedVideoIds(videos.map((v: any) => v.id));
    } else {
      setSelectedVideoIds([]);
    }
  };

  const handleBatchAction = async (action: 'unpublish' | 'hard_delete' | 'publish') => {
    const actionMap = {
      unpublish: "隐藏",
      hard_delete: "永久删除",
      publish: "发布 (取消隐藏)"
    };
    const actionName = actionMap[action];
    
    if (selectedVideoIds.length === 0) {
      alert(`请至少选择一个视频进行${actionName}`);
      return;
    }

    const confirmed = window.confirm(
      `您确定要对选中的 ${selectedVideoIds.length} 个视频执行“${actionName}”操作吗？` +
      (action === 'hard_delete' ? '\n\n⚠️ 警告：永久删除操作不可恢复！' : '')
    );

    if (confirmed) {
      try {
        await batchVideoAction(action, selectedVideoIds);
        fetchVideos(); // Refresh videos after action
        setSelectedVideoIds([]); // Clear selection
        alert(`批量${actionName}操作成功`);
      } catch (error: any) {
        console.error(`Failed to batch ${action} videos:`, error);
        alert(`批量${actionName}失败：` + (error.response?.data?.message || error.message));
      }
    }
  };


  const handleVideoAction = async (action, videoIds) => {
    if (!Array.isArray(videoIds)) videoIds = [videoIds];
    try {
      await batchVideoAction(action, videoIds);
      fetchVideos(); // Refresh videos after action
    } catch (error) {
      console.error(`Failed to ${action} video(s):`, error);
      alert(`Failed to perform action: ${action}`);
    }
  };

  const handleHardDelete = async (videoId) => {
    const confirmed = window.confirm(
      '⚠️ 警告：这将永久删除视频及其所有相关数据（评论、点赞、观看历史等），此操作不可恢复！\n\n确定要继续吗？'
    );

    if (confirmed) {
      const doubleConfirmed = window.confirm(
        '🚨 最后确认：您确定要永久删除这个视频吗？\n\n点击"确定"将立即执行删除操作。'
      );

      if (doubleConfirmed) {
        try {
          await batchVideoAction('hard_delete', [videoId]);
          fetchVideos(); // Refresh videos after deletion
          alert('视频已永久删除');
        } catch (error) {
          console.error('Failed to hard delete video:', error);
          alert('删除失败：' + (error.response?.data?.message || error.message));
        }
      }
    }
  };

  const handleCleanupOrphaned = async () => {
    const confirmed = window.confirm(
      '🔍 这将检查并清理所有没有对应文件的视频记录。\n\n确定要继续吗？'
    );

    if (confirmed) {
      try {
        // 首先检查孤立记录
        const checkResponse = await fetch('/api/admin/videos/check-orphaned', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (!checkResponse.ok) {
          throw new Error('检查孤立记录失败');
        }

        const checkData = await checkResponse.json();
        const orphanedVideos = checkData.data.orphanedVideos;

        if (orphanedVideos.length === 0) {
          alert('没有发现孤立的视频记录！');
          return;
        }

        const cleanupConfirmed = window.confirm(
          `发现 ${orphanedVideos.length} 个孤立记录：\n\n${orphanedVideos.map(v => `- ${v.title} (缺失: ${v.missingFiles.join(', ')})`).slice(0, 5).join('\n')}${orphanedVideos.length > 5 ? '\n...' : ''}\n\n确定要清理这些记录吗？`
        );

        if (cleanupConfirmed) {
          const cleanupResponse = await fetch('/api/admin/videos/cleanup-orphaned', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
              videoIds: orphanedVideos.map(v => v.id)
            })
          });

          if (!cleanupResponse.ok) {
            throw new Error('清理孤立记录失败');
          }

          const cleanupData = await cleanupResponse.json();
          alert(`清理完成：${cleanupData.message}`);
          fetchVideos(); // Refresh videos after cleanup
        }
      } catch (error) {
        console.error('Failed to cleanup orphaned videos:', error);
        alert('清理失败：' + error.message);
      }
    }
  };

  const getStatusLabel = (status) => {
    const statusMap = {
      'published': '已发布',
      'reviewing': '审核中',
      'rejected': '已拒绝',
      'draft': '草稿'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'reviewing':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getVisibilityLabel = (visibility) => {
    const visibilityMap = {
      'public': '公开',
      'private': '私有',
      'member_only': '仅会员',
      'vip_only': '仅VIP'
    };
    return visibilityMap[visibility] || visibility;
  };

  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4 md:space-y-6">
      {/* 页面标题和操作按钮 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-xl md:text-2xl font-bold">视频管理</h1>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={handleCleanupOrphaned}
            className="px-3 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 flex items-center space-x-2 text-sm"
            title="清理孤立记录"
          >
            <Database size={16} />
            <span>清理孤立记录</span>
          </button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={selectedVideoIds.length === 0}
              >
                批量操作 ({selectedVideoIds.length})
          </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onSelect={() => handleBatchAction('publish')}>
                <Eye className="mr-2 h-4 w-4" />
                <span>发布选中</span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleBatchAction('unpublish')}>
                <Ban className="mr-2 h-4 w-4" />
                <span>隐藏选中</span>
              </DropdownMenuItem>
              <DropdownMenuItem onSelect={() => handleBatchAction('hard_delete')} className="text-red-600">
                <Trash2 className="mr-2 h-4 w-4" />
                <span>删除选中</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-card p-4 rounded-lg border">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
              <input
                type="text"
                placeholder="搜索视频标题或上传者..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-input rounded-md"
              />
            </div>
          </div>
          
          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-2 bg-background border border-input rounded-md"
          >
            <option value="all">所有状态</option>
            <option value="published">已发布</option>
            <option value="private">私有 (隐藏)</option>
            <option value="reviewing">审核中</option>
            <option value="rejected">已拒绝</option>
            <option value="draft">草稿</option>
          </select>
          
          <select
            value={filters.category}
            onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
            className="px-3 py-2 bg-background border border-input rounded-md"
            disabled={categoriesLoading}
          >
            <option value="all">
              {categoriesLoading ? '加载中...' : '所有分类'}
            </option>
            {!categoriesLoading && categories.map((category: any) => (
              <option key={category.id} value={category.id.toString()}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Videos Grid */}
      {loading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner text="正在加载视频数据..." />
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-500">{error}</p>
        </div>
      ) : videos.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">暂无视频数据</p>
        </div>
      ) : (
        <>
        <div className="flex items-center space-x-2 mb-4">
            <Checkbox
              id="select-all"
              checked={selectedVideoIds.length === videos.length && videos.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <label htmlFor="select-all" className="text-sm font-medium">
              全选
            </label>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
          {(videos as any[]).map((video) => (
          <div key={video.id} className="bg-card rounded-lg border overflow-hidden relative">
            <div className="absolute top-2 left-2 z-10">
              <Checkbox
                checked={selectedVideoIds.includes(video.id)}
                onCheckedChange={(checked) => handleSelectVideo(video.id, !!checked)}
              />
            </div>
            <div className="relative">
              <img 
                src={video.thumbnail} 
                alt={video.title}
                className="w-full h-36 md:h-48 object-cover"
              />
              <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                {formatDuration(video.duration)}
              </div>
              <div className="absolute bottom-2 left-2">
                <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(video.status)}`}>
                  {getStatusLabel(video.status)}
                </span>
              </div>
            </div>
            
            <div className="p-3 md:p-4 space-y-2 md:space-y-3">
              <div>
                <h3 className="font-semibold text-sm line-clamp-2">{video.title}</h3>
                <p className="text-xs text-muted-foreground mt-1 line-clamp-2 hidden md:block">{video.description}</p>
              </div>
              
              <div className="flex items-center justify-between text-xs">
                {(() => {
                  const uploaderName = video.username || video.nickname;
                  const isSystemAdmin = uploaderName === t('videoInfo.systemAdmin');
                  const displayName = uploaderName || t('videoInfo.anonymousAuthor');

                  return !isSystemAdmin ? (
                    <span className="text-muted-foreground truncate max-w-[50%]">by {displayName}</span>
                  ) : (
                    <span className="text-muted-foreground truncate max-w-[50%]">系统视频</span>
                  );
                })()}
                <span className="bg-secondary px-2 py-1 rounded text-xs">{video.category_name || '未分类'}</span>
              </div>
              
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="space-y-1">
                  <div className="flex items-center space-x-1">
                    <Eye size={12} />
                    <span>{video.view_count.toLocaleString()}</span>
                  </div>
                  <div className="hidden md:block">可见性: {getVisibilityLabel(video.visibility)}</div>
                </div>
                <div className="space-y-1 text-right">
                  <div className="hidden md:block">大小: {video.file_size}</div>
                  <div>{video.created_at}</div>
                </div>
              </div>
              
              <div className="flex items-center justify-between pt-2 border-t">
                <div className="flex space-x-1 md:space-x-2">
                  <button className="p-1 text-blue-600 hover:bg-blue-100 rounded" title="预览">
                    <Play size={16} />
                  </button>
                  {video.status === 'private' ? (
                    <button onClick={() => handleVideoAction('publish', video.id)} className="p-1 text-green-600 hover:bg-green-100 rounded" title="发布 (取消隐藏)">
                      <Eye size={16} />
                    </button>
                  ) : (
                    <button onClick={() => handleVideoAction('unpublish', video.id)} className="p-1 text-yellow-600 hover:bg-yellow-100 rounded" title="隐藏">
                      <Ban size={16} />
                    </button>
                  )}
                  {video.status === 'reviewing' && (
                    <button onClick={() => handleVideoAction('approve', video.id)} className="p-1 text-green-600 hover:bg-green-100 rounded" title="批准">
                      <CheckCircle size={16} />
                    </button>
                  )}
                  <button onClick={() => handleVideoAction('delete', video.id)} className="p-1 text-red-600 hover:bg-red-100 rounded" title="软删除">
                    <Trash2 size={16} />
                  </button>
                  <button onClick={() => handleHardDelete(video.id)} className="p-1 text-red-800 hover:bg-red-200 rounded" title="永久删除">
                    <X size={16} />
                  </button>
                </div>
                <button className="p-1 text-muted-foreground hover:bg-accent rounded" title="更多操作">
                  <MoreHorizontal size={16} />
                </button>
              </div>
            </div>
          </div>
          ))}
        </div>
      </>
      )}

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          第 {pagination.page} 页，共 {Math.ceil(pagination.total / pagination.pageSize)} 页 (总计 {pagination.total} 个视频)
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setPagination(p => ({ ...p, page: p.page - 1 }))}
            className="px-3 py-1 border border-input rounded-md hover:bg-accent disabled:opacity-50"
            disabled={pagination.page <= 1}
          >
            上一页
          </button>
          <button
            onClick={() => setPagination(p => ({ ...p, page: p.page + 1 }))}
            className="px-3 py-1 border border-input rounded-md hover:bg-accent disabled:opacity-50"
            disabled={pagination.page * pagination.pageSize >= pagination.total}
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminVideos;
