const userService = require('../services/userService');
const logger = require('../../../utils/logger');

// 用户活动追踪中间件
const activityTracker = (activityType = 'general') => {
  return async (req, res, next) => {
    // 只有已认证用户才追踪活动
    if (!req.user || !req.user.id) {
      return next();
    }

    try {
      // 异步更新用户活动状态，不阻塞请求
      setImmediate(async () => {
        try {
          await userService.updateUserActivity(req.user.id, activityType);
        } catch (error) {
          logger.error('更新用户活动状态失败:', error);
        }
      });
    } catch (error) {
      // 活动追踪失败不应该影响正常请求
      logger.error('用户活动追踪失败:', error);
    }

    next();
  };
};

// 特定活动类型的追踪中间件
const trackActivity = {
  // 视频观看活动
  videoWatch: activityTracker('video_watch'),
  
  // 视频上传活动
  videoUpload: activityTracker('video_upload'),
  
  // 评论活动
  comment: activityTracker('comment'),
  
  // 点赞活动
  like: activityTracker('like'),
  
  // 搜索活动
  search: activityTracker('search'),
  
  // 资料更新活动
  profileUpdate: activityTracker('profile_update'),
  
  // 社交活动（关注、取消关注）
  social: activityTracker('social'),
  
  // 通用活动
  general: activityTracker('general')
};

// 在线状态中间件
const onlineStatusMiddleware = async (req, res, next) => {
  if (req.user && req.user.id) {
    // 设置用户在线状态
    req.user.isOnline = await userService.isUserOnline(req.user.id);
  }
  next();
};

// 用户统计中间件
const userStatsMiddleware = async (req, res, next) => {
  if (req.user && req.user.id) {
    try {
      // 获取用户统计信息
      const userStats = await userService.getUserFullInfo(req.user.id);
      if (userStats) {
        req.user.stats = {
          videoCount: userStats.video_count || 0,
          favoriteCount: userStats.favorite_count || 0,
          commentCount: userStats.comment_count || 0
        };
      }
    } catch (error) {
      logger.error('获取用户统计信息失败:', error);
    }
  }
  next();
};

module.exports = {
  activityTracker,
  trackActivity,
  onlineStatusMiddleware,
  userStatsMiddleware
};
