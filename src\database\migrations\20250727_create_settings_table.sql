-- -------------------------------------------------------------
-- Table: settings
-- Description: 存储系统级别的配置，采用键值对形式。
-- -------------------------------------------------------------
CREATE TABLE IF NOT EXISTS `settings` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `key` VARCHAR(255) NOT NULL UNIQUE,
  `value` TEXT,
  `name` VARCHAR(255) COMMENT '配置项的可读名称',
  `description` TEXT COMMENT '配置项的详细描述',
  `type` VARCHAR(50) DEFAULT 'string' COMMENT '数据类型 (string, number, boolean, json)',
  `group` VARCHAR(100) DEFAULT 'system' COMMENT '配置分组',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- -------------------------------------------------------------
-- Initial Data: settings
-- 插入一些系统默认配置
-- -------------------------------------------------------------
INSERT INTO `settings` (`key`, `value`, `name`, `description`, `type`, `group`) VALUES
('site.name', '视频平台', '网站名称', '网站的公开名称', 'string', 'general'),
('site.description', '专业的视频分享平台', '网站描述', '网站的简短描述', 'string', 'general'),
('site.contactEmail', '<EMAIL>', '联系邮箱', '网站的官方联系邮箱', 'string', 'general'),
('media.maxUploadSize', '500', '最大上传大小(MB)', '允许上传的单个视频文件的最大体积（单位：MB）', 'number', 'media'),
('media.allowedVideoFormats', 'mp4,avi,mov,wmv', '允许的视频格式', '允许上传的视频文件扩展名，以逗号分隔', 'string', 'media'),
('user.enableRegistration', 'true', '允许用户注册', '是否开放新用户注册功能', 'boolean', 'user'),
('user.enableComments', 'true', '启用评论功能', '是否允许用户发表评论', 'boolean', 'user'),
('email.smtpHost', 'smtp.example.com', 'SMTP服务器', '用于发送邮件的SMTP服务器地址', 'string', 'email'),
('email.smtpPort', '587', 'SMTP端口', 'SMTP服务器的端口号', 'number', 'email'),
('email.smtpUser', '<EMAIL>', 'SMTP用户名', '用于登录SMTP服务器的用户名', 'string', 'email'),
('email.smtpPassword', '', 'SMTP密码', '用于登录SMTP服务器的密码', 'string', 'email'),
('email.fromEmail', '<EMAIL>', '发件人邮箱', '发送邮件时显示的发件人邮箱地址', 'string', 'email'),
('email.fromName', '视频平台', '发件人名称', '发送邮件时显示的发件人名称', 'string', 'email'),
('security.enableTwoFactor', 'false', '启用双因素认证', '是否为用户账户启用双因素认证', 'boolean', 'security'),
('security.sessionTimeout', '3600', '会话超时时间(秒)', '用户无操作时会话保持有效的时长（单位：秒）', 'number', 'security'),
('security.maxLoginAttempts', '5', '最大登录尝试次数', '在锁定账户前允许的最大失败登录尝试次数', 'number', 'security'),
('security.passwordMinLength', '8', '最小密码长度', '用户设置密码时要求的最小长度', 'number', 'security'),
('security.requireStrongPassword', 'true', '要求强密码', '是否要求密码包含字母、数字和特殊字符', 'boolean', 'security')
ON DUPLICATE KEY UPDATE `key`=`key`; -- 如果键已存在则不执行任何操作 