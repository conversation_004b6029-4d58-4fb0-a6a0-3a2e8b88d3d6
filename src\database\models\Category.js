const BaseModel = require('../BaseModel');
const { AppError } = require('../../middleware/errorHandler');

class Category extends BaseModel {
  constructor() {
    super('categories');
  }

  // 创建分类
  async createCategory(categoryData) {
    const { name, slug, description, parentId = null, sortOrder = 0 } = categoryData;

    // 自动生成slug（如果未提供或为空）
    let finalSlug = slug;
    if (!finalSlug || finalSlug.trim() === '') {
      finalSlug = await this.generateUniqueSlug(name);
    } else {
      // 检查手动提供的slug是否已存在
      const existingCategory = await this.findOne({ slug: finalSlug });
      if (existingCategory) {
        throw new AppError('分类标识已存在', 409, 'SLUG_EXISTS');
      }
    }

    // 如果有父分类，检查父分类是否存在
    if (parentId) {
      const parentCategory = await this.findById(parentId);
      if (!parentCategory) {
        throw new AppError('父分类不存在', 404, 'PARENT_CATEGORY_NOT_FOUND');
      }
    }

    const category = await this.create({
      name,
      slug: finalSlug,
      description,
      parent_id: parentId,
      sort_order: sortOrder,
      status: 'active'
    });

    return category;
  }

  // 生成唯一的slug
  async generateUniqueSlug(name) {
    // 基础slug生成：移除特殊字符，转换为URL友好格式
    let baseSlug = name
      .toLowerCase()
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '') // 保留中文、英文、数字、空格
      .replace(/\s+/g, '-') // 空格转连字符
      .replace(/^-+|-+$/g, ''); // 移除首尾连字符

    // 如果处理后为空，使用默认前缀
    if (!baseSlug) {
      baseSlug = 'category';
    }

    // 检查唯一性，如果重复则添加数字后缀
    let finalSlug = baseSlug;
    let counter = 1;

    while (await this.findOne({ slug: finalSlug })) {
      finalSlug = `${baseSlug}-${counter}`;
      counter++;
    }

    return finalSlug;
  }

  // 获取所有分类（树形结构）
  async getCategoryTree() {
    const categories = await this.findAll(
      { status: 'active' },
      { orderBy: 'sort_order', order: 'ASC' }
    );

    return this.buildCategoryTree(categories);
  }

  // 构建分类树
  buildCategoryTree(categories, parentId = null) {
    const tree = [];
    
    for (const category of categories) {
      if (category.parent_id === parentId) {
        const children = this.buildCategoryTree(categories, category.id);
        if (children.length > 0) {
          category.children = children;
        }
        tree.push(category);
      }
    }
    
    return tree;
  }

  // 获取分类及其子分类的ID列表
  async getCategoryWithChildren(categoryId) {
    const allCategories = await this.findAll({ status: 'active' });
    const categoryIds = [categoryId];
    
    const findChildren = (parentId) => {
      for (const category of allCategories) {
        if (category.parent_id === parentId) {
          categoryIds.push(category.id);
          findChildren(category.id);
        }
      }
    };
    
    findChildren(categoryId);
    return categoryIds;
  }

  // 获取分类统计信息
  async getCategoryStats(categoryId = null) {
    let sql = `
      SELECT 
        c.id,
        c.name,
        c.slug,
        COUNT(v.id) as video_count,
        COUNT(CASE WHEN v.status = 'published' THEN 1 END) as published_count,
        SUM(v.view_count) as total_views,
        AVG(v.view_count) as avg_views
      FROM categories c
      LEFT JOIN videos v ON c.id = v.category_id AND v.status != 'deleted'
      WHERE c.status = 'active'
    `;
    
    const params = [];
    
    if (categoryId) {
      sql += ' AND c.id = ?';
      params.push(categoryId);
    }
    
    sql += ' GROUP BY c.id ORDER BY c.sort_order ASC';
    
    return await this.query(sql, params);
  }

  // 获取热门分类
  async getPopularCategories(limit = 10) {
    const sql = `
      SELECT 
        c.id,
        c.name,
        c.slug,
        c.description,
        COUNT(v.id) as video_count,
        SUM(v.view_count) as total_views
      FROM categories c
      LEFT JOIN videos v ON c.id = v.category_id 
        AND v.status = 'published' 
        AND v.visibility = 'public'
      WHERE c.status = 'active'
      GROUP BY c.id
      HAVING video_count > 0
      ORDER BY total_views DESC, video_count DESC
      LIMIT ?
    `;
    
    return await this.query(sql, [limit]);
  }

  // 更新分类
  async updateCategory(categoryId, updateData) {
    const allowedFields = ['name', 'description', 'parent_id', 'sort_order', 'status'];
    const filteredData = {};
    
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    // 如果更新slug，检查是否重复
    if (updateData.slug) {
      const existingCategory = await this.findOne({ slug: updateData.slug });
      if (existingCategory && existingCategory.id !== categoryId) {
        throw new AppError('分类标识已存在', 409, 'SLUG_EXISTS');
      }
      filteredData.slug = updateData.slug;
    }

    // 如果更新父分类，检查是否会造成循环引用
    if (updateData.parent_id) {
      const isCircular = await this.checkCircularReference(categoryId, updateData.parent_id);
      if (isCircular) {
        throw new AppError('不能设置为自己的子分类', 400, 'CIRCULAR_REFERENCE');
      }
    }

    if (Object.keys(filteredData).length === 0) {
      throw new AppError('没有可更新的字段', 400, 'NO_UPDATE_FIELDS');
    }

    return await this.update(categoryId, filteredData);
  }

  // 检查循环引用
  async checkCircularReference(categoryId, parentId) {
    if (categoryId === parentId) {
      return true;
    }

    const parent = await this.findById(parentId);
    if (!parent || !parent.parent_id) {
      return false;
    }

    return await this.checkCircularReference(categoryId, parent.parent_id);
  }

  // 删除分类
  async deleteCategory(categoryId) {
    // 检查是否有子分类
    const children = await this.findAll({ parent_id: categoryId, status: 'active' });
    if (children.length > 0) {
      throw new AppError('请先删除子分类', 400, 'HAS_CHILDREN');
    }

    // 检查是否有视频
    const videoCount = await this.query(
      'SELECT COUNT(*) as count FROM videos WHERE category_id = ? AND status != "deleted"',
      [categoryId]
    );

    if (videoCount[0].count > 0) {
      throw new AppError('该分类下还有视频，无法删除', 400, 'HAS_VIDEOS');
    }

    // 软删除
    return await this.update(categoryId, { status: 'inactive' });
  }

  // 获取分类路径（面包屑）
  async getCategoryPath(categoryId) {
    const path = [];
    let currentId = categoryId;

    while (currentId) {
      const category = await this.findById(currentId);
      if (!category) break;

      path.unshift({
        id: category.id,
        name: category.name,
        slug: category.slug
      });

      currentId = category.parent_id;
    }

    return path;
  }

  // 搜索分类
  async searchCategories(keyword, limit = 20) {
    const sql = `
      SELECT 
        c.*,
        COUNT(v.id) as video_count
      FROM categories c
      LEFT JOIN videos v ON c.id = v.category_id AND v.status = 'published'
      WHERE c.status = 'active' 
        AND (c.name LIKE ? OR c.description LIKE ?)
      GROUP BY c.id
      ORDER BY c.name ASC
      LIMIT ?
    `;

    const searchTerm = `%${keyword}%`;
    return await this.query(sql, [searchTerm, searchTerm, limit]);
  }

  // 获取分类的最新视频
  async getCategoryLatestVideos(categoryId, limit = 10) {
    const sql = `
      SELECT 
        v.id,
        v.title,
        v.thumbnail,
        v.duration,
        v.view_count,
        v.like_count,
        v.created_at,
        u.username,
        u.nickname
      FROM videos v
      LEFT JOIN users u ON v.user_id = u.id
      WHERE v.category_id = ? 
        AND v.status = 'published'
        AND v.visibility = 'public'
      ORDER BY v.created_at DESC
      LIMIT ?
    `;

    return await this.query(sql, [categoryId, limit]);
  }

  // 重新排序分类
  async reorderCategories(categoryOrders) {
    const connection = await this.beginTransaction();
    
    try {
      for (const { id, sortOrder } of categoryOrders) {
        await this.executeInTransaction(connection, 
          'UPDATE categories SET sort_order = ? WHERE id = ?',
          [sortOrder, id]
        );
      }
      
      await this.commitTransaction(connection);
      return true;
    } catch (error) {
      await this.rollbackTransaction(connection);
      throw error;
    }
  }

  // 获取分类建议（基于用户观看历史）
  async getSuggestedCategories(userId, limit = 5) {
    const sql = `
      SELECT 
        c.id,
        c.name,
        c.slug,
        COUNT(wh.id) as watch_count
      FROM categories c
      JOIN videos v ON c.id = v.category_id
      JOIN watch_history wh ON v.id = wh.video_id
      WHERE wh.user_id = ?
        AND c.status = 'active'
      GROUP BY c.id
      ORDER BY watch_count DESC
      LIMIT ?
    `;

    return await this.query(sql, [userId, limit]);
  }
}

module.exports = new Category();
