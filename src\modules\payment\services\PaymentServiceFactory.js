const EpayService = require('./EpayService');
const WechatPayService = require('./WechatPayService');
const AlipayService = require('./AlipayService');
// 😬 新增: 引入PayPal支付服务 (假设已创建)
// const PaypalService = require('./PaypalService'); 
const PaymentGateway = require('../../../database/models/PaymentGateway');
const { logger } = require('../../../utils/advancedLogger');

/**
 * 支付服务工厂
 * 负责创建和管理不同的支付服务实例
 */
class PaymentServiceFactory {
  constructor() {
    this.services = new Map();
    this.defaultService = null;
    this.paymentGatewayModel = new PaymentGateway();
  }

  /**
   * 异步初始化支付服务
   * 从数据库加载启用的支付通道
   */
  async initialize() {
    logger.info('开始从数据库初始化支付服务工厂...');
    this.services.clear();
    
    const gateways = await this.paymentGatewayModel.getEnabledGateways();

    for (const gateway of gateways) {
      try {
        let serviceInstance;
        const config = typeof gateway.config === 'string' ? JSON.parse(gateway.config) : gateway.config;
        config.enabled = gateway.enabled; // 确保enabled状态传递给服务

        switch (gateway.key) {
          case 'alipay':
            serviceInstance = new AlipayService(config);
            break;
          case 'wechat':
            serviceInstance = new WechatPayService(config);
            break;
          // 😬 新增: 对Epay不同币种的支持
          case 'epay-usdt':
          case 'epay-trx':
          case 'epay': // 兼容旧的epay
            serviceInstance = new EpayService(config);
            break;
          // 😬 新增: PayPal支付
          // case 'paypal':
          //   serviceInstance = new PaypalService(config);
          //   break;
          default:
            logger.warn(`未知的支付通道key: ${gateway.key}`);
            continue;
        }

        if (serviceInstance && serviceInstance.isConfigValid()) {
          this.services.set(gateway.key, serviceInstance);
          logger.info(`支付服务 [${gateway.name}] 初始化成功`);
        } else {
          logger.warn(`支付服务 [${gateway.name}] 配置无效或初始化失败，已跳过。`);
        }
      } catch (error) {
        logger.error(`初始化支付服务 [${gateway.name}] 时发生错误:`, error);
      }
    }

    this.setDefaultService();
    logger.info(`支付服务工厂初始化完成，可用服务: ${Array.from(this.services.keys()).join(', ')}`);
  }

  /**
   * 获取支付服务
   * @param {string} paymentMethod 支付方式 (现在应该直接对应gateway.key)
   * @returns {BasePaymentService} 支付服务实例
   */
  getService(paymentMethod) {
    const service = this.services.get(paymentMethod);

    if (!service) {
      if (this.defaultService) {
        logger.warn(`支付方式 ${paymentMethod} 不可用，使用默认服务 ${this.defaultService.name}`);
        return this.defaultService;
      }
      throw new Error(`不支持的支付方式: ${paymentMethod}`);
    }

    return service;
  }

  /**
   * 获取所有可用的支付方式
   * @returns {Array} 支付方式列表
   */
  getAvailablePaymentMethods() {
    const methods = [];

    for (const [serviceName, service] of this.services) {
      switch (serviceName) {
        case 'epay':
          methods.push(
            { code: 'epay_alipay', name: '易支付-支付宝', service: 'epay' },
            { code: 'epay_wechat', name: '易支付-微信', service: 'epay' },
            { code: 'epay_qq', name: '易支付-QQ钱包', service: 'epay' }
          );
          break;
        case 'wechat':
          methods.push(
            { code: 'wechat', name: '微信支付', service: 'wechat' }
          );
          break;
        case 'alipay':
          methods.push(
            { code: 'alipay', name: '支付宝', service: 'alipay' }
          );
          break;
      }
    }

    return methods;
  }

  /**
   * 检查支付方式是否可用
   * @param {string} paymentMethod 支付方式
   * @returns {boolean} 是否可用
   */
  isPaymentMethodAvailable(paymentMethod) {
    // 硬编码修复：TRON支付不依赖于一个具体的服务类，因此在这里直接返回true
    if (paymentMethod === 'tron_usdt') {
      return true;
    }
    try {
      this.getService(paymentMethod);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 设置默认支付服务
   */
  setDefaultService() {
    // 优先级: 支付宝 > 微信 > 易支付
    const priority = ['alipay', 'wechat', 'epay'];
    
    for (const serviceName of priority) {
      if (this.services.has(serviceName)) {
        this.defaultService = this.services.get(serviceName);
        logger.info(`设置默认支付服务: ${serviceName}`);
        break;
      }
    }

    if (!this.defaultService && this.services.size > 0) {
      this.defaultService = this.services.values().next().value;
      logger.info(`设置默认支付服务: ${this.defaultService.name}`);
    }
  }

  /**
   * 获取默认支付服务
   * @returns {BasePaymentService} 默认支付服务
   */
  getDefaultService() {
    if (!this.defaultService) {
      throw new Error('没有可用的支付服务');
    }
    return this.defaultService;
  }

  /**
   * 异步重新加载配置
   */
  async reload() {
    logger.info('重新加载支付服务配置...');
    await this.initialize();
  }

  /**
   * 获取服务状态
   * @returns {Object} 服务状态
   */
  getStatus() {
    const status = {
      totalServices: this.services.size,
      availableServices: [],
      defaultService: this.defaultService?.name || null,
      paymentMethods: this.getAvailablePaymentMethods()
    };

    for (const [name, service] of this.services) {
      status.availableServices.push({
        name,
        isValid: service.isConfigValid(),
        config: service.getConfig()
      });
    }

    return status;
  }

  /**
   * 创建支付订单
   * @param {string} paymentMethod 支付方式
   * @param {Object} orderData 订单数据
   * @returns {Promise<Object>} 支付结果
   */
  async createPayment(paymentMethod, orderData) {
    const service = this.getService(paymentMethod);
    return await service.createPayment(orderData);
  }

  /**
   * 查询支付状态
   * @param {string} paymentMethod 支付方式
   * @param {string} orderNo 订单号
   * @returns {Promise<Object>} 支付状态
   */
  async queryPayment(paymentMethod, orderNo) {
    const service = this.getService(paymentMethod);
    return await service.queryPayment(orderNo);
  }

  /**
   * 处理支付回调
   * @param {string} paymentMethod 支付方式
   * @param {Object} callbackData 回调数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleCallback(paymentMethod, callbackData) {
    const service = this.getService(paymentMethod);
    return await service.handleCallback(callbackData);
  }

  /**
   * 申请退款
   * @param {string} paymentMethod 支付方式
   * @param {Object} refundData 退款数据
   * @returns {Promise<Object>} 退款结果
   */
  async refund(paymentMethod, refundData) {
    const service = this.getService(paymentMethod);
    return await service.refund(refundData);
  }
}

// 创建单例实例
const paymentServiceFactory = new PaymentServiceFactory();

module.exports = paymentServiceFactory;
