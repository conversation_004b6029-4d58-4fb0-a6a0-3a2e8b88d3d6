# 播放列表 API 文档

## 概述

播放列表API提供完整的播放列表管理功能，包括播放列表的创建、管理、播放历史记录等功能。

**基础URL**: `/api/playlists`

## 认证

大部分API需要用户认证，在请求头中包含JWT令牌：
```
Authorization: Bearer <your-jwt-token>
```

## 播放列表管理

### 1. 获取用户播放列表
```
GET /api/playlists
```

**查询参数**:
- `includeItems` (boolean): 是否包含播放列表项目，默认false

**响应示例**:
```json
{
  "success": true,
  "message": "获取播放列表成功",
  "data": {
    "playlists": [
      {
        "id": 1,
        "name": "我的收藏",
        "description": "我最喜欢的视频",
        "isPublic": false,
        "playMode": "sequence",
        "itemCount": 5,
        "createdAt": "2025-01-22T10:00:00.000Z",
        "updatedAt": "2025-01-22T10:00:00.000Z"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取播放列表详情
```
GET /api/playlists/:playlistId
```

**权限**: 公开播放列表无需认证，私有播放列表需要所有者权限

**响应示例**:
```json
{
  "success": true,
  "message": "获取播放列表详情成功",
  "data": {
    "playlist": {
      "id": 1,
      "name": "我的收藏",
      "description": "我最喜欢的视频",
      "isPublic": false,
      "playMode": "sequence",
      "itemCount": 5,
      "items": [
        {
          "id": 1,
          "videoId": 123,
          "position": 0,
          "addedAt": "2025-01-22T10:00:00.000Z",
          "video": {
            "id": 123,
            "title": "示例视频",
            "thumbnail": "/uploads/thumbnails/123.jpg",
            "duration": 300,
            "mediaType": "video"
          }
        }
      ]
    },
    "permissions": {
      "canView": true,
      "canEdit": true,
      "canDelete": true,
      "isOwner": true,
      "isPublic": false
    }
  }
}
```

### 3. 创建播放列表
```
POST /api/playlists
```

**请求体**:
```json
{
  "name": "新播放列表",
  "description": "描述信息",
  "isPublic": false,
  "playMode": "sequence"
}
```

**字段说明**:
- `name` (string, 必填): 播放列表名称，1-255字符
- `description` (string, 可选): 描述信息，最多1000字符
- `isPublic` (boolean, 可选): 是否公开，默认false
- `playMode` (string, 可选): 播放模式，可选值: sequence/loop/random，默认sequence

### 4. 更新播放列表
```
PUT /api/playlists/:playlistId
```

**请求体**: 与创建播放列表相同，所有字段都是可选的

### 5. 删除播放列表
```
DELETE /api/playlists/:playlistId
```

## 播放列表项目管理

### 6. 添加视频到播放列表
```
POST /api/playlists/:playlistId/items
```

**请求体**:
```json
{
  "videoId": 123,
  "position": 0
}
```

**字段说明**:
- `videoId` (number, 必填): 视频ID
- `position` (number, 可选): 插入位置，不指定则添加到末尾

### 7. 从播放列表移除视频
```
DELETE /api/playlists/:playlistId/items/:videoId
```

### 8. 批量添加视频
```
POST /api/playlists/:playlistId/items/batch
```

**请求体**:
```json
{
  "videoIds": [123, 456, 789]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "批量添加完成，成功 3 个，失败 0 个",
  "data": {
    "results": [
      {"videoId": 123, "success": true},
      {"videoId": 456, "success": true},
      {"videoId": 789, "success": true}
    ],
    "summary": {
      "total": 3,
      "success": 3,
      "failure": 0
    }
  }
}
```

### 9. 重新排序播放列表项目
```
PUT /api/playlists/:playlistId/items/reorder
```

**请求体**:
```json
{
  "items": [
    {"videoId": 123, "position": 0},
    {"videoId": 456, "position": 1},
    {"videoId": 789, "position": 2}
  ]
}
```

### 10. 复制播放列表
```
POST /api/playlists/:playlistId/duplicate
```

**请求体**:
```json
{
  "newName": "播放列表副本"
}
```

## 统计信息

### 11. 获取播放列表统计
```
GET /api/playlists/stats
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取播放列表统计信息成功",
  "data": {
    "stats": {
      "totalPlaylists": 5,
      "totalItems": 25,
      "publicPlaylists": 2,
      "privatePlaylists": 3,
      "playModeStats": {
        "sequence": 3,
        "loop": 1,
        "random": 1
      }
    }
  }
}
```

## 播放历史

### 12. 记录播放历史
```
POST /api/playlists/history/record
```

**请求体**:
```json
{
  "videoId": 123,
  "watchDuration": 180,
  "videoDuration": 300,
  "completed": false
}
```

### 13. 获取播放历史
```
GET /api/playlists/history
```

**查询参数**:
- `limit` (number): 限制数量，默认50，最大100
- `offset` (number): 偏移量，默认0
- `videoId` (number): 筛选特定视频的历史

### 14. 获取最近播放
```
GET /api/playlists/history/recent
```

**查询参数**:
- `limit` (number): 限制数量，默认10

### 15. 获取播放统计
```
GET /api/playlists/history/stats
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取播放统计成功",
  "data": {
    "stats": {
      "totalPlays": 150,
      "uniqueVideos": 45,
      "totalWatchTime": 18000,
      "totalWatchTimeFormatted": "05:00:00",
      "completedVideos": 30,
      "avgWatchTime": 120,
      "avgWatchTimeFormatted": "02:00",
      "completionRate": 20
    }
  }
}
```

### 16. 获取播放趋势
```
GET /api/playlists/history/trends
```

**查询参数**:
- `days` (number): 统计天数，默认7天

### 17. 获取观看进度（断点续播）
```
GET /api/playlists/history/progress/:videoId
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取观看进度成功",
  "data": {
    "videoId": 123,
    "hasProgress": true,
    "watchDuration": 180,
    "videoDuration": 300,
    "completionRate": 60,
    "completed": false,
    "lastPlayedAt": "2025-01-22T10:00:00.000Z",
    "canResume": true
  }
}
```

### 18. 清除播放历史
```
DELETE /api/playlists/history
```

**请求体**:
```json
{
  "videoId": 123,
  "clearAll": false
}
```

## 错误响应

所有API在出错时返回统一格式：

```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE"
}
```

## 常见错误码

- `VALIDATION_ERROR`: 数据验证失败
- `AUTH_REQUIRED`: 需要登录
- `ACCESS_DENIED`: 权限不足
- `NOT_FOUND`: 资源不存在
- `DUPLICATE_ENTRY`: 数据重复
- `RATE_LIMIT_EXCEEDED`: 请求频率超限

## 播放模式说明

- `sequence`: 顺序播放，播放完最后一个停止
- `loop`: 循环播放，播放完最后一个回到第一个
- `random`: 随机播放，随机选择下一个播放项目
