require('dotenv').config();
const mysql = require('mysql2/promise');
const logger = require('../src/utils/logger');

async function debugMembershipIssue() {
  try {
    console.log('=== 会员管理问题诊断 ===\n');

    // 1. 检查数据库连接
    console.log('1. 检查数据库连接...');
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'video_user',
      password: process.env.DB_PASSWORD || 'secure_password',
      database: process.env.DB_NAME || 'video_platform'
    });
    console.log('✓ 数据库连接成功\n');

    // 2. 检查用户表数据
    console.log('2. 检查用户表数据...');
    const [users] = await connection.execute('SELECT id, username, email, role, status FROM users ORDER BY id');
    console.log(`用户总数: ${users.length}`);
    console.log('用户角色分布:');
    const roleStats = {};
    users.forEach(user => {
      roleStats[user.role] = (roleStats[user.role] || 0) + 1;
    });
    console.log(roleStats);
    console.log('\n前5个用户:');
    users.slice(0, 5).forEach(user => {
      console.log(`- ID: ${user.id}, 用户名: ${user.username}, 角色: ${user.role}, 状态: ${user.status}`);
    });
    console.log('');

    // 3. 检查会员计划表
    console.log('3. 检查会员计划表...');
    const [plans] = await connection.execute('SELECT * FROM membership_plans ORDER BY id');
    console.log(`会员计划总数: ${plans.length}`);
    plans.forEach(plan => {
      console.log(`- ID: ${plan.id}, 名称: ${plan.name}, 价格: ${plan.price}, 状态: ${plan.is_active ? '启用' : '禁用'}`);
    });
    console.log('');

    // 4. 检查会员表数据
    console.log('4. 检查会员表数据...');
    const [memberships] = await connection.execute(`
      SELECT 
        m.id, m.user_id, m.plan_id, m.status, m.start_date, m.end_date, m.created_at,
        u.username, u.role as user_role,
        mp.name as plan_name
      FROM memberships m
      LEFT JOIN users u ON m.user_id = u.id
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      ORDER BY m.created_at DESC
    `);
    console.log(`会员记录总数: ${memberships.length}`);
    
    if (memberships.length > 0) {
      console.log('会员状态分布:');
      const statusStats = {};
      memberships.forEach(membership => {
        statusStats[membership.status] = (statusStats[membership.status] || 0) + 1;
      });
      console.log(statusStats);
      
      console.log('\n最近的会员记录:');
      memberships.slice(0, 10).forEach(membership => {
        console.log(`- ID: ${membership.id}, 用户: ${membership.username}(${membership.user_id}), 计划: ${membership.plan_name}, 状态: ${membership.status}, 开始: ${membership.start_date}, 结束: ${membership.end_date}`);
      });
    } else {
      console.log('⚠️  没有找到任何会员记录！');
    }
    console.log('');

    // 5. 检查订单表中的会员订单
    console.log('5. 检查订单表中的会员订单...');
    const [orders] = await connection.execute(`
      SELECT 
        o.id, o.user_id, o.type, o.target_id, o.payment_status, o.final_amount, o.created_at, o.payment_time,
        u.username
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.type = 'membership'
      ORDER BY o.created_at DESC
      LIMIT 10
    `);
    console.log(`会员订单总数: ${orders.length}`);
    
    if (orders.length > 0) {
      console.log('订单支付状态分布:');
      const paymentStats = {};
      orders.forEach(order => {
        paymentStats[order.payment_status] = (paymentStats[order.payment_status] || 0) + 1;
      });
      console.log(paymentStats);
      
      console.log('\n最近的会员订单:');
      orders.forEach(order => {
        console.log(`- ID: ${order.id}, 用户: ${order.username}(${order.user_id}), 金额: ${order.final_amount}, 支付状态: ${order.payment_status}, 创建时间: ${order.created_at}, 支付时间: ${order.payment_time}`);
      });
    } else {
      console.log('⚠️  没有找到任何会员订单！');
    }
    console.log('');

    // 6. 检查管理员接口查询逻辑
    console.log('6. 测试管理员会员用户查询逻辑...');
    const adminQuery = `
      SELECT
        u.id,
        u.username,
        u.email,
        u.nickname,
        u.role,
        u.status as user_status,
        m.id as membership_id,
        m.status as membership_status,
        m.start_date as membership_start_date,
        m.end_date as membership_end_date,
        m.auto_renew,
        m.created_at as membership_created_at,
        mp.id as plan_id,
        mp.name as plan_name,
        mp.price as plan_price,
        mp.duration_days as plan_duration
      FROM users u
      LEFT JOIN memberships m ON u.id = m.user_id
        AND m.status IN ('active', 'expired', 'cancelled')
        AND m.id = (
          SELECT id FROM memberships m2
          WHERE m2.user_id = u.id
          ORDER BY m2.created_at DESC
          LIMIT 1
        )
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE u.role = 'member'
      ORDER BY m.created_at DESC
      LIMIT 10
    `;
    
    const [adminResults] = await connection.execute(adminQuery);
    console.log(`管理员查询结果数量: ${adminResults.length}`);
    
    if (adminResults.length > 0) {
      console.log('查询结果:');
      adminResults.forEach(result => {
        console.log(`- 用户: ${result.username}(${result.id}), 角色: ${result.role}, 会员状态: ${result.membership_status}, 计划: ${result.plan_name}`);
      });
    } else {
      console.log('⚠️  管理员查询没有返回任何会员用户！');
    }
    console.log('');

    // 7. 分析问题
    console.log('7. 问题分析:');
    
    // 检查是否有已支付的订单但没有对应的会员记录
    const [paidOrdersWithoutMembership] = await connection.execute(`
      SELECT o.*, u.username
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN memberships m ON o.user_id = m.user_id AND o.target_id = m.plan_id
      WHERE o.type = 'membership' 
        AND o.payment_status = 'paid'
        AND m.id IS NULL
    `);
    
    if (paidOrdersWithoutMembership.length > 0) {
      console.log(`⚠️  发现 ${paidOrdersWithoutMembership.length} 个已支付但没有创建会员记录的订单:`);
      paidOrdersWithoutMembership.forEach(order => {
        console.log(`- 订单ID: ${order.id}, 用户: ${order.username}(${order.user_id}), 计划ID: ${order.target_id}, 金额: ${order.final_amount}`);
      });
    } else {
      console.log('✓ 所有已支付的会员订单都有对应的会员记录');
    }
    
    // 检查是否有会员记录但用户角色不是member
    const [membersWithWrongRole] = await connection.execute(`
      SELECT u.id, u.username, u.role, m.status as membership_status
      FROM users u
      INNER JOIN memberships m ON u.id = m.user_id
      WHERE m.status = 'active' AND m.end_date > NOW() AND u.role != 'member'
    `);
    
    if (membersWithWrongRole.length > 0) {
      console.log(`⚠️  发现 ${membersWithWrongRole.length} 个有活跃会员记录但角色不是member的用户:`);
      membersWithWrongRole.forEach(user => {
        console.log(`- 用户: ${user.username}(${user.id}), 当前角色: ${user.role}, 会员状态: ${user.membership_status}`);
      });
    } else {
      console.log('✓ 所有活跃会员的用户角色都正确');
    }

    await connection.end();
    
  } catch (error) {
    console.error('诊断过程中发生错误:', error);
  }
}

// 运行诊断
debugMembershipIssue().then(() => {
  console.log('\n=== 诊断完成 ===');
  process.exit(0);
}).catch(error => {
  console.error('诊断失败:', error);
  process.exit(1);
});
