const { AppError, asyncHandler } = require('../../../middleware/errorHandler');
const { operationLogger } = require('../../../middleware/requestLogger');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const Order = require('../../../database/models/Order');
const User = require('../../../database/models/User');
const Video = require('../../../database/models/Video'); // Import Video model
const MembershipPlan = require('../../../database/models/MembershipPlan');
const Earning = require('../../../database/models/Earning'); // Import Earning model
const BalanceLog = require('../../../database/models/BalanceLog'); // Import BalanceLog model
const paymentServiceFactory = require('../services/PaymentServiceFactory');
const purchaseService = require('../services/purchaseService');
const orderProcessingService = require('../services/orderProcessingService');
const { mysql: db } = require('../../../config/database');
const crypto = require('crypto');
const PaymentGateway = require('../../../database/models/PaymentGateway');
const qrcode = require('qrcode');
const axios = require('axios');

class PaymentController {
  /**
   * @desc    获取所有启用的支付方式
   * @route   GET /api/payment/methods
   * @access  Public
   */
  getAvailableMethods = asyncHandler(async (req, res) => {
    const paymentGatewayModel = new PaymentGateway();
    const gateways = await paymentGatewayModel.findAll({ enabled: true });

    const availableMethods = gateways.map(gateway => ({
      key: gateway.key,
      name: gateway.name
    }));

    res.json({ success: true, data: availableMethods });
  });

  /**
   * 创建支付订单
   */
  createOrder = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { type, targetId, amount, paymentMethod, description } = req.body;

    // 1. 首先，验证支付方式是否受支持
    if (!paymentServiceFactory.isPaymentMethodAvailable(paymentMethod)) {
      throw new AppError('不支持的支付方式', 400, 'UNSUPPORTED_PAYMENT_METHOD');
    }

    // 2. 针对tron_usdt的特殊处理
    if (paymentMethod === 'tron_usdt') {
      return this.createTronOrder(req, res);
    }

    // 3. 对于其他标准支付方式，执行通用逻辑
    // 验证订单类型和目标
    await this.validateOrderTarget(type, targetId, userId);

    // 生成订单号
    const orderNo = this.generateOrderNo();

    // 计算订单金额
    const finalAmount = await this.calculateOrderAmount(type, targetId, amount);

    // 设置订单过期时间（30分钟）
    const expiresAt = new Date(Date.now() + 30 * 60 * 1000);

    // 🔧 使用数据库事务确保数据一致性
    const connection = await db.getConnection();

    try {
      await connection.beginTransaction();

      // 创建订单记录
      const orderId = await Order.createOrder({
        user_id: userId,
        type,
        target_id: targetId,
        amount: finalAmount,
        final_amount: finalAmount,
        payment_method: paymentMethod,
        status: 'pending',
        order_no: orderNo,
        expires_at: expiresAt,
        description
      }, connection);

      // 获取客户端IP
      const clientIp = req.ip || req.connection.remoteAddress || '127.0.0.1';

      // 创建支付订单
      const paymentResult = await paymentServiceFactory.createPayment(paymentMethod, {
        orderNo,
        amount: finalAmount,
        subject: description || this.getOrderSubject(type, targetId),
        clientIp,
        userId,
        tradeType: req.body.tradeType || 'NATIVE',
        paymentMethod: paymentMethod,
      });

      if (!paymentResult.success) {
        // 🔧 支付创建失败时回滚事务
        await connection.rollback();
        throw new AppError('创建支付订单失败', 500, 'PAYMENT_CREATE_FAILED');
      }

      // 🔧 所有操作成功，提交事务
      await connection.commit();

      // 记录操作日志
      operationLogger.logUserOperation(
        req,
        'payment_create',
        orderId,
        '创建支付订单',
        {
          orderNo,
          type,
          amount: finalAmount,
          paymentMethod
        }
      );

      res.status(201).json({
        success: true,
        message: '支付订单创建成功',
        data: {
          order: {
            id: orderId,
            orderNo,
            amount: finalAmount,
            finalAmount,
            paymentStatus: 'pending',
            expiresAt,
            paymentUrl: paymentResult.paymentUrl,
            qrCode: paymentResult.qrCode,
            codeUrl: paymentResult.codeUrl
          }
        }
      });

    } catch (error) {
      // 🔧 发生错误时回滚事务
      if (connection) {
        await connection.rollback();
      }
      logger.error('创建支付订单失败:', error);
      throw new AppError('创建支付订单失败', 500, 'PAYMENT_CREATE_FAILED');
    } finally {
      // 🔧 释放数据库连接
      if (connection) {
        connection.release();
      }
    }
  });

  /**
   * 创建TRON支付订单 (特殊处理)
   */
  createTronOrder = asyncHandler(async (req, res) => {
    // 1. (在事务外) 获取并验证TRON收款配置
    const paymentGatewayModel = new PaymentGateway();
    // 使用全局连接，确保读取到最新提交的数据
    const tronGateway = await paymentGatewayModel.findOne({ key: 'tron_usdt', enabled: true });
    if (!tronGateway || !tronGateway.config) {
      throw new AppError('TRON支付方式当前不可用', 503, 'TRON_GATEWAY_UNAVAILABLE');
    }
    const config = typeof tronGateway.config === 'string' ? JSON.parse(tronGateway.config) : tronGateway.config;
    if (!config.walletAddress) {
      throw new AppError('TRON收款地址未配置', 500, 'TRON_ADDRESS_MISSING');
    }

    const userId = req.user.id;
    const { type, targetId, amount, description } = req.body;
    let connection;

    try {
      // 2. 验证订单目标并计算USDT基础金额
      await this.validateOrderTarget(type, targetId, userId);
      const baseAmountUsdt = await this.calculateOrderAmount(type, targetId, amount);

      // 3. 全新的、更精确的金额生成逻辑
      let finalAmountTrx, finalAmountUsdt;
      let finalAmountTrxInteger, finalAmountUsdtInteger;
      const DECIMALS = 4; // Use 4 decimal places in total

      try {
        const response = await axios.get('https://www.okx.com/api/v5/market/ticker?instId=TRX-USDT');
        if (!response.data || response.data.code !== '0' || !response.data.data || response.data.data.length === 0) {
          throw new Error('Failed to fetch valid rate data from OKX.');
        }
        const rate = parseFloat(response.data.data[0].last);
        if (!(rate > 0)) {
           throw new Error('Invalid exchange rate from OKX.');
        }

        // 步骤 1: 计算 TRX 基础价格
        const baseAmountTrx = baseAmountUsdt / rate;

        // 步骤 2: 生成统一的2位随机整数后缀 (e.g., 10-99)
        const uniqueSuffix = Math.floor(Math.random() * 90) + 10;

        // 步骤 3: 创建“干净”的、只有两位小数的基础金额，然后转换为整数，为后缀留出空间
        const cleanBaseUsdtInt = Math.round(parseFloat(baseAmountUsdt.toFixed(2)) * 100) * 100;
        const cleanBaseTrxInt = Math.round(parseFloat(baseAmountTrx.toFixed(2)) * 100) * 100;
        
        // 步骤 4: 加上统一的后缀，得到最终用于匹配的整数
        finalAmountUsdtInteger = cleanBaseUsdtInt + uniqueSuffix;
        finalAmountTrxInteger = cleanBaseTrxInt + uniqueSuffix;

        // 步骤 5: 从最终的整数计算出用于前端显示的浮点数
        finalAmountUsdt = finalAmountUsdtInteger / Math.pow(10, DECIMALS);
        finalAmountTrx = finalAmountTrxInteger / Math.pow(10, DECIMALS);
        
      } catch (rateError) {
        logger.error('获取TRX/USDT汇率或计算唯一金额失败', { error: rateError.message });
        throw new AppError('无法获取实时汇率，请稍后再试', 503, 'RATE_UNAVAILABLE');
      }


      // 5. 开始事务
      connection = await db.getConnection();
      await connection.beginTransaction();

      const orderNo = this.generateOrderNo();
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30分钟过期

      // 6. 创建订单，同时存储唯一的TRX和USDT金额
      const orderData = {
        user_id: userId,
        type,
        target_id: targetId,
        amount: baseAmountUsdt, // 原始USDT金额
        final_amount: finalAmountTrx, // 用于兼容和显示的TRX金额
        final_amount_usdt: finalAmountUsdt, // 用于兼容和显示的USDT金额
        final_amount_trx_integer: finalAmountTrxInteger, // **新增：唯一的TRX整数金额**
        final_amount_usdt_integer: finalAmountUsdtInteger, // **新增：唯一的USDT整数金额**
        payment_method: 'tron_usdt',
        payment_status: 'pending',
        order_no: orderNo,
        expires_at: expiresAt,
        description: description || this.getOrderSubject(type, targetId)
      };
      const orderId = await Order.createOrder(orderData, connection);

      // 7. 生成支付二维码 (仅包含地址)
      const qrCodeDataURL = await qrcode.toDataURL(config.walletAddress);

      await connection.commit();

      // 8. 返回包含唯一TRX和USDT收款信息的响应
      res.status(201).json({
        success: true,
        message: 'TRON支付订单创建成功，请精确支付所示TRX或USDT金额',
        data: {
          order: {
            id: orderId,
            orderNo,
            amount: baseAmountUsdt, // 原始USDT金额
            finalAmount: finalAmountTrx, // **要求用户支付的、唯一的TRX金额**
            finalAmountUsdt: finalAmountUsdt, // **要求用户支付的、唯一的USDT金额**
            paymentStatus: 'pending',
            expiresAt,
            paymentMethod: 'tron_usdt',
            walletAddress: config.walletAddress,
            qrCode: qrCodeDataURL
          }
        }
      });
    } catch (error) {
      if (connection) await connection.rollback();
      logger.error('创建TRON支付订单失败:', error);
      throw new AppError(error.message || '创建TRON订单时发生错误', error.statusCode || 500, error.errorCode);
    } finally {
      if (connection) connection.release();
    }
  });

  /**
   * 查询订单状态
   */
  queryOrder = asyncHandler(async (req, res) => {
    const { orderNo } = req.params;
    const userId = req.user.id;

    const order = await Order.getOrderByNo(orderNo);
    if (!order) {
      throw new AppError('订单不存在', 404, 'ORDER_NOT_FOUND');
    }

    // 检查权限
    if (order.user_id !== userId && req.user.role !== 'admin') {
      throw new AppError('无权查看此订单', 403, 'INSUFFICIENT_PERMISSIONS');
    }

    // 如果订单状态为pending，查询支付状态
    if (order.payment_status === 'pending') {
      try {
        const paymentResult = await paymentServiceFactory.queryPayment(
          order.payment_method,
          orderNo
        );

        if (paymentResult.success && paymentResult.status === 'paid') {
          // 更新订单状态
          await this.handlePaymentSuccess(order, paymentResult);
          order.payment_status = 'paid';
          order.payment_time = paymentResult.payTime;
          order.transaction_id = paymentResult.transactionId;
        }
      } catch (error) {
        logger.warn('查询支付状态失败:', error);
      }
    }

    res.json({
      success: true,
      data: {
        order: {
          id: order.id,
          orderNo: order.order_no,
          type: order.type,
          amount: order.amount,
          finalAmount: order.final_amount,
          paymentStatus: order.payment_status,
          paymentMethod: order.payment_method,
          paymentTime: order.payment_time,
          expiresAt: order.expires_at,
          createdAt: order.created_at
        }
      }
    });
  });

  /**
   * 获取我的订单列表
   */
  getMyOrders = asyncHandler(async (req, res) => {
    logger.info('[PaymentController] getMyOrders收到请求，查询参数:', req.query); // 添加诊断日志
    const { page = 1, pageSize = 10, type, status } = req.query;

    const options = {
      page: parseInt(page, 10),
      pageSize: parseInt(pageSize, 10),
      type,
      status
    };

    const orders = await Order.getUserOrders(req.user.id, options);
    res.json({ success: true, data: orders });
  });

  /**
   * 支付回调处理
   */
  handleCallback = asyncHandler(async (req, res) => {
    const { provider } = req.params;
    const callbackData = req.body;

    try {
      logger.info('收到支付回调:', { provider, callbackData });

      // 处理支付回调
      const result = await paymentServiceFactory.handleCallback(provider, callbackData);

      if (result.success) {
        // 查找订单
        const order = await Order.getOrderByNo(result.orderNo);
        if (order && order.payment_status === 'pending') {
          await this.handlePaymentSuccess(order, result);
        }
      }

      // 返回成功响应（根据不同支付平台要求）
      if (provider === 'wechat') {
        res.set('Content-Type', 'application/xml');
        res.send('<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>');
      } else if (provider === 'alipay') {
        res.send('success');
      } else {
        res.send('success');
      }

    } catch (error) {
      logger.error('处理支付回调失败:', error);
      res.status(500).send('fail');
    }
  });

  /**
   * 申请退款
   */
  requestRefund = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { orderNo, reason, amount } = req.body;

    const order = await Order.getOrderByNo(orderNo);
    if (!order) {
      throw new AppError('订单不存在', 404, 'ORDER_NOT_FOUND');
    }

    // 检查权限
    if (order.user_id !== userId && req.user.role !== 'admin') {
      throw new AppError('无权操作此订单', 403, 'INSUFFICIENT_PERMISSIONS');
    }

    // 检查订单状态
    if (order.payment_status !== 'paid') {
      throw new AppError('订单未支付，无法退款', 400, 'ORDER_NOT_PAID');
    }

    // 检查退款金额
    const refundAmount = amount || order.final_amount;
    if (refundAmount > order.final_amount) {
      throw new AppError('退款金额不能超过订单金额', 400, 'INVALID_REFUND_AMOUNT');
    }

    try {
      // 申请退款
      const refundResult = await paymentServiceFactory.refund(order.payment_method, {
        orderNo,
        transactionId: order.transaction_id,
        refundAmount,
        totalAmount: order.final_amount,
        reason
      });

      if (refundResult.success) {
        // 更新订单状态
        await Order.updateOrderStatus(orderNo, 'refunded', {
          refund_amount: refundAmount,
          refund_time: new Date()
        });

        // 记录操作日志
        operationLogger.logUserOperation(
          req,
          'payment_refund',
          order.id,
          '申请退款',
          { orderNo, refundAmount, reason }
        );
      }

      res.json({
        success: refundResult.success,
        message: refundResult.success ? '退款申请成功' : '退款申请失败',
        data: {
          refundId: refundResult.refundId,
          message: refundResult.message
        }
      });

    } catch (error) {
      logger.error('申请退款失败:', error);
      throw new AppError('申请退款失败', 500, 'REFUND_FAILED');
    }
  });

  /**
   * 获取支付方式列表
   */
  getPaymentMethods = asyncHandler(async (req, res) => {
    const methods = paymentServiceFactory.getAvailablePaymentMethods();

    res.json({
      success: true,
      data: {
        methods
      }
    });
  });

  /**
   * 获取支付统计
   */
  getPaymentStats = asyncHandler(async (req, res) => {
    const { startDate, endDate } = req.query;

    const stats = await Order.getOrderStats(
      startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate || new Date()
    );

    res.json({
      success: true,
      data: { stats }
    });
  });

  /**
   * @desc    为购买视频创建订单
   * @route   POST /api/payment/orders/video
   * @access  Private
   */
  createVideoOrder = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { videoId } = req.body;

    if (!videoId) {
      throw new AppError('视频ID不能为空', 400, 'MISSING_VIDEO_ID');
    }

    const newOrder = await purchaseService.createVideoOrder(userId, videoId);
    
    res.status(201).json({
      success: true,
      message: '视频购买订单创建成功',
      data: newOrder,
    });
  });

  /**
   * @desc    使用余额支付订单
   * @route   POST /api/payment/orders/:orderId/pay
   * @access  Private
   */
  payForOrderWithBalance = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { orderId } = req.params;

    const result = await purchaseService.payForOrderWithBalance(userId, orderId);

    res.status(200).json({
      success: true,
      message: '订单支付成功',
      data: result,
    });
  });

  /**
   * 支付测试接口
   */
  test = asyncHandler(async (req, res) => {
    const status = paymentServiceFactory.getStatus();

    res.json({
      success: true,
      message: '支付模块测试接口',
      data: {
        module: 'payment',
        status,
        user: req.user || null
      }
    });
  });

  /**
   * @desc    为待处理的会员订单重新生成支付信息
   * @route   POST /api/payment/orders/:orderNo/regenerate-payment
   * @access  Private
   */
  regeneratePaymentInfo = asyncHandler(async (req, res) => {
    const { orderNo } = req.params;
    const userId = req.user.id;

    const order = await Order.getOrderByNo(orderNo);

    // 1. 验证订单
    if (!order) {
      throw new AppError('订单不存在', 404, 'ORDER_NOT_FOUND');
    }
    if (order.user_id !== userId) {
      throw new AppError('无权操作此订单', 403, 'FORBIDDEN');
    }
    if (order.payment_status !== 'pending') {
      throw new AppError('订单状态不正确，无法重新支付', 400, 'INVALID_ORDER_STATUS');
    }
    if (order.type !== 'membership' || order.payment_method !== 'tron_usdt') {
      throw new AppError('订单类型或支付方式不正确', 400, 'INVALID_ORDER_TYPE_OR_METHOD');
    }

    // 2. 获取TRON收款配置 (copied from createTronOrder)
    const paymentGatewayModel = new PaymentGateway();
    const tronGateway = await paymentGatewayModel.findOne({ key: 'tron_usdt', enabled: true });
    if (!tronGateway || !tronGateway.config) {
      throw new AppError('TRON支付方式当前不可用', 503, 'TRON_GATEWAY_UNAVAILABLE');
    }
    const config = typeof tronGateway.config === 'string' ? JSON.parse(tronGateway.config) : tronGateway.config;
    if (!config.walletAddress) {
      throw new AppError('TRON收款地址未配置', 500, 'TRON_ADDRESS_MISSING');
    }

    // 3. 直接使用订单中已存的金额 (REMOVED exchange rate logic)
    const finalAmountTrx = order.final_amount;
    const baseAmountUsdt = order.final_amount_usdt;

    // 4. 生成新的支付二维码
    const qrCodeDataURL = await qrcode.toDataURL(config.walletAddress);

    // 5. 返回新生成的支付信息给前端 (we don't update the DB)
    res.json({
      success: true,
      data: {
        orderNo: order.order_no,
        walletAddress: config.walletAddress,
        finalAmount: finalAmountTrx, // Use the original TRX amount from the order
        finalAmountUsdt: baseAmountUsdt, // Use the original USDT amount from the order
        qrCodeUrl: qrCodeDataURL,
      },
    });
  });

  // 私有方法

  /**
   * 生成订单号
   */
  generateOrderNo() {
    const timestamp = Date.now();
    const random = crypto.randomBytes(4).toString('hex').toUpperCase();
    return `ORDER_${timestamp}_${random}`;
  }

  /**
   * 验证订单目标
   */
  async validateOrderTarget(type, targetId, userId, connection = null) {
    switch (type) {
      case 'membership':
        if (!targetId) {
          throw new AppError('会员计划ID不能为空', 400, 'MISSING_TARGET_ID');
        }
        const plan = await MembershipPlan.findById(targetId, connection);
        if (!plan || !plan.is_active) {
          throw new AppError('会员计划不存在或已停用', 404, 'PLAN_NOT_FOUND');
        }
        break;
      case 'video':
        // 视频付费逻辑
        break;
      case 'recharge':
        // 充值逻辑, 不需要额外的目标验证
        break;
      default:
        throw new AppError('不支持的订单类型', 400, 'INVALID_ORDER_TYPE');
    }
  }

  /**
   * 计算订单金额
   */
  async calculateOrderAmount(type, targetId, amount, connection = null) {
    switch (type) {
      case 'membership':
        const plan = await MembershipPlan.findById(targetId, connection);
        if (!plan || !plan.price) {
          throw new AppError('会员计划价格未定义', 500, 'PLAN_PRICE_UNDEFINED');
        }
        return parseFloat(plan.price);
      case 'recharge':
        const rechargeAmount = parseFloat(amount);
        if (isNaN(rechargeAmount) || rechargeAmount <= 0) {
          throw new AppError('无效的充值金额', 400, 'INVALID_RECHARGE_AMOUNT');
        }
        return rechargeAmount;
      default:
        const defaultAmount = parseFloat(amount);
        if (isNaN(defaultAmount)) {
          throw new AppError('无效的订单金额', 400, 'INVALID_ORDER_AMOUNT');
        }
        return defaultAmount;
    }
  }

  /**
   * 获取订单标题
   */
  getOrderSubject(type, targetId) {
    switch (type) {
      case 'membership':
        return '会员订阅';
      case 'video':
        return '视频购买';
      case 'recharge':
        return '账户充值';
      default:
        return '订单支付';
    }
  }

  /**
   * 处理支付成功
   */
  async handlePaymentSuccess(order, paymentResult) {
    const connection = await db.getConnection();
    await connection.beginTransaction();

    try {
      // 更新订单状态
      await Order.updateOrderStatus(order.order_no, 'paid', {
        transaction_id: paymentResult.transactionId,
        payment_time: new Date(paymentResult.payTime || Date.now())
      }, connection);

      // 根据订单类型处理不同的业务逻辑
      if (order.type === 'membership' || order.type === 'recharge' || order.type === 'video') {
        await orderProcessingService.processOrderBusiness(order, connection);
      }
      
      // 如果是余额支付，需要额外扣除用户余额并记录日志
      if (order.payment_method === 'balance') {
        const rechargeAmount = parseFloat(order.final_amount);
        if (isNaN(rechargeAmount) || rechargeAmount <= 0) {
          throw new AppError('无效的充值金额', 400, 'INVALID_RECHARGE_AMOUNT');
        }
        const updateRechargeBalanceSql = 'UPDATE users SET balance = balance + ? WHERE id = ?';
        await connection.query(updateRechargeBalanceSql, [rechargeAmount, order.user_id]);
        logger.info(`用户(ID: ${order.user_id}) 充值成功: ${rechargeAmount}元`);
      }

      // 提交事务
      await connection.commit();

      // 支付成功后，主动清除相关缓存以确保数据实时性
      try {
        // 1. 清除仪表板统计缓存
        const adminStatsKey = cache.generateKey(CACHE_KEYS.ADMIN, 'dashboard_stats');
        await cache.del(adminStatsKey);
        logger.info(`[Cache] 仪表板缓存已清除: ${adminStatsKey}`);

        // 2. 清除会员管理概览缓存
        const membershipOverviewKey = cache.generateKey(CACHE_KEYS.ADMIN, 'membership_overview');
        await cache.del(membershipOverviewKey);
        logger.info(`[Cache] 会员管理概览缓存已清除: ${membershipOverviewKey}`);

        // 3. 清除支付页面相关统计缓存 (假设键名为 'payment_stats')
        const paymentStatsKey = cache.generateKey(CACHE_KEYS.PAYMENT, 'stats');
        await cache.del(paymentStatsKey);
        logger.info(`[Cache] 支付统计缓存已清除: ${paymentStatsKey}`);

        // 4. 清除用户的会员信息缓存
        const userMembershipKey = cache.generateKey(CACHE_KEYS.MEMBERSHIP, 'user', order.user_id);
        // 使用模式匹配删除该用户所有会员相关缓存
        await cache.delPattern(`${userMembershipKey}:*`);
        logger.info(`[Cache] 用户 ${order.user_id} 的会员缓存已清除`);

        // 5. 新增：清除用户个人资料缓存，以确保角色等信息能立即更新
        const userProfileKey = cache.generateKey(CACHE_KEYS.USER, 'profile', order.user_id);
        await cache.del(userProfileKey);
        logger.info(`[Cache] 用户 ${order.user_id} 的个人资料缓存已清除: ${userProfileKey}`);

      } catch (cacheError) {
        logger.error('清除缓存时发生错误，但这不影响支付结果:', cacheError);
      }

    } catch (error) {
      await connection.rollback();
      logger.error('处理支付成功失败:', {
        error: error.message,
        stack: error.stack,
        orderNo: order.order_no,
        userId: order.user_id,
        orderType: order.type,
        targetId: order.target_id,
        paymentMethod: order.payment_method,
        transactionId: order.transaction_id
      });

      // 记录到专门的支付错误表或发送告警
      // TODO: 实现支付失败重试机制或人工处理队列

      // 注意：这里不向上抛出错误，因为回调需要成功响应给支付网关
      // 但我们需要记录这个严重错误，并可能需要人工介入处理
    } finally {
      connection.release();
    }
  }
}

module.exports = new PaymentController();
