const BaseModel = require('../BaseModel');

class PaymentGateway extends BaseModel {
  constructor() {
    super('payment_gateways');
  }

  /**
   * 获取所有启用的支付通道及其配置
   * @returns {Promise<Array>}
   */
  async getEnabledGateways() {
    return await this.findAll({ enabled: true });
  }

  /**
   * 根据key获取支付通道
   * @param {string} key
   * @returns {Promise<Object|null>}
   */
  async findByKey(key) {
    return await this.findOne({ key });
  }

  /**
   * 更新支付通道配置
   * @param {number} id
   * @param {boolean} enabled
   * @param {Object} config
   * @returns {Promise<Object|null>}
   */
  async updateGateway(id, { enabled, config }) {
    const updateData = {};
    if (enabled !== undefined) {
      updateData.enabled = enabled;
    }
    if (config) {
      updateData.config = JSON.stringify(config); // 确保config是JSON字符串
    }

    if (Object.keys(updateData).length === 0) {
      const gateway = await this.findById(id);
      if (gateway && typeof gateway.config === 'string') {
        try {
          gateway.config = JSON.parse(gateway.config);
        } catch (e) {
          // ignore parsing error, return raw
        }
      }
      return gateway;
    }

    const updatedGateway = await this.update(id, updateData);
    
    // update方法返回的记录也需要解析config
    if (updatedGateway && typeof updatedGateway.config === 'string') {
      try {
        updatedGateway.config = JSON.parse(updatedGateway.config);
      } catch (e) {
        // ignore parsing error, return raw
      }
    }
    
    return updatedGateway;
  }
}

module.exports = PaymentGateway; 