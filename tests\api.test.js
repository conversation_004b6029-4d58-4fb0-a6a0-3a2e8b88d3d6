const request = require('supertest');
const app = require('../app');
const { expect } = require('chai');

describe('API测试套件', () => {
  let authToken;
  let adminToken;
  let testUserId;
  let testVideoId;

  // 测试数据
  const testUser = {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123',
    nickname: '测试用户'
  };

  const testAdmin = {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    nickname: '管理员'
  };

  before(async () => {
    console.log('开始API测试...');
  });

  after(async () => {
    console.log('API测试完成');
  });

  describe('认证模块测试', () => {
    it('应该成功注册新用户', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send(testUser)
        .expect(201);

      expect(response.body.success).to.be.true;
      expect(response.body.data.user.email).to.equal(testUser.email);
      expect(response.body.data.tokens.accessToken).to.exist;
      
      authToken = response.body.data.tokens.accessToken;
      testUserId = response.body.data.user.id;
    });

    it('应该拒绝重复邮箱注册', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send(testUser)
        .expect(409);

      expect(response.body.success).to.be.false;
      expect(response.body.code).to.equal('EMAIL_EXISTS');
    });

    it('应该成功登录', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.tokens.accessToken).to.exist;
    });

    it('应该拒绝错误密码登录', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.success).to.be.false;
    });
  });

  describe('用户模块测试', () => {
    it('应该获取用户资料', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.user.email).to.equal(testUser.email);
    });

    it('应该更新用户资料', async () => {
      const updateData = {
        nickname: '更新的昵称',
        bio: '这是我的个人简介'
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.user.nickname).to.equal(updateData.nickname);
    });

    it('应该拒绝未认证的用户资料访问', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .expect(401);

      expect(response.body.success).to.be.false;
    });
  });

  describe('视频模块测试', () => {
    it('应该获取视频列表', async () => {
      const response = await request(app)
        .get('/api/videos/list')
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.data).to.be.an('array');
      expect(response.body.data.pagination).to.exist;
    });

    it('应该支持视频搜索', async () => {
      const response = await request(app)
        .get('/api/videos/search?keyword=测试')
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.data).to.be.an('array');
    });

    it('应该获取热门视频', async () => {
      const response = await request(app)
        .get('/api/videos/popular')
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.videos).to.be.an('array');
    });

    it('应该拒绝非管理员上传视频', async () => {
      const response = await request(app)
        .post('/api/videos/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.success).to.be.false;
    });
  });

  describe('互动模块测试', () => {
    it('应该获取视频评论', async () => {
      const response = await request(app)
        .get('/api/interactions/videos/1/comments')
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.data).to.be.an('array');
    });

    it('应该要求认证才能发表评论', async () => {
      const response = await request(app)
        .post('/api/interactions/comments')
        .send({
          videoId: 1,
          content: '测试评论'
        })
        .expect(401);

      expect(response.body.success).to.be.false;
    });

    it('应该要求认证才能点赞', async () => {
      const response = await request(app)
        .post('/api/interactions/likes')
        .send({
          targetId: 1,
          targetType: 'video'
        })
        .expect(401);

      expect(response.body.success).to.be.false;
    });
  });

  describe('会员模块测试', () => {
    it('应该获取会员计划列表', async () => {
      const response = await request(app)
        .get('/api/members/plans')
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.plans).to.be.an('array');
    });

    it('应该要求认证才能查看我的会员信息', async () => {
      const response = await request(app)
        .get('/api/members/my-membership')
        .expect(401);

      expect(response.body.success).to.be.false;
    });

    it('应该获取我的会员信息（已认证）', async () => {
      const response = await request(app)
        .get('/api/members/my-membership')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).to.be.true;
      expect(response.body.data.level).to.exist;
    });
  });

  describe('管理模块测试', () => {
    it('应该拒绝非管理员访问管理接口', async () => {
      const response = await request(app)
        .get('/api/admin/dashboard/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.success).to.be.false;
    });

    // 注意：这里需要管理员token才能测试管理接口
    // 在实际测试中，需要先创建管理员账户或使用测试数据库
  });

  describe('错误处理测试', () => {
    it('应该返回404对于不存在的路由', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .expect(404);

      expect(response.body.success).to.be.false;
    });

    it('应该处理无效的JSON数据', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      expect(response.body.success).to.be.false;
    });

    it('应该处理缺少必需字段的请求', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>'
          // 缺少password字段
        })
        .expect(400);

      expect(response.body.success).to.be.false;
    });
  });

  describe('限流测试', () => {
    it('应该在过多请求时触发限流', async function() {
      this.timeout(10000); // 增加超时时间

      const requests = [];
      for (let i = 0; i < 20; i++) {
        requests.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'wrongpassword'
            })
        );
      }

      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      
      expect(rateLimitedResponses.length).to.be.greaterThan(0);
    });
  });

  describe('数据验证测试', () => {
    it('应该验证邮箱格式', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser2',
          email: 'invalid-email',
          password: 'password123'
        })
        .expect(400);

      expect(response.body.success).to.be.false;
    });

    it('应该验证密码长度', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser3',
          email: '<EMAIL>',
          password: '123' // 太短
        })
        .expect(400);

      expect(response.body.success).to.be.false;
    });

    it('应该验证用户名长度', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'ab', // 太短
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(400);

      expect(response.body.success).to.be.false;
    });
  });
});
