const os = require('os');
const disk = require('diskusage');
const fs = require('fs').promises;
const path = require('path');
const { cache, CACHE_KEYS } = require('../../../utils/cache');
const logger = require('../../../utils/logger');
const connectionManager = require('../../../database/ConnectionManager');
const { projectRoot } = require('../../../utils/pathResolver');

class MonitorService {
  constructor() {
    this.startTime = Date.now();
    this.metrics = {
      requests: 0,
      errors: 0,
      responseTime: []
    };
  }

  // 获取系统信息
  async getSystemInfo() {
    const cacheKey = cache.generateKey(CACHE_KEYS.ADMIN, 'system_info');
    let systemInfo = await cache.get(cacheKey);

    if (!systemInfo) {
      systemInfo = {
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        uptime: process.uptime(),
        hostname: os.hostname(),
        cpus: os.cpus().length,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        loadAverage: os.loadavg(),
        networkInterfaces: Object.keys(os.networkInterfaces()),
        timestamp: new Date()
      };

      await cache.set(cacheKey, systemInfo, 60); // 1分钟缓存
    }

    return systemInfo;
  }

  // 获取进程信息
  getProcessInfo() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      pid: process.pid,
      uptime: process.uptime(),
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      versions: process.versions
    };
  }

  // 获取数据库状态
  async getDatabaseStatus() {
    try {
      const stats = await connectionManager.getDatabaseStats();
      const poolStatus = connectionManager.getPoolStatus();

      return {
        status: 'connected',
        stats,
        pool: poolStatus,
        lastCheck: new Date()
      };
    } catch (error) {
      logger.error('获取数据库状态失败:', error);
      return {
        status: 'error',
        error: error.message,
        lastCheck: new Date()
      };
    }
  }

  // 获取Redis状态
  async getRedisStatus() {
    try {
      const info = await cache.redis.info();
      const dbSize = await cache.redis.dbsize();
      const memory = await cache.redis.memory('usage');

      return {
        status: 'connected',
        info: this.parseRedisInfo(info),
        dbSize,
        memoryUsage: memory,
        lastCheck: new Date()
      };
    } catch (error) {
      logger.error('获取Redis状态失败:', error);
      return {
        status: 'error',
        error: error.message,
        lastCheck: new Date()
      };
    }
  }

  // 解析Redis信息
  parseRedisInfo(info) {
    const lines = info.split('\r\n');
    const parsed = {};
    let section = '';

    for (const line of lines) {
      if (line.startsWith('#')) {
        section = line.substring(2).toLowerCase();
        parsed[section] = {};
      } else if (line.includes(':')) {
        const [key, value] = line.split(':');
        if (section) {
          parsed[section][key] = isNaN(value) ? value : Number(value);
        }
      }
    }

    return parsed;
  }

  // 获取磁盘使用情况
  async getDiskUsage() {
    try {
      const checkPath = os.platform() === 'win32' ? projectRoot.split(path.sep)[0] + path.sep : '/';
      const diskUsage = await disk.check(checkPath);
      const formattedUsage = this.formatBytes(diskUsage.total);

      return {
        total: formattedUsage,
        used: this.formatBytes(diskUsage.used),
        free: this.formatBytes(diskUsage.free),
        percentage: diskUsage.percentage,
        lastCheck: new Date()
      };
    } catch (error) {
      logger.error('获取磁盘使用情况失败:', error);
      return {
        error: error.message,
        lastCheck: new Date()
      };
    }
  }

  // 格式化字节
  formatBytes(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }

  // 获取应用指标
  getApplicationMetrics() {
    const avgResponseTime = this.metrics.responseTime.length > 0 
      ? this.metrics.responseTime.reduce((a, b) => a + b, 0) / this.metrics.responseTime.length 
      : 0;

    return {
      requests: this.metrics.requests,
      errors: this.metrics.errors,
      errorRate: this.metrics.requests > 0 ? (this.metrics.errors / this.metrics.requests * 100) : 0,
      avgResponseTime: Math.round(avgResponseTime * 100) / 100,
      uptime: Date.now() - this.startTime,
      lastReset: new Date(this.startTime)
    };
  }

  // 记录请求指标
  recordRequest(responseTime, isError = false) {
    this.metrics.requests++;
    if (isError) {
      this.metrics.errors++;
    }
    
    this.metrics.responseTime.push(responseTime);
    
    // 保持最近1000个响应时间记录
    if (this.metrics.responseTime.length > 1000) {
      this.metrics.responseTime = this.metrics.responseTime.slice(-1000);
    }
  }

  // 重置指标
  resetMetrics() {
    this.metrics = {
      requests: 0,
      errors: 0,
      responseTime: []
    };
    this.startTime = Date.now();
  }

  // 获取健康检查结果
  async getHealthCheck() {
    const checks = {
      database: { status: 'unknown' },
      redis: { status: 'unknown' },
      memory: { status: 'unknown' },
      disk: { status: 'unknown' }
    };

    try {
      // 数据库检查
      const dbStatus = await this.getDatabaseStatus();
      checks.database = {
        status: dbStatus.status === 'connected' ? 'healthy' : 'unhealthy',
        details: dbStatus
      };

      // Redis检查
      const redisStatus = await this.getRedisStatus();
      checks.redis = {
        status: redisStatus.status === 'connected' ? 'healthy' : 'unhealthy',
        details: redisStatus
      };

      // 内存检查
      const memoryUsage = process.memoryUsage();
      const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
      checks.memory = {
        status: memoryUsagePercent < 90 ? 'healthy' : 'warning',
        usage: memoryUsagePercent,
        details: memoryUsage
      };

      // 磁盘检查
      const diskUsage = await this.getDiskUsage();
      checks.disk = {
        status: 'healthy', // 简化处理
        details: diskUsage
      };

    } catch (error) {
      logger.error('健康检查失败:', error);
    }

    const overallStatus = Object.values(checks).every(check => 
      check.status === 'healthy'
    ) ? 'healthy' : 'degraded';

    return {
      status: overallStatus,
      checks,
      timestamp: new Date()
    };
  }

  // 获取性能报告
  async getPerformanceReport() {
    const [systemInfo, processInfo, dbStatus, redisStatus, appMetrics] = await Promise.all([
      this.getSystemInfo(),
      this.getProcessInfo(),
      this.getDatabaseStatus(),
      this.getRedisStatus(),
      this.getApplicationMetrics()
    ]);

    return {
      system: systemInfo,
      process: processInfo,
      database: dbStatus,
      redis: redisStatus,
      application: appMetrics,
      timestamp: new Date()
    };
  }

  // 获取实时指标
  async getRealTimeMetrics() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const loadAverage = os.loadavg();

    return {
      memory: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        rss: memoryUsage.rss,
        external: memoryUsage.external
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
        loadAverage: loadAverage[0] // 1分钟平均负载
      },
      uptime: process.uptime(),
      timestamp: Date.now()
    };
  }

  // 启动监控
  startMonitoring() {
    // 每分钟记录一次系统指标
    setInterval(async () => {
      try {
        const metrics = await this.getRealTimeMetrics();
        logger.debug('系统指标:', metrics);
      } catch (error) {
        logger.error('记录系统指标失败:', error);
      }
    }, 60000);

    logger.info('系统监控已启动');
  }

  // 停止监控
  stopMonitoring() {
    // 清理定时器等资源
    logger.info('系统监控已停止');
  }
}

// 创建监控服务实例
const monitorService = new MonitorService();

// 启动监控
monitorService.startMonitoring();

module.exports = monitorService;
