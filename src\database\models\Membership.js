const BaseModel = require('../BaseModel');
const { AppError } = require('../../middleware/errorHandler');
const logger = require('../../utils/logger');

class Membership extends BaseModel {
  constructor() {
    super('memberships');
  }

  // 创建会员记录
  async createMembership(membershipData) {
    const {
      userId,
      planId,
      planName,
      startDate = new Date(),
      endDate,
      status = 'active',
      paymentMethod,
      transactionId
    } = membershipData;

    // 如果没有传入 planName，尝试从数据库获取
    let finalPlanName = planName;
    if (!finalPlanName && planId) {
      try {
        const planQuery = membershipData.connection ? 
          await membershipData.connection.execute('SELECT name FROM membership_plans WHERE id = ?', [planId]) :
          await this.query('SELECT name FROM membership_plans WHERE id = ?', [planId]);
        
        const planResult = membershipData.connection ? planQuery[0] : planQuery;
        if (planResult && planResult.length > 0) {
          finalPlanName = planResult[0].name;
        }
      } catch (error) {
        logger.warn(`获取计划名称失败，使用默认值: ${error.message}`);
        finalPlanName = 'Unknown Plan';
      }
    }

    // 如果仍然没有 planName，使用默认值
    if (!finalPlanName) {
      finalPlanName = 'Unknown Plan';
    }

    const membership = await this.create({
      user_id: userId,
      plan_id: planId,
      plan_name: finalPlanName,
      start_date: startDate,
      end_date: endDate,
      status,
      payment_method: paymentMethod,
      transaction_id: transactionId,
      auto_renew: false
    }, membershipData.connection);

    logger.info(`会员记录创建成功: ${membership.id}`, { userId, planId, planName: finalPlanName });
    return membership;
  }

  // 获取用户当前会员信息
  async getUserMembership(userId, connection = null) {
    const sql = `
      SELECT 
        m.*,
        mp.name as plan_name,
        mp.price,
        mp.duration_days,
        mp.features,
        mp.max_video_uploads,
        mp.max_storage_gb
      FROM memberships m
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.user_id = ? 
        AND m.status = 'active'
        AND m.end_date > NOW()
      ORDER BY m.end_date DESC
      LIMIT 1
    `;

    let result;
    if (connection) {
      // 在现有事务中执行查询
      [result] = await connection.execute(sql, [userId]);
    } else {
      // 使用标准连接执行查询
      result = await this.query(sql, [userId]);
    }

    if (result.length === 0) {
      return null;
    }

    const membership = result[0];
    
    // 解析特性
    if (membership.features) {
      try {
        membership.features = JSON.parse(membership.features);
      } catch (error) {
        membership.features = [];
      }
    }

    return membership;
  }

  // 检查用户是否为会员
  async isUserMember(userId) {
    const membership = await this.getUserMembership(userId);
    return !!membership;
  }

  // 检查用户会员等级（简化版：只区分免费和会员）
  async getUserMembershipLevel(userId) {
    const membership = await this.getUserMembership(userId);
    if (!membership) {
      return 'free';
    }

    // 所有付费用户都是会员，不再区分等级
    return 'member';
  }

  // 授予会员权限（新用户开通会员或续费）
  async grantMembership(userId, plan, connection = null) {
    const logger = require('../../utils/logger');

    logger.info(`[Membership] grantMembership开始，用户ID: ${userId}, 计划: ${JSON.stringify(plan)}`);

    // 如果传入的是计划对象，直接使用；如果是ID，需要查询
    let planInfo;
    if (typeof plan === 'object' && plan.id) {
      planInfo = plan;
      logger.info(`[Membership] 使用传入的计划对象: ${planInfo.name}`);
    } else {
      // 兼容传入planId的情况
      const planId = typeof plan === 'object' ? plan.id : plan;
      logger.info(`[Membership] 需要查询计划信息，计划ID: ${planId}`);

      if (connection) {
        const [planResult] = await connection.execute('SELECT * FROM membership_plans WHERE id = ?', [planId]);
        planInfo = planResult[0];
      } else {
        const planResult = await this.query('SELECT * FROM membership_plans WHERE id = ?', [planId]);
        planInfo = planResult[0];
      }

      logger.info(`[Membership] 计划查询结果: ${planInfo ? planInfo.name : '未找到'}`);
    }

    if (!planInfo) {
      logger.error(`[Membership] 会员计划不存在，计划信息: ${JSON.stringify(plan)}`);
      throw new AppError('会员计划不存在', 404, 'PLAN_NOT_FOUND');
    }

    logger.info(`[Membership] 开始调用renewMembership，用户ID: ${userId}, 计划ID: ${planInfo.id}`);

    // 调用renewMembership来处理实际的会员创建逻辑
    const result = await this.renewMembership(userId, planInfo.id, {
      method: 'system',
      transactionId: `grant_${Date.now()}_${userId}`
    }, connection);

    logger.info(`[Membership] grantMembership完成，结果: ${JSON.stringify(result)}`);

    return result;
  }

  // 续费会员
  async renewMembership(userId, planId, paymentData, connection = null) {
    // 获取计划信息
    let plan;
    if (connection) {
      const [planResult] = await connection.execute('SELECT * FROM membership_plans WHERE id = ?', [planId]);
      plan = planResult;
    } else {
      plan = await this.query('SELECT * FROM membership_plans WHERE id = ?', [planId]);
    }

    if (plan.length === 0) {
      throw new AppError('会员计划不存在', 404, 'PLAN_NOT_FOUND');
    }

    // 获取当前会员信息（移除FOR UPDATE锁以避免死锁）
    let currentMembershipResult;
    if (connection) {
      const [result] = await connection.execute(
        `SELECT * FROM memberships WHERE user_id = ? AND status = 'active' AND end_date > NOW() ORDER BY end_date DESC LIMIT 1`,
        [userId]
      );
      currentMembershipResult = result;
    } else {
      currentMembershipResult = await this.query(
        `SELECT * FROM memberships WHERE user_id = ? AND status = 'active' AND end_date > NOW() ORDER BY end_date DESC LIMIT 1`,
        [userId]
      );
    }
    const currentMembership = currentMembershipResult[0] || null;

    const planInfo = plan[0];
    const durationDays = planInfo.duration_days;

    let startDate, endDate;
    
    if (currentMembership && new Date(currentMembership.end_date) > new Date()) {
      // 如果当前会员还未过期，从过期日期开始计算
      startDate = new Date(currentMembership.end_date);
      endDate = new Date(startDate.getTime() + durationDays * 24 * 60 * 60 * 1000);
    } else {
      // 如果没有会员或已过期，从现在开始计算
      startDate = new Date();
      endDate = new Date(startDate.getTime() + durationDays * 24 * 60 * 60 * 1000);
    }

    // 创建新的会员记录
    const newMembership = await this.createMembership({
      userId,
      planId,
      planName: planInfo.name,
      startDate,
      endDate,
      paymentMethod: paymentData.method,
      transactionId: paymentData.transactionId,
      connection
    });

    // 如果有当前会员，将其设为过期
    if (currentMembership) {
      await this.update(currentMembership.id, { status: 'expired' }, connection);
    }

    // 同步用户角色
    try {
      const syncResult = await this.syncUserRole(userId, connection);
      logger.info(`[Membership] renewMembership 角色同步结果:`, syncResult);
    } catch (error) {
      logger.error(`[Membership] renewMembership 角色同步失败，但会员记录已创建:`, {
        userId,
        planId,
        error: error.message
      });
      // 注意：这里不抛出错误，因为会员记录已经创建成功
      // 但我们记录这个错误，可能需要后续手动处理
    }

    logger.info(`会员续费成功: ${userId}`, { planId, endDate });
    return newMembership;
  }

  // 取消会员
  async cancelMembership(userId, reason = null) {
    const membership = await this.getUserMembership(userId);
    if (!membership) {
      throw new AppError('用户不是会员', 404, 'NOT_A_MEMBER');
    }

    await this.update(membership.id, {
      status: 'cancelled',
      cancelled_at: new Date(),
      cancel_reason: reason,
      auto_renew: false
    });

    // 同步用户角色（取消会员后角色变回普通用户）
    try {
      const syncResult = await this.syncUserRole(userId);
      logger.info(`[Membership] cancelMembership 角色同步结果:`, syncResult);
    } catch (error) {
      logger.error(`[Membership] cancelMembership 角色同步失败:`, {
        userId,
        membershipId: membership.id,
        error: error.message
      });
      // 取消会员时角色同步失败是比较严重的问题，但不影响取消操作本身
    }

    logger.info(`会员取消成功: ${userId}`, { membershipId: membership.id, reason });
    return true;
  }

  // 获取会员历史记录
  async getMembershipHistory(userId, options = {}) {
    const {
      page = 1,
      pageSize = 20
    } = options;

    const sql = `
      SELECT 
        m.*,
        mp.name as plan_name,
        mp.price,
        mp.duration_days
      FROM memberships m
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.user_id = ?
      ORDER BY m.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const offset = (page - 1) * pageSize;
    const memberships = await this.query(sql, [userId, pageSize, offset]);

    // 获取总数
    const countResult = await this.query(
      'SELECT COUNT(*) as total FROM memberships WHERE user_id = ?',
      [userId]
    );
    const total = countResult[0].total;

    return {
      data: memberships,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  // 检查会员权限（简化版：所有会员享受相同权益）
  async checkMembershipPermission(userId, permission) {
    const membership = await this.getUserMembership(userId);
    if (!membership) {
      return false;
    }

    // 检查特性权限
    if (membership.features && membership.features.includes(permission)) {
      return true;
    }

    // 所有会员享受统一的基础权限
    const memberPermissions = {
      'watch_member_videos': true,
      'download_videos': true,
      'ad_free': true,
      'priority_support': true,
      'high_quality': true
    };

    return memberPermissions[permission] || false;
  }

  // 同步用户角色（简化版：只区分user和member）
  async syncUserRole(userId, connection = null) {
    const User = require('./User');
    const { cache, CACHE_KEYS } = require('../../utils/cache');

    try {
      logger.info(`[Membership] 开始同步用户角色，用户ID: ${userId}`);

      // 检查用户当前是否为会员, 必须在事务中进行
      const isMember = await this.getUserMembership(userId, connection);
      const targetRole = isMember ? 'member' : 'user';

      logger.info(`[Membership] 用户 ${userId} 会员状态检查: ${isMember ? '是会员' : '非会员'}, 目标角色: ${targetRole}`);

      // 获取用户当前角色 (使用事务连接或普通连接)
      let currentUser;
      if (connection) {
        const [rows] = await connection.execute('SELECT role FROM users WHERE id = ?', [userId]);
        currentUser = rows[0];
      } else {
        currentUser = await User.findById(userId);
      }

      if (!currentUser) {
        logger.error(`[Membership] 用户不存在: ${userId}`);
        throw new Error(`用户不存在: ${userId}`);
      }

      const currentRole = currentUser.role;
      logger.info(`[Membership] 用户 ${userId} 当前角色: ${currentRole}, 目标角色: ${targetRole}`);

      // 核心修复：如果用户是管理员，则不进行任何角色同步
      if (currentRole === 'admin') {
        logger.info(`[Membership] 用户 ${userId} 是管理员，跳过角色同步。`);
        return {
          success: true,
          changed: false,
          oldRole: currentRole,
          newRole: currentRole,
          userId: userId
        };
      }

      // 只有在角色不同时才更新
      if (currentRole !== targetRole) {
        logger.info(`[Membership] 开始更新用户 ${userId} 角色: ${currentRole} -> ${targetRole}`, {
          userId,
          currentRole,
          targetRole,
          isMember: !!isMember,
          hasConnection: !!connection
        });

        try {
          // 在事务中更新用户角色
          const updateResult = await User.update(userId, { role: targetRole }, connection);

          if (!updateResult) {
            throw new Error(`用户角色更新失败：用户 ${userId} 不存在或更新无效`);
          }

          logger.info(`[Membership] 用户角色数据库更新成功: ${userId}`, {
            updateResult: updateResult ? 'success' : 'failed',
            oldRole: currentRole,
            newRole: targetRole
          });

          // 清除用户相关缓存
          const userCacheKey = cache.generateKey(CACHE_KEYS.USER, 'profile', userId);
          await cache.del(userCacheKey);

          logger.info(`[Membership] 用户角色同步完全成功: ${userId}`, {
            oldRole: currentRole,
            newRole: targetRole,
            isMember: !!isMember,
            cacheCleared: true
          });

          return {
            success: true,
            changed: true,
            oldRole: currentRole,
            newRole: targetRole,
            userId: userId
          };
        } catch (updateError) {
          logger.error(`[Membership] 用户角色更新失败: ${userId}`, {
            error: updateError.message,
            stack: updateError.stack,
            currentRole,
            targetRole,
            isMember: !!isMember,
            hasConnection: !!connection
          });
          throw new Error(`角色更新失败: ${updateError.message}`);
        }
      } else {
        logger.info(`[Membership] 用户 ${userId} 角色无需更新，当前已是: ${currentRole}`);

        return {
          success: true,
          changed: false,
          oldRole: currentRole,
          newRole: targetRole,
          userId: userId
        };
      }

    } catch (error) {
      logger.error(`[Membership] 同步用户角色失败: ${userId}`, {
        error: error.message,
        stack: error.stack
      });

      // 重新抛出错误，让调用方知道同步失败
      throw new Error(`角色同步失败: ${error.message}`);
    }
  }

  // 获取会员统计
  async getMembershipStats() {
    const sql = `
      SELECT 
        COUNT(*) as total_memberships,
        COUNT(CASE WHEN status = 'active' AND end_date > NOW() THEN 1 END) as active_memberships,
        COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_memberships,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_memberships,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_memberships_30d
      FROM memberships
    `;

    const result = await this.query(sql);
    return result[0];
  }

  // 获取即将过期的会员
  async getExpiringMemberships(days = 7) {
    const sql = `
      SELECT 
        m.*,
        u.username,
        u.email,
        mp.name as plan_name
      FROM memberships m
      LEFT JOIN users u ON m.user_id = u.id
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.status = 'active'
        AND m.end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY)
      ORDER BY m.end_date ASC
    `;

    return await this.query(sql, [days]);
  }

  // 自动处理过期会员
  async processExpiredMemberships() {
    // 先获取即将过期的会员用户ID
    const expiredMemberships = await this.query(`
      SELECT DISTINCT user_id
      FROM memberships
      WHERE status = 'active' AND end_date < NOW()
    `);

    const sql = `
      UPDATE memberships
      SET status = 'expired'
      WHERE status = 'active'
        AND end_date < NOW()
    `;

    const result = await this.query(sql);

    // 同步这些用户的角色
    let syncSuccessCount = 0;
    let syncFailCount = 0;

    for (const membership of expiredMemberships) {
      try {
        const syncResult = await this.syncUserRole(membership.user_id);
        logger.info(`[Membership] processExpiredMemberships 用户 ${membership.user_id} 角色同步结果:`, syncResult);
        syncSuccessCount++;
      } catch (error) {
        logger.error(`[Membership] processExpiredMemberships 同步用户角色失败: ${membership.user_id}`, {
          error: error.message,
          stack: error.stack
        });
        syncFailCount++;
      }
    }

    logger.info(`[Membership] processExpiredMemberships 角色同步统计: 成功 ${syncSuccessCount}, 失败 ${syncFailCount}`);

    logger.info(`处理过期会员: ${result.affectedRows} 个会员已过期`);
    return result.affectedRows;
  }

  // 获取会员收入统计
  async getMembershipRevenue(startDate, endDate) {
    const sql = `
      SELECT 
        DATE(m.created_at) as date,
        COUNT(*) as new_memberships,
        SUM(mp.price) as revenue,
        mp.name as plan_name
      FROM memberships m
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.created_at BETWEEN ? AND ?
        AND m.status != 'cancelled'
      GROUP BY DATE(m.created_at), mp.id
      ORDER BY date DESC
    `;

    return await this.query(sql, [startDate, endDate]);
  }

  // 设置自动续费
  async setAutoRenew(userId, autoRenew = true) {
    const membership = await this.getUserMembership(userId);
    if (!membership) {
      throw new AppError('用户不是会员', 404, 'NOT_A_MEMBER');
    }

    await this.update(membership.id, { auto_renew: autoRenew });
    
    logger.info(`自动续费设置: ${userId}`, { autoRenew });
    return true;
  }

  // 处理自动续费
  async processAutoRenewals() {
    const sql = `
      SELECT 
        m.*,
        u.email,
        mp.price,
        mp.duration_days
      FROM memberships m
      LEFT JOIN users u ON m.user_id = u.id
      LEFT JOIN membership_plans mp ON m.plan_id = mp.id
      WHERE m.status = 'active'
        AND m.auto_renew = true
        AND m.end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 3 DAY)
    `;

    const renewals = await this.query(sql);
    
    for (const membership of renewals) {
      try {
        // 这里应该调用支付服务进行自动扣费
        // 暂时只记录日志
        logger.info(`需要自动续费: ${membership.user_id}`, {
          membershipId: membership.id,
          planId: membership.plan_id,
          price: membership.price
        });
      } catch (error) {
        logger.error(`自动续费失败: ${membership.user_id}`, error);
      }
    }

    return renewals.length;
  }
}

module.exports = new Membership();
