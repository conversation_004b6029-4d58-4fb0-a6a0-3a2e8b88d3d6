#!/usr/bin/env node

/**
 * 简化的修复验证脚本
 * 验证关键安全问题的修复效果
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 验证安全修复\n');

/**
 * 检查文件中是否包含特定内容
 */
function checkFileContent(filePath, searchText, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const found = content.includes(searchText);
    
    if (found) {
      console.log(`✅ ${description} - 已修复`);
      return true;
    } else {
      console.log(`❌ ${description} - 未修复`);
      return false;
    }
  } catch (error) {
    console.log(`⚠️ ${description} - 文件检查失败: ${error.message}`);
    return false;
  }
}

/**
 * 检查文件中是否不包含特定内容（已移除）
 */
function checkFileNotContains(filePath, searchText, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const found = content.includes(searchText);
    
    if (!found) {
      console.log(`✅ ${description} - 已修复`);
      return true;
    } else {
      console.log(`❌ ${description} - 未修复`);
      return false;
    }
  } catch (error) {
    console.log(`⚠️ ${description} - 文件检查失败: ${error.message}`);
    return false;
  }
}

/**
 * 主验证函数
 */
function verifyFixes() {
  console.log('🔍 检查安全修复实施情况');
  console.log('='.repeat(50));
  
  let fixedCount = 0;
  let totalChecks = 0;
  
  // 1. 检查Redis失败时的安全处理
  console.log('\n1. Redis失败时的安全处理:');
  totalChecks++;
  if (checkFileContent(
    'src/middleware/auth.js',
    'throw new AppError(\'系统暂时不可用，请稍后再试\', 503, \'SERVICE_UNAVAILABLE\')',
    '   Redis失败时拒绝请求'
  )) {
    fixedCount++;
  }
  
  // 2. 检查双因子认证修复
  console.log('\n2. 双因子认证修复:');
  totalChecks++;
  if (checkFileContent(
    'src/modules/auth/services/authService.js',
    'const speakeasy = require(\'speakeasy\')',
    '   引入speakeasy库'
  )) {
    fixedCount++;
  }
  
  totalChecks++;
  if (checkFileNotContains(
    'src/modules/auth/services/authService.js',
    'const validCodes = [\'123456\', \'000000\'];',
    '   移除固定验证码'
  )) {
    fixedCount++;
  }
  
  totalChecks++;
  if (checkFileContent(
    'src/modules/auth/services/authService.js',
    'speakeasy.totp.verify',
    '   使用TOTP验证'
  )) {
    fixedCount++;
  }
  
  // 3. 检查支付订单事务处理
  console.log('\n3. 支付订单事务处理:');
  totalChecks++;
  if (checkFileContent(
    'src/modules/payment/controllers/paymentController.js',
    'await connection.beginTransaction()',
    '   使用数据库事务'
  )) {
    fixedCount++;
  }
  
  totalChecks++;
  if (checkFileContent(
    'src/modules/payment/controllers/paymentController.js',
    'await connection.rollback()',
    '   事务回滚处理'
  )) {
    fixedCount++;
  }
  
  // 4. 检查权限控制改进
  console.log('\n4. 权限控制改进:');
  totalChecks++;
  if (checkFileContent(
    'src/modules/user/controllers/userController.js',
    'id.toString() === adminId.toString()',
    '   严格的ID比较'
  )) {
    fixedCount++;
  }
  
  totalChecks++;
  if (checkFileContent(
    'src/middleware/auth.js',
    'async function checkResourceOwnership',
    '   资源所有权检查函数'
  )) {
    fixedCount++;
  }
  
  // 5. 检查缓存一致性修复
  console.log('\n5. 缓存一致性修复:');
  totalChecks++;
  if (checkFileContent(
    'src/modules/video/controllers/videoController.js',
    'cachedVideo.view_count = (cachedVideo.view_count || 0) + 1',
    '   观看次数缓存更新'
  )) {
    fixedCount++;
  }
  
  // 6. 检查数据库schema更新
  console.log('\n6. 数据库schema更新:');
  totalChecks++;
  if (checkFileContent(
    'src/database/schema.sql',
    'two_factor_enabled BOOLEAN DEFAULT FALSE',
    '   双因子认证字段'
  )) {
    fixedCount++;
  }
  
  totalChecks++;
  if (checkFileContent(
    'src/database/schema.sql',
    'two_factor_secret VARCHAR(255)',
    '   双因子认证密钥字段'
  )) {
    fixedCount++;
  }
  
  // 7. 检查依赖包安装
  console.log('\n7. 依赖包检查:');
  totalChecks++;
  if (checkFileContent(
    'package.json',
    '"speakeasy"',
    '   speakeasy包'
  )) {
    fixedCount++;
  }
  
  totalChecks++;
  if (checkFileContent(
    'package.json',
    '"qrcode"',
    '   qrcode包'
  )) {
    fixedCount++;
  }
  
  // 输出总结
  console.log('\n' + '='.repeat(50));
  console.log('📊 修复验证结果:');
  console.log(`✅ 已修复: ${fixedCount}/${totalChecks}`);
  console.log(`📈 修复率: ${Math.round((fixedCount / totalChecks) * 100)}%`);
  
  if (fixedCount === totalChecks) {
    console.log('\n🎉 所有关键安全问题已修复！');
    console.log('\n📋 修复清单:');
    console.log('1. ✅ Redis失败时安全处理');
    console.log('2. ✅ 双因子认证真实实现');
    console.log('3. ✅ 支付订单事务处理');
    console.log('4. ✅ 权限控制逻辑改进');
    console.log('5. ✅ 缓存一致性问题');
    console.log('6. ✅ 数据库schema更新');
    console.log('7. ✅ 必要依赖包安装');
    
    console.log('\n🚀 下一步建议:');
    console.log('- 重启应用服务器以应用所有更改');
    console.log('- 运行数据库迁移脚本更新表结构');
    console.log('- 配置生产环境的支付参数');
    console.log('- 进行完整的集成测试');
    
  } else {
    console.log('\n⚠️ 还有部分问题未完全修复，请检查上述失败项目');
  }
  
  return fixedCount === totalChecks;
}

// 运行验证
if (require.main === module) {
  const success = verifyFixes();
  process.exit(success ? 0 : 1);
}

module.exports = { verifyFixes };
