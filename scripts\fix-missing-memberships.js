require('dotenv').config();
const mysql = require('mysql2/promise');
const logger = require('../src/utils/logger');

async function fixMissingMemberships() {
  let connection;
  
  try {
    console.log('=== 修复缺失的会员记录 ===\n');

    // 1. 连接数据库
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'video_user',
      password: process.env.DB_PASSWORD || 'secure_password',
      database: process.env.DB_NAME || 'video_platform'
    });

    console.log('✓ 数据库连接成功\n');

    // 2. 查找已支付但没有会员记录的订单
    console.log('2. 查找需要修复的订单...');
    const [ordersToFix] = await connection.execute(`
      SELECT 
        o.id, o.user_id, o.target_id as plan_id, o.final_amount, 
        o.payment_method, o.transaction_id, o.created_at, o.payment_time,
        u.username, u.role as user_role,
        mp.name as plan_name, mp.duration_days
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      LEFT JOIN membership_plans mp ON o.target_id = mp.id
      LEFT JOIN memberships m ON o.user_id = m.user_id AND o.target_id = m.plan_id
      WHERE o.type = 'membership' 
        AND o.payment_status = 'paid'
        AND m.id IS NULL
      ORDER BY o.created_at ASC
    `);

    console.log(`找到 ${ordersToFix.length} 个需要修复的订单\n`);

    if (ordersToFix.length === 0) {
      console.log('✓ 没有需要修复的订单');
      return;
    }

    // 3. 开始事务处理
    await connection.beginTransaction();

    let fixedCount = 0;
    let errorCount = 0;

    for (const order of ordersToFix) {
      try {
        console.log(`处理订单 ${order.id}: 用户 ${order.username}(${order.user_id}) - 计划 ${order.plan_name}`);

        // 3.1 计算会员开始和结束时间
        const startDate = order.payment_time || order.created_at;
        const endDate = new Date(new Date(startDate).getTime() + order.duration_days * 24 * 60 * 60 * 1000);

        console.log(`  开始时间: ${startDate}`);
        console.log(`  结束时间: ${endDate}`);

        // 3.2 检查是否已经过期
        const now = new Date();
        const status = endDate > now ? 'active' : 'expired';
        console.log(`  状态: ${status}`);

        // 3.3 创建会员记录
        const [membershipResult] = await connection.execute(`
          INSERT INTO memberships (
            user_id, plan_id, start_date, end_date, status, 
            payment_method, transaction_id, auto_renew, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, false, ?, NOW())
        `, [
          order.user_id,
          order.plan_id,
          startDate,
          endDate,
          status,
          order.payment_method,
          order.transaction_id || `fix_${order.id}_${Date.now()}`,
          startDate
        ]);

        console.log(`  ✅ 创建会员记录 ID: ${membershipResult.insertId}`);

        // 3.4 更新用户角色（如果需要且会员仍然有效）
        if (order.user_role !== 'admin' && status === 'active') {
          if (order.user_role !== 'member') {
            await connection.execute(`
              UPDATE users SET role = 'member' WHERE id = ?
            `, [order.user_id]);
            console.log(`  ✅ 更新用户角色: ${order.user_role} -> member`);
          } else {
            console.log(`  ✓ 用户角色已经是 member`);
          }
        } else if (status === 'expired') {
          console.log(`  ⚠️  会员已过期，不更新用户角色`);
        }

        fixedCount++;
        console.log(`  ✅ 订单 ${order.id} 修复完成\n`);

      } catch (error) {
        console.error(`  ❌ 处理订单 ${order.id} 失败:`, error.message);
        errorCount++;
      }
    }

    // 4. 提交事务
    await connection.commit();
    console.log(`\n=== 修复完成 ===`);
    console.log(`成功修复: ${fixedCount} 个订单`);
    console.log(`失败: ${errorCount} 个订单`);

    // 5. 验证修复结果
    console.log('\n5. 验证修复结果...');
    
    // 检查还有多少未修复的订单
    const [remainingOrders] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM orders o
      LEFT JOIN memberships m ON o.user_id = m.user_id AND o.target_id = m.plan_id
      WHERE o.type = 'membership' 
        AND o.payment_status = 'paid'
        AND m.id IS NULL
    `);

    console.log(`剩余未修复订单: ${remainingOrders[0].count}`);

    // 检查当前活跃会员数
    const [activeMemberships] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM memberships
      WHERE status = 'active' AND end_date > NOW()
    `);

    console.log(`当前活跃会员数: ${activeMemberships[0].count}`);

    // 检查member角色用户数
    const [memberUsers] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM users
      WHERE role = 'member'
    `);

    console.log(`member角色用户数: ${memberUsers[0].count}`);

  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('修复过程中发生错误:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行修复
fixMissingMemberships().then(() => {
  console.log('\n=== 修复脚本执行完成 ===');
  process.exit(0);
}).catch(error => {
  console.error('修复脚本执行失败:', error);
  process.exit(1);
});
