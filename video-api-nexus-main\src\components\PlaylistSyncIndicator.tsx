import React from 'react';
import { Loader2, CheckCircle, AlertCircle, Cloud, CloudOff } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface SyncStatus {
  isLoading: boolean;
  lastSyncTime: Date | null;
  error: string | null;
  pendingOperations: number;
}

interface PlaylistSyncIndicatorProps {
  syncStatus: SyncStatus;
  isAuthenticated: boolean;
  onManualSync?: () => void;
  className?: string;
}

export const PlaylistSyncIndicator: React.FC<PlaylistSyncIndicatorProps> = ({
  syncStatus,
  isAuthenticated,
  onManualSync,
  className
}) => {
  const { toast } = useToast();

  const handleManualSync = () => {
    if (onManualSync) {
      onManualSync();
      toast({
        title: '同步中',
        description: '正在同步播放列表数据...',
      });
    }
  };

  // 如果用户未登录，不显示同步指示器
  if (!isAuthenticated) {
    return null;
  }

  const getSyncStatusInfo = () => {
    if (syncStatus.isLoading || syncStatus.pendingOperations > 0) {
      return {
        icon: <Loader2 className="h-3 w-3 animate-spin" />,
        text: syncStatus.pendingOperations > 0 ? '同步中...' : '加载中...',
        variant: 'secondary' as const,
        color: 'text-blue-600'
      };
    }

    if (syncStatus.error) {
      return {
        icon: <AlertCircle className="h-3 w-3" />,
        text: '同步失败',
        variant: 'destructive' as const,
        color: 'text-red-600'
      };
    }

    if (syncStatus.lastSyncTime) {
      const timeDiff = Date.now() - syncStatus.lastSyncTime.getTime();
      const minutes = Math.floor(timeDiff / 60000);
      
      return {
        icon: <CheckCircle className="h-3 w-3" />,
        text: minutes < 1 ? '刚刚同步' : `${minutes}分钟前同步`,
        variant: 'outline' as const,
        color: 'text-green-600'
      };
    }

    return {
      icon: <CloudOff className="h-3 w-3" />,
      text: '未同步',
      variant: 'outline' as const,
      color: 'text-gray-500'
    };
  };

  const statusInfo = getSyncStatusInfo();

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Badge 
        variant={statusInfo.variant}
        className={cn('flex items-center gap-1 text-xs', statusInfo.color)}
      >
        {statusInfo.icon}
        {statusInfo.text}
      </Badge>
      
      {/* 手动同步按钮 */}
      {onManualSync && !syncStatus.isLoading && syncStatus.pendingOperations === 0 && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleManualSync}
          className="h-6 px-2 text-xs"
          title="手动同步播放列表"
        >
          <Cloud className="h-3 w-3" />
        </Button>
      )}
      
      {/* 错误详情 */}
      {syncStatus.error && (
        <span className="text-xs text-red-500 max-w-32 truncate" title={syncStatus.error}>
          {syncStatus.error}
        </span>
      )}
    </div>
  );
};

export default PlaylistSyncIndicator;
