-- 数据库迁移脚本：将视频系统扩展为统一媒体系统
-- 执行前请备份数据库！

USE video_platform;

-- 1. 为videos表添加新字段
ALTER TABLE videos 
ADD COLUMN media_type ENUM('video', 'audio') DEFAULT 'video' COMMENT '媒体类型' AFTER category_id,
ADD COLUMN bitrate INT COMMENT '音频比特率(kbps)' AFTER format,
ADD COLUMN sample_rate INT COMMENT '音频采样率(Hz)' AFTER bitrate,
ADD COLUMN channels TINYINT COMMENT '音频声道数' AFTER sample_rate;

-- 2. 添加媒体类型索引
ALTER TABLE videos ADD INDEX idx_media_type (media_type);

-- 3. 更新现有记录的媒体类型为video
UPDATE videos SET media_type = 'video' WHERE media_type IS NULL;

-- 4. 更新video_processing_tasks表的task_type枚举
ALTER TABLE video_processing_tasks 
MODIFY COLUMN task_type ENUM('transcode', 'thumbnail', 'hls', 'audio_process', 'waveform') NOT NULL COMMENT '任务类型';

-- 5. 更新file_uploads表的枚举值
ALTER TABLE file_uploads 
MODIFY COLUMN file_type ENUM('video', 'audio', 'image', 'document', 'other') NOT NULL COMMENT '文件类型',
MODIFY COLUMN upload_type ENUM('avatar', 'video', 'audio', 'thumbnail', 'attachment') NOT NULL COMMENT '上传类型';

-- 6. 更新表注释
ALTER TABLE videos COMMENT = '媒体表(视频和音频统一)';
ALTER TABLE video_processing_tasks COMMENT = '媒体处理任务表';
ALTER TABLE watch_history COMMENT = '播放记录表';

-- 7. 验证迁移结果
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN media_type = 'video' THEN 1 END) as video_count,
    COUNT(CASE WHEN media_type = 'audio' THEN 1 END) as audio_count
FROM videos;

-- 迁移完成
SELECT 'Database migration to unified media system completed successfully!' as status;
