import api from './api';

/**
 * @deprecated This endpoint seems to be causing issues. Use paymentApi.createOrder instead.
 */
const subscribe = (planId: number, paymentMethod: string) => {
  return api.post('/member/subscribe', { planId, paymentMethod });
};

const getPlans = () => {
  return api.get('/member/plans');
};

const getMembershipStatus = () => {
  return api.get('/member/my-membership');
};

const getPaymentMethods = () => {
  return api.get('/payment/methods');
};

export const memberApi = {
  subscribe,
  getPlans,
  getMembershipStatus,
  getPaymentMethods,
}; 