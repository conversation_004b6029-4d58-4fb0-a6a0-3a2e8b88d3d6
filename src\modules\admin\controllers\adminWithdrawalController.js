const asyncHandler = require('express-async-handler');
const adminWithdrawalService = require('../services/adminWithdrawalService');

class AdminWithdrawalController {

  /**
   * @desc    获取提现请求列表
   * @route   GET /api/admin/withdrawals
   * @access  Admin
   */
  getWithdrawals = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, status, userId, keyword } = req.query;
    console.log('[Admin] 获取提现请求列表，查询参数:', { page, limit, status, userId, keyword });
    
    const result = await adminWithdrawalService.getWithdrawals({ page, limit, status, userId, keyword });
    
    console.log('[Admin] 提现请求查询结果:', {
      totalItems: result.totalItems,
      dataLength: result.data?.length || 0,
      currentPage: result.currentPage,
      totalPages: result.totalPages
    });
    
    if (result.data && result.data.length > 0) {
      console.log('[Admin] 提现请求列表预览:', result.data.map(w => ({
        id: w.id,
        username: w.username,
        amount: w.amount,
        status: w.status,
        requested_at: w.requested_at
      })));
    } else {
      console.log('[Admin] 未找到提现请求数据');
    }
    
    res.json({ success: true, data: result });
  });

  /**
   * @desc    批准提现请求
   * @route   POST /api/admin/withdrawals/:id/approve
   * @access  Admin
   */
  approveWithdrawal = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { transaction_hash, notes } = req.body;
    const adminId = req.user.id;
    
    await adminWithdrawalService.approveWithdrawal(id, adminId, transaction_hash, notes);
    
    res.json({ success: true, message: '提现请求已批准' });
  });

  /**
   * @desc    拒绝提现请求
   * @route   POST /api/admin/withdrawals/:id/reject
   * @access  Admin
   */
  rejectWithdrawal = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { reason, notes } = req.body;
    const adminId = req.user.id;

    await adminWithdrawalService.rejectWithdrawal(id, adminId, reason, notes);
    
    res.json({ success: true, message: '提现请求已拒绝' });
  });

}

module.exports = new AdminWithdrawalController(); 