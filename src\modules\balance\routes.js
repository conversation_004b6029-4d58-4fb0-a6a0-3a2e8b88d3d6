const express = require('express');
const router = express.Router();
const authMiddleware = require('../../middleware/auth');
const balanceController = require('./controllers/balanceController');
const { validate, balanceSchemas } = require('../../middleware/validation');
const withdrawalController = require('./controllers/withdrawalController');

// 所有余额相关路由都需要登录
router.use(authMiddleware.verifyToken);

// 获取当前用户余额
router.get('/', balanceController.getUserBalance);

// 创建充值订单
router.post(
  '/recharge', 
  validate(balanceSchemas.recharge, 'body'), 
  balanceController.createRechargeOrder
);

// 提现相关路由
router.post('/withdrawals', withdrawalController.requestWithdrawal);
router.get('/withdrawals', withdrawalController.getWithdrawalHistory);

module.exports = router;
