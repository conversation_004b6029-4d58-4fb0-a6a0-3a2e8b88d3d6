#!/usr/bin/env node

/**
 * API文档更新验证脚本
 * 验证HTML文档是否包含所有新的API接口
 */

const fs = require('fs');
const path = require('path');

console.log('📚 验证API文档更新\n');

/**
 * 检查HTML文档内容
 */
function verifyHTMLDocumentation() {
  const htmlPath = path.join(__dirname, '../docs/api.html');
  
  if (!fs.existsSync(htmlPath)) {
    console.log('❌ HTML文档不存在');
    return false;
  }
  
  const htmlContent = fs.readFileSync(htmlPath, 'utf8');
  
  console.log('🔍 检查API文档更新情况:');
  console.log('='.repeat(50));
  
  let allChecksPass = true;
  
  // 1. 检查双因子认证API
  console.log('\n1. 双因子认证API:');
  const twoFactorAPIs = [
    '/api/auth/2fa/generate',
    '/api/auth/2fa/enable', 
    '/api/auth/2fa/verify',
    '/api/auth/2fa/disable',
    '/api/auth/2fa/backup-codes',
    '/api/auth/2fa/use-backup'
  ];
  
  twoFactorAPIs.forEach(api => {
    if (htmlContent.includes(api)) {
      console.log(`  ✅ ${api}`);
    } else {
      console.log(`  ❌ ${api} - 缺失`);
      allChecksPass = false;
    }
  });
  
  // 2. 检查支付API详细参数
  console.log('\n2. 支付API详细参数:');
  const paymentChecks = [
    { name: '支付方式更新', content: 'epay|alipay|wechat|epay_alipay' },
    { name: '订单创建响应', content: 'paymentUrl' },
    { name: '订单状态查询', content: 'paymentStatus' },
    { name: '退款参数', content: 'orderNo' }
  ];
  
  paymentChecks.forEach(check => {
    if (htmlContent.includes(check.content)) {
      console.log(`  ✅ ${check.name}`);
    } else {
      console.log(`  ❌ ${check.name} - 缺失`);
      allChecksPass = false;
    }
  });
  
  // 3. 检查接口数量更新
  console.log('\n3. 接口数量统计:');
  const countChecks = [
    { name: '认证模块接口数', content: '17个接口', section: '认证模块' },
    { name: '总接口数', content: '98个接口', section: '总计' },
    { name: '模块数', content: '7个核心模块', section: '基础信息' }
  ];
  
  countChecks.forEach(check => {
    if (htmlContent.includes(check.content)) {
      console.log(`  ✅ ${check.name}: ${check.content}`);
    } else {
      console.log(`  ❌ ${check.name} - 数量未更新`);
      allChecksPass = false;
    }
  });
  
  // 4. 检查新错误代码
  console.log('\n4. 新错误代码:');
  const errorCodes = [
    'PAYMENT_CREATE_FAILED',
    'ORDER_NOT_FOUND',
    'ORDER_NOT_PAID',
    'REFUND_FAILED',
    'TWO_FACTOR_NOT_ENABLED',
    'TWO_FACTOR_CODE_INVALID',
    'SERVICE_UNAVAILABLE'
  ];
  
  errorCodes.forEach(code => {
    if (htmlContent.includes(code)) {
      console.log(`  ✅ ${code}`);
    } else {
      console.log(`  ❌ ${code} - 缺失`);
      allChecksPass = false;
    }
  });
  
  // 5. 检查文档结构完整性
  console.log('\n5. 文档结构完整性:');
  const structureChecks = [
    { name: 'CSS样式', pattern: /<style>.*<\/style>/s },
    { name: 'JavaScript功能', pattern: /<script>.*<\/script>/s },
    { name: '导航链接', pattern: /href="#auth"/ },
    { name: '复制按钮功能', pattern: /navigator\.clipboard\.writeText/ },
    { name: '平滑滚动', pattern: /scrollIntoView/ }
  ];
  
  structureChecks.forEach(check => {
    if (check.pattern.test(htmlContent)) {
      console.log(`  ✅ ${check.name}`);
    } else {
      console.log(`  ❌ ${check.name} - 缺失`);
      allChecksPass = false;
    }
  });
  
  return allChecksPass;
}

/**
 * 检查文档文件大小和完整性
 */
function checkDocumentationFiles() {
  console.log('\n📁 检查文档文件:');
  
  const docs = [
    { name: 'api.html', path: '../docs/api.html', minSize: 50000 },
    { name: 'API.md', path: '../docs/API.md', minSize: 10000 },
    { name: 'API_QUICK_REFERENCE.md', path: '../docs/API_QUICK_REFERENCE.md', minSize: 5000 }
  ];
  
  let allFilesOK = true;
  
  docs.forEach(doc => {
    const docPath = path.join(__dirname, doc.path);
    if (fs.existsSync(docPath)) {
      const stats = fs.statSync(docPath);
      const sizeKB = Math.round(stats.size / 1024);
      
      if (stats.size >= doc.minSize) {
        console.log(`  ✅ ${doc.name}: ${sizeKB}KB (正常)`);
      } else {
        console.log(`  ⚠️ ${doc.name}: ${sizeKB}KB (可能不完整)`);
        allFilesOK = false;
      }
    } else {
      console.log(`  ❌ ${doc.name}: 文件不存在`);
      allFilesOK = false;
    }
  });
  
  return allFilesOK;
}

/**
 * 生成文档更新报告
 */
function generateUpdateReport() {
  console.log('\n📋 文档更新报告:');
  console.log('='.repeat(50));
  
  const htmlPath = path.join(__dirname, '../docs/api.html');
  const htmlContent = fs.readFileSync(htmlPath, 'utf8');
  
  // 统计接口数量
  const endpointMatches = htmlContent.match(/<div class="endpoint">/g);
  const totalEndpoints = endpointMatches ? endpointMatches.length : 0;
  
  // 统计模块数量
  const sectionMatches = htmlContent.match(/<div class="section" id="[^"]*">/g);
  const totalSections = sectionMatches ? sectionMatches.length - 1 : 0; // 减去errors部分
  
  // 统计错误代码数量
  const errorCodeMatches = htmlContent.match(/<h6>[A-Z_]+ \(\d+\)<\/h6>/g);
  const totalErrorCodes = errorCodeMatches ? errorCodeMatches.length : 0;
  
  console.log(`📊 统计信息:`);
  console.log(`- 总接口数: ${totalEndpoints}个`);
  console.log(`- 模块数: ${totalSections}个`);
  console.log(`- 错误代码: ${totalErrorCodes}个`);
  
  // 检查新增内容
  const newFeatures = [
    { name: '双因子认证', count: (htmlContent.match(/2fa/g) || []).length },
    { name: '支付接口', count: (htmlContent.match(/payment/g) || []).length },
    { name: '详细参数', count: (htmlContent.match(/请求参数|响应示例/g) || []).length }
  ];
  
  console.log(`\n🆕 新增内容:`);
  newFeatures.forEach(feature => {
    console.log(`- ${feature.name}: ${feature.count}处提及`);
  });
  
  return {
    totalEndpoints,
    totalSections,
    totalErrorCodes,
    newFeatures
  };
}

/**
 * 主验证函数
 */
function main() {
  console.log('🚀 开始API文档更新验证');
  console.log('='.repeat(50));
  
  const htmlValid = verifyHTMLDocumentation();
  const filesValid = checkDocumentationFiles();
  const report = generateUpdateReport();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 验证结果汇总:');
  console.log('='.repeat(50));
  
  console.log(`HTML文档更新: ${htmlValid ? '✅ 完成' : '❌ 不完整'}`);
  console.log(`文档文件检查: ${filesValid ? '✅ 正常' : '❌ 异常'}`);
  
  const overallSuccess = htmlValid && filesValid;
  
  if (overallSuccess) {
    console.log('\n🎉 API文档更新验证通过！');
    console.log('\n📋 更新内容总结:');
    console.log('1. ✅ 添加了6个双因子认证API');
    console.log('2. ✅ 完善了支付接口的详细参数');
    console.log('3. ✅ 更新了接口数量统计');
    console.log('4. ✅ 添加了7个新错误代码');
    console.log('5. ✅ 保持了文档结构完整性');
    
    console.log('\n🔗 文档访问:');
    console.log('- HTML版本: docs/api.html');
    console.log('- Markdown版本: docs/API.md');
    console.log('- 快速参考: docs/API_QUICK_REFERENCE.md');
    
  } else {
    console.log('\n⚠️ 文档更新验证发现问题，请检查上述错误');
  }
  
  return overallSuccess;
}

// 运行验证
if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = { main, verifyHTMLDocumentation, checkDocumentationFiles };
