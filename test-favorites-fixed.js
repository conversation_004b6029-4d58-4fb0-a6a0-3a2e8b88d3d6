require('dotenv').config();
const Favorite = require('./src/database/models/Favorite');

async function testFavorites() {
  console.log('=== 测试修复后的收藏功能 ===');
  
  try {
    console.log('1. 测试getUserFavorites方法...');
    const userFavorites = await Favorite.getUserFavorites(1, {
      page: 1,
      pageSize: 10,
      sortBy: 'created_at',
      sortOrder: 'DESC'
    });
    console.log('✅ 用户收藏列表:', JSON.stringify(userFavorites, null, 2));

    console.log('\n2. 测试toggleFavorite方法...');
    const toggleResult = await Favorite.toggleFavorite(1, 1);
    console.log('✅ 切换收藏结果:', toggleResult);

    console.log('\n3. 测试isFavorited方法...');
    const isFavorited = await Favorite.isFavorited(1, 1);
    console.log('✅ 是否已收藏:', isFavorited);

    console.log('\n4. 测试batchCheckFavorited方法...');
    const batchCheck = await Favorite.batchCheckFavorited(1, [1]);
    console.log('✅ 批量检查收藏状态:', batchCheck);

    console.log('\n5. 测试getPopularFavorites方法...');
    const popularFavorites = await Favorite.getPopularFavorites({
      limit: 5,
      timeRange: '7d'
    });
    console.log('✅ 热门收藏:', popularFavorites);

    console.log('\n6. 测试getUserFavoriteCategories方法...');
    const favoriteCategories = await Favorite.getUserFavoriteCategories(1);
    console.log('✅ 用户收藏分类统计:', favoriteCategories);

    console.log('\n7. 测试getVideoFavorites方法...');
    const videoFavorites = await Favorite.getVideoFavorites(1, {
      page: 1,
      pageSize: 10
    });
    console.log('✅ 视频收藏用户列表:', videoFavorites);

    console.log('\n=== 🎉 所有测试通过！收藏功能已修复 ===');

  } catch (error) {
    console.log('❌ 测试失败:', error.message);
    console.log('错误详情:', error);
  } finally {
    process.exit(0);
  }
}

testFavorites();
