const { db } = require('./src/config/database');
const logger = require('./src/utils/logger');

/**
 * 手动激活用户会员 - 临时解决方案
 * 用于 Creem webhook 失败时的紧急处理
 */
async function manualActivateMembership() {
  try {
    const userId = 2;
    const userEmail = '<EMAIL>';
    const orderId = 'ord_7LrhSleHiyxI8e0WrOeC82';
    const planName = '普通会员sssssssssd';
    const amount = 18.00;
    const currency = 'USD';
    const durationDays = 30;

    // 计算会员到期时间
    const startDate = new Date();
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + durationDays);

    console.log('开始手动激活会员...');
    console.log(`用户ID: ${userId}`);
    console.log(`邮箱: ${userEmail}`);
    console.log(`订单ID: ${orderId}`);
    console.log(`计划: ${planName}`);
    console.log(`有效期: ${startDate.toISOString()} 到 ${endDate.toISOString()}`);

    // 检查是否已经存在该订单的会员记录
    const [existingRows] = await db.query(
      'SELECT * FROM memberships WHERE user_id = ? AND payment_reference = ?',
      [userId, orderId]
    );

    if (existingRows.length > 0) {
      console.log('❌ 该订单已经处理过，无需重复激活');
      return;
    }

    // 检查用户是否已有活跃会员
    const [activeMemberships] = await db.query(
      'SELECT * FROM memberships WHERE user_id = ? AND status = "active"',
      [userId]
    );

    if (activeMemberships.length > 0) {
      console.log('用户已有活跃会员，延长会员期限...');
      const currentEndDate = new Date(activeMemberships[0].end_date);
      const newEndDate = new Date(currentEndDate);
      newEndDate.setDate(newEndDate.getDate() + durationDays);

      await db.query(
        'UPDATE memberships SET end_date = ?, updated_at = NOW() WHERE id = ?',
        [newEndDate, activeMemberships[0].id]
      );

      console.log(`✅ 会员期限已延长至: ${newEndDate.toISOString()}`);
    } else {
      console.log('创建新的会员记录...');
      await db.query(`
        INSERT INTO memberships (
          user_id, 
          plan_name, 
          start_date, 
          end_date, 
          status, 
          payment_method, 
          payment_reference, 
          amount, 
          currency, 
          auto_renew,
          created_at, 
          updated_at
        ) VALUES (?, ?, ?, ?, 'active', 'creem', ?, ?, ?, false, NOW(), NOW())
      `, [userId, planName, startDate, endDate, orderId, amount, currency]);

      console.log(`✅ 新会员记录创建成功，有效期至: ${endDate.toISOString()}`);
    }

    // 验证激活结果
    const [membershipCheck] = await db.query(
      'SELECT * FROM memberships WHERE user_id = ? AND status = "active"',
      [userId]
    );

    if (membershipCheck.length > 0) {
      console.log('✅ 会员激活验证成功');
      console.log('会员信息:', {
        id: membershipCheck[0].id,
        planName: membershipCheck[0].plan_name,
        startDate: membershipCheck[0].start_date,
        endDate: membershipCheck[0].end_date,
        paymentReference: membershipCheck[0].payment_reference
      });
    } else {
      console.log('❌ 会员激活验证失败');
    }

  } catch (error) {
    console.error('手动激活会员失败:', error);
  } finally {
    process.exit(0);
  }
}

// 运行脚本
manualActivateMembership();