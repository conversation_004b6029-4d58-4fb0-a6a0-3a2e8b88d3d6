import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, useLocation } from 'react-router-dom';
import { useFollowContext } from '../../contexts/FollowContext';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { FollowButton } from '../common/FollowButton';
import { UserStatsSimple } from './UserStats';
import { followApi, User } from '../../services/followApi';
import { Loader2, Users, UserPlus } from 'lucide-react';
import { toast } from 'sonner';

interface FollowListPageProps {
  className?: string;
}

export const FollowListPage: React.FC<FollowListPageProps> = ({ className = '' }) => {
  const { userId } = useParams<{ userId: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const location = useLocation();
  const { refreshCounter } = useFollowContext();
  
  // 根据URL路径确定默认tab
  const getDefaultTab = () => {
    if (location.pathname.includes('/followers')) return 'followers';
    if (location.pathname.includes('/following')) return 'following';
    return 'followers'; // 默认显示粉丝
  };
  
  const tab = searchParams.get('tab') || getDefaultTab();

  const [followers, setFollowers] = useState<User[]>([]);
  const [following, setFollowing] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [followersPage, setFollowersPage] = useState(1);
  const [followingPage, setFollowingPage] = useState(1);
  const [hasMoreFollowers, setHasMoreFollowers] = useState(true);
  const [hasMoreFollowing, setHasMoreFollowing] = useState(true);

  const fetchFollowers = async (page = 1, append = false) => {
    if (!userId) return;
    
    setLoading(true);
    try {
      const response = await followApi.getFollowers(parseInt(userId), page);
      console.log('Followers API response:', response); // 调试日志
      
      // 确保数据结构正确
      const apiData = response.data?.data || response.data || {};
      const newData = Array.isArray(apiData.data) ? apiData.data : [];
      const pagination = apiData.pagination || {};
      
      if (append) {
        setFollowers(prev => [...prev, ...newData]);
      } else {
        setFollowers(newData);
      }
      
      setHasMoreFollowers(pagination.page < pagination.totalPages);
    } catch (error: any) {
      console.error('获取粉丝列表错误:', error);
      toast.error(error.response?.data?.message || '获取粉丝列表失败');
      setFollowers([]); // 设置为空数组避免错误
    } finally {
      setLoading(false);
    }
  };

  const fetchFollowing = async (page = 1, append = false) => {
    if (!userId) return;
    
    setLoading(true);
    try {
      const response = await followApi.getFollowing(parseInt(userId), page);
      console.log('Following API response:', response); // 调试日志
      
      // 确保数据结构正确
      const apiData = response.data?.data || response.data || {};
      const newData = Array.isArray(apiData.data) ? apiData.data : [];
      const pagination = apiData.pagination || {};
      
      if (append) {
        setFollowing(prev => [...prev, ...newData]);
      } else {
        setFollowing(newData);
      }
      
      setHasMoreFollowing(pagination.page < pagination.totalPages);
    } catch (error: any) {
      console.error('获取关注列表错误:', error);
      toast.error(error.response?.data?.message || '获取关注列表失败');
      setFollowing([]); // 设置为空数组避免错误
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (tab === 'followers') {
      fetchFollowers();
      setFollowersPage(1);
    } else {
      fetchFollowing();
      setFollowingPage(1);
    }
  }, [userId, tab, refreshCounter]);

  const handleTabChange = (value: string) => {
    setSearchParams({ tab: value });
  };

  const loadMoreFollowers = () => {
    const nextPage = followersPage + 1;
    setFollowersPage(nextPage);
    fetchFollowers(nextPage, true);
  };

  const loadMoreFollowing = () => {
    const nextPage = followingPage + 1;
    setFollowingPage(nextPage);
    fetchFollowing(nextPage, true);
  };

  const UserListItem: React.FC<{ user: User }> = ({ user }) => {
    const handleFollowChange = () => {
      // 当关注状态改变时，重新获取当前页面的数据
      if (tab === 'followers') {
        fetchFollowers(1, false);
      } else {
        fetchFollowing(1, false);
      }
    };

    return (
      <Card className="mb-3">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 flex-1">
              <Avatar className="w-12 h-12">
                <AvatarImage src={user.avatar} alt={user.nickname || user.username} />
                <AvatarFallback>
                  {(user.nickname || user.username)?.[0]?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold text-gray-900 truncate">
                    {user.nickname || user.username}
                  </h3>
                </div>
                
                <UserStatsSimple 
                  userId={user.id} 
                  className="mt-1"
                />
                
                {user.bio && (
                  <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                    {user.bio}
                  </p>
                )}
              </div>
            </div>
            
            <div className="ml-3">
              <FollowButton 
                userId={user.id} 
                initialFollowStatus={user.is_following}
                onFollowChange={handleFollowChange}
                size="sm"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const EmptyState: React.FC<{ type: 'followers' | 'following' }> = ({ type }) => (
    <div className="text-center py-12">
      <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {type === 'followers' ? '暂无粉丝' : '暂未关注任何人'}
      </h3>
      <p className="text-gray-600 mb-4">
        {type === 'followers' 
          ? '还没有用户关注，继续创作优质内容吧！' 
          : '发现并关注感兴趣的创作者'}
      </p>
      {type === 'following' && (
        <Button variant="outline">
          <UserPlus className="w-4 h-4 mr-2" />
          发现用户
        </Button>
      )}
    </div>
  );

  return (
    <div className={`max-w-2xl mx-auto ${className}`}>
      <Tabs value={tab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="followers" className="flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>粉丝</span>
          </TabsTrigger>
          <TabsTrigger value="following" className="flex items-center space-x-2">
            <UserPlus className="w-4 h-4" />
            <span>关注</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="followers" className="mt-0">
          {followers.length === 0 && !loading ? (
            <EmptyState type="followers" />
          ) : (
            <div>
              {followers.map(user => (
                <UserListItem key={user.id} user={user} />
              ))}
              
              {hasMoreFollowers && (
                <div className="text-center mt-6">
                  <Button 
                    onClick={loadMoreFollowers}
                    disabled={loading}
                    variant="outline"
                  >
                    {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                    {loading ? '加载中...' : '加载更多'}
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="following" className="mt-0">
          {following.length === 0 && !loading ? (
            <EmptyState type="following" />
          ) : (
            <div>
              {following.map(user => (
                <UserListItem key={user.id} user={user} />
              ))}
              
              {hasMoreFollowing && (
                <div className="text-center mt-6">
                  <Button 
                    onClick={loadMoreFollowing}
                    disabled={loading}
                    variant="outline"
                  >
                    {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                    {loading ? '加载中...' : '加载更多'}
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};