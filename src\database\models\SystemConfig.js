const BaseModel = require('../BaseModel');

class SystemConfig extends BaseModel {
  constructor() {
    super('system_configs');
  }

  /**
   * 获取所有配置项
   * @returns {Promise<Array>} 所有配置项的数组
   */
  async getAll() {
    return await this.findAll();
  }
  
  /**
   * 按分组获取配置项
   * @param {string} group - 配置分组名
   * @returns {Promise<Array>} 指定分组的配置项数组
   */
  async getByGroup(group) {
    return await this.findAll({ config_group: group });
  }

  /**
   * 批量更新或插入配置项 (UPSERT)
   * @param {Object} settings - 包含键值对的配置对象
   * @param {string} [group=null] - 可选的配置分组名
   * @returns {Promise<number>} 受影响的行数
   */
  async bulkUpdate(settings, group = null) {
    // 增加健壮性检查，如果settings为空或不是对象/数组，则直接返回
    if (!settings || typeof settings !== 'object') {
      return 0;
    }

    let values;
    // 兼容数组格式 e.g. [{key: 'a', value: 'b'}] 和对象格式 e.g. {a: 'b'}
    if (Array.isArray(settings)) {
      values = settings.map(item => [item.key, String(item.value), item.group || group]);
    } else {
      values = Object.entries(settings).map(([key, value]) => {
        const finalValue = typeof value === 'boolean' ? (value ? '1' : '0') : String(value);
        return [key, finalValue, group];
      });
    }
    
    if (values.length === 0) {
      return 0;
    }

    // 动态生成占位符, e.g. '(?, ?, ?), (?, ?, ?)'
    const placeholders = values.map(() => '(?, ?, ?)').join(', ');
    
    // 压平参数数组
    const flatValues = values.flat();

    const sql = `
      INSERT INTO ${this.tableName} (config_key, config_value, config_group)
      VALUES ${placeholders}
      ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), config_group = VALUES(config_group)
    `;

    const result = await this.query(sql, flatValues);
    const metadata = Array.isArray(result) ? result[0] : result;
    return metadata.affectedRows;
  }
}

module.exports = new SystemConfig(); 