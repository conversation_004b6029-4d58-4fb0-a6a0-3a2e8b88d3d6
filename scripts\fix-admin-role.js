#!/usr/bin/env node

/**
 * 修复管理员角色脚本
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const User = require('../src/database/models/User');
const logger = require('../src/utils/logger');

async function fixAdminRole() {
  try {
    logger.info('修复管理员角色...');
    
    // 将用户ID为1的用户角色改回admin
    await User.update(1, { role: 'admin' });
    
    logger.info('管理员角色已修复');
    
    // 显示当前用户角色分布
    const roleStats = await User.query(`
      SELECT role, COUNT(*) as count 
      FROM users 
      WHERE status != 'deleted'
      GROUP BY role
    `);
    
    logger.info('当前用户角色分布:');
    roleStats.forEach(stat => {
      logger.info(`  ${stat.role}: ${stat.count} 人`);
    });
    
  } catch (error) {
    logger.error('修复管理员角色失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixAdminRole()
    .then(() => {
      logger.info('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { fixAdminRole };
