import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import playlistApi, { 
  CreatePlaylistRequest, 
  UpdatePlaylistRequest, 
  AddVideoRequest,
  BatchAddVideosRequest,
  ReorderItemsRequest,
  RecordPlayHistoryRequest
} from '@/services/playlistApi';

// Query Keys
export const playlistQueryKeys = {
  all: ['playlists'] as const,
  lists: () => [...playlistQueryKeys.all, 'list'] as const,
  list: (includeItems: boolean) => [...playlistQueryKeys.lists(), includeItems] as const,
  details: () => [...playlistQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...playlistQueryKeys.details(), id] as const,
  stats: () => [...playlistQueryKeys.all, 'stats'] as const,
  history: () => [...playlistQueryKeys.all, 'history'] as const,
  historyList: (limit: number, offset: number, videoId?: number) => 
    [...playlistQueryKeys.history(), 'list', limit, offset, videoId] as const,
  recent: (limit: number) => [...playlistQueryKeys.history(), 'recent', limit] as const,
  playStats: () => [...playlistQueryKeys.history(), 'stats'] as const,
  trends: (days: number) => [...playlistQueryKeys.history(), 'trends', days] as const,
  progress: (videoId: number) => [...playlistQueryKeys.history(), 'progress', videoId] as const,
};

// 播放列表查询 Hooks

/**
 * 获取用户播放列表
 */
export function useUserPlaylists(includeItems: boolean = false, options: any = {}) {
  return useQuery({
    queryKey: playlistQueryKeys.list(includeItems),
    queryFn: () => playlistApi.getUserPlaylists(includeItems),
    staleTime: 5 * 60 * 1000, // 5分钟
    ...options,
  });
}

/**
 * 获取播放列表详情
 */
export function usePlaylistDetail(playlistId: number, enabled: boolean = true) {
  return useQuery({
    queryKey: playlistQueryKeys.detail(playlistId),
    queryFn: () => playlistApi.getPlaylistById(playlistId),
    enabled: enabled && !!playlistId,
    staleTime: 2 * 60 * 1000, // 2分钟
  });
}

/**
 * 获取播放列表统计
 */
export function usePlaylistStats() {
  return useQuery({
    queryKey: playlistQueryKeys.stats(),
    queryFn: () => playlistApi.getPlaylistStats(),
    staleTime: 10 * 60 * 1000, // 10分钟
  });
}

/**
 * 获取播放历史
 */
export function usePlayHistory(limit: number = 50, offset: number = 0, videoId?: number) {
  return useQuery({
    queryKey: playlistQueryKeys.historyList(limit, offset, videoId),
    queryFn: () => playlistApi.getPlayHistory(limit, offset, videoId),
    staleTime: 1 * 60 * 1000, // 1分钟
  });
}

/**
 * 获取最近播放
 */
export function useRecentlyPlayed(limit: number = 10) {
  return useQuery({
    queryKey: playlistQueryKeys.recent(limit),
    queryFn: () => playlistApi.getRecentlyPlayed(limit),
    staleTime: 30 * 1000, // 30秒
  });
}

/**
 * 获取播放统计
 */
export function usePlayStats() {
  return useQuery({
    queryKey: playlistQueryKeys.playStats(),
    queryFn: () => playlistApi.getPlayStats(),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

/**
 * 获取播放趋势
 */
export function usePlayTrends(days: number = 7) {
  return useQuery({
    queryKey: playlistQueryKeys.trends(days),
    queryFn: () => playlistApi.getPlayTrends(days),
    staleTime: 10 * 60 * 1000, // 10分钟
  });
}

/**
 * 获取观看进度
 */
export function useWatchProgress(videoId: number, enabled: boolean = true) {
  return useQuery({
    queryKey: playlistQueryKeys.progress(videoId),
    queryFn: () => playlistApi.getWatchProgress(videoId),
    enabled: enabled && !!videoId,
    staleTime: 30 * 1000, // 30秒
  });
}

// 播放列表变更 Hooks

/**
 * 创建播放列表
 */
export function useCreatePlaylist() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePlaylistRequest) => playlistApi.createPlaylist(data),
    onSuccess: (newPlaylist) => {
      // 更新播放列表列表缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.stats() });
      
      toast({
        title: '创建成功',
        description: `播放列表 "${newPlaylist.name}" 创建成功`,
      });
    },
    onError: (error: any) => {
      toast({
        title: '创建失败',
        description: error.response?.data?.message || '创建播放列表失败',
        variant: 'destructive',
      });
    },
  });
}

/**
 * 更新播放列表
 */
export function useUpdatePlaylist() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ playlistId, data }: { playlistId: number; data: UpdatePlaylistRequest }) =>
      playlistApi.updatePlaylist(playlistId, data),
    onSuccess: (updatedPlaylist, { playlistId }) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.detail(playlistId) });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.stats() });
      
      toast({
        title: '更新成功',
        description: `播放列表 "${updatedPlaylist.name}" 更新成功`,
      });
    },
    onError: (error: any) => {
      toast({
        title: '更新失败',
        description: error.response?.data?.message || '更新播放列表失败',
        variant: 'destructive',
      });
    },
  });
}

/**
 * 删除播放列表
 */
export function useDeletePlaylist() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (playlistId: number) => playlistApi.deletePlaylist(playlistId),
    onSuccess: (_, playlistId) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.lists() });
      queryClient.removeQueries({ queryKey: playlistQueryKeys.detail(playlistId) });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.stats() });
      
      toast({
        title: '删除成功',
        description: '播放列表删除成功',
      });
    },
    onError: (error: any) => {
      toast({
        title: '删除失败',
        description: error.response?.data?.message || '删除播放列表失败',
        variant: 'destructive',
      });
    },
  });
}

/**
 * 添加视频到播放列表
 */
export function useAddVideoToPlaylist() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ playlistId, data }: { playlistId: number; data: AddVideoRequest }) =>
      playlistApi.addVideoToPlaylist(playlistId, data),
    onSuccess: (_, { playlistId }) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.detail(playlistId) });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.stats() });
      
      toast({
        title: '添加成功',
        description: '视频已添加到播放列表',
      });
    },
    onError: (error: any) => {
      toast({
        title: '添加失败',
        description: error.response?.data?.message || '添加视频到播放列表失败',
        variant: 'destructive',
      });
    },
  });
}

/**
 * 从播放列表移除视频
 */
export function useRemoveVideoFromPlaylist() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ playlistId, videoId }: { playlistId: number; videoId: number }) =>
      playlistApi.removeVideoFromPlaylist(playlistId, videoId),
    onSuccess: (_, { playlistId }) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.detail(playlistId) });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.stats() });
      
      toast({
        title: '移除成功',
        description: '视频已从播放列表移除',
      });
    },
    onError: (error: any) => {
      toast({
        title: '移除失败',
        description: error.response?.data?.message || '从播放列表移除视频失败',
        variant: 'destructive',
      });
    },
  });
}

/**
 * 批量添加视频
 */
export function useBatchAddVideos() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ playlistId, data }: { playlistId: number; data: BatchAddVideosRequest }) =>
      playlistApi.addMultipleVideos(playlistId, data),
    onSuccess: (result, { playlistId }) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.detail(playlistId) });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.stats() });
      
      toast({
        title: '批量添加完成',
        description: `成功添加 ${result.summary.success} 个视频，失败 ${result.summary.failure} 个`,
      });
    },
    onError: (error: any) => {
      toast({
        title: '批量添加失败',
        description: error.response?.data?.message || '批量添加视频失败',
        variant: 'destructive',
      });
    },
  });
}

/**
 * 重新排序播放列表项目
 */
export function useReorderPlaylistItems() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ playlistId, data }: { playlistId: number; data: ReorderItemsRequest }) =>
      playlistApi.reorderPlaylistItems(playlistId, data),
    onSuccess: (_, { playlistId }) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.detail(playlistId) });
      
      toast({
        title: '排序成功',
        description: '播放列表项目排序已更新',
      });
    },
    onError: (error: any) => {
      toast({
        title: '排序失败',
        description: error.response?.data?.message || '重新排序失败',
        variant: 'destructive',
      });
    },
  });
}

/**
 * 复制播放列表
 */
export function useDuplicatePlaylist() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ playlistId, newName }: { playlistId: number; newName?: string }) =>
      playlistApi.duplicatePlaylist(playlistId, newName),
    onSuccess: (newPlaylist) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.stats() });
      
      toast({
        title: '复制成功',
        description: `播放列表 "${newPlaylist.name}" 复制成功`,
      });
    },
    onError: (error: any) => {
      toast({
        title: '复制失败',
        description: error.response?.data?.message || '复制播放列表失败',
        variant: 'destructive',
      });
    },
  });
}

/**
 * 记录播放历史
 */
export function useRecordPlayHistory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: RecordPlayHistoryRequest) => playlistApi.recordPlayHistory(data),
    onSuccess: (_, { videoId }) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.history() });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.progress(videoId) });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.playStats() });
    },
    onError: (error: any) => {
      console.error('记录播放历史失败:', error);
    },
  });
}

/**
 * 清除播放历史
 */
export function useClearPlayHistory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ videoId, clearAll }: { videoId?: number; clearAll?: boolean }) =>
      playlistApi.clearPlayHistory(videoId, clearAll),
    onSuccess: (result, { clearAll }) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.history() });
      queryClient.invalidateQueries({ queryKey: playlistQueryKeys.playStats() });
      
      if (clearAll) {
        queryClient.removeQueries({ queryKey: playlistQueryKeys.history() });
      }
      
      toast({
        title: '清除成功',
        description: `已清除 ${result.deletedCount} 条播放历史`,
      });
    },
    onError: (error: any) => {
      toast({
        title: '清除失败',
        description: error.response?.data?.message || '清除播放历史失败',
        variant: 'destructive',
      });
    },
  });
}
