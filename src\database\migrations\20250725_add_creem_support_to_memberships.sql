-- 为 memberships 表添加 Creem 支付支持字段
-- 迁移日期: 2025-07-25

-- 检查并添加 plan_name 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'memberships' 
  AND COLUMN_NAME = 'plan_name';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE memberships ADD COLUMN plan_name VARCHAR(255) NOT NULL DEFAULT \'\' COMMENT \'会员计划名称\' AFTER plan_id', 
  'SELECT \'Column plan_name already exists\' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 payment_reference 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'memberships' 
  AND COLUMN_NAME = 'payment_reference';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE memberships ADD COLUMN payment_reference VARCHAR(255) COMMENT \'支付参考号（订单ID、交易ID等）\' AFTER payment_method', 
  'SELECT \'Column payment_reference already exists\' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 amount 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'memberships' 
  AND COLUMN_NAME = 'amount';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE memberships ADD COLUMN amount DECIMAL(10, 2) COMMENT \'支付金额\' AFTER transaction_id', 
  'SELECT \'Column amount already exists\' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 currency 字段（如果不存在）
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'memberships' 
  AND COLUMN_NAME = 'currency';

SET @sql = IF(@col_exists = 0, 
  'ALTER TABLE memberships ADD COLUMN currency VARCHAR(3) DEFAULT \'CNY\' COMMENT \'货币类型\' AFTER amount', 
  'SELECT \'Column currency already exists\' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 修改现有字段，允许 plan_id 为空（如果当前不允许空值）
ALTER TABLE memberships 
MODIFY COLUMN plan_id INT NULL COMMENT '会员计划ID（本地计划）';

-- 检查并添加 payment_reference 索引（如果不存在）
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'memberships' 
  AND INDEX_NAME = 'idx_payment_reference';

SET @sql = IF(@index_exists = 0, 
  'ALTER TABLE memberships ADD INDEX idx_payment_reference (payment_reference)', 
  'SELECT \'Index idx_payment_reference already exists\' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新表注释
ALTER TABLE memberships COMMENT='会员表（支持本地和第三方支付）';