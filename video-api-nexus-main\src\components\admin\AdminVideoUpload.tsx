import { useState, useEffect } from 'react';
import { UploadCloud, File, X, Music, Plus, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { uploadVideo, getCategories, uploadVideoFromUrl } from '@/lib/api';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTranslation } from 'react-i18next';

interface Category {
  id: number;
  name: string;
}

const AdminVideoUpload = () => {
  const { t } = useTranslation();
  // 文件上传状态
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  // URL上传状态
  const [videoUrl, setVideoUrl] = useState('');
  const [uploadMethod, setUploadMethod] = useState<'file' | 'url'>('file');

  // 基本信息
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [categoryId, setCategoryId] = useState<string>('');

  // 高级设置
  const [tags, setTags] = useState<string[]>([]);
  const [currentTag, setCurrentTag] = useState('');
  const [visibility, setVisibility] = useState<string>('public');
  const [price, setPrice] = useState<string>('0');

  // 分类数据
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  const { toast } = useToast();

  // 获取分类列表
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoadingCategories(true);
      try {
        const response = await getCategories();
        console.log('分类API完整响应:', response); // 调试日志

        // 检查响应结构 - 使用与AdminCategories.tsx中相同的健壮解析逻辑
        if (response && response.data) {
          const categoriesData = response.data?.data?.categories || response.data?.categories || response.data || [];
          console.log('提取的分类数据:', categoriesData); // 调试日志
          setCategories(Array.isArray(categoriesData) ? categoriesData : []);
        } else {
          console.warn('分类API响应格式异常:', response);
          setCategories([]);
        }
      } catch (error) {
        console.error('获取分类失败:', error);
        console.error('错误详情:', error.response?.data || error.message);

        // 显示错误提示
        toast({
          title: '获取分类失败',
          description: '无法加载视频分类，请检查网络连接或联系管理员',
          variant: 'destructive',
        });
        setCategories([]);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // 标签处理函数
  const addTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim()) && tags.length < 10) {
      setTags([...tags, currentTag.trim()]);
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      // You can add more validation here (e.g., file size, type)
      setSelectedFile(file);
      setTitle(file.name.replace(/\.[^/.]+$/, ''));
      setUploadProgress(0);
    }
  };

  const handleSubmit = async () => {
    if (uploadMethod === 'file') {
      await handleFileUpload();
    } else {
      await handleUrlUpload();
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile) {
      toast({ title: '错误', description: '请先选择一个文件', variant: 'destructive' });
      return;
    }
    if (!title.trim()) {
      toast({ title: '错误', description: '请输入视频标题', variant: 'destructive' });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('media', selectedFile);
    formData.append('title', title.trim());

    if (description.trim()) {
      formData.append('description', description.trim());
    }

    if (categoryId && categoryId.trim()) {
      formData.append('categoryId', categoryId);
    }

    if (tags.length > 0) {
      formData.append('tags', JSON.stringify(tags));
    }

    formData.append('visibility', visibility);

    const priceValue = parseFloat(price);
    if (priceValue > 0) {
      formData.append('price', price);
    }

    try {
      await uploadVideo(formData, (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        setUploadProgress(percentCompleted);
      });

      toast({
        title: '成功',
        description: '视频上传成功，正在进入处理和审核流程。'
      });

      // 重置表单
      resetForm();
    } catch (error) {
      toast({
        title: '上传失败',
        description: '视频上传失败，请重试',
        variant: 'destructive',
      });
      console.error(error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleUrlUpload = async () => {
    if (!videoUrl.trim()) {
      toast({ title: '错误', description: '请输入有效的视频链接', variant: 'destructive' });
      return;
    }
    try {
      new URL(videoUrl);
    } catch (_) {
      toast({ title: '错误', description: '请输入一个合法的URL', variant: 'destructive' });
      return;
    }
    if (!title.trim()) {
      toast({ title: '错误', description: '请输入视频标题', variant: 'destructive' });
      return;
    }

    setIsUploading(true);

    const payload = {
      url: videoUrl,
      title: title.trim(),
      description: description.trim(),
      categoryId: categoryId || null,
      tags,
      visibility,
      price: parseFloat(price) || 0,
    };

    try {
      await uploadVideoFromUrl(payload);
      toast({
        title: '成功',
        description: '视频链接已提交，后台将开始下载和处理。'
      });
      resetForm();
    } catch (error: any) {
      toast({
        title: '提交失败',
        description: error.response?.data?.message || '无法提交视频链接，请检查链接或网络',
        variant: 'destructive',
      });
      console.error(error);
    } finally {
      setIsUploading(false);
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setVideoUrl('');
    setTitle('');
    setDescription('');
    setCategoryId('');
    setTags([]);
    setCurrentTag('');
    setVisibility('public');
    setPrice('0');
    setUploadProgress(0);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">发布视频</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：基本信息和高级设置 */}
        <div className="space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 标题 */}
              <div>
                <Label htmlFor="title">{t('form.titleRequired')}</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder={t('form.titlePlaceholder')}
                  disabled={isUploading}
                  className="mt-1"
                />
              </div>

              {/* 描述 */}
              <div>
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="请输入视频描述（可选）"
                  disabled={isUploading}
                  className="mt-1"
                  rows={4}
                />
              </div>

              {/* 分类 */}
              <div>
                <Label htmlFor="category">{t('form.categoryLabel')}</Label>
                <Select value={categoryId || undefined} onValueChange={(value) => setCategoryId(value || '')} disabled={isUploading || isLoadingCategories}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder={
                      isLoadingCategories
                        ? t('form.categoryLoading')
                        : categories.length === 0
                          ? t('common.noData')
                          : t('form.categoryPlaceholder')
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingCategories ? (
                      <SelectItem value="loading" disabled>正在加载...</SelectItem>
                    ) : categories.length === 0 ? (
                      <SelectItem value="empty" disabled>暂无可用分类</SelectItem>
                    ) : (
                      categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {/* 调试信息 */}
                {process.env.NODE_ENV === 'development' && (
                  <p className="text-xs text-gray-500 mt-1">
                    调试: 加载中={isLoadingCategories.toString()}, 分类数量={categories.length}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 高级设置 */}
          <Card>
            <CardHeader>
              <CardTitle>高级设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 标签 */}
              <div>
                <Label htmlFor="tags">标签</Label>
                <div className="mt-1 space-y-2">
                  <div className="flex gap-2">
                    <Input
                      id="tags"
                      value={currentTag}
                      onChange={(e) => setCurrentTag(e.target.value)}
                      onKeyPress={handleTagKeyPress}
                      placeholder="输入标签后按回车添加"
                      disabled={isUploading || tags.length >= 10}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addTag}
                      disabled={!currentTag.trim() || tags.length >= 10 || isUploading}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          {tag}
                          <button
                            onClick={() => removeTag(tag)}
                            disabled={isUploading}
                            className="ml-1 hover:text-destructive"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                  <p className="text-xs text-muted-foreground">
                    已添加 {tags.length}/10 个标签
                  </p>
                </div>
              </div>

              {/* 可见性 */}
              <div>
                <Label htmlFor="visibility">可见性</Label>
                <Select value={visibility} onValueChange={setVisibility} disabled={isUploading}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">公开</SelectItem>
                    <SelectItem value="paid">付费</SelectItem>
                    <SelectItem value="member_only">会员专享</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 价格输入 */}
              {visibility === 'paid' && (
              <div>
                  <Label htmlFor="price">价格</Label>
                <Input
                  id="price"
                  type="number"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                    placeholder="例如: 1.00"
                  disabled={isUploading}
                  className="mt-1"
                    min="0.01"
                    step="0.01"
                />
                  <p className="text-xs text-muted-foreground mt-1">设置一个大于0的价格</p>
              </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 右侧：文件上传 */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>文件上传</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={uploadMethod} onValueChange={(value) => setUploadMethod(value as 'file' | 'url')} className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="file">本地上传</TabsTrigger>
                  <TabsTrigger value="url">网址上传</TabsTrigger>
                </TabsList>
                <TabsContent value="file" className="mt-4">
              <input
                type="file"
                id="file-upload"
                className="hidden"
                onChange={handleFileChange}
                accept="video/*,audio/mp3"
                disabled={isUploading}
              />

              {!selectedFile ? (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <UploadCloud className="mx-auto h-16 w-16 text-gray-400" />
                        <h3 className="mt-4 text-lg font-semibold">选择或拖拽文件</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      支持 MP4, AVI, MOV, WMV, FLV, WebM, MP3 格式<br />
                      最大 500MB
                    </p>
                  </label>
                  <Button
                    onClick={() => document.getElementById('file-upload')?.click()}
                    className="mt-4"
                    disabled={isUploading}
                  >
                    选择文件
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-4 border rounded-lg">
                    {selectedFile.type.startsWith('audio/') ?
                      <Music className="h-8 w-8 text-blue-500" /> :
                      <File className="h-8 w-8 text-green-500" />
                    }
                    <div className="flex-1">
                      <p className="font-medium">{selectedFile.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <button
                      onClick={() => setSelectedFile(null)}
                      disabled={isUploading}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>

                      {isUploading && uploadMethod === 'file' && (
                    <div className="space-y-2">
                      <Progress value={uploadProgress} />
                      <p className="text-sm text-center">{uploadProgress}%</p>
                        </div>
                      )}
                    </div>
                  )}
                </TabsContent>
                <TabsContent value="url" className="mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="video-url">视频链接</Label>
                    <Input
                      id="video-url"
                      type="url"
                      value={videoUrl}
                      onChange={(e) => setVideoUrl(e.target.value)}
                      placeholder="https://example.com/video.mp4"
                      disabled={isUploading}
                    />
                    <p className="text-xs text-muted-foreground">
                      请提供视频文件的直接下载链接。
                    </p>
                  </div>
                </TabsContent>
              </Tabs>
                  <Button
                onClick={handleSubmit}
                disabled={isUploading || !title.trim() || (uploadMethod === 'file' && !selectedFile) || (uploadMethod === 'url' && !videoUrl.trim())}
                className="w-full mt-6"
                    size="lg"
                  >
                    {isUploading ? '正在发布...' : '发布视频'}
                  </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminVideoUpload;
