import React, { useState, useRef, useCallback } from 'react';
import { Upload, Camera, Trash2, User, Loader2 } from 'lucide-react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { uploadAvatar, deleteAvatar } from '@/lib/api';
import { useTranslation } from 'react-i18next';

interface AvatarUploadProps {
  currentAvatar?: string;
  username?: string;
  onAvatarUpdate?: (avatarUrl: string) => void;
  onAvatarDelete?: () => void;
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({
  currentAvatar,
  username = 'User',
  onAvatarUpdate,
  onAvatarDelete
}) => {
  const { t } = useTranslation();
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件选择
  const handleFileSelect = useCallback(async (file: File) => {
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "文件格式错误",
        description: "请选择 JPG、PNG、GIF 或 WebP 格式的图片",
        variant: "destructive",
      });
      return;
    }

    // 验证文件大小 (2MB)
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
      toast({
        title: "文件过大",
        description: "头像文件大小不能超过 2MB",
        variant: "destructive",
      });
      return;
    }

    // 创建预览
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // 上传文件
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await uploadAvatar(formData);
      const avatarUrl = response.data.data.avatarUrl;

      toast({
        title: "头像上传成功",
        description: "您的头像已更新",
      });

      setPreviewUrl(null);
      onAvatarUpdate?.(avatarUrl);
    } catch (error: any) {
      console.error('头像上传失败:', error);
      const errorMessage = error.response?.data?.message || '头像上传失败，请重试';
      toast({
        title: "上传失败",
        description: errorMessage,
        variant: "destructive",
      });
      setPreviewUrl(null);
    } finally {
      setIsUploading(false);
    }
  }, [onAvatarUpdate]);

  // 处理文件输入变化
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // 处理拖拽
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // 点击上传
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  // 删除头像
  const handleDeleteAvatar = async () => {
    if (!currentAvatar) return;

    try {
      await deleteAvatar();
      toast({
        title: "头像已删除",
        description: "您的头像已恢复为默认头像",
      });
      onAvatarDelete?.();
    } catch (error: any) {
      console.error('删除头像失败:', error);
      const errorMessage = error.response?.data?.message || '删除头像失败，请重试';
      toast({
        title: "删除失败",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* 头像显示区域 */}
      <div
        className={`relative group ${
          isDragOver ? 'ring-2 ring-primary ring-offset-2' : ''
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Avatar className="h-32 w-32 border-4 border-background shadow-lg">
          <AvatarImage 
            src={previewUrl || currentAvatar || "/placeholder.svg"} 
            alt={username}
            className="object-cover"
          />
          <AvatarFallback className="text-2xl">
            {username.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>

        {/* 上传覆盖层 */}
        <div 
          className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
          onClick={handleUploadClick}
        >
          {isUploading ? (
            <Loader2 className="h-8 w-8 text-white animate-spin" />
          ) : (
            <Camera className="h-8 w-8 text-white" />
          )}
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleUploadClick}
          disabled={isUploading}
          className="flex items-center space-x-2"
        >
          <Upload className="h-4 w-4" />
          <span>{isUploading ? t('avatar.uploading') : t('avatar.changeAvatar')}</span>
        </Button>

        {currentAvatar && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleDeleteAvatar}
            disabled={isUploading}
            className="flex items-center space-x-2 text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
            <span>{t('avatar.deleteAvatar')}</span>
          </Button>
        )}
      </div>

      {/* 提示文本 */}
      <p className="text-sm text-muted-foreground text-center max-w-xs">
        {t('avatar.supportedFormats')}
      </p>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/gif,image/webp"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

export default AvatarUpload;
