import { useState, useEffect, useCallback } from 'react';
import { Search, Filter, MoreHorizontal, Ban, UserCheck, Trash2, RefreshCw } from 'lucide-react';
import { formatInTimeZone } from 'date-fns-tz';
import { getAdminUsers, batchUserAction } from '@/lib/api';
import { useDebounce } from '@/hooks/useDebounce';
import LoadingSpinner from '@/components/LoadingSpinner';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  nickname: string;
  role: string;
  status: string;
  created_at: string;
  last_login: string;
  content_counts: {
    videos: number;
    comments: number;
  };
}

const AdminUsers = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({ role: 'all', status: 'all' });
  
  const debouncedSearch = useDebounce(searchQuery, 500);

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        search: debouncedSearch,
        role: filters.role === 'all' ? '' : filters.role,
        status: filters.status === 'all' ? '' : filters.status,
      };
      const response = await getAdminUsers(params);

      // 安全地访问响应数据
      const responseData = response?.data?.data;
      if (responseData) {
        setUsers(responseData.users || []);
        setPagination(prev => ({ ...prev, total: responseData.total || 0 }));
        setError(null);
      } else {
        setUsers([]);
        setError('数据格式错误，请检查API响应');
      }
    } catch (err: any) {
      console.error('获取用户列表失败:', err);
      if (err.response?.status === 401) {
        setError('认证失败，请重新登录');
      } else if (err.response?.status === 403) {
        setError('权限不足，无法访问用户管理');
      } else if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('网络连接失败，请检查后端服务是否运行');
      } else {
        setError(err.response?.data?.message || '获取用户列表失败，请稍后重试');
      }
      setUsers([]);
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.pageSize, debouncedSearch, filters]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);
  
  const handleUserAction = async (action, userIds) => {
    if (!Array.isArray(userIds)) userIds = [userIds];
    try {
      await batchUserAction(action, userIds);
      fetchUsers(); // Refresh users after action
    } catch (error) {
      console.error(`Failed to ${action} user(s):`, error);
      alert(`Failed to perform action: ${action}`);
    }
  };

  const getRoleLabel = (role) => {
    const roleMap = {
      'user': '普通用户',
      'member': '会员',
      'vip': 'VIP',
      'admin': '管理员'
    };
    return roleMap[role] || role;
  };

  const getStatusLabel = (status) => {
    const statusMap = {
      'active': '活跃',
      'inactive': '非活跃',
      'banned': '已封禁'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800';
      case 'banned':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">用户管理</h1>
        <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
          导出用户数据
        </button>
      </div>

      {/* Filters */}
      <div className="bg-card p-4 rounded-lg border">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={20} />
              <input
                type="text"
                placeholder="搜索用户名、邮箱或昵称..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-input rounded-md"
              />
            </div>
          </div>
          
          <select
            value={filters.role}
            onChange={(e) => setFilters(prev => ({ ...prev, role: e.target.value }))}
            className="px-3 py-2 bg-background border border-input rounded-md"
          >
            <option value="all">所有角色</option>
            <option value="user">普通用户</option>
            <option value="member">会员</option>
            <option value="vip">VIP</option>
            <option value="admin">管理员</option>
          </select>
          
          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-2 bg-background border border-input rounded-md"
          >
            <option value="all">所有状态</option>
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
            <option value="banned">已封禁</option>
            <option value="deleted">已删除</option>
          </select>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-card rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted">
              <tr>
                <th className="px-4 py-3 text-left font-medium">用户信息</th>
                <th className="px-4 py-3 text-left font-medium">角色</th>
                <th className="px-4 py-3 text-left font-medium">状态</th>
                <th className="px-4 py-3 text-left font-medium">注册时间</th>
                <th className="px-4 py-3 text-left font-medium">最后登录</th>
                <th className="px-4 py-3 text-left font-medium">内容统计</th>
                <th className="px-4 py-3 text-left font-medium">操作</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr><td colSpan="7" className="text-center py-12"><LoadingSpinner text="正在加载用户数据..." /></td></tr>
              ) : error ? (
                <tr><td colSpan="7" className="text-center py-12 text-red-500">{error}</td></tr>
              ) : users.length === 0 ? (
                <tr><td colSpan="7" className="text-center py-12 text-muted-foreground">暂无用户数据</td></tr>
              ) : users.map((user) => (
                <tr key={user.id} className="border-b border-border hover:bg-muted/50">
                  <td className="px-4 py-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                        <span className="text-primary-foreground font-medium">
                          {user.username[0].toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">{user.nickname || user.username}</p>
                        <p className="text-sm text-muted-foreground">@{user.username}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <span className="px-2 py-1 bg-secondary text-secondary-foreground rounded-full text-xs">
                      {getRoleLabel(user.role)}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(user.status)}`}>
                      {getStatusLabel(user.status)}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-muted-foreground">
                    {user.created_at ? formatInTimeZone(new Date(user.created_at), 'Asia/Shanghai', 'yyyy-MM-dd HH:mm:ss') : 'N/A'}
                  </td>
                  <td className="px-4 py-3 text-sm text-muted-foreground">
                    {user.last_login ? formatInTimeZone(new Date(user.last_login), 'Asia/Shanghai', 'yyyy-MM-dd HH:mm:ss') : '从未'}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <div className="space-y-1">
                      <div>视频: {user.content_counts.videos}</div>
                      <div>评论: {user.content_counts.comments}</div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center space-x-2">
                      {user.status === 'banned' ? (
                        <button onClick={() => handleUserAction('unban', user.id)} className="p-1 text-green-600 hover:bg-green-100 rounded" title="解封用户">
                          <UserCheck size={16} />
                        </button>
                      ) : user.status === 'deleted' ? (
                        <button onClick={() => handleUserAction('restore', user.id)} className="p-1 text-blue-600 hover:bg-blue-100 rounded" title="恢复用户">
                          <RefreshCw size={16} />
                        </button>
                      ) : (
                        <button onClick={() => handleUserAction('ban', user.id)} className="p-1 text-red-600 hover:bg-red-100 rounded" title="封禁用户">
                          <Ban size={16} />
                        </button>
                      )}
                      <button onClick={() => handleUserAction('delete', user.id)} className="p-1 text-red-600 hover:bg-red-100 rounded" title="删除用户">
                        <Trash2 size={16} />
                      </button>
                      <button className="p-1 text-muted-foreground hover:bg-accent rounded" title="更多操作">
                        <MoreHorizontal size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          第 {pagination.page} 页，共 {Math.ceil(pagination.total / pagination.pageSize)} 页 (总计 {pagination.total} 个用户)
        </div>
        <div className="flex space-x-2">
          <button 
            onClick={() => setPagination(p => ({ ...p, page: p.page - 1 }))}
            className="px-3 py-1 border border-input rounded-md hover:bg-accent disabled:opacity-50" 
            disabled={pagination.page <= 1}
          >
            上一页
          </button>
          <button 
            onClick={() => setPagination(p => ({ ...p, page: p.page + 1 }))}
            className="px-3 py-1 border border-input rounded-md hover:bg-accent disabled:opacity-50" 
            disabled={pagination.page * pagination.pageSize >= pagination.total}
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminUsers;
