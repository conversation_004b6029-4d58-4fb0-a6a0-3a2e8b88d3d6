const express = require('express');
const router = express.Router();

// 导入控制器和中间件
const memberController = require('./controllers/memberController');
const { verifyToken, requireMember, requireAdmin } = require('../../middleware/auth');
const { disabledRateLimiter } = require('../../middleware/rateLimiter');

// 公开路由
router.get('/plans', memberController.getMembershipPlans);
router.get('/plans/:id', memberController.getPlanDetails);
router.post('/plans/compare', memberController.comparePlans);

// 需要认证的路由
router.use(verifyToken);

// 会员信息
router.get('/my-membership', memberController.getMyMembership);
router.get('/my-history', memberController.getMembershipHistory);

// 会员操作
router.post('/subscribe', memberController.subscribePlan);
router.post('/cancel', memberController.cancelMembership);
router.post('/auto-renew', memberController.setAutoRenew);

// 会员专用功能
router.get('/benefits', requireMember, memberController.getMemberBenefits);
router.get('/exclusive-content', requireMember, memberController.getExclusiveContent);

// 管理员路由
router.use('/admin', requireAdmin);
router.get('/admin/stats', memberController.getMembershipStats);
router.get('/admin/overview', memberController.getMembershipOverview);
router.get('/admin/plans', memberController.getAllPlans);
router.post('/admin/plans', memberController.createPlan);
router.put('/admin/plans/:id', memberController.updatePlan);
router.delete('/admin/plans/:id', memberController.deletePlan);
router.get('/admin/users', memberController.getAdminMemberUsers);

// 测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '会员模块测试接口',
    module: 'member',
    user: req.user || null
  });
});

module.exports = router;
