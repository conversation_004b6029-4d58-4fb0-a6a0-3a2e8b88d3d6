import { useState } from 'react';
import { <PERSON>, Eye, Heart, MessageCircle, Clock, Star, Plus } from 'lucide-react';
import { toggleLike, toggleFavorite } from '@/lib/api';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';

interface Video {
  id: string | number;
  title: string;
  thumbnail?: string;
  duration: number;
  username: string;
  category_name: string;
  view_count: number;
  like_count: number;
  comment_count: number;
  is_liked?: boolean;
  is_favorited?: boolean;
}

interface VideoCardProps {
  video: Video;
  onPlay: () => void;
  variant?: 'default' | 'compact';
  onFavoriteChange?: (type: 'like' | 'favorite', newState: boolean) => void;
  onAddToPlaylist?: (video: Video) => void;
}

const VideoCard: React.FC<VideoCardProps> = ({ video, onPlay, variant = 'default', onFavoriteChange, onAddToPlaylist }) => {
  const { t } = useTranslation();
  const [isLiked, setIsLiked] = useState(video.is_liked);
  const [likeCount, setLikeCount] = useState(video.like_count);
  const [isFavorited, setIsFavorited] = useState(video.is_favorited);

  // 优化上传者显示逻辑
  const isSystemAdmin = video.username === t('videoInfo.systemAdmin');
  const displayUsername = video.username || t('videoInfo.anonymousAuthor');

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatNumber = (num) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  const handleLike = async (e) => {
    e.stopPropagation(); // Prevent card's onPlay from firing
    try {
      const response = await toggleLike(video.id, 'video');
      const newLikedState = response.data.data.liked;
      const newCount = newLikedState ? likeCount + 1 : Math.max(0, likeCount - 1);

      setIsLiked(newLikedState);
      setLikeCount(newCount);

      // 通知父组件状态变化
      if (onFavoriteChange) {
        onFavoriteChange('like', newLikedState);
      }
    } catch (error) {
      console.error("Failed to toggle like:", error);
    }
  };

  const handleFavorite = async (e) => {
    e.stopPropagation(); // Prevent card's onPlay from firing
    try {
      await toggleFavorite(video.id);
      const newFavoritedState = !isFavorited;
      setIsFavorited(newFavoritedState);

      // 通知父组件收藏状态变化
      if (onFavoriteChange) {
        onFavoriteChange('favorite', newFavoritedState);
      }
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
    }
  };

  const handleAddToPlaylist = (e) => {
    e.stopPropagation(); // Prevent card's onPlay from firing
    if (onAddToPlaylist) {
      onAddToPlaylist(video);
    }
  };

  if (variant === 'compact') {
    return (
      <div 
        className="flex items-center space-x-4 bg-card rounded-lg overflow-hidden border hover:shadow-md transition-shadow cursor-pointer p-2"
        onClick={onPlay}
      >
        <div className="relative group flex-shrink-0">
          <img 
            src={video.thumbnail || "/placeholder.svg"} 
            alt={video.title}
            className="w-32 h-20 object-cover rounded"
          />
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center">
            <div className="w-8 h-8 bg-primary/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
              <Play className="h-5 w-5 text-primary-foreground ml-0.5" fill="currentColor" />
            </div>
          </div>
          <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
            {formatDuration(video.duration)}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-sm mb-1 line-clamp-2">{video.title}</h3>
          {!isSystemAdmin && (
            <p className="text-xs text-muted-foreground mb-1">{displayUsername}</p>
          )}
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <div className="flex items-center space-x-0.5">
              <Eye size={12} />
              <span>{formatNumber(video.view_count)}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card rounded-lg overflow-hidden border hover:shadow-lg transition-shadow cursor-pointer">
      <div className="relative group" onClick={onPlay}>
        <img 
          src={video.thumbnail || "/placeholder.svg"} 
          alt={video.title}
          className="w-full h-48 object-cover"
        />
        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center">
          <div className="w-12 h-12 bg-primary/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <Play className="h-6 w-6 text-primary-foreground ml-1" fill="currentColor" />
          </div>
        </div>
        <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
          <div className="flex items-center space-x-1">
            <Clock size={12} />
            <span>{formatDuration(video.duration)}</span>
          </div>
        </div>
      </div>
      
      <div className="p-4">
        <h3 className="font-semibold text-sm mb-2 line-clamp-2">{video.title}</h3>
        
        {!isSystemAdmin && (
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
            <span>{displayUsername}</span>
          </div>
        )}
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1">
              <Eye size={12} />
              <span>{formatNumber(video.view_count)}</span>
            </div>
            <div 
              className="flex items-center space-x-1 cursor-pointer"
              onClick={handleLike}
            >
              <Heart size={12} className={isLiked ? "fill-red-500 text-red-500" : ""} />
              <span>{formatNumber(likeCount)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <MessageCircle size={12} />
              <span>{formatNumber(video.comment_count)}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div
              className="flex items-center space-x-1 cursor-pointer"
              onClick={handleFavorite}
            >
              <Star size={12} className={isFavorited ? "fill-yellow-400 text-yellow-400" : ""} />
            </div>
            {onAddToPlaylist && (
              <div
                className="flex items-center space-x-1 cursor-pointer hover:text-primary"
                onClick={handleAddToPlaylist}
                title="添加到播放列表"
              >
                <Plus size={12} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoCard;
