# 服务器配置
PORT=3000
NODE_ENV=development

# HTTPS配置
ENABLE_HTTPS=true
HTTPS_PORT=3443
SSL_CERT_PATH=./certs/certificate.crt
SSL_KEY_PATH=./certs/private.key
FORCE_HTTPS=false
AUTO_REDIRECT_HTTPS=true

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=video_platform

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 文件上传配置
MAX_FILE_SIZE=500MB
ALLOWED_VIDEO_FORMATS=mp4,avi,mov,wmv,flv,webm
ALLOWED_IMAGE_FORMATS=jpg,jpeg,png,gif,webp

# FFmpeg配置
# 注意：请根据您的系统设置正确的FFmpeg路径
# Windows: FFMPEG_PATH=ffmpeg (如果已添加到PATH) 或 FFMPEG_PATH=./bin/ffmpeg.exe
# Linux/macOS: FFMPEG_PATH=ffmpeg (如果已添加到PATH) 或 FFMPEG_PATH=./bin/ffmpeg
FFMPEG_PATH=ffmpeg
VIDEO_QUALITY_LEVELS=360p,480p,720p,1080p

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# 存储路径配置
UPLOAD_PATH=./uploads
VIDEO_PATH=./uploads/videos
AVATAR_PATH=./uploads/avatars
TEMP_PATH=./uploads/temp

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# 支付配置
API_BASE_URL=http://localhost:3000
FRONTEND_URL=http://localhost:8081

# Creem.io 支付配置
CREEM_API_KEY=your_creem_api_key
CREEM_WEBHOOK_SECRET=your_creem_webhook_secret

## 易支付配置
EPAY_ENABLED=true
EPAY_API_URL=https://pay.example.com
EPAY_PARTNER_ID=your_partner_id
EPAY_PARTNER_KEY=your_partner_key

## 微信支付配置
WECHAT_PAY_ENABLED=false
WECHAT_APP_ID=your_wechat_app_id
WECHAT_MCH_ID=your_merchant_id
WECHAT_API_KEY=your_api_key
WECHAT_CERT_PATH=./certs/wechat/apiclient_cert.pem
WECHAT_KEY_PATH=./certs/wechat/apiclient_key.pem

## 支付宝配置
ALIPAY_ENABLED=false
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_private_key
ALIPAY_PUBLIC_KEY=your_alipay_public_key
ALIPAY_GATEWAY_URL=https://openapi.alipay.com/gateway.do

## 支付通用配置
ORDER_EXPIRE_MINUTES=30
PAYMENT_TIMEOUT_SECONDS=300
ENABLE_PAYMENT_LOG=true
PAYMENT_CURRENCY=CNY
MIN_PAYMENT_AMOUNT=0.01
MAX_PAYMENT_AMOUNT=50000
SUPPORTED_PAYMENT_METHODS=epay,alipay,wechat
DEFAULT_PAYMENT_METHOD=epay
