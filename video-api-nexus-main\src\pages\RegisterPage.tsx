import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQuery } from '@tanstack/react-query';
import { Link, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { getPublicSettings, sendRegistrationCode, register as registerUser } from '@/services/api'; // 修正导入
import { User, Mail, Lock, UserPlus } from 'lucide-react';


const registerSchema = z.object({
  username: z.string().min(3, "用户名至少需要3个字符"),
  email: z.string().email("请输入有效的邮箱地址"),
  password: z.string().min(6, "密码至少需要6个字符"),
  verificationCode: z.string().optional(),
});

type RegisterFormValues = z.infer<typeof registerSchema>;

const RegisterPage = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const { toast } = useToast();

  const { data: publicSettingsData, isLoading: isLoadingSettings } = useQuery({
    queryKey: ['publicSettings'],
    queryFn: getPublicSettings, // 直接使用导入的函数
  });

  const settings = publicSettingsData?.data || {};
  const requireVerification = settings['user.requireEmailVerification'] === true;


  const { register, handleSubmit, formState: { errors }, getValues } = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
  });

  const [isSendingCode, setIsSendingCode] = React.useState(false);
  const [countdown, setCountdown] = React.useState(0);

  React.useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const sendCodeMutation = useMutation({
    mutationFn: (email: string) => sendRegistrationCode(email), // 直接使用导入的函数
    onMutate: () => {
      setIsSendingCode(true);
    },
    onSuccess: (data: any) => {
      toast({ title: "成功", description: "验证码已发送至您的邮箱。" });
      setCountdown(data.data.ttl || 60);
    },
    onError: (error: any) => {
      toast({ title: "错误", description: error.response?.data?.message || "发送失败", variant: "destructive" });
      setCountdown(0);
    },
    onSettled: () => {
      setIsSendingCode(false);
    }
  });

  const handleSendCode = () => {
    const email = getValues("email");
    const emailError = registerSchema.shape.email.safeParse(email).error;
    if (!email || emailError) {
      toast({ title: "提示", description: "请输入有效的邮箱地址再发送验证码", variant: "destructive" });
      return;
    }
    sendCodeMutation.mutate(email);
  };

  const registrationMutation = useMutation({
    mutationFn: (data: RegisterFormValues) => registerUser(data), // 使用导入的重命名函数
    onSuccess: (data: any) => {
      login(data.data.token);
      toast({ title: "注册成功", description: "欢迎您的加入！" });
      navigate("/");
    },
    onError: (error: any) => {
      toast({
        title: "注册失败",
        description: error.response?.data?.message || "发生未知错误",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: RegisterFormValues) => {
    if (requireVerification && (!data.verificationCode || data.verificationCode.length !== 6)) {
      toast({ title: "提示", description: "请输入6位验证码", variant: "destructive" });
      return;
    }
    registrationMutation.mutate(data);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 px-4">
       <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">创建账户</CardTitle>
          <CardDescription>加入我们，开始您的视频之旅</CardDescription>
        </CardHeader>
        <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">用户名</Label>
             <div className="relative">
                <User className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input id="username" {...register("username")} className="pl-10 h-11" placeholder="设置一个独特的用户名"/>
            </div>
            {errors.username && <p className="text-red-500 text-sm mt-1">{errors.username.message}</p>}
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">邮箱</Label>
             <div className="relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input id="email" type="email" {...register("email")} className="pl-10 h-11" placeholder="用于登录和接收通知"/>
            </div>
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">密码</Label>
            <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input id="password" type="password" {...register("password")} className="pl-10 h-11" placeholder="设置一个安全的密码"/>
            </div>
            {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
          </div>
          
          {isLoadingSettings ? (
             <div className="space-y-2">
                <div className="h-6 w-24 bg-gray-200 rounded animate-pulse dark:bg-gray-700" />
                <div className="h-11 w-full bg-gray-200 rounded animate-pulse dark:bg-gray-700" />
            </div>
          ) : requireVerification && (
            <div className="space-y-2">
              <Label htmlFor="verificationCode">验证码</Label>
              <div className="flex space-x-2">
                <Input id="verificationCode" {...register("verificationCode")} placeholder="6位数字"/>
                <Button type="button" onClick={handleSendCode} disabled={sendCodeMutation.isPending || countdown > 0}>
                  {sendCodeMutation.isPending ? "发送中..." : countdown > 0 ? `${countdown}s` : "获取验证码"}
                </Button>
              </div>
              {errors.verificationCode && <p className="text-red-500 text-sm mt-1">{errors.verificationCode.message}</p>}
            </div>
          )}

          <Button type="submit" className="w-full h-11" disabled={registrationMutation.isPending}>
             <UserPlus className="w-4 h-4 mr-2" />
            {registrationMutation.isPending ? "注册中..." : "创建账户"}
          </Button>
        </form>
        </CardContent>
        <CardFooter className="text-center text-sm">
            <p className="w-full">
                已有账户?{" "}
                <Link to="/login" className="font-medium text-blue-600 hover:underline dark:text-blue-500">
                    立即登录
                </Link>
            </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default RegisterPage; 