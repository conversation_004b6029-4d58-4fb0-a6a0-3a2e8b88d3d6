const connectionManager = require('../../../database/ConnectionManager');
const Withdrawal = require('../../../database/models/Withdrawal');
const User = require('../../../database/models/User');
const { AppError } = require('../../../middleware/errorHandler');
const { getPagination, getPagingData } = require('../../../utils/pagination');

class AdminWithdrawalService {

  async getWithdrawals({ page, limit, status, userId, keyword }) {
    const { limit: queryLimit, offset } = getPagination(page, limit);
    
    let whereConditions = ['1 = 1'];
    let queryParams = [];

    if (status) {
      whereConditions.push('w.status = ?');
      queryParams.push(status);
    }
    if (userId) {
      whereConditions.push('w.user_id = ?');
      queryParams.push(userId);
    }
    if (keyword) {
      whereConditions.push('(u.username LIKE ? OR u.email LIKE ? OR w.wallet_address LIKE ?)');
      const searchTerm = `%${keyword}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }
    
    const whereClause = whereConditions.join(' AND ');

    const countQuery = `SELECT COUNT(*) as total FROM withdrawals w LEFT JOIN users u ON w.user_id = u.id WHERE ${whereClause}`;
    const totalResult = await connectionManager.executeQuery(countQuery, queryParams);
    const totalItems = totalResult[0]?.total || 0;

    const dataQuery = `
      SELECT w.*, u.username, u.email
      FROM withdrawals w
      LEFT JOIN users u ON w.user_id = u.id
      WHERE ${whereClause}
      ORDER BY w.requested_at DESC
      LIMIT ${parseInt(queryLimit, 10)} OFFSET ${parseInt(offset, 10)}
    `;
    const withdrawals = await connectionManager.executeQuery(dataQuery, queryParams);
    
    return getPagingData({ data: withdrawals, totalItems, page, limit });
  }

  async approveWithdrawal(withdrawalId, adminId, transaction_hash, notes) {
    return connectionManager.transaction(async (connection) => {
      const withdrawal = await Withdrawal.findById(withdrawalId, 'FOR UPDATE', connection);
      if (!withdrawal || withdrawal.status !== 'pending') {
        throw new AppError('提现请求不存在或状态不正确', 404, 'WITHDRAWAL_NOT_FOUND_OR_INVALID_STATUS');
      }

      // 只是批准，等待财务打款后再标记为完成
      // 如果提供了交易哈希，则直接标记为完成
      const newStatus = transaction_hash ? 'completed' : 'approved';

      await Withdrawal.update(withdrawalId, {
        status: newStatus,
        processed_by: adminId,
        processed_at: new Date(),
        transaction_hash: transaction_hash || null,
        notes: notes || null,
      }, connection);
    });
  }

  async rejectWithdrawal(withdrawalId, adminId, reason, notes) {
    return connectionManager.transaction(async (connection) => {
      const withdrawal = await Withdrawal.findById(withdrawalId, 'FOR UPDATE', connection);
      if (!withdrawal || withdrawal.status !== 'pending') {
        throw new AppError('提现请求不存在或状态不正确', 404, 'WITHDRAWAL_NOT_FOUND_OR_INVALID_STATUS');
      }

      // 将金额退还给用户
      const user = await User.findById(withdrawal.user_id, 'FOR UPDATE', connection);
      const newBalance = parseFloat(user.balance) + parseFloat(withdrawal.amount);
      await User.update(user.id, { balance: newBalance }, connection);

      // 更新提现请求状态
      await Withdrawal.update(withdrawalId, {
        status: 'rejected',
        processed_by: adminId,
        processed_at: new Date(),
        rejection_reason: reason,
        notes: notes,
      }, connection);
    });
  }
}

module.exports = new AdminWithdrawalService(); 