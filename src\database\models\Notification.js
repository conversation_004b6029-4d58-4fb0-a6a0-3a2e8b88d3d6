const BaseModel = require('../BaseModel');

class Notification extends BaseModel {
  constructor() {
    super('notifications');
  }

  // 创建通知
  async createNotification(data) {
    const notificationData = {
      user_id: data.userId,
      type: data.type,
      title: data.title,
      message: data.message,
      reference_id: data.referenceId,
      reference_type: data.referenceType,
    };
    return await this.create(notificationData);
  }

  // 根据用户ID查找通知
  async findByUserId(userId, options = {}) {
    const { unreadOnly = false, page = 1, pageSize = 20 } = options;
    const where = { user_id: userId };
    if (unreadOnly) {
      where.is_read = false;
    }

    const findAllOptions = {
      orderBy: 'created_at',
      orderDirection: 'DESC',
      limit: pageSize,
      offset: (page - 1) * pageSize,
    };

    const notifications = await this.findAll(where, findAllOptions);
    const total = await this.count(where);
    
    return {
      rows: notifications,
      count: total,
    };
  }

  // 标记为已读
  async markAsRead(notificationId, userId) {
    return await this.updateBy({ id: notificationId, user_id: userId }, { is_read: true });
  }

  // 全部标记为已读
  async markAllAsRead(userId) {
    return await this.updateBy({ user_id: userId, is_read: false }, { is_read: true });
  }
}

module.exports = new Notification(); 