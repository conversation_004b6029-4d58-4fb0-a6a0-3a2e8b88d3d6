import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import VideoCard from '@/components/VideoCard';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/hooks/useAuth';
import { useVideoInteractions } from '../hooks/useVideoInteractions';
import { usePlaylistSync } from '../hooks/usePlaylistSync';
import { PlaylistItem } from '../types/playlist';
import { toast } from '../hooks/use-toast';
import PlaylistSyncStatus from '../components/playlist/PlaylistSyncStatus';
import SearchBar from '@/components/SearchBar';
import { useDebounce } from '@/hooks/useDebounce';
import { useTranslation } from 'react-i18next';

const Index = () => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();
  const [videos, setVideos] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pagination, setPagination] = useState({ page: 1, limit: 12, totalPages: 1 });
  const [error, setError] = useState<string | null>(null);

  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const [selectedCategory, setSelectedCategory] = useState(() => localStorage.getItem('selectedCategory') || t('common.all'));
  const [sortOrder, setSortOrder] = useState(() => localStorage.getItem('sortOrder') || t('common.recommended'));

  const { interactionStates, updateInteractionState } = useVideoInteractions(videos.map(v => v.id));

  // 分类名称翻译函数
  const translateCategoryName = useCallback((categoryName: string) => {
    if (!categoryName) return '';
    // 如果是"全部"分类，使用通用翻译
    if (categoryName === t('common.all')) return categoryName;
    // 尝试从categories翻译键中获取翻译，如果没有则返回原名称
    const translationKey = `categories.${categoryName}`;
    const translated = t(translationKey);
    // 如果翻译键不存在，t()会返回键本身，我们检查是否等于键来判断是否有翻译
    return translated !== translationKey ? translated : categoryName;
  }, [t]);

  const fetchVideos = useCallback(async (pageToFetch = 1, shouldAppend = false) => {
    if (pageToFetch === 1) setLoading(true);
    else setLoadingMore(true);
        setError(null);

    try {
      const params = new URLSearchParams({
        page: String(pageToFetch),
        limit: String(pagination.limit),
      });

      const sortMap = { [t('page.home.sort.latest')]: 'created_at', [t('page.home.sort.popular')]: 'view_count' };
      if (sortOrder !== t('page.home.sort.recommended') && sortMap[sortOrder]) {
        params.append('sortBy', sortMap[sortOrder]);
        params.append('order', 'desc');
      }

      const categoryToFind = categories.find(c => c.name === selectedCategory);
      if (selectedCategory !== t('common.all') && categoryToFind) {
        params.append('categoryId', String(categoryToFind.id));
      }

      if (debouncedSearchQuery) {
        params.append('keyword', debouncedSearchQuery);
      }

      const response = await fetch(`/api/video/list?${params.toString()}`);
      if (!response.ok) throw new Error('网络响应错误');
      
      const result = await response.json();
      const newVideos = result.data?.videos || [];
      const newPagination = result.data?.pagination || {};

      setVideos(prev => shouldAppend ? [...prev, ...newVideos] : newVideos);
      setPagination(prev => ({ ...prev, ...newPagination }));

      } catch (err: any) {
        setError(err.message);
      console.error("获取视频数据失败:", err);
      } finally {
      if (pageToFetch === 1) setLoading(false);
      else setLoadingMore(false);
      }
  }, [pagination.limit, sortOrder, selectedCategory, categories, debouncedSearchQuery]);

  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch('/api/video/categories/list');
      if (!response.ok) throw new Error(t('page.home.fetchCategoriesError'));
      const result = await response.json();
      const categoriesFromApi = result.data?.categories || [];
      const allCategories = [{ id: null, name: t('common.all') }, ...categoriesFromApi];
      setCategories(allCategories);
    } catch (err) {
      console.error(err);
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  useEffect(() => {
    if (categories.length > 0) {
      setVideos([]);
      fetchVideos(1, false);
    }
  }, [selectedCategory, sortOrder, debouncedSearchQuery, categories]);

  useEffect(() => {
    localStorage.setItem('selectedCategory', selectedCategory);
  }, [selectedCategory]);

  useEffect(() => {
    localStorage.setItem('sortOrder', sortOrder);
  }, [sortOrder]);

  // 处理语言切换时的分类状态同步
  useEffect(() => {
    // 如果当前选中的是"全部"分类，需要更新为当前语言的"全部"
    const allCategoryTranslation = t('common.all');
    if (selectedCategory !== allCategoryTranslation &&
        (selectedCategory === '全部' || selectedCategory === 'All' ||
         selectedCategory === 'すべて' || selectedCategory === '전체')) {
      setSelectedCategory(allCategoryTranslation);
    }
  }, [t, selectedCategory]);

  const handleLoadMore = () => {
    if (pagination.page < pagination.totalPages) {
      fetchVideos(pagination.page + 1, true);
    }
  };
  
  const playlist = usePlaylistSync({ autoSync: false });
  const handleAddToPlaylist = (video: any) => {
    const playlistItem: PlaylistItem = {
      id: `item_${video.id}_${Date.now()}`,
      videoId: video.id,
      title: video.title,
      duration: video.duration || 0,
      thumbnail: video.thumbnail_url,
      mediaType: video.media_type || 'video',
      addedAt: new Date(),
      url: video.url,
    };
    playlist.addToPlaylistSync(playlistItem);
    toast({
      title: '已添加到播放列表',
      description: `"${video.title}" 已添加到播放列表并同步到服务器`,
    });
  };

  return (
    <div className="container mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <h2 className="text-xl sm:text-2xl font-bold mb-2 sm:mb-0">{t('page.home.title')}</h2>
        <div className="w-full sm:w-auto">
          <SearchBar searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
        </div>
      </div>

      <div className="space-y-4 mb-6">
        <div className="overflow-x-auto">
          <div className="flex items-center space-x-2 pb-2 min-w-max">
            {(categories.length === 0 && loading) ? (
              Array.from({ length: 5 }).map((_, index) => (
                <Skeleton key={index} className="h-8 sm:h-10 w-16 sm:w-24 rounded-md flex-shrink-0" />
              ))
            ) : (
              categories.map((category) => (
                <Button
                  key={category.id || 'all'}
                  variant={selectedCategory === category.name ? 'default' : 'ghost'}
                  size="sm"
                  className="whitespace-nowrap flex-shrink-0 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
                  onClick={() => setSelectedCategory(category.name)}
                >
                  {translateCategoryName(category.name)}
                </Button>
              ))
            )}
          </div>
        </div>

        <div className="flex items-center justify-start sm:justify-end space-x-1 sm:space-x-2">
          {[
            { key: 'latest', label: t('common.latest') },
            { key: 'popular', label: t('common.popular') },
            { key: 'recommended', label: t('common.recommended') }
          ].map(order => (
          <Button
              key={order.key}
              variant={sortOrder === order.label ? 'default' : 'ghost'}
            size="sm"
            className="text-xs sm:text-sm px-2 sm:px-3"
              onClick={() => setSortOrder(order.label)}
          >
              {order.label}
          </Button>
          ))}
        </div>
      </div>

      {error && <p className="text-destructive text-center text-sm">{error}</p>}

      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-6">
        {loading ? (
          Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="bg-card rounded-lg overflow-hidden shadow-sm border">
              <Skeleton className="w-full h-36 sm:h-48" />
              <div className="p-3 sm:p-4 space-y-2 sm:space-y-3">
                <Skeleton className="h-4 sm:h-5 w-3/4" />
                <Skeleton className="h-3 sm:h-4 w-1/2" />
                <div className="flex justify-between items-center pt-1 sm:pt-2">
                  <div className="flex space-x-2 sm:space-x-4">
                    <Skeleton className="h-3 sm:h-4 w-8 sm:w-12" />
                    <Skeleton className="h-3 sm:h-4 w-8 sm:w-12" />
                    <Skeleton className="h-3 sm:h-4 w-8 sm:w-12" />
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          videos.map(video => {
            const videoId = String(video.id);
            const interactionState = interactionStates[videoId];
            const enhancedVideo = {
              ...video,
              is_liked: interactionState?.isLiked || false,
              is_favorited: interactionState?.isFavorited || false,
            };
            return (
              <VideoCard
                key={video.id}
                video={enhancedVideo}
                onInteractionChange={(type, newState) => {
                  updateInteractionState(video.id, type, newState);
                }}
                onAddToPlaylist={handleAddToPlaylist}
              />
            );
          })
        )}
      </div>

      {!loading && pagination.page < pagination.totalPages && (
        <div className="text-center mt-8">
          <Button onClick={handleLoadMore} disabled={loadingMore}>
            {loadingMore ? t('page.home.loading') : t('page.home.loadMore')}
          </Button>
        </div>
      )}

      {!loading && pagination.page >= pagination.totalPages && videos.length > 0 && (
         <div className="text-center mt-8 text-muted-foreground">
           {t('common.noMoreContent')}
         </div>
      )}
    </div>
  );
};

export default Index;
