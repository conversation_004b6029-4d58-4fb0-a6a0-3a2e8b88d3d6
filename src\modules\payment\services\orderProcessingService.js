const logger = require('../../../utils/logger');
const { mysql: db } = require('../../../config/database');
const Video = require('../../../database/models/Video');
const Earning = require('../../../database/models/Earning');
const User = require('../../../database/models/User');
const BalanceLog = require('../../../database/models/BalanceLog');

/**
 * 处理订单业务逻辑
 * @param {object} order - 订单对象
 * @param {object} connection - 数据库连接对象，用于事务处理
 */
async function processOrderBusiness(order, connection) {
  const executor = connection || db;

  switch (order.type) {
    case 'membership':
      // 激活会员
      const Membership = require('../../../database/models/Membership');
      // 确保 renewMembership 也能处理事务连接
      await Membership.renewMembership(order.user_id, order.target_id, {
        method: order.payment_method,
        transactionId: order.transaction_id
      }, connection);
      break;
    case 'video':
      logger.info(`[Earnings] Processing video order: ${order.order_no}`);
      // --- NEW: Video Sales Earning Logic ---

      // 1. Get video and creator info
      const video = await Video.findById(order.target_id, connection);
      if (!video) {
        logger.error(`[Earnings] Video not found for order ${order.order_no} with videoId ${order.target_id}. Skipping earning process.`);
        return;
      }
      logger.info(`[Earnings] Found video "${video.title}" (ID: ${video.id}) for order ${order.order_no}.`);

      const creatorId = video.user_id;
      const buyerId = order.user_id;
      logger.info(`[Earnings] Creator ID: ${creatorId}, Buyer ID: ${buyerId}.`);

      // Prevent user from earning from their own purchase
      if (creatorId === buyerId) {
        logger.warn(`[Earnings] Creator is the same as buyer for order ${order.order_no}. Skipping earning process.`);
        return;
      }
      
      // 2. Calculate earnings (e.g., 30% platform commission)
      const commissionRate = 0.30;
      const totalAmount = parseFloat(order.final_amount);
      const platformFee = totalAmount * commissionRate;
      const creatorEarning = totalAmount - platformFee;
      logger.info(`[Earnings] Calculation: Total=${totalAmount}, Fee=${platformFee}, Earning=${creatorEarning}`);

      // 3. Create an earning record
      const earningData = {
        order_id: order.id,
        video_id: video.id,
        creator_id: creatorId,
        buyer_id: buyerId,
        total_amount: totalAmount,
        platform_fee: platformFee,
        creator_earning: creatorEarning,
        commission_rate: commissionRate
      };
      const earningId = await Earning.create(earningData, connection);
      logger.info(`[Earnings] Created earning record with ID: ${earningId}.`);
      
      // 4. Update creator's balance
      const updateCreatorBalanceSql = 'UPDATE users SET balance = balance + ? WHERE id = ?';
      const [updateResult] = await executor.query(updateCreatorBalanceSql, [creatorEarning, creatorId]);
      logger.info(`[Earnings] Updated balance for creator ${creatorId}. Rows affected: ${updateResult.affectedRows}.`);
      
      // 5. Get creator's new balance for logging
      const creator = await User.findById(creatorId, connection);
      const balanceAfter = creator.balance;

      // 6. Create a balance log
      const logData = {
        user_id: creatorId,
        amount: creatorEarning,
        balance_after: balanceAfter,
        type: 'video_earning',
        description: `视频 "${video.title}" 售出收益`,
        related_id: earningId,
        related_type: 'earning'
      };
      await BalanceLog.create(logData, connection);
      logger.info(`[Earnings] Created balance log for creator ${creatorId}. New balance: ${balanceAfter}.`);

      logger.info(`[Earnings] Processed successfully for order ${order.order_no}. Creator ${creatorId} earned ${creatorEarning}.`);
      
      break;
    case 'recharge':
      // 增加用户余额
      const rechargeAmount = parseFloat(order.final_amount);
      if (isNaN(rechargeAmount) || rechargeAmount <= 0) {
        throw new AppError('无效的充值金额', 400, 'INVALID_RECHARGE_AMOUNT');
      }

      const updateRechargeBalanceSql = 'UPDATE users SET balance = balance + ? WHERE id = ?';
      await executor.query(updateRechargeBalanceSql, [rechargeAmount, order.user_id]);

      // (可选) 记录余额变动日志
      const balanceLogSql = `
        INSERT INTO balance_logs (user_id, amount, balance_after, type, description, related_id, related_type)
        SELECT ?, ?, u.balance, 'recharge', ?, ?, 'order'
        FROM users u WHERE u.id = ?
      `;
      // 为避免找不到表而报错，先注释掉
      /*
      await executor.query(balanceLogSql, [
        order.user_id,
        rechargeAmount,
        `订单充值: ${order.order_no}`,
        order.id,
        order.user_id
      ]);
      */
      logger.info(`用户(ID: ${order.user_id}) 充值成功: ${rechargeAmount}元`);
      break;
  }
}

module.exports = {
  processOrderBusiness
}; 